// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SGoods is the golang structure for table S_Goods.
type SGoods struct {
	Gid         int         `json:"GID"         orm:"GID"         ` //
	StoreID     int         `json:"StoreID"     orm:"StoreID"     ` //
	AreaID      int         `json:"AreaID"      orm:"AreaID"      ` //
	Scclassid   int         `json:"SCCLASSID"   orm:"SCCLASSID"   ` //
	GName       string      `json:"GName"       orm:"GName"       ` //
	Gday        int         `json:"GDAY"        orm:"GDAY"        ` //
	GPriceYJ    float64     `json:"GPriceYJ"    orm:"GPriceYJ"    ` //
	GPrice      float64     `json:"GPrice"      orm:"GPrice"      ` //
	GPrice2     float64     `json:"GPrice2"     orm:"GPrice2"     ` //
	GPrice3     float64     `json:"GPrice3"     orm:"GPrice3"     ` //
	FXPrice2    float64     `json:"FXPrice2"    orm:"FXPrice2"    ` //
	FXPrice     float64     `json:"FXPrice"     orm:"FXPrice"     ` //
	FXPrice3    float64     `json:"FXPrice3"    orm:"FXPrice3"    ` //
	GCounts     int         `json:"GCounts"     orm:"GCounts"     ` //
	GSoldCount  int         `json:"GSoldCount"  orm:"GSoldCount"  ` //
	GImg        string      `json:"GImg"        orm:"GImg"        ` //
	GSImg       string      `json:"GSImg"       orm:"GSImg"       ` //
	GContent    string      `json:"GContent"    orm:"GContent"    ` //
	GPost       string      `json:"GPost"       orm:"GPost"       ` //
	GPostCost   int         `json:"GPostCost"   orm:"GPostCost"   ` //
	GTime       *gtime.Time `json:"GTime"       orm:"GTime"       ` //
	GIState     uint        `json:"GIState"     orm:"GIState"     ` //
	GTop        uint        `json:"GTop"        orm:"GTop"        ` //
	GIntroduce  string      `json:"G_Introduce" orm:"G_Introduce" ` //
	CodeClassID int         `json:"CodeClassID" orm:"CodeClassID" ` //
	GSort       int         `json:"GSort"       orm:"GSort"       ` //
	GvipLeve    int         `json:"GvipLeve"    orm:"GvipLeve"    ` //
	GArea       int         `json:"GArea"       orm:"GArea"       ` //
	GSales      int         `json:"GSales"      orm:"GSales"      ` //
	GWeight     int         `json:"GWeight"     orm:"GWeight"     ` //
	Gdw         string      `json:"Gdw"         orm:"Gdw"         ` //
}
