// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLFormDao is internal type for wrapping internal DAO implements.
type internalLFormDao = *internal.LFormDao

// lFormDao is the data access object for table L_Form.
// You can define custom methods on it to extend its functionality as you wish.
type lFormDao struct {
	internalLFormDao
}

var (
	// LForm is globally public accessible object for table L_Form operations.
	LForm = lFormDao{
		internal.NewLFormDao(),
	}
)

// Fill with you ideas below.
