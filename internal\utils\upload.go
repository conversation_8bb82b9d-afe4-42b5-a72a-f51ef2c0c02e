package utils

import (
	"fmt"
	"io"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
)

// serverroot: "/home/<USER>"
// usession: manage
// uppath: "/home/<USER>"

var ctx = gctx.New()
var uploadsroot = g.Cfg().MustGet(ctx, "50cms.uploadsroot").String()
var uploads = g.Cfg().MustGet(ctx, "50cms.uploads").String()

// var upbase：=serverroot+

func ImgUrl() string {
	return "easygoadmin.image_url"
}

// 获取文件地址
func GetImageUrl(path string) string {
	return ImgUrl() + path
}

func InStringArray(value string, array []string) bool {
	for _, v := range array {
		if v == value {
			return true
		}
	}
	return false
}

func UpImg(dirname string, files ghttp.UploadFiles) ([]string, error) {

	// files := r.GetUploadFiles(filesname)

	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.New(gtime.Now().Time.In(loc))
	// 判断文件地址是否为空
	dateDirName := mytime.Format("Ymd")

	file_path := fmt.Sprintf("%s%s%s", uploadsroot+uploads, dateDirName, "/")
	//如果没有filepath文件目录就创建一个
	if _, err := os.Stat(file_path); err != nil {
		if !os.IsExist(err) {
			os.MkdirAll(file_path, os.ModePerm)
		}
	}
	//上传到的路径

	//filename_arr := strings.Split(files.Filename, ".")
	//重新名片-lunix系统不支持中文
	//name_str := md5Str(fmt.Sprintf("%v%s", nowTime, filename_arr[0]))      //组装文件保存名字
	// file_Filename := fmt.Sprintf("%s%s%s", name_str, ".", filename_arr[1]) //文件加.后缀
	// path := file_path + file_Filename
	// 上传文件到指定的目录
	names, err := files.Save(gfile.Join(file_path, dateDirName), true)

	//gfile.Join 用"/"拼接
	//fileName, err := File.Save(gfile.Join(uploadPath, dateDirName), RandomName)
	if err != nil {
		return nil, err
	}
	return names, err

}

func Upload(req *ghttp.Request) (msg string, code int) {
	contentType := req.Header.Get("content-type")
	contentLen := req.ContentLength
	code = 1 //失败

	fmt.Printf("upload content-type:%s,content-length:%d", contentType, contentLen)
	if !strings.Contains(contentType, "multipart/form-data") {

		msg = "content-type must be multipart/form-data"
	}
	if contentLen >= 5000*1024*1024 { // 10 MB
		msg = "file to large,limit 4MB"
	}

	err := req.ParseMultipartForm(5000 * 1024 * 1024)
	if err != nil {
		//http.Error(w, err.Error(), http.StatusInternalServerError)
		msg = "ParseMultipartForm error:" + err.Error()
	}
	if len(req.MultipartForm.File) == 0 {
		msg = "not have any file"
	}

	for name, files := range req.MultipartForm.File {
		fmt.Printf("req.MultipartForm.File,name=%s", name)

		if len(files) != 1 {
			msg = "too many files"
		}
		if name == "" {
			msg = "is not FileData"
		}

		for _, f := range files {
			handle, err := f.Open()
			if err != nil {
				msg = fmt.Sprintf("unknown error,fileName=%s,fileSize=%d,err:%s", f.Filename, f.Size, err.Error())
			}

			// 创建目录

			//curDir, _ := os.Getwd()
			filePath, _ := exec.LookPath(os.Args[0])
			absFilePath, _ := filepath.Abs(filePath)
			curDir := path.Dir(absFilePath)

			basePath := uploadsroot

			loc, _ := time.LoadLocation("Asia/Shanghai")
			mytime := gtime.Now().Time.In(loc)
			Imgname := uploads + mytime.Format("Ymd")

			dirPath := basePath + Imgname

			if !CreateDir(curDir + dirPath) {
				msg = "文件目录创建失败"
			}

			fmt.Printf("path-------------------%s", curDir+dirPath)

			nowTime := mytime.Unix() //当前时间
			filename_arr := strings.Split(f.Filename, ".")
			//重新名片-lunix系统不支持中文
			name_str := md5Str(fmt.Sprintf("%v%s", nowTime, filename_arr[0]))                        //组装文件保存名字
			file_Filename := fmt.Sprintf("%s%s%s", name_str, ".", filename_arr[len(filename_arr)-1]) //文件加.后缀

			path := "." + dirPath + "/" + file_Filename
			dst, irr := os.Create(path)
			if irr != nil {
				msg = fmt.Sprintf("unknown error os.Create(path): %s, error: %s", path, irr.Error())
			}

			io.Copy(dst, handle)
			dst.Close()
			fmt.Printf("successful uploaded,fileName=%s,fileSize=%.2f MB,savePath=%s \n", f.Filename, float64(contentLen)/1024/1024, path)

			//return url.QueryEscape(file_Filename)
			code = 0
			msg = Imgname + "/" + file_Filename
		}
	}
	return msg, code
}

func SaveImage(url string, dirname string) (string, error) {
	// 判断文件地址是否为空
	if gstr.Equal(url, "") {
		return "", gerror.New("文件地址不能为空")
	}

	// 判断是否本站图片
	if gstr.Contains(url, ImgUrl()) {
		// 本站图片

		// 是否临时图片
		if gstr.Contains(url, "temp") {
			// 临时图片

			loc, _ := time.LoadLocation("Asia/Shanghai")
			mytime := gtime.Now().Time.In(loc)
			// 创建目录
			dirPath := ImagePath() + "/" + dirname + "/" + mytime.Format("Ymd")
			if !CreateDir(dirPath) {
				return "", gerror.New("文件目录创建失败")
			}
			// 原始图片地址
			oldPath := gstr.Replace(url, ImgUrl(), UploadPath())
			// 目标目录地址
			newPath := ImagePath() + "/" + dirname + gstr.Replace(url, ImgUrl()+"/temp", "")
			// 移动文件
			os.Rename(oldPath, newPath)
			return gstr.Replace(newPath, UploadPath(), ""), nil
		} else {
			// 非临时图片
			path := gstr.Replace(url, ImgUrl(), "")
			return path, nil
		}
	} else {
		// 远程图片
		// TODO...
	}
	return "", gerror.New("保存文件异常")
}
