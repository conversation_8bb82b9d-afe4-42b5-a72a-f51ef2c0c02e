// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalInfoDao is internal type for wrapping internal DAO implements.
type internalInfoDao = *internal.InfoDao

// infoDao is the data access object for table Info.
// You can define custom methods on it to extend its functionality as you wish.
type infoDao struct {
	internalInfoDao
}

var (
	// Info is globally public accessible object for table Info operations.
	Info = infoDao{
		internal.NewInfoDao(),
	}
)

// Fill with you ideas below.
