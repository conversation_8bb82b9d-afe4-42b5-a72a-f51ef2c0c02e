<div class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">基础使用</div>
        <div class="layui-card-body">
            <div class="pear-row pear-col-space10">
                <div class="pear-col pear-col-md12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6">
                    <div class="grid-demo"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">栅格偏移</div>
        <div class="layui-card-body">
            <div class="pear-row pear-col-space10">
                <div class="pear-col pear-col-md12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6 pear-col-md-offset6">
                    <div class="grid-demo"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">自适应性</div>
        <div class="layui-card-body">
            <div class="pear-row pear-col-space10">
                <div class="pear-col pear-col-md6 pear-col-xs12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6 pear-col-xs12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6 pear-col-xs12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6 pear-col-xs12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6 pear-col-xs12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6 pear-col-xs12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6 pear-col-xs12">
                    <div class="grid-demo"></div>
                </div>
                <div class="pear-col pear-col-md6 pear-col-xs12">
                    <div class="grid-demo"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .grid-demo {
        padding: 10px;
        line-height: 60px;
        border-radius: var(--global-border-radius);
        background-color: var(--global-primary-color);
        text-align: center;
        color: #fff;
        min-height: 60px;
    }
</style>