// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SUaddress is the golang structure of table S_UADDRESS for DAO operations like Where/Data.
type SUaddress struct {
	g.Meta         `orm:"table:S_UADDRESS, do:true"`
	Uaid           interface{} //
	Uid            interface{} //
	Uopenid        interface{} //
	UPeopleName    interface{} //
	UAPhone        interface{} //
	UAProvance     interface{} //
	UACity         interface{} //
	UADistrict     interface{} //
	UAStreet       interface{} //
	UAddName       interface{} //
	UAddress       interface{} //
	UDetailAddress interface{} //
	UAAddTime      *gtime.Time //
	UAState        interface{} //
	Ulatng         interface{} //
}
