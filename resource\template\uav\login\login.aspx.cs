﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class shop_Login_login : System.Web.UI.Page
{

    public string reurl = "/4g/user.aspx", mimg;
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Request.QueryString["reurl"] != null && Request.QueryString["reurl"] != "")
        {
            reurl = Request.QueryString["reurl"].ToString();
        }
        var sysset = new SysSet().loadCacheConfig(Utils.GetMapPath("~/App_Data/WebSet.config"));
        //  
        mimg = sysset.WaterFont.ToString();

    }
    protected void wxlogin_Click(object sender, EventArgs e)
    {
        //到wxProces中取OpenId，取到后再跳回当前页面
        if (Session["OpenId"] == null || Session["OpenId"].ToString() == String.Empty)
        {
            Response.Write("<script>window.open('/5g/Login/wxProcess.aspx?reurl=" + HttpUtility.UrlEncode(Request.RawUrl) + "','_top');</script>");
        }
        else
        {
            if (Session["nologin"] == null || Session["nologin"].ToString() == String.Empty)
            {
                string wxid = Session["OpenId"].ToString();

                if (new UserBase().ULogin(wxid))
                {
                    //保存日志
                    new UserBase().SaveLogs(wxid, "[wx登录]：登录成功！");
                    Response.Redirect(reurl);
                }
            }

        }



    }

        


    protected void LoginBUT_Click(object sender, EventArgs e)
    {
        string UserName = txtUserName.Text.Trim();
        string UserPwd = Base64.Base64Decode(txtUserPwd.Text.Trim());
        if (UserName.Equals("") || UserPwd.Equals(""))
        {  
            Page.ClientScript.RegisterClientScriptBlock(GetType(), "js", "<script> layer.open({content: '请输入您要登录用户名,密码,以及验证码'});</script>");
        }
        else
        {
         

                if (Session["AdminLoginSun"] == null)
                {
                    Session["AdminLoginSun"] = 1;
                }
                else
                {
                    Session["AdminLoginSun"] = Convert.ToInt32(Session["AdminLoginSun"]) + 1;
                }
                //判断登录
                if (Session["AdminLoginSun"] != null && Convert.ToInt32(Session["AdminLoginSun"]) > 5)
                {
                Page.ClientScript.RegisterClientScriptBlock(GetType(), "js", "<script> layer.open({content: '登录错误超过5次，请关闭浏览器重新登录。'});</script>");
                 
                 }

                else if (new UserBase().ULogin(UserName, DESEncrypt.Encrypt(UserPwd)))
                {

                 string UID= Session["UID"].ToString();
                //保存日志
                new UserBase().SaveLogs(UID, "[QT用户登录]状态：登录成功！");
                     Response.Redirect(reurl);
                 }
                else
                {

                Page.ClientScript.RegisterClientScriptBlock(GetType(), "js", "<script> layer.open({content: '您输入的用户名或密码不正确。'});</script>");
   
                    //保存日志
                    new UserBase().SaveErrorLogs(UserName+" 状态：登录失败！" + txtUserPwd.Text);

                }

            
        }

    }



}