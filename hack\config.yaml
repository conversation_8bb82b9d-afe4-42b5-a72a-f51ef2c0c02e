
# CLI tool, only in development environment.
# https://goframe.org/pages/viewpage.action?pageId=3673173
gfcli:
  docker:
    build: "-a amd64 -s linux -p temp -ew"
    tagPrefixes:
      - my.image.pub/my-app
      
  gen:
    dao:
      #link:  "sqlite::@file(manifest/50cms.db)"
      link:  "mysql:pdstudy:kZwtTB6s8epxrTi6@tcp(43.228.77.237:20006)/pdstudy?parseTime=true"
      jsonCase: ""  # | 设置为空，或其他值    | 变量名和表字段名会保持一致 |  

  build: 
    name: "50go"    #与程序入口go文件同名
    arch: "amd64"
    system: "windows,linux"
    path: "./bin/"  # 输出的可执行文件路径，
    #output: "./bin/" 当该参数指定时，name和path参数失效，常用于编译单个可执行文件。
    version: "1.0.1"
    pack: ""
    #packSrc: "manifest/config"
    extra: ""
    # build: "-a amd64 -s linux -p temp -ew"
    # docker:
    # build: "-a amd64 -s linux -p temp -ew"
    # tagPrefixes:
    # - my.image.pub/my-app
# output参数没有用
# 会输出到temp/v1.0.1 目标里
# 可以使用path参数
#自动编译-run  
  run:
    # path:  "bin"
    extra: ""
    # args:  "all"
    watchPaths:
    - api/*.go
    - internal/controller/*.go

# database:
#   default:
#     link:  "sqlite::@file(manifest/50cms.db)"
#     jsonCase: "SnakeFirstUpper"  # | 设置为空，或其他值    | 变量名和表字段名会保持一致 |
#   h4a:
#     # link:  "mssql:YnSmes2023:12345678@tcp(43.228.77.237:14203)/YnSmes?encrypt=disable"
