// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SStore is the golang structure for table S_Store.
type SStore struct {
	Sid        int         `json:"SID"        orm:"SID"        ` //
	AreaID     int         `json:"areaID"     orm:"areaID"     ` //
	Sname      string      `json:"Sname"      orm:"Sname"      ` //
	Sstime     string      `json:"Sstime"     orm:"Sstime"     ` //
	Setime     string      `json:"Setime"     orm:"Setime"     ` //
	Stel       string      `json:"Stel"       orm:"Stel"       ` //
	Simg       string      `json:"Simg"       orm:"Simg"       ` //
	Saddress   string      `json:"Saddress"   orm:"Saddress"   ` //
	Slatitude  string      `json:"Slatitude"  orm:"Slatitude"  ` //
	Slongitude string      `json:"Slongitude" orm:"Slongitude" ` //
	Sintroduce string      `json:"Sintroduce" orm:"Sintroduce" ` //
	Stime      *gtime.Time `json:"Stime"      orm:"Stime"      ` //
	IsLock     uint        `json:"IsLock"     orm:"IsLock"     ` //
	SType      string      `json:"SType"      orm:"SType"      ` //
	SPower     string      `json:"SPower"     orm:"SPower"     ` //
}
