<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />
  <link rel="stylesheet" href="/admin/css/admin.css" />
  <link rel="stylesheet" href="/admin/css/admin.dark.css" />
  <link rel="stylesheet" href="/admin/css/variables.css" />
  <link rel="stylesheet" href="/admin/css/reset.css" />

</head>

<body class="pear-container pear-admin">
<div style="padding: 16px;" >

  <div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form"   method="get" action="#"  id="newslist">
            <div class="layui-form-pane">

                <div class="layui-form-item layui-inline">
                    <label class="layui-form-label">内容标题</label>
                    <div class="layui-input-inline">
                        <input type="text" name="title" placeholder="" class="layui-input" value="{{.search.title}}">
                    </div>
                </div>
                <div class="layui-form-item layui-inline">
                  <label class="layui-form-label">分类</label>
                  <div class="layui-input-inline">
       
                    <select name="classid" >
                      <option value="">所有分类</option>
                      {{range $key, $value := .classlist}}
                      <option value="{{$value.cid}}" {{if eq $.search.classid $value.cid }}selected{{end}} >{{$value.cnmae}}({{$value.cid}})</option>
                      {{end}}
  
                  </select>
               
                  </div>
              </div>
                <div class="layui-form-item layui-inline">
                    <label class="layui-form-label">作者</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" name="author" placeholder="" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item layui-inline">             
                  <div class="layui-input-inline" style="width: 100px;">




                    <select name="ctype" >
                      <option value="">属性 {{.search.ctype}}</option>
                      <option value="istop" {{if eq $.search.ctype "istop"}}selected{{end}} >推荐</option>
                      <option value="islock" {{if eq $.search.ctype "islock"}}selected{{end}}>锁定</option>
                      <option value="isred" {{if eq $.search.ctype "isred"}}selected{{end}}>图片</option>
                      <option value="ishistory" {{if eq $.search.ctype "ishistory"}}selected{{end}}>已删除</option>                    
                  </select>  

                  </div>
              </div>
                <div class="layui-form-item layui-inline">
                    <label class="layui-form-label">发布日期</label>
                    <div class="layui-input-inline">
                        <input type="text" name="time"  autocomplete="off" id="ID-laydate" placeholder=" - " class="layui-input"  value="{{.search.time}}" >
                    </div>
                </div>
                <div class="layui-form-item layui-inline">
                    <button class="layui-btn layui-btn-sm " lay-submit lay-filter="user-query">
                        <i class="layui-icon layui-icon-search"></i>
                        查询 
                    </button>
                    <!-- <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary"  style="color: var(--global-primary-color)">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button> -->
                </div>
            </div>

              <div class="layui-table-tool-self ">
                <button class="layui-btn layui-btn-sm ayui-btn-warm" lay-event="getCheckData" onclick="add()" load style="background-color: #36b368;"><i class="pear-icon pear-icon-add"></i>添加新闻</button>
              </div>
       
      

        </form>
    </div>
</div>

  <div class="layui-card">
    <div class="layui-card-body">

      <table class="layui-table"  id="role-table" >
        <thead>
          <tr>
            <th>标题</th>
            <th>分类</th>
            <th>时间</th>
            <th>属性</th>
       
            <th>删除</th>
            <th>修改</th>
          </tr>
        </thead>
        <tbody>
          {{range $key, $value := .newss}}
          <tr>
            <td>{{$value.title}}</td>
            <td>{{$value.classid}}</td>
            <td>{{$value.time}}</td>
            <td class="layui-form">
              <input type="checkbox" name="CCC" title="锁定|正常" lay-skin="switch" lay-filter="aaa" value="islock"  data-id="{{$value.nid}}" {{if eq $value.islock 1}} checked{{end}} alt="{{$value.islock}}">
              <input type="checkbox" name="CCC" title="推荐|非荐" lay-skin="switch" lay-filter="aaa" value="istop" data-id="{{$value.nid}}" {{if eq $value.istop 1}} checked{{end}} alt="{{$value.istop}}">
              <input type="checkbox" name="CCC" title="图片|非图" lay-skin="switch" lay-filter="aaa" value="isred" data-id="{{$value.nid}}" {{if eq $value.isred 1}} checked{{end}} alt="{{$value.isred}}">
            
   
            
              {{if eq $value.IsHistory 1}}           <button lay-id="44" type="button" class="tag-item layui-btn-radius layui-btn layui-btn-danger layui-btn-sm">已删除</button>{{end}}

  
            </td>
            <td><a class="layui-btn layui-btn-xs " onclick="remove({{$value.nid}})"><i class="pear-icon pear-icon-error"></i>删除</a></td>

            <td><a class="layui-btn layui-btn-xs" onclick="edit({{$value.nid}})" style="background-color: #36b368;"><i class="pear-icon pear-icon-edit"></i>编辑</a></td>
           



          </tr>
          {{end}}

        </tbody>
      </table>
      <div>{{.page}}</div> 
   
      
    </div>
  </div>

  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>

  <script>

// layui.use('form', function(){
//     var form = layui.form
//     form.render();

//     form.on('switch(aaa)', function(chkbox){

//   }
// });


    layui.use(["button","form","jquery","laydate"], function () {
      var admin = layui.admin;
      var button = layui.button;
      let form = layui.form;
      let $ = layui.jquery;
      var laydate = layui.laydate;

// window.addEventListener('storage', function(event) {
//   if (event.key === 'dark') {
//     darkTheme();
//   }
// });

// function darkTheme() {
//     if (localStorage.getItem('dark') === 'true') {
//         //此处需要根据你的iframe页面结构调整
//         $(".pear-admin").addClass("pear-admin-dark");
//     } else {
// 	//此处需要根据你的iframe页面结构调整
//         $(".pear-admin").removeClass("pear-admin-dark");
//     }
// }

 // 日期范围
 laydate.render({
    elem: "#ID-laydate",
    range: true,
    shortcuts: [
      {
        text: "上个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month - 1, 1),
            new Date(year, month, 0)
          ];
        }
      },
      {
        text: "这个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month, 1),
            new Date(year, month + 1, 0)
          ];
        }
      },
      {
        text: "下个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month + 1, 1),
            new Date(year, month + 2, 0)
          ];
        }
      }
    ]
  });

      button.load({
        elem: '[load]',
        time: 600,
        done: function () {
        //  popup.success("加载完成");
        }
      })
    })

    layui.use(['toast', 'jquery', 'layer',"form","popup"], function () {
      // var toast = layui.toast;
      let $ = layui.jquery;
      var form = layui.form
      var popup=layui.popup
    form.render();
      // toast.error({ title: "2危险消息", message: "消息描述" })
      // toast.warning({ title: "警告消息", message: "消息描述" })
      // toast.info({ title: "通知消息", message: "消息描述" })
      window.edit = function(obj) {
      layer.open({ 
        title: '编辑 - id:' + obj,
        type: 2,
        area: ['90%', '95%'],
        content: '/system/news/edit?nid=' + obj,
        end: function(){
          location.reload();
        }
      })
    }

    form.on('switch(aaa)', function(chkbox){
      layer.load(2, { shade: [0.35, '#ccc'] });
           console.log(chkbox.elem); //开关是否开启，true或者false
           console.log(chkbox.elem.checked); //开关是否开启，true或者false
           console.log(chkbox.value)
           console.log(">>>",chkbox.elem.dataset['id']); //开关是否开启，true或者false
           $.ajax({
                    url:  '/system/news/charge?nid='+chkbox.elem.dataset['id'],
                    data:{type:chkbox.value},
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							          popup.success("成功", function () {
                           // location.reload();
					            	});
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
             return false;

    });
        window.remove = function (obj) {
		    layer.confirm('确定删除吗?',{
					icon: 3,
					title: '提示'
                }, function (index) {
                  $.ajax({
                    url: '/system/news/charge?nid=' + obj,
                    data:{type:"del"},
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							             popup.success("删除成功", function () {
                          location.reload();
						           });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
          });
			}

    })
  

    window.add = function(obj) {
      layer.open({
        title: '添加',
        type: 2,
        area: ['90%', '95%'],
        content: '/system/news/edit?nid=0',
        end: function(){
          location.reload();
        }
      })
    }




    // layui.use(['notice', 'jquery', 'layer', 'code'], function () {
    //   var notice = layui.notice;

    //   notice.success("成功消息")
    //   notice.error("危险消息")
    //   notice.warning("警告消息")
    //   notice.info("通用消息")
    
    // })

  </script>
</body>

</html>