// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Task is the golang structure for table Task.
type Task struct {
	Id            int         `json:"ID"            orm:"ID"            ` //
	StudentID     int         `json:"StudentID"     orm:"StudentID"     ` //
	TeacherID     int         `json:"TeacherID"     orm:"TeacherID"     ` //
	Guid          string      `json:"Guid"          orm:"Guid"          ` //
	GPrice        string      `json:"GPrice"        orm:"GPrice"        ` //
	GPrice2       string      `json:"GPrice2"       orm:"GPrice2"       ` //
	GPrice3       string      `json:"GPrice3"       orm:"GPrice3"       ` //
	FXPrice2      string      `json:"FXPrice2"      orm:"FXPrice2"      ` //
	FXPrice       string      `json:"FXPrice"       orm:"FXPrice"       ` //
	QDimg         string      `json:"QDimg"         orm:"QDimg"         ` //
	QDContent     string      `json:"QDContent"     orm:"QDContent"     ` //
	QDTime        *gtime.Time `json:"QDTime"        orm:"QDTime"        ` //
	Qduid         string      `json:"QDUID"         orm:"QDUID"         ` //
	JHimg         string      `json:"JHimg"         orm:"JHimg"         ` //
	JHContent     string      `json:"JHContent"     orm:"JHContent"     ` //
	JHTime        *gtime.Time `json:"JHTime"        orm:"JHTime"        ` //
	Jhuid         string      `json:"JHUID"         orm:"JHUID"         ` //
	CTcount       int         `json:"CTcount"       orm:"CTcount"       ` //
	CTTime        string      `json:"CTTime"        orm:"CTTime"        ` //
	PJLeve        int         `json:"PJLeve"        orm:"PJLeve"        ` //
	PJTime        *gtime.Time `json:"PJTime"        orm:"PJTime"        ` //
	Pjuid         string      `json:"PJUID"         orm:"PJUID"         ` //
	PJContent     string      `json:"PJContent"     orm:"PJContent"     ` //
	PJimg         string      `json:"PJimg"         orm:"PJimg"         ` //
	PJstate       string      `json:"PJstate"       orm:"PJstate"       ` //
	Tstate        int         `json:"Tstate"        orm:"Tstate"        ` //
	Leve          int         `json:"Leve"          orm:"Leve"          ` //
	Feedback      string      `json:"Feedback"      orm:"Feedback"      ` //
	JZView        int         `json:"JZView"        orm:"JZView"        ` //
	JZFirstTime   *gtime.Time `json:"JZFirstTime"   orm:"JZFirstTime"   ` //
	JZContent     string      `json:"JZContent"     orm:"JZContent"     ` //
	ManageContent string      `json:"ManageContent" orm:"ManageContent" ` //
	ManageTime    *gtime.Time `json:"ManageTime"    orm:"ManageTime"    ` //
	ManageUID     string      `json:"ManageUID"     orm:"ManageUID"     ` //
}
