// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TaskDao is the data access object for the table Task.
type TaskDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TaskColumns        // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TaskColumns defines and stores column names for the table Task.
type TaskColumns struct {
	Id            string //
	StudentID     string //
	TeacherID     string //
	Guid          string //
	GPrice        string //
	GPrice2       string //
	GPrice3       string //
	FXPrice2      string //
	FXPrice       string //
	QDimg         string //
	QDContent     string //
	QDTime        string //
	Qduid         string //
	JHimg         string //
	JHContent     string //
	JHTime        string //
	Jhuid         string //
	CTcount       string //
	CTTime        string //
	PJLeve        string //
	PJTime        string //
	Pjuid         string //
	PJContent     string //
	PJimg         string //
	PJstate       string //
	Tstate        string //
	Leve          string //
	Feedback      string //
	JZView        string //
	JZFirstTime   string //
	JZContent     string //
	ManageContent string //
	ManageTime    string //
	ManageUID     string //
}

// taskColumns holds the columns for the table Task.
var taskColumns = TaskColumns{
	Id:            "ID",
	StudentID:     "StudentID",
	TeacherID:     "TeacherID",
	Guid:          "Guid",
	GPrice:        "GPrice",
	GPrice2:       "GPrice2",
	GPrice3:       "GPrice3",
	FXPrice2:      "FXPrice2",
	FXPrice:       "FXPrice",
	QDimg:         "QDimg",
	QDContent:     "QDContent",
	QDTime:        "QDTime",
	Qduid:         "QDUID",
	JHimg:         "JHimg",
	JHContent:     "JHContent",
	JHTime:        "JHTime",
	Jhuid:         "JHUID",
	CTcount:       "CTcount",
	CTTime:        "CTTime",
	PJLeve:        "PJLeve",
	PJTime:        "PJTime",
	Pjuid:         "PJUID",
	PJContent:     "PJContent",
	PJimg:         "PJimg",
	PJstate:       "PJstate",
	Tstate:        "Tstate",
	Leve:          "Leve",
	Feedback:      "Feedback",
	JZView:        "JZView",
	JZFirstTime:   "JZFirstTime",
	JZContent:     "JZContent",
	ManageContent: "ManageContent",
	ManageTime:    "ManageTime",
	ManageUID:     "ManageUID",
}

// NewTaskDao creates and returns a new DAO object for table data access.
func NewTaskDao(handlers ...gdb.ModelHandler) *TaskDao {
	return &TaskDao{
		group:    "default",
		table:    "Task",
		columns:  taskColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TaskDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TaskDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TaskDao) Columns() TaskColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TaskDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TaskDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TaskDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
