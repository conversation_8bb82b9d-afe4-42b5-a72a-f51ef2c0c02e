// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ABJiuy is the golang structure of table AB_jiuy for DAO operations like Where/Data.
type ABJiuy struct {
	g.Meta        `orm:"table:AB_jiuy, do:true"`
	JyId          interface{} //
	Guid          interface{} //
	OpenID        interface{} //
	Uid           interface{} //
	Fcren         interface{} //
	Fctel         interface{} //
	Fctitle       interface{} //
	Fclat         interface{} //
	Fclong        interface{} //
	Jcren         interface{} //
	Jctel         interface{} //
	Jctitle       interface{} //
	Jcaddress     interface{} //
	Jclat         interface{} //
	Jclong        interface{} //
	Distance      interface{} //
	Carjz         interface{} //
	Carzt         interface{} //
	Cartype       interface{} //
	Content       interface{} //
	AddTime       *gtime.Time //
	EditTime      *gtime.Time //
	State         interface{} //
	DelTime       *gtime.Time //
	Paystat       interface{} //
	Payway        interface{} //
	Paytime       *gtime.Time //
	OutTradeNo    interface{} //
	OOrderNumber  interface{} //
	Paynotice     interface{} //
	Imgs          interface{} //
	Jjimgs        interface{} //
	Idimgs        interface{} //
	Didimgs       interface{} //
	Caridimgs     interface{} //
	Price         interface{} //
	Price2        interface{} //
	Type          interface{} //
	TransactionId interface{} //
	Uwxapp        interface{} //
	UsjID         interface{} //
	BegainUsjID   interface{} //
	BegainTime    *gtime.Time //
	BegainImg     interface{} //
	BegainContent interface{} //
	EndUsjID      interface{} //
	EndTime       *gtime.Time //
	EndImg        interface{} //
	EndContent    interface{} //
	Yytime        *gtime.Time //
	Yydate        *gtime.Time //
	Isdel         interface{} //
	Endstate      interface{} //
	Sh1           interface{} //
	Shtime        *gtime.Time //
	Shmark        interface{} //
	Shren         interface{} //
}
