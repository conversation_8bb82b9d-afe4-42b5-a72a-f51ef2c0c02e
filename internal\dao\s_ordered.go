// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalSOrderedDao is internal type for wrapping internal DAO implements.
type internalSOrderedDao = *internal.SOrderedDao

// sOrderedDao is the data access object for table S_Ordered.
// You can define custom methods on it to extend its functionality as you wish.
type sOrderedDao struct {
	internalSOrderedDao
}

var (
	// SOrdered is globally public accessible object for table S_Ordered operations.
	SOrdered = sOrderedDao{
		internal.NewSOrderedDao(),
	}
)

// Fill with you ideas below.
