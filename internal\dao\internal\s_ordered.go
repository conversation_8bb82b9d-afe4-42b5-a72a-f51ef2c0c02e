// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SOrderedDao is the data access object for the table S_Ordered.
type SOrderedDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SOrderedColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SOrderedColumns defines and stores column names for the table S_Ordered.
type SOrderedColumns struct {
	Oid       string //
	OrderId   string //
	OHxOpenId string //
	OHxTime   string //
}

// sOrderedColumns holds the columns for the table S_Ordered.
var sOrderedColumns = SOrderedColumns{
	Oid:       "OID",
	OrderId:   "OrderId",
	OHxOpenId: "OHxOpenId",
	OHxTime:   "OHxTime",
}

// NewSOrderedDao creates and returns a new DAO object for table data access.
func NewSOrderedDao(handlers ...gdb.ModelHandler) *SOrderedDao {
	return &SOrderedDao{
		group:    "default",
		table:    "S_Ordered",
		columns:  sOrderedColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SOrderedDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SOrderedDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SOrderedDao) Columns() SOrderedColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SOrderedDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SOrderedDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SOrderedDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
