﻿using System;
using System.Web;
using WxPayAPI;

public partial class wxProcess : System.Web.UI.Page
{
    private static string appid = WxPayConfig.APPID;
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            string reurl = "/4G/user.aspx";
            //传递参数，获取用户信息后，可跳转到自己定义的页面，想怎么处理就怎么处理
            if (Request.QueryString["reurl"] != null && Request.QueryString["reurl"] != "")
            {
                reurl = Request.QueryString["reurl"].ToString();
            }
            //弹出授权页面(如在不弹出授权页面基础下未获得openid，弹出授权页面，提示用户授权)
            if (Request.QueryString["auth"] != null && Request.QueryString["auth"] != "" && Request.QueryString["auth"] == "1")
            {
                Response.Redirect("https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + appid + "&redirect_uri=http://" + HttpContext.Current.Request.Url.Host + "/4G/login/wxProcess2.aspx?reurl=" + reurl + "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect");
            }
            else
            {
                //不弹出授权页面
                Response.Redirect("https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + appid + "&redirect_uri=http://" + HttpContext.Current.Request.Url.Host + "/4G/login/wxProcess2.aspx?reurl=" + reurl + "&response_type=code&scope=snsapi_base&state=1#wechat_redirect");
            }
        }
    }
}
