// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskStudent is the golang structure for table Task_Student.
type TaskStudent struct {
	Id          int         `json:"ID"          orm:"ID"          ` //
	SGuid       string      `json:"SGuid"       orm:"SGuid"       ` //
	Tid         string      `json:"TID"         orm:"TID"         ` //
	Fid         string      `json:"FID"         orm:"FID"         ` //
	ISvipleve   int         `json:"ISvipleve"   orm:"ISvipleve"   ` //
	OState      int         `json:"OState"      orm:"OState"      ` //
	OType       string      `json:"OType"       orm:"OType"       ` //
	OCreatTime  *gtime.Time `json:"OCreatTime"  orm:"OCreatTime"  ` //
	Name        string      `json:"Name"        orm:"Name"        ` //
	Tel         string      `json:"Tel"         orm:"Tel"         ` //
	Provance    string      `json:"Provance"    orm:"Provance"    ` //
	City        string      `json:"City"        orm:"City"        ` //
	District    string      `json:"District"    orm:"District"    ` //
	Street      string      `json:"Street"      orm:"Street"      ` //
	ADDress     string      `json:"ADDress"     orm:"ADDress"     ` //
	BertheDay   *gtime.Time `json:"BertheDay"   orm:"BertheDay"   ` //
	ORemarks    string      `json:"ORemarks"    orm:"ORemarks"    ` //
	Com         string      `json:"Com"         orm:"Com"         ` //
	School      string      `json:"School"      orm:"School"      ` //
	Class       string      `json:"Class"       orm:"Class"       ` //
	ADDlocation string      `json:"ADDlocation" orm:"ADDlocation" ` //
	ADDname     string      `json:"ADDname"     orm:"ADDname"     ` //
	ADDtitle    string      `json:"ADDtitle"    orm:"ADDtitle"    ` //
	ADDdetail   string      `json:"ADDdetail"   orm:"ADDdetail"   ` //
	ADDlat      string      `json:"ADDlat"      orm:"ADDlat"      ` //
	ADDlon      string      `json:"ADDlon"      orm:"ADDlon"      ` //
}
