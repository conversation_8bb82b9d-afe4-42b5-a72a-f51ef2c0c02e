﻿@charset "utf-8";


body {
	min-width: 1200px;
	background: url(../images/body.jpg) #f5f5f5 top center no-repeat;
}
.header {
	width: 100%;
	height: 180px;
	position: relative;
}
.container {
	width: 1200px;
	margin: 0 auto;
	position: relative;
}
.container_col {
	background: #fff;
	padding: 15px;
	width: 1170px;
	margin: 0 auto;
}
#weather {
	float: left;
	font-size: 12px;
	color: #666;
	line-height: 24px;
	width: 260px;
	margin-top: 25px;
	margin-left: 20px;
}
.toptxt {
	font-size: 12px;
	color: #888;
	line-height: 34px;
	text-align: right;
}
.toptxt a {
	font-size: 12px;
	color: #888;
	line-height: 34px;
	padding: 0 10px;
}
.toptxt a:hover {
	color: #b61412;
}
.toptxt .diqu {
	position: absolute;
	left: 0;
	top: 0;
	line-height: 34px;
	color: #333;
}
.search {
	height: 20px;
	padding: 10px 0;
	width: 300px;
	background: #fff;
	position: absolute;
	right: 0;
	top: 80px;
	padding-right: 90px;
}
.search .search_btn {
	display: block;
	width: 80px;
	height: 40px;
	background: #cf0d0d;
	border: 0;
	color: #fff;
	font-size: 16px;
	line-height: 40px;
	text-align: center;
	position: absolute;
	top: 0;
	right: 0;
	transition: all .3s ease;
}
.search .search_btn:hover {
	background: #b60606;
}
.ipt-sea {
	height: 20px;
	line-height: 20px;
	font-size: 14px;
	color: #666;
	outline: none;
	border: 0;
	width: 285px;
	padding-left: 15px;
}
.logo {
	width: 420px;
	height: 112px;
	position: absolute;
	left: 0;
	top: 50px;
}
.nav {
	width: 1200px;
	height: 80px;
	margin: 0 auto 15px auto;
	background: #cf0d0d;
}
.nav li {
	width: 148px;
	float: left;
	height: 80px;
	position: relative;
	text-align: center;
	padding-right: 2px;
	background: url(../images/shuxian.png) right top no-repeat;
}
.nav li a {
	display: block;
	text-align: center;
	line-height: 16px;
	height: 80px;
	font-size: 15px;
	color: #fff;
	transition: all .3s ease;
	overflow: hidden;
}
.nav li a img {
	display: block;
	margin: 15px auto 10px auto;
	transition: all .3s ease;
}
.nav li a:hover, .nav li.active a {
	background: #b60606;
	color: #fff;
}
.nav li a:hover img {
	-moz-transform: scaleX(-1);
	-webkit-transform: scaleX(-1);
	-o-transform: scaleX(-1);
	transform: scaleX(-1);
}
/*滚动新闻*/
.zxgg {
	width: 1170px;
	margin: 10px auto auto auto;
	background: url(../images/laba.jpg) no-repeat #fcfcfc;
	padding-left: 30px;
}
.BreakingNewsController {
	width: 100%;
	overflow: hidden;
	height: auto;
	position: relative;
	height: 35px;
}
.BreakingNewsController .bn-title {
	display: inline-block;
	float: left;
	padding: 0 20px;
	line-height: 35px;
}
.BreakingNewsController ul {
	padding: 0;
	margin: 0;
	display: block;
	list-style: none;
	position: absolute;
	left: 180px;
	right: 50px;
}
.BreakingNewsController ul li {
	list-style: none;
	padding: 0 20px;
	display: none;
	height: 35px;
}
.BreakingNewsController ul li a {
	text-decoration: none;
	color: #333;
	display: inline-block;
	overflow: hidden;
	padding: 0;
	line-height: 35px;
}
.BreakingNewsController .bn-arrows {
	display: inline-block;
	float: right;
	width: 50px;
	position: absolute;
	right: 0;
	top: 0;
	bottom: 0;
}
.BreakingNewsController .bn-arrows span {
	display: block;
	width: 20px;
	position: absolute;
	top: 0;
	bottom: 0;
	cursor: pointer;
	opacity: 0.2;
}
.BreakingNewsController .bn-arrows span:hover {
	opacity: 1;
}
.BreakingNewsController .bn-arrows-left {
	left: 0;
	background: url(../images/bn-arrows.png) left center no-repeat;
}
.BreakingNewsController .bn-arrows-right {
	right: 10px;
	background: url(../images/bn-arrows.png) right center no-repeat;
}
.easing a, .easing span {
	transition: .25s linear;
	-moz-transition: .25s linear;
	-webkit-transition: .25s linear;
}
.news-notice {
	margin: 0;
}
.indnews {
	width: 560px;
	height: 362px;
	float: left;
}
.news-pic {
	width: 560px;
	height: 362px;
}
.news-pic, .news-pic img {
	width: 560px;
	height: 362px;
	overflow: hidden;
}
.news-notice .news-txt {
	width: 592px;
	height: 362px;
	float: right;
	border: 1px solid #efefef;
}
.news-title {
	height: 38px;
	background: #fafafa;
	border-bottom: 1px solid #efefef;
	line-height: 38px;
	position: relative;
}
.news-title .name {
	position: absolute;
	left: -1px;
	top: -1px;
	height: 39px;
	background: #084466;
	color: #fff;
	text-align: center;
	line-height: 39px;
	font-size: 15px;
	padding: 0 15px;
	font-weight: bold;
}
.news-name.tab-nav {
	position: absolute;
	left: -1px;
	top: -1px;
	height: 39px;
}
.news-name.tab-nav a {
	display: block;
	float: left;
	height: 39px;
	line-height: 39px;
	color: #333;
	text-align: center;
	line-height: 39px;
	font-size: 15px;
	padding: 0 15px;
	margin: 0;
	position: relative;
}
.news-name.tab-nav a.current {
	background: #cf0d0d;
	color: #fff;
	font-weight: bold;
}
.news-name.tab-nav a i {
	width: 9px;
	height: 5px;
	background: url(../images/jtx.png) no-repeat;
	left: 10px;
	bottom: -5px;
	display: none;
}
.news-name.tab-nav a.current i {
	display: block;
}
.news-title .name i {
	width: 9px;
	height: 5px;
	background: url(../images/jtx.png) no-repeat;
	left: 10px;
	bottom: -5px;
	display: block;
}
.news-title .more {
	position: absolute;
	right: 10px;
	line-height: 38px;
	top: 0;
	font-size: 12px;
	color: #999;
	transition: all .3s ease;
}
.news-title .more:hover {
	color: #084466;
}
.newslist {
	display: block;
	padding: 10px 12px;
}
.hotnews {
	margin: 12px 12px 0 12px;
}
.hotnews h1 {
	font-size: 16px;
	color: #333;
	text-align: center;
	margin: 0 10px;
	height: 30px;
	line-height: 30px;
	word-break: break-all;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
.hotnews .hotcon {
	margin-top: 12px;
	word-break: break-all;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
.hotnews .hotcon a {
	color: #999;
	line-height: 24px;
	font-size: 14px;
}
.hotnews .hotcon a:hover, .newslist li a:hover {
	color: #b60606;
}
.newslist li {
	border-bottom: 1px dotted #dedede;
	position: relative;
	padding: 0 80px 0 16px;
	background: url(../images/listnot.png) 0px 15px no-repeat;
}
.newslist li a {
	display: block;
	height: 36px;
	width: 100%;
	line-height: 36px;
	font-size: 14px;
	color: #666;
	word-break: break-all;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
	position: relative;
}
.newslist li span {
	position: absolute;
	right: 0;
	top: 0;
	line-height: 36px;
	color: #bbb;
	text-align: center;
}
.newslist li:last-child {
	border: 0;
}
.newslist.ny li {
	padding: 8px 80px 8px 14px;
	background: url(../images/listnot.png) 2px 24px no-repeat;
}
.newslist.ny li a:after {
	display: none;
}
.nytit {
	border-bottom: 1px solid #ededed;
	line-height: 30px;
	font-size: 16px;
	padding-bottom: 10px;
	margin-bottom: 10px;
	font-weight: bold;
}
.xzgg {
	margin: 15px auto;
	width: 1200px;
}
.xzgg img {
	width: 100%;
}
.r-radius:50%;
margin:0 auto;
transition:all .3s ease;
}
.fwzn .iconlist a:hover {
	color: #F71013;
}
.toutiao {
	background: #f5f5f5;
	height: 50px;
	position: relative;
	padding: 12px 90px;
	margin-bottom: 15px;
}
.toutiao i {
	width: 82px;
	height: 69px;
	background: url(../images/toutiao.png) no-repeat;
	position: absolute;
	left: 0;
	top: 0;
}
.toutiao h1 {
	text-align: center;
}
.toutiao h1 a {
	font-size: 18px;
	color: #333;
}
.toutiao h1 a:hover {
	color: #b60606;
}
.toutiao p {
	height: 24px;
	line-height: 24px;
	font-size: 14px;
	color: #666;
	word-break: break-all;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
.fwzn {
	margin: 20px 0;
	background: #d5e7f6;
	height: 140px;
	overflow: hidden;
}
.fwzn .tit {
	width: 58px;
	background: #cf0d0d;
	text-align: center;
	font-size: 16px;
	color: #fff;
	height: 115px;
	padding-top: 25px;
	float: left;
}
.fwzn .iconlist {
	float: left;
	width: 1112px;
}
.fwzn .iconlist a {
	display: block;
	width: 158px;
	text-align: center;
	float: left;
	color: #333;
	font-size: 14px;
	line-height: 30px;
}
.fwzn .iconlist a .pic {
	width: 82px;
	height: 82px;
	margin: 20px auto auto auto;
	transition: all .3s ease;
}
.fwzn .iconlist a.icon1 .pic {
	background: #eacd75;
}
.fwzn .iconlist a.icon2 .pic {
	background: #93d884;
}
.fwzn .iconlist a.icon3 .pic {
	background: #f57b7b;
}
.fwzn .iconlist a.icon4 .pic {
	background: #b19de5;
}
.fwzn .iconlist a.icon5 .pic {
	background: #60dcb5;
}
.fwzn .iconlist a.icon6 .pic {
	background: #7ebfe2;
}
.fwzn .iconlist a.icon7 .pic {
	background: #dec67c;
}
.fwzn .iconlist a.icon1:hover .pic {
	background: #084466;
}
.fwzn .iconlist a.icon2:hover .pic {
	background: #084466;
}
.fwzn .iconlist a.icon3:hover .pic {
	background: #084466;
}
.fwzn .iconlist a.icon4:hover .pic {
	background: #084466;
}
.fwzn .iconlist a.icon5:hover .pic {
	background: #084466;
}
.fwzn .iconlist a.icon6:hover .pic {
	background: #084466;
}
.fwzn .iconlist a.icon7:hover .pic {
	background: #084466;
}
.xz-col {
	background: #fff;
	padding: 15px;
	height: 370px;
	float: left;
	margin-right: 15px;
	margin-top: 15px;
	margin-bottom: 15px;
}
.xz-col.abt {
	width: 360px;
}
.xz-col.abt img {
	width: 100%;
}
.xz-col.gsgg {
	width: 360px;
	margin-right: 0;
}
.xz-col.jlhd {
	width: 360px;
	margin-right: 0;
	float: right;
}
.hudongicon a {
	display: block;
	float: left;
	width: 175px;
	height: 102px;
	border-bottom: 1px solid #f2f2f2;
	border-right: 1px solid #f2f2f2;
	text-align: center;
}
.hudongicon a img {
	margin: 15px auto;
	display: block;
	transition: all .3s ease;
}
.hudongicon a.nor {
	border-right: 0;
}
.hudongicon a.nob {
	border-bottom: 0;
}
.hudongicon a:hover {
	color: #cf0d0d;
}
.hudongicon a:hover img {
	-moz-transform: scaleX(-1);
	-webkit-transform: scaleX(-1);
	-o-transform: scaleX(-1);
	transform: scaleX(-1);
}
.col-title {
	background: url(../images/titbot.png) left bottom no-repeat;
	overflow: hidden;
	padding-bottom: 15px;
}
.col-title span p {
	font-size: 18px;
	color: #cf0d0d;
}
.col-title span i {
	font-size: 12px;
	color: #b6b6b6;
	margin-top: 10px;
}
.col-title a {
	float: right;
	line-height: 30px;
}
.col-con {
	margin-top: 15px;
}
.col-con .abttxt {
	font-size: 14px;
	line-height: 28px;
	color: #666;
	margin-top: 15px;
	word-break: break-all;
	display: -webkit-box;
	-webkit-line-clamp: 6;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
.videolist {
	overflow: hidden;
}
.videolist ul {
	width: 1200px;
}
.videolist ul li {
	width: 286px;
	height: 222px;
	float: left;
	text-align: center;
	line-height: 30px;
	margin-right: 8px;
	font-size: 14px;
}
.videolist ul li img {
	width: 286px;
	height: 192px;
}
.sqbs li {
	width: 33.33%;
	text-align: center;
	float: left;
	margin-bottom: 15px;
	margin-top: 10px;
}
.sqbs li a:hover {
	color: #b61412;
}
.mqmy {
	margin: 20px 0;
}
.mqmy li {
	width: 301px;
	height: 60px;
	margin: 10px auto;
	border: 1px solid #dddddd;
}
.bmfw {
	margin-right: -15px;
}
.bmfw li {
	width: 235px;
	float: left;
	margin-right: 25px;
	padding-left: 120px;
	margin-bottom: 30px;
	margin-top: 15px;
	position: relative;
}
.bmfw li .pic {
	display: block;
	width: 100px;
	height: 100px;
	position: absolute;
	left: 0;
	top: 0;
}
.bmfw li .pic img {
	width: 100px;
	height: 100px;
}
.bmfw li .pic.rad {
	border-radius: 50%;
	overflow: hidden;
}
.bmfw .info .list {
	line-height: 15px;
	font-size: 12px;
	margin-bottom: 8px;
	margin-top: 2px;
}
.bmfw .info .list i {
	height: 15px;
	width: 15px;
	float: left;
	margin-right: 5px;
}
.gsht {
	float: left;
	width: 400px;
}
.col-2-r .tit {
	height: 40px;
	background: url(../images/sficon.png) 0 9px no-repeat;
	padding-left: 30px;
	line-height: 40px;
	color: #333;
	font-size: 16px;
	float: left;
}
.sfbox {
	padding: 0 10px 15px 5px;
	height: 213px;
}
.sftxt {
	float: left;
	width: 424px;
	height: 215px;
	position: relative;
}
.sftxt .info {
	height: 198px;
	line-height: 22px;
	font-size: 12px;
	color: #666;
	word-break: break-all;
	display: -webkit-box;
	-webkit-line-clamp: 9;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
.sftxt a {
	display: block;
	position: absolute;
	left: 0;
	bottom: -2px;
	text-align: center;
	color: #F30;
}
.sftxt a:hover {
	color: #39F;
}
.jisuan {
	float: right;
	padding-left: 30px;
	width: 270px;
	border-left: 1px dashed #efefef;
	height: 213px;
}
.jisuan h1 {
	color: #084466;
	line-height: 30px;
	font-size: 15px;
}
.jsbox {
	position: relative;
	padding-left: 65px;
	margin-top: 8px;
}
.jsbox span {
	position: absolute;
	left: 0;
	top: 0;
	line-height: 27px;
	display: block;
	width: 65px;
	color: #7e7e7e;
}
.jsbox .jsq {
	border: 1px solid #f0f0f0;
	height: 19px;
	padding: 3px 3px 3px 10px;
	border-radius: 2px;
}
.jsbox .jsq .ipt-jsq {
	border: 0;
	height: 19px;
	line-height: 19px;
	width: 100%;
	outline: none;
}
.jsbox .jsq a {
	display: block;
	width: 42px;
	height: 19px;
	text-align: center;
	line-height: 19px;
	background: #55aeec;
	position: absolute;
	top: 4px;
	right: 4px;
	color: #fff;
	border-radius: 2px;
}
.jsjg {
	background: #f0f0f0;
	padding: 4px 8px;
	margin-top: 10px;
	position: relative;
}
.jsjg .tab {
	line-height: 28px;
	border-bottom: 1px solid #e2e2e2;
	color: #6a6a6a;
	height: 28px;
	overflow: hidden;
}
.jsjg .tab span {
	float: right;
}
.jsjg .tab.last {
	border: 0;
}
.jsjg:before {
	content: '';
	width: 9px;
	height: 5px;
	background: url(../images/jtt.png) no-repeat;
	display: block;
	position: absolute;
	right: 20px;
	top: -5px;
}
.jisuan .smtxt {
	color: #55aeec;
	line-height: 40px;
}
.xxlinks {
	height: 24px;
	overflow: hidden;
}
.xxlinks a {
	color: #F33;
	line-height: 24px;
}
.xxlinks a:hover {
	color: #55aeec;
	text-decoration: underline;
}
.newslist.htlist {
	padding: 0;
	width: 360px;
}
.col-4 {
	width: 289px;
	float: left;
	margin-right: 12px;
	border: 1px solid #efefef;
}
.col-4.last {
	margin-right: 0;
}
.news-title .name2 {
	float: left;
	line-height: 39px;
	font-size: 15px;
	font-weight: bold;
	color: #084466;
	padding-left: 20px;
	background: url(../images/shu.png) 8px 11px no-repeat;
}
.linksbox {
	background: #fff;
	margin: 15px auto;
	width: 1200px;
}
.txtlisttit {
	height: 38px;
	border-bottom: 2px solid #d00c0d;
}
.txtlisttit a {
	display: block;
	height: 38px;
	line-height: 38px;
	float: left;
	padding: 0 40px;
	color: #333;
	transition: all .3s ease;
	font-size: 14px;
}
.txtlisttit a.current {
	background: #d00c0d;
	color: #fff;
}
.linksbox .listcon {
	height: 70px;
}
.linksbox .linkslist li {
	margin: 5px 10px;
	line-height: 30px;
	float: left;
	font-size: 14px;
}
.foot {
	font-size: 12px;
	color: #787878;
	line-height: 30px;
	background: #fff;
	text-align: center;
	width: 1200px;
	text-align: center;
	padding: 20px 0;
	margin: 15px auto;
}
.mg-t-b {
	margin: 15px auto;
}
.page-left {
	width: 200px;
	float: left;
}
.page-right {
	width: 900px;
	float: right;
	background: #fff;
	padding: 40px;
}
.lujing {
	margin: 20px 0;
	font-size: 14px;
	color: #888;
}
.pageny {
	overflow: hidden;
	padding-bottom: 30px;
}
.fllist {
	border: 1px solid #ededed;
}
.fllist li {
	height: 60px;
	background: #fff;
	padding: 0 20px;
}
.fllist li a {
	display: block;
	border-bottom: 1px solid #ededed;
	line-height: 59px;
	font-size: 16px;
	color: #666;
}
.fllist li:hover, .fllist li.active {
	background: #F15B5B;
}
.fllist li:hover a, .fllist li.active a {
	color: #fff;
	border-bottom: 0;
}
.fllist li.active {
	border-left: 4px solid #b61412;
}
.hotarticl {
	margin-top: 20px;
}
.hottit {
	border-bottom: 1px solid #efefef;
	line-height: 40px;
	font-size: 16px;
	color: #F30;
	margin: 12px 12px 0 12px;
}
.pagelujing {
	height: 40px;
	border-bottom: 2px solid #efefef;
	position: relative;
}
.pagelujing .name {
	position: absolute;
	left: 0;
	top: 0;
	height: 40px;
	border-bottom: 2px solid #F33;
	line-height: 40px;
	font-size: 16px;
	font-weight: bold;
}
.pagelujing span {
	position: absolute;
	right: 0;
	line-height: 40px;
	color: #888;
}
.news-txt.ny, .newslist.ny {
	border: 0;
	padding: 0;
}
.news-txt.ny {
	min-height: 600px;
}
.newslist.ny li:last-child {
	border-bottom: 1px dotted #efefef;
}
.liuyantab {
	border: 1px solid #f5f5f5;
	background: #f8f8f8;
	margin-top: 20px;
	padding: 20px;
}
/*分页*/

.biaoti {
	font-size: 18px;
	color: #333;
	text-align: center;
	margin: 0 20px;
	font-weight: 600;
	margin-top: 30px;
}
.sshuomign {
	color: #888;
	font-size: 12px;
	line-height: 30px;
	text-align: center;
	font-weight: normal;
}
.sshuomign span {
	margin: 0 10px;
}
.pages {
	clear: both;
	margin: 20px;
	overflow: hidden;
	margin-left: 0px;
	text-align: center;
	font-size: 12px
}
.pages ul li {
	display: inline-block;
	border: 1px solid #ccc;
	padding: 4px 12px;
	margin: 0 1px;
	line-height: 24px;
	background: #fff;
	color: #999
}
.pages ul li:hover {
	background: #cf0d0d;
	color: #fff;
	border: 1px solid #cf0d0d
}
.pages ul li:hover a {
	color: #fff;
}
.pages ul li.thisclass {
	display: inline-block;
	border: 1px solid #cf0d0d;
	padding: 4px 12px;
	margin: 0 1px;
	background: #cf0d0d;
	color: #fff;
}
.pages ul li.thisclass a {
	color: #fff;
}
.pages ul li a {
	display: block;
	color: #999
}
.pages ul li a:hover {
	color: #fff;
}
.article_txt {
	font-size: 14px;
	line-height: 28px;
	color: #666;
	margin-top: 20px;
	overflow: hidden
}
.article_txt img {
	max-width: 930px;
}
/* 反馈模块 */
.contact-com-body {
	padding: 30px 10px 25px 10px;
	border-bottom: 1px solid #d3d3d3
}
.contact-com-body .row {
}
.row:before, .row:after {
	content: "";
	display: table;
	line-height: 0
}
.row:after {
	clear: both
}
.row {
	margin-bottom: 15px;
*zoom: 1
}
.col-2, .col-10 {
	float: left;
	position: relative;
	min-height: 1px
}
.contact-com-body .col-2 {
	width: 60px;
	line-height: 40px;
	margin-right: 10px;
	text-align: left
}
.contact-com-body .input-control {
	padding: 5px;
	line-height: 30px;
	height: 30px;
	width: 400px;
	border: 1px solid #ccc
}
.contact-com-body .textarea-control {
	height: 106px;
	width: 490px;
	padding: 5px;
	border: 1px solid #ccc
}
.contact-com-body .help-inline {
	line-height: 40px;
	color: #F00
}
.contact-com-body .input-shade {
	background-color: #f8f8f8
}
.contact-com-body .input-short {
	width: 340px
}
.contact-com-body .input-shorter {
	width: 140px
}
.contact-com-body img {
	width: 80px;
	height: 41px;
	margin: 2px 5px
}
.contact-com-btn {
	margin-left: 70px;
	padding-top: 20px
}
.contact-com-btn .btn {
	margin-right: 30px
}
.btn-warning {
	padding: 12px 44px;
	font-weight: bold;
	color: #fff;
	border: none;
	background-color: #ff8942;
	background: -webkit-linear-gradient(top, #ff9454, #ff7f30);
	background: -moz-linear-gradient(top, #ff9454, #ff7f30);
	background: -ms-linear-gradient(top, #ff9454, #ff7f30);
	background: -o-linear-gradient(top, #ff9454, #ff7f30)
}
.btn-warning:hover {
	color: #fff;
	background-color: #ff8945;
	background: -webkit-linear-gradient(top, #ff8237, #fd8f4d);
	background: -moz-linear-gradient(top, #ff8237, #fd8f4d);
	background: -ms-linear-gradient(top, #ff8237, #fd8f4d);
	background: -o-linear-gradient(top, #ff8237, #fd8f4d)
}
.btn-danger {
	padding: 12px 44px;
	font-weight: bold;
	color: #fff;
	border: none;
	background-color: #13bd49;
	background: -webkit-linear-gradient(top, #08d248, #13bd49);
	background: -moz-linear-gradient(top, #08d248, #13bd49);
	background: -ms-linear-gradient(top, #08d248, #13bd49);
	background: -o-linear-gradient(top, #08d248, #13bd49)
}
.btn-danger:hover {
	color: #fff;
	background-color: #0ba43b;
	background: -webkit-linear-gradient(top, #13bd49, #0ba43b);
	background: -moz-linear-gradient(top, #13bd49, #0ba43b);
	background: -ms-linear-gradient(top, #13bd49, #0ba43b);
	background: -o-linear-gradient(top, #13bd49, #0ba43b)
}
.message ul li {
	margin-bottom: 10px;
	line-height: 25px;
	padding-bottom: 10px;
	clear: both;
	position: relative;
	border-bottom: 1px dotted #ccc;
}
.message li p {
	padding-bottom: 5px;
	font-size: 14px;
}
.datetime {
	color: #999999;
	font-size: 12px;
	width: 120px;
	text-align: center;
	display: inline;
	float: right;
}
.para01 {
	display: inline-block;
	padding-bottom: 0px;
	font-size: 16px;
	font-family: "微软雅黑";
}
.para01 a {
	font-size: 16px;
}
#maximg img{ max-width:100%; height:auto !important}
.pagebar { padding:20px; overflow:hidden; clear:both}
.pagebar .pagination {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.pagination a {
  background: #fff;
  border: 1px solid #ccc;
  color: #333;
  font-size: 12px;
  padding: 8px 10px;
  margin: 0 2px;
  border-radius: 3px; line-height:100%
}
.pagination span {
    color: #333;
    font-size: 12px;
    padding: 7px 2px;
    margin: 0 2px;
    border-radius: 3px;
}
.pagination a:hover {
  color: #333;
  border: 1px solid #333;
}
.pagination a.page-num-current {
  color: #fff;
  background: #333;
  border: 1px solid #333;
}
.pagination .st{ font-family:宋体}
.text-secondary{ text-align:center; padding:20px 0}