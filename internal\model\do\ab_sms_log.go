// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ABSmsLog is the golang structure of table AB_sms_log for DAO operations like Where/Data.
type ABSmsLog struct {
	g.Meta     `orm:"table:AB_sms_log, do:true"`
	Id         interface{} // 主键ID
	Guid       interface{} // 订单GUID
	JyId       interface{} // 订单编号
	SmsType    interface{} // 短信类型：1-支付成功通知客户，2-新订单通知审核员，3-修改订单通知审核员，4-审核结果通知客户，5-审核结果通知司机
	Phone      interface{} // 接收手机号
	Content    interface{} // 短信内容
	Status     interface{} // 发送状态：0-失败，1-成功
	ErrorMsg   interface{} // 失败原因
	SendTime   *gtime.Time // 发送时间
	CreateTime *gtime.Time // 创建时间
	UpdateTime *gtime.Time // 更新时间
	Operator   interface{} // 操作人
	Remark     interface{} // 备注
	SenderUid  interface{} // 发送人
	SenderType interface{} //
}
