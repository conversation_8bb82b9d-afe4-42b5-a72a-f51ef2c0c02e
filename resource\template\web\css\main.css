body {
  font-family: MicrosoftYaHei;
  background-color: #F5F5F5;
  overflow-x: hidden;
}

.page-bg {
  position: absolute;
  z-index: 1;
  display: block;
  width: 100%;
  height: auto;
}

.page-1200 {
  position: relative;
  z-index: 9;
  width: 1200px;
  margin: 0 auto;
}

.icon {
  margin-right: 5px;
  font-size: 0;
}

a:link, a:visited {
  font-size: inherit;
  color: #141414;
}

a:hover {
  font-size: inherit;
  color: #000;
  text-decoration: underline;
}

.page-header {
  padding: 20px 0;
  text-align: right;
}
.page-header .logo,
.page-header .web-name,
.page-header .today {
  display: inline-block;
  height: 20px;
  line-height: 20px;
  vertical-align: middle;
}
.page-header .logo {
  height: 18px;
  width: auto;
  font-size: 0;
}
.page-header .web-name {
  margin: 0 15px 0 5px;
  font-size: 18px;
  color: #FFF;
}
.page-header .today {
  font-size: 16px;
  color: #FFF;
}

.page-title {
  margin: 50px auto 80px;
}
.page-title .title-img {
  width: 100%;
  height: auto;
}

.top-section {
  background: #FFF;
  border-top: 3px solid #F2E5C2;
  border-bottom: 1px solid #F2E5C2;
}
.top-section .line {
  position: absolute;
  top: -15px;
  right: 0;
  height: 12px;
  width: auto;
}
.top-section .top-header {
  position: absolute;
  top: 12px;
  left: 30px;
  height: 22px;
  width: auto;
}
.top-section .swiper {
  position: relative;
  margin: 0 auto;
  width: 1100px;
  height: 40px;
  padding: 30px 40px 30px 0;
}
.top-section .swiper .swiper-item {
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
  text-align: center;
}
.top-section .swiper .swiper-item .title {
  display: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size: 28px;
  font-weight: bold;
  color: #000;
}
.top-section .swiper .swiper-dots {
  position: absolute;
  right: 0;
  top: 24px;
  text-align: center;
  width: 35px;
}
.top-section .swiper .swiper-dots .prev,
.top-section .swiper .swiper-dots .next {
  height: 6px;
  width: auto;
  cursor: pointer;
}
.top-section .swiper .swiper-dots .dot {
  padding: 0;
  height: 40px;
  line-height: 40px;
  font-family: Arial;
  cursor: pointer;
}

.main-news-section {
  display: table;
  background: #FFF;
}
.main-news-section .col-worker,
.main-news-section .col-tabpanel,
.main-news-section .col-swiper {
  display: table-cell;
  white-space: nowrap;
  vertical-align: top;
}
.main-news-section .col-tabpanel {
  width: 440px;
  padding: 25px 25px 0;
}
.main-news-section .col-tabpanel .tabs {
  width: 440px;
  display: table;
}
.main-news-section .col-tabpanel .tabs .item {
  display: table-cell;
  padding-bottom: 12px;
  border-bottom: 1px solid #D2DFEF;
}
.main-news-section .col-tabpanel .tabs .item {
  width: 110px;
  height: 30px;
  line-height: 30px;
  font-size: 16px;
  text-align: center;
  color: #8A8B91;
}
.main-news-section .col-tabpanel .tabs .item span {
  font-size: inherit;
  color: inherit;
}
.main-news-section .col-tabpanel .tabs .active {
  border-bottom: 1px solid #BD1A2D;
  font-weight: bold;
  color: #000;
}
.main-news-section .col-tabpanel .tabs .icon, .main-news-section .col-tabpanel .tabs span {
  font-size: 16px;
  display: inline-block;
  vertical-align: middle;
}
.main-news-section .col-tabpanel .tabs .icon {
  height: 18px;
  width: auto;
}
.main-news-section .col-tabpanel .tab-conts {
  width: 440px;
  padding: 10px 0 0 0;
}
.main-news-section .col-tabpanel .tab-conts ul {
  display: none;
  width: 430px;
  overflow: hidden;
}
.main-news-section .col-tabpanel .tab-conts ul li {
  height: 40px;
  line-height: 40px;
  white-space: nowrap;
}
.main-news-section .col-tabpanel .tab-conts ul li span {
  display: inline-block;
  padding: 0 8px;
}
.main-news-section .col-tabpanel .tab-conts ul li a {
  font-size: 14px;
  color: #000;
}
.main-news-section .col-swiper {
  position: relative;
  width: 360px;
  padding-top: 25px;
}
.main-news-section .col-swiper .swiper-item {
  position: absolute;
  top: 25px;
  left: 0;
  width: 340px;
  height: 270px;
  display: none;
}
.main-news-section .col-swiper .swiper-item .img {
  display: block;
  width: 100%;
  height: 100%;
}
.main-news-section .col-swiper .swiper-item .title {
  padding-top: 15px;
  display: block;
  font-size: 16px;
  font-weight: normal !important;
  text-align: center;
  white-space: nowrap;
}
.main-news-section .col-swiper .dots {
  position: absolute;
  top: 345px;
  width: 340px;
  text-align: center;
  font-size: 0;
}
.main-news-section .col-swiper .dots .dot {
  display: inline-block;
  width: 20px;
  height: 2px;
  font-size: 0;
  background: #E6E6E6;
  text-indent: -9999px;
  cursor: pointer;
}
.main-news-section .col-swiper .dots .current {
  width: 30px;
  background: #075395;
}

.col-worker {
  padding: 30px 20px 20px;
  width: 300px;
  background: #FDFBF6;
}
.col-worker .user-plate {
  position: relative;
}
.col-worker .user-plate .user,
.col-worker .user-plate .icon,
.col-worker .user-plate .name,
.col-worker .user-plate .links,
.col-worker .user-plate .link,
.col-worker .user-plate span {
  display: inline-block;
  vertical-align: middle;
  height: 30px;
  line-height: 30px;
}
.col-worker .user-plate .user .icon {
  height: 30px;
  width: auto;
}
.col-worker .user-plate .user .name {
  font-size: 15px;
}
.col-worker .user-plate .user .name span {
  font-size: inherit;
  font-weight: bold;
  color: #BD1A2D;
}
.col-worker .user-plate .links {
  position: absolute;
  right: 0;
  top: 0;
  text-align: right;
}
.col-worker .user-plate .links .icon {
  height: 12px;
  width: auto;
}
.col-worker .user-plate .links .link {
  margin-left: 15px;
}
.col-worker .worker-plate {
  margin: 20px auto;
  padding: 15px;
  width: 270px;
  height: 65px;
}
.col-worker .worker-plate.red {
  background: url("../images/worker_plate_red.png") repeat;
}
.col-worker .worker-plate.orange {
  background: url("../images/worker_plate_orange.png") repeat;
}
.col-worker .worker-plate .icon,
.col-worker .worker-plate span,
.col-worker .worker-plate .link {
  display: inline-block;
  vertical-align: middle;
}
.col-worker .worker-plate .title .icon {
  height: 30px;
  width: auto;
}
.col-worker .worker-plate .title span {
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  font-weight: bold;
}
.col-worker .worker-plate .link {
  margin: 12px 25px 0 10px;
  padding-left: 10px;
  height: 20px;
  line-height: 20px;
  background-image: url(../images/icon_arrow_right.png);
  background-repeat: no-repeat;
  background-position: left center;
  font-size: 12px;
}
.col-worker .worker-plate .link span {
  margin-left: 10px;
  font-family: Arial;
  font-size: inherit;
  color: #BD1A2D;
}
.col-worker .buttons {
  text-align: center;
  width: 300px;
}
.col-worker .buttons .btn {
  width: 140px;
  height: 48px;
  margin: 0 4px;
  line-height: 48px;
  display: inline-block;
  text-align: center;
  background-image: url(../images/worker_btn.png);
  background-repeat: no-repeat;
  background-position: center center;
}

.banners-swiper {
  position: relative;
  margin: 40px auto 0;
}
.banners-swiper .swiper-item {
  position: relative;
  width: 1200px;
  height: 130px;
}
.banners-swiper .swiper-item div {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}
.banners-swiper .swiper-item .img {
  width: 1200px;
  height: 130px;
}
.banners-swiper .dots {
  position: absolute;
  right: 0;
  bottom: 0;
  display: table;
}
.banners-swiper .dots .dot {
  display: table-cell;
  background: #161823;
  width: 18px;
  height: 18px;
  line-height: 18px;
  color: #FFF;
  text-align: center;
  font-family: Arial;
  font-size: 12px;
  cursor: pointer;
}
.banners-swiper .dots .current {
  background: #9196af;
}

.content-panel {
  width: 1200px;
  margin: 0 auto;
}
.content-panel .col {
  width: 33.3333333333%;
}
.content-panel .col .plate {
  position: relative;
  display: inline-block;
  margin-top: 40px;
  width: 370px;
  height: 280px;
  background: #FFF;
}
.content-panel .col .plate .title {
  padding: 30px 0 0 30px;
  font-size: 22px;
  font-weight: bold;
  -moz-text-align-last: left;
       text-align-last: left;
}
.content-panel .col .plate .title .icon {
  display: inline-block;
  vertical-align: middle;
  height: 28px;
  width: auto;
}
.content-panel .col .plate .title span {
  display: inline-block;
  vertical-align: middle;
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
}
.content-panel .col .plate .title-line {
  margin: 15px 0 0 30px;
  width: 40px;
  height: 2px;
  background: #BD1A2D;
}
.content-panel .col .plate .blue {
  margin-left: 28px;
  width: 30px;
  background: #075395;
}
.content-panel .col .plate .more {
  position: absolute;
  right: 30px;
  top: 30px;
  width: 20px;
  height: 20px;
}
.content-panel .col .plate .more .more-img {
  width: 100%;
  height: 100%;
}
.content-panel .col .plate .list {
  padding: 20px 30px 20px;
}
.content-panel .col .plate .list li {
  height: 42px;
  line-height: 42px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
}
.content-panel .col .plate .list li span {
  margin: 0 4px 0 0;
  display: inline-block;
}
.content-panel .col .plate .calendar-list {
  padding: 20px 30px 20px;
  text-align: left;
}
.content-panel .col .plate .calendar-list li {
  margin-bottom: 20px;
}
.content-panel .col .plate .calendar-list .day {
  display: inline-block;
  vertical-align: middle;
  width: 38px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  font-size: 20px;
  font-family: Arial;
  background-image: url(../images/list_day.jpeg);
  background-repeat: no-repeat;
  background-position: center center;
}
.content-panel .col .plate .calendar-list .tt {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
}
.content-panel .col .plate .calendar-list .tt .date {
  color: #075395;
  font-size: 12px;
}
.content-panel .col .plate .calendar-list .tt .t {
  margin-top: 5px;
  font-size: 14px;
}
.content-panel .col-mid {
  text-align: center;
}
.content-panel .col-right {
  text-align: right;
}

.unit-panel {
  position: relative;
  margin-top: 60px;
}
.unit-panel .title .line-left,
.unit-panel .title .line-right {
  position: absolute;
  top: 10px;
  width: 520px;
  height: 1px;
  background: #B3B3B3;
}
.unit-panel .title .line-left span,
.unit-panel .title .line-right span {
  position: absolute;
  top: -1px;
  width: 40px;
  height: 3px;
  background: #BD1A2D;
}
.unit-panel .title .line-left {
  left: 0;
}
.unit-panel .title .line-left span {
  right: 0;
}
.unit-panel .title .line-right {
  right: 0;
}
.unit-panel .title .line-right span {
  left: 0;
}
.unit-panel .title .tt {
  width: 100%;
  text-align: center;
  font-size: 22px;
  font-weight: bold;
}
.unit-panel .unit-cont {
  width: 100%;
  padding: 20px 0;
  text-align: center;
  border-bottom: 1px solid #B3B3B3;
}
.unit-panel .unit-cont .item {
  padding: 20px 25px;
  display: inline-block;
  vertical-align: middle;
  font-size: 18px;
}

.feature-and-info {
  display: table;
  margin-top: 60px;
  padding-bottom: 35px;
  background: #FFF;
  border-bottom: 1px solid #E5E5E5;
}
.feature-and-info .headers .title {
  display: inline-block;
  vertical-align: middle;
  font-size: 22px;
  font-weight: bold;
}
.feature-and-info .headers .more {
  margin-left: 20px;
  display: inline-block;
  vertical-align: middle;
}
.feature-and-info .headers .more a {
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
  color: #7F7F7F;
}
.feature-and-info .headers .more .more-img {
  margin-left: 5px;
  display: inline-block;
  vertical-align: middle;
  height: 14px;
}
.feature-and-info .feature-plate {
  display: table-cell;
  width: 625px;
  padding: 35px 20px 0 35px;
}
.feature-and-info .feature-plate .f-banners {
  width: 100%;
  margin-top: 20px;
}
.feature-and-info .feature-plate .f-banners .banner {
  display: inline-block;
  vertical-align: middle;
  font-size: 0;
  margin-right: 20px;
  width: 290px;
  height: 75px;
}
.feature-and-info .info-plate {
  display: table-cell;
}
.feature-and-info .info-plate .buttons .btn,
.feature-and-info .info-plate .buttons .btn .icon,
.feature-and-info .info-plate .buttons .btn span {
  display: inline-block;
  vertical-align: middle;
}
.feature-and-info .info-plate .buttons .btn {
  margin-top: 25px;
  width: 170px;
}
.feature-and-info .info-plate .buttons .icon {
  height: 14px;
}
.feature-and-info .info-plate .buttons span {
  white-space: nowrap;
  font-size: 15px;
}

.apps {
  position: relative;
  padding-bottom: 35px;
  margin-bottom: 20px;
  background: #FFF;
}
.apps .headers {
  padding: 35px 35px 0;
}
.apps .headers .title {
  display: inline-block;
  vertical-align: middle;
  font-size: 22px;
  font-weight: bold;
}
.apps .headers .count {
  display: inline-block;
  vertical-align: middle;
  padding: 0 35px;
  font-family: Arial;
  font-size: 20px;
  color: #8D8D8D;
}
.apps .conts {
  position: relative;
  z-index: 1;
  height: 160px;
  overflow: hidden;
  text-align: center;
}
.apps .conts .btn {
  display: inline-block;
  margin: 26px 26px 0;
  padding-left: 50px;
  padding-right: 20px;
  width: 270px;
  height: 50px;
  line-height: 50px;
  white-space: nowrap;
  overflow: hidden;
  background-image: url(../images/app_btn.png);
  background-repeat: no-repeat;
  font-size: 14px;
  text-align: left;
}
.apps .conts.show {
  height: auto;
}
.apps .mask-button {
  position: absolute;
  z-index: 9;
  bottom: -15px;
  left: 0;
  width: 1200px;
}
.apps .mask-button .to-hide {
  display: none;
}
.apps .mask-button.show {
  position: absolute;
  bottom: -18px;
}

.copy {
  position: relative;
  width: 100%;
  height: 372px;
  margin-top: 100px;
  border-top: 12px solid #075395;
}
.copy .copy-top-line {
  position: absolute;
  top: -24px;
  right: 0;
  height: 12px;
}
.copy .copy-bg {
  position: absolute;
  z-index: 1;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 372px;
}
.copy .copy-panel {
  position: relative;
  z-index: 9;
  padding-top: 20px;
}
.copy .copy-panel .select-plate {
  position: relative;
}
.copy .copy-panel .select-plate .selector {
  position: relative;
  z-index: 1;
  margin-top: 40px;
  padding-left: 80px;
  padding-right: 40px;
  width: 360px;
  height: 70px;
  line-height: 70px;
  font-size: 18px;
  background-image: url(../images/guide_select.png);
  background-repeat: no-repeat;
}
.copy .copy-panel .select-plate .options {
  position: absolute;
  z-index: 9;
  bottom: 80px;
  left: 0;
  width: 400px;
  padding: 30px 40px;
  font-size: 14px;
  background: #3b3b3b;
}
.copy .copy-panel .select-plate .options a {
  display: block;
  height: 36px;
  line-height: 36px;
  color: #FFF;
}
.copy .copy-panel .info-plate {
  position: absolute;
  z-index: 9;
  top: 120px;
  right: 0;
  text-align: right;
}
.copy .copy-panel .info-plate .logo {
  padding-bottom: 20px;
  height: 50px;
}
.copy .copy-panel .info-plate .txt p {
  height: 20px;
  line-height: 20px;
  font-size: 14px;
}

.list-content {
  position: relative;
}
.list-content td {
  position: relative;
  vertical-align: top;
}
.list-content .left-td {
  width: 340px;
}
.list-content .side-panel {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 340px;
  background: #FFF;
}
.list-content .side-panel .col-worker {
  border-top: 3px solid #F2E5C2;
}
.list-content .side-panel .menu {
  width: 300px;
  padding: 30px 20px;
}
.list-content .side-panel .menu .menu-list {
  padding-left: 30px;
}
.list-content .side-panel .menu .menu-list .menu-item {
  height: 30px;
  line-height: 30px;
  font-size: 16px;
}
.list-content .side-panel .menu .menu-list .menu-item a, .list-content .side-panel .menu .menu-list .menu-item .icon {
  display: inline-block;
  vertical-align: middle;
}
.list-content .side-panel .menu .menu-list .menu-item .icon {
  margin-left: 5px;
}
.list-content .side-panel .menu .item-list {
  margin-left: 25px;
}
.list-content .side-panel .menu .item-list .list-item {
  padding-left: 15px;
  height: 30px;
  line-height: 30px;
  border-left: 1px solid #E6E6E6;
}
.list-content .side-panel .menu .item-list .list-item a {
  display: inline-block;
  height: 14px;
  line-height: 14px;
  padding: 5px 8px;
  font-size: 14px;
}
.list-content .side-panel .menu .item-list .list-item a.current {
  background: #BD1A2D;
  color: #FFF;
}
.list-content .list-panel {
  vertical-align: top;
  width: 820px;
  margin-left: 40px;
  background: #FFF;
  border-top: 3px solid #F2E5C2;
}
.list-content .list-panel .title {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
}
.list-content .list-panel .line {
  width: 50px;
  height: 3px;
  margin: 30px auto 40px;
  background: #BD1A2D;
}
.list-content .list-panel .list {
  padding: 0 35px;
}
.list-content .list-panel .list li {
  position: relative;
  padding: 12px 0;
  height: 20px;
  line-height: 20px;
  font-size: 16px;
}
.list-content .list-panel .list li span {
  font-size: inherit;
  margin-right: 8px;
}
.list-content .list-panel .list li span.date {
  position: absolute;
  right: 0;
  top: 14px;
  margin-right: 0;
  color: #7F7F7F;
}
.list-content .list-panel .list li.space {
  height: 0;
  margin-top: 12px;
  border-top: 1px dashed #dadada;
}
.list-content .list-panel .pagination {
  position: relative;
  padding: 30px 30px 50px;
}
.list-content .list-panel .pagination .icon {
  display: block;
  margin: 0 auto;
  padding-top: 8px;
  height: 14px;
}
.list-content .list-panel .pagination a {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin: 0 5px;
  font-size: 16px;
  font-family: Arial;
  text-align: center;
  background: #dadada;
}
.list-content .list-panel .pagination span {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin: 0 5px;
  font-size: 16px;
  font-family: Arial;
  text-align: center;
  color: #075395;
}
.list-content .list-panel .pagination p {
  position: absolute;
  right: 30px;
  top: 38px;
  font-size: 16px;
  color: #7F7F7F;
}

.breadcrumb {
  padding: 30px 20px;
  width: 780px;
  font-size: 14px;
}
.breadcrumb .icon {
  display: inline-block;
  vertical-align: middle;
  height: 12px;
}
.breadcrumb a {
  margin-right: 5px;
  color: #075395;
}
.breadcrumb span {
  margin-right: 5px;
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
}

.read-content {
  background: #FFF;
}
.read-content .title {
  width: 970px;
  margin: 40px auto;
  text-align: center;
  font-size: 28px;
  font-weight: bold;
}
.read-content .info {
  width: 970px;
  margin: 40px auto;
  padding: 20px 0;
  text-align: center;
  border-top: 1px dashed #dadada;
  border-bottom: 1px dashed #dadada;
}
.read-content .info .item {
  display: inline-block;
  vertical-align: middle;
  min-width: 250px;
  font-size: 14px;
}
.read-content .info .item .icon {
  display: inline-block;
  vertical-align: middle;
  height: 12px;
}
.read-content .info .item span {
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
}
.read-content .info .media span {
  padding: 5px 10px;
  border: 1px solid #F5DDE0;
  background: #FCF3F4;
}
.read-content .info .readed {
  min-width: 120px !important;
  padding: 5px 10px;
  border: 1px solid #D9D9D9;
  background: #F2F2F2;
}
.read-content .article {
  width: 970px;
  margin: 40px auto 0;
  padding-bottom: 80px;
}
.read-content .article p {
  padding-bottom: 15px;
  font-size: 18px;
  line-height: 32px;
  text-indent: 36px;
}