package wx

import (
	"50go/internal/utils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// 规范路由
func (c *WxController) UploadFile(r *ghttp.Request) {
	//file := r.GetUploadFiles("file")
	//utils.SaveImage()
	//imgUrl, err := utils.UpImg("/uoload", file)
	// if err == nil {
	imgUrl, code := utils.Upload(r)
	r.Response.WriteJson(g.Map{
		"error": code,
		"msg":   "成功",
		"url":   imgUrl,
	})
}
