<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>登录</title>
	<link rel="stylesheet" href="/component/pear/css/pear.css" />
	<link rel="stylesheet" href="/admin/css/other/login.css" />
	<link rel="stylesheet" href="/admin/css/variables.css" />
	<script>if (window.self != window.top) { top.location.reload();}</script>
</head>

<body>
	<div class="login-page" style="background-image: url(./admin/images/background.svg)">
		<form class="layui-form" action="" id="loginform">
		<div class="layui-row">
			<div class="layui-col-sm6 login-bg layui-hide-xs">
				<img class="login-bg-img" src="/admin/images/banner.png" alt="" />
			</div>
			<div class="layui-col-sm6 layui-col-xs12 login-form">
				<div class="layui-form">
					<div class="form-center">
						<div class="form-center-box">
							<div class="top-log-title">
								<img class="top-log" src="{{.site.logo}}" alt="" />
								<span>{{.site.name}}</span>
							</div>
							<div class="top-desc">
								以 超 乎 想 象 的 速 度 构 建 内 部 工 具
							</div>
							<div style="margin-top: 30px;">
								<div class="layui-form-item">
									<div class="layui-input-wrap">
										<div class="layui-input-prefix">
											<i class="layui-icon layui-icon-username"></i>
										</div>
										<input lay-verify="required" placeholder="账户" autocomplete="off"  name="uname" 
											class="layui-input">
									</div>
								</div>
								<div class="layui-form-item">
									<div class="layui-input-wrap">
										<div class="layui-input-prefix">
											<i class="layui-icon layui-icon-password"></i>
										</div>
										<input type="password" name="confirmPassword" value=""
											lay-verify="required|confirmPassword" placeholder="密码" autocomplete="off"
											class="layui-input" lay-affix="eye">
									</div>
								</div>
								<div class="tab-log-verification">
										<div class="verification-text">
											<div class="layui-input-wrap">
												<div class="layui-input-prefix">
													<i class="layui-icon layui-icon-auz"></i>
												</div>
												<input lay-verify="required" value="" name="answer" placeholder="验证码"
													autocomplete="off" class="layui-input">
											</div>
										</div>

<img src="{{.codeimg}}" alt="" class="verification-img" onclick="refreshCaptcha(this);">
										<input type="" id="codeid" name="codeid" value="{{.codeid}}" style="display: none;">
							    </div>

								<div class="layui-form-item">
									<div class="remember-passsword">
										<div class="remember-cehcked">
											<input type="checkbox" name="like1[write]" lay-skin="primary" title="自动登录">
										</div>
									</div>
								</div>
								<div class="login-btn">
									<button type="button" lay-submit lay-filter="login" class="layui-btn login" >登 录</button>
								</div>
								<div class="other-login">
									<div class="other-login-methods">
										其他方式
									</div>
									<div class="greenText">注册账号</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		</form>
	</div>
	<!-- 资 源 引 入 -->
	<!-- 资 源 引 入 -->
	<script src="/component/layui/layui.js"></script>
	<script src="/component/pear/pear.js"></script>
	<script>
		layui.use(['form', 'button', 'popup','jquery'], function () {
			var form = layui.form;
			var button = layui.button;
			var popup = layui.popup;
			let $ = layui.jquery;

			// 登 录 提 交
			// form.on('submit(login)', function (data) {

			// 	/// 验证
			// 	/// 登录
			// 	/// 动画
			// 	button.load({
			// 		elem: '.login',
			// 		time: 1500,
			// 		done: function () {
			// 			popup.success("登录成功", function () {
			// 				location.href = "/admin/index.html"
			// 			});
			// 		}
			// 	})

			// 	return false;
			// });




            form.on('submit(login)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                $.ajax({
                    url: '/mysession/sign-in',
                    data: $('#loginform').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							popup.success("登录成功", function () {
							location.href = "/system/index"
						});
                        } else {

							refreshCaptcha('.verification-img');
							//popup.error("登录失败", function () {});
                           layer.msg(result.msg,{ icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });

			window.refreshCaptcha= function(imgElement){
				$.ajax({
				url: '/mysession/refresh-captcha',
				type: 'GET',
				success: function(response) {
					if(response.code == 200) {
						// Update image src with new URL and timestamp to prevent caching
						imgElement.src = response.data.img;
						$('#codeid').val(response.data.id);
					}
				},
				error: function() {
					layer.msg('验证码刷新失败', {icon: 2, time: 1000});
				}
			});
 
           }



		})

	</script>
</body>

</html>
