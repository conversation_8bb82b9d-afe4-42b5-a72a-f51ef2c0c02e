// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ABSmsLog is the golang structure for table AB_sms_log.
type ABSmsLog struct {
	Id         int         `json:"id"          orm:"id"          ` // 主键ID
	Guid       string      `json:"guid"        orm:"guid"        ` // 订单GUID
	JyId       string      `json:"jy_id"       orm:"jy_id"       ` // 订单编号
	SmsType    int         `json:"sms_type"    orm:"sms_type"    ` // 短信类型：1-支付成功通知客户，2-新订单通知审核员，3-修改订单通知审核员，4-审核结果通知客户，5-审核结果通知司机
	Phone      string      `json:"phone"       orm:"phone"       ` // 接收手机号
	Content    string      `json:"content"     orm:"content"     ` // 短信内容
	Status     int         `json:"status"      orm:"status"      ` // 发送状态：0-失败，1-成功
	ErrorMsg   string      `json:"error_msg"   orm:"error_msg"   ` // 失败原因
	SendTime   *gtime.Time `json:"send_time"   orm:"send_time"   ` // 发送时间
	CreateTime *gtime.Time `json:"create_time" orm:"create_time" ` // 创建时间
	UpdateTime *gtime.Time `json:"update_time" orm:"update_time" ` // 更新时间
	Operator   string      `json:"operator"    orm:"operator"    ` // 操作人
	Remark     string      `json:"remark"      orm:"remark"      ` // 备注
	SenderUid  string      `json:"sender_uid"  orm:"sender_uid"  ` // 发送人
	SenderType string      `json:"sender_type" orm:"sender_type" ` //
}
