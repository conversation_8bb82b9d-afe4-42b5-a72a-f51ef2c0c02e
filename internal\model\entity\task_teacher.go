// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskTeacher is the golang structure for table Task_Teacher.
type TaskTeacher struct {
	Id          int         `json:"ID"          orm:"ID"          ` //
	Uid         string      `json:"UID"         orm:"UID"         ` //
	Guid        string      `json:"Guid"        orm:"Guid"        ` //
	OParentId   int         `json:"OParentId"   orm:"OParentId"   ` //
	ISvipleve   int         `json:"ISvipleve"   orm:"ISvipleve"   ` //
	OState      int         `json:"OState"      orm:"OState"      ` //
	OType       string      `json:"OType"       orm:"OType"       ` //
	OCreatTime  *gtime.Time `json:"OCreatTime"  orm:"OCreatTime"  ` //
	Name        string      `json:"Name"        orm:"Name"        ` //
	Tel         string      `json:"Tel"         orm:"Tel"         ` //
	Tel2        string      `json:"Tel2"        orm:"Tel2"        ` //
	Provance    string      `json:"Provance"    orm:"Provance"    ` //
	City        string      `json:"City"        orm:"City"        ` //
	District    string      `json:"District"    orm:"District"    ` //
	Street      string      `json:"Street"      orm:"Street"      ` //
	ADDress     string      `json:"ADDress"     orm:"ADDress"     ` //
	BertheDay   *gtime.Time `json:"BertheDay"   orm:"BertheDay"   ` //
	Content     string      `json:"Content"     orm:"Content"     ` //
	Photo       string      `json:"Photo"       orm:"Photo"       ` //
	IDpics      string      `json:"IDpics"      orm:"IDpics"      ` //
	School      string      `json:"School"      orm:"School"      ` //
	ZhuanYe     string      `json:"ZhuanYe"     orm:"ZhuanYe"     ` //
	ADDlocation string      `json:"ADDlocation" orm:"ADDlocation" ` //
	ADDname     string      `json:"ADDname"     orm:"ADDname"     ` //
	ADDtitle    string      `json:"ADDtitle"    orm:"ADDtitle"    ` //
	ADDdetail   string      `json:"ADDdetail"   orm:"ADDdetail"   ` //
	ADDlat      string      `json:"ADDlat"      orm:"ADDlat"      ` //
	ADDlon      string      `json:"ADDlon"      orm:"ADDlon"      ` //
	JiGuan      string      `json:"JiGuan"      orm:"JiGuan"      ` //
}
