// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Task is the golang structure of table Task for DAO operations like Where/Data.
type Task struct {
	g.Meta        `orm:"table:Task, do:true"`
	Id            interface{} //
	StudentID     interface{} //
	TeacherID     interface{} //
	Guid          interface{} //
	GPrice        interface{} //
	GPrice2       interface{} //
	GPrice3       interface{} //
	FXPrice2      interface{} //
	FXPrice       interface{} //
	QDimg         interface{} //
	QDContent     interface{} //
	QDTime        *gtime.Time //
	Qduid         interface{} //
	JHimg         interface{} //
	JHContent     interface{} //
	JHTime        *gtime.Time //
	Jhuid         interface{} //
	CTcount       interface{} //
	CTTime        interface{} //
	PJLeve        interface{} //
	PJTime        *gtime.Time //
	Pjuid         interface{} //
	PJContent     interface{} //
	PJimg         interface{} //
	PJstate       interface{} //
	Tstate        interface{} //
	Leve          interface{} //
	Feedback      interface{} //
	JZView        interface{} //
	JZFirstTime   *gtime.Time //
	JZContent     interface{} //
	ManageContent interface{} //
	ManageTime    *gtime.Time //
	ManageUID     interface{} //
}
