// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalSUaddressDao is internal type for wrapping internal DAO implements.
type internalSUaddressDao = *internal.SUaddressDao

// sUaddressDao is the data access object for table S_UADDRESS.
// You can define custom methods on it to extend its functionality as you wish.
type sUaddressDao struct {
	internalSUaddressDao
}

var (
	// SUaddress is globally public accessible object for table S_UADDRESS operations.
	SUaddress = sUaddressDao{
		internal.NewSUaddressDao(),
	}
)

// Fill with you ideas below.
