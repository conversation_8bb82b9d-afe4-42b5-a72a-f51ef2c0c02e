// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdminDao is the data access object for the table Admin.
type AdminDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  AdminColumns       // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// AdminColumns defines and stores column names for the table Admin.
type AdminColumns struct {
	Uid               string //
	Uopenid           string //
	Uwxopenid         string //
	Uwxtel            string //
	Uwxhead           string //
	Uuniid            string //
	Uguid             string //
	Uparentid         string //
	Uindirectparentid string //
	Uwx               string //
	Uzfb              string //
	Utel              string //
	Umima             string //
	Utouxiang         string //
	Uname             string //
	Uheadimg          string //
	Unickname         string //
	Utruename         string //
	Ustate            string //
	Ismember          string //
	Ustateendtime     string //
	Logintime         string //
	Regtime           string //
	Ujifen            string //
	Utype             string //
	Upower            string //
	Ucdpower          string //
	Usex              string //
	Uprovince         string //
	Issh              string //
	Ispc              string //
}

// adminColumns holds the columns for the table Admin.
var adminColumns = AdminColumns{
	Uid:               "uid",
	Uopenid:           "uopenid",
	Uwxopenid:         "uwxopenid",
	Uwxtel:            "uwxtel",
	Uwxhead:           "uwxhead",
	Uuniid:            "uuniid",
	Uguid:             "uguid",
	Uparentid:         "uparentid",
	Uindirectparentid: "uindirectparentid",
	Uwx:               "uwx",
	Uzfb:              "uzfb",
	Utel:              "utel",
	Umima:             "umima",
	Utouxiang:         "utouxiang",
	Uname:             "uname",
	Uheadimg:          "uheadimg",
	Unickname:         "unickname",
	Utruename:         "utruename",
	Ustate:            "ustate",
	Ismember:          "ismember",
	Ustateendtime:     "ustateendtime",
	Logintime:         "logintime",
	Regtime:           "regtime",
	Ujifen:            "ujifen",
	Utype:             "utype",
	Upower:            "upower",
	Ucdpower:          "ucdpower",
	Usex:              "usex",
	Uprovince:         "uprovince",
	Issh:              "issh",
	Ispc:              "ispc",
}

// NewAdminDao creates and returns a new DAO object for table data access.
func NewAdminDao(handlers ...gdb.ModelHandler) *AdminDao {
	return &AdminDao{
		group:    "default",
		table:    "Admin",
		columns:  adminColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdminDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdminDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdminDao) Columns() AdminColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdminDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdminDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdminDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
