// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SOrdered is the golang structure for table S_Ordered.
type SOrdered struct {
	Oid       int         `json:"OID"       orm:"OID"       ` //
	OrderId   int         `json:"OrderId"   orm:"OrderId"   ` //
	OHxOpenId string      `json:"OHxOpenId" orm:"OHxOpenId" ` //
	OHxTime   *gtime.Time `json:"OHxTime"   orm:"OHxTime"   ` //
}
