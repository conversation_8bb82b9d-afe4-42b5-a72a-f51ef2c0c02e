﻿@charset "UTF-8";
/**
    Theme Name: SkyLine
    Theme URI: http://engotheme.com/
    Author: Engo Team
    Author URI: http://engotheme.com/
    Description: Engo ABC HTML Template
    Version: 1.0
    Last change:    2017/12/11 [Initial Release]
    License: GNU General Public License v2 or later
    Text Domain: Engotheme
 **/
/*------------------------------------------------------------------
[Table of contents]
    components
        1.Base
            1.1 Mixin
            1.2 Normalize
            1.3 Variable
        2.Layout
            2.1 Nav
            2.2 Header
            2.3 Footer
            2.4 Content
                2.4.1 Map
                2.4.2 ROOMS
                2.4.3 GALLERY
                2.4.4 NEW
                2.4.5 EVENTS
                2.4.6 SCROLL TOP
                2.4.7 TESTIMONIALS
                2.4.8 BEST
                2.4.9 ABOUT 
                2.4.10 BODY-PAGE
                2.4.11 BANNER-TEMS
                2.4.12 BODY-TEMS
                2.4.13 BODY-ROOM 
                2.4.14 CHECK-OUT 
                2.4.15 RESTAURANTS
                2.4.16 CONTACT
                2.4.17 BLOG
                2.4.18 SIDEBAR
                2.4.19 WIDGETSEARCH
                2.4.20 POST
                2.4.21 NAVIGATION
                2.4.22 VIDEO
                2.4.23 COMMENT
                2.4.24 AttRACTIONS
                2.4.25 SECTION-TEAM
                2.4.26 RESERVATION
                2.4.27 DATEPICKER
                2.4.28 PRODUCT
                2.4.29 GUEST-BOOK 
            2.5 Boostrap  
        3.Reponsive
            4.1 Desktop
            4.2 Table
            4.3 Mobile

-------------------------------------------------------------------*/
/*!
 * Bootstrap v3.3.7 (http://getbootstrap.com)
 * Copyright 2011-2016 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%; }

body {
  margin: 0; }

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block; }

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline; }

audio:not([controls]) {
  display: none;
  height: 0; }

[hidden],
template {
  display: none; }

a {
  background-color: transparent; }

a:active,
a:hover {
  outline: 0; }

abbr[title] {
  border-bottom: 1px dotted; }

b,
strong {
  font-weight: bold; }

dfn {
  font-style: italic; }

h1 {
  font-size: 2em;
  margin: 0.67em 0; }

mark {
  background: #ff0;
  color: #000; }

small {
  font-size: 80%; }

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sup {
  top: -0.5em; }

sub {
  bottom: -0.25em; }

img {
  border: 0; }

svg:not(:root) {
  overflow: hidden; }

figure {
  margin: 1em 40px; }

hr {
  box-sizing: content-box;
  height: 0; }

pre {
  overflow: auto; }

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em; }

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0; }

button {
  overflow: visible; }

button,
select {
  text-transform: none; }

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer; }

button[disabled],
html input[disabled] {
  cursor: default; }

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0; }

input {
  line-height: normal; }

input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0; }

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto; }

input[type="search"] {
  -webkit-appearance: textfield;
  box-sizing: content-box; }

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em; }

legend {
  border: 0;
  padding: 0; }

textarea {
  overflow: auto; }

optgroup {
  font-weight: bold; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

td,
th {
  padding: 0; }

/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important;
    box-shadow: none !important;
    text-shadow: none !important; }

  a,
  a:visited {
    text-decoration: underline; }

  a[href]:after {
    content: " (" attr(href) ")"; }

  abbr[title]:after {
    content: " (" attr(title) ")"; }

  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: ""; }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid; }

  thead {
    display: table-header-group; }

  tr,
  img {
    page-break-inside: avoid; }

  img {
    max-width: 100% !important; }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3; }

  h2,
  h3 {
    page-break-after: avoid; }

  .navbar {
    display: none; }

  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: #000 !important; }

  .label {
    border: 1px solid #000; }

  .table {
    border-collapse: collapse !important; }
    .table td,
    .table th {
      background-color: #fff !important; }

  .table-bordered th,
  .table-bordered td {
    border: 1px solid #ddd !important; } }
@font-face {
  font-family: 'Glyphicons Halflings';
  src: url("../fonts/bootstrap/glyphicons-halflings-regular.eot");
  src: url("../fonts/bootstrap/glyphicons-halflings-regular.eot?#iefix") format("embedded-opentype"), url("../fonts/bootstrap/glyphicons-halflings-regular.woff2") format("woff2"), url("../fonts/bootstrap/glyphicons-halflings-regular.woff") format("woff"), url("../fonts/bootstrap/glyphicons-halflings-regular.ttf") format("truetype"), url("../fonts/bootstrap/glyphicons-halflings-regular.svg#glyphicons_halflingsregular") format("svg"); }
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.glyphicon-asterisk:before {
  content: "\002a"; }

.glyphicon-plus:before {
  content: "\002b"; }

.glyphicon-euro:before,
.glyphicon-eur:before {
  content: "\20ac"; }

.glyphicon-minus:before {
  content: "\2212"; }

.glyphicon-cloud:before {
  content: "\2601"; }

.glyphicon-envelope:before {
  content: "\2709"; }

.glyphicon-pencil:before {
  content: "\270f"; }

.glyphicon-glass:before {
  content: "\e001"; }

.glyphicon-music:before {
  content: "\e002"; }

.glyphicon-search:before {
  content: "\e003"; }

.glyphicon-heart:before {
  content: "\e005"; }

.glyphicon-star:before {
  content: "\e006"; }

.glyphicon-star-empty:before {
  content: "\e007"; }

.glyphicon-user:before {
  content: "\e008"; }

.glyphicon-film:before {
  content: "\e009"; }

.glyphicon-th-large:before {
  content: "\e010"; }

.glyphicon-th:before {
  content: "\e011"; }

.glyphicon-th-list:before {
  content: "\e012"; }

.glyphicon-ok:before {
  content: "\e013"; }

.glyphicon-remove:before {
  content: "\e014"; }

.glyphicon-zoom-in:before {
  content: "\e015"; }

.glyphicon-zoom-out:before {
  content: "\e016"; }

.glyphicon-off:before {
  content: "\e017"; }

.glyphicon-signal:before {
  content: "\e018"; }

.glyphicon-cog:before {
  content: "\e019"; }

.glyphicon-trash:before {
  content: "\e020"; }

.glyphicon-home:before {
  content: "\e021"; }

.glyphicon-file:before {
  content: "\e022"; }

.glyphicon-time:before {
  content: "\e023"; }

.glyphicon-road:before {
  content: "\e024"; }

.glyphicon-download-alt:before {
  content: "\e025"; }

.glyphicon-download:before {
  content: "\e026"; }

.glyphicon-upload:before {
  content: "\e027"; }

.glyphicon-inbox:before {
  content: "\e028"; }

.glyphicon-play-circle:before {
  content: "\e029"; }

.glyphicon-repeat:before {
  content: "\e030"; }

.glyphicon-refresh:before {
  content: "\e031"; }

.glyphicon-list-alt:before {
  content: "\e032"; }

.glyphicon-lock:before {
  content: "\e033"; }

.glyphicon-flag:before {
  content: "\e034"; }

.glyphicon-headphones:before {
  content: "\e035"; }

.glyphicon-volume-off:before {
  content: "\e036"; }

.glyphicon-volume-down:before {
  content: "\e037"; }

.glyphicon-volume-up:before {
  content: "\e038"; }

.glyphicon-qrcode:before {
  content: "\e039"; }

.glyphicon-barcode:before {
  content: "\e040"; }

.glyphicon-tag:before {
  content: "\e041"; }

.glyphicon-tags:before {
  content: "\e042"; }

.glyphicon-book:before {
  content: "\e043"; }

.glyphicon-bookmark:before {
  content: "\e044"; }

.glyphicon-print:before {
  content: "\e045"; }

.glyphicon-camera:before {
  content: "\e046"; }

.glyphicon-font:before {
  content: "\e047"; }

.glyphicon-bold:before {
  content: "\e048"; }

.glyphicon-italic:before {
  content: "\e049"; }

.glyphicon-text-height:before {
  content: "\e050"; }

.glyphicon-text-width:before {
  content: "\e051"; }

.glyphicon-align-left:before {
  content: "\e052"; }

.glyphicon-align-center:before {
  content: "\e053"; }

.glyphicon-align-right:before {
  content: "\e054"; }

.glyphicon-align-justify:before {
  content: "\e055"; }

.glyphicon-list:before {
  content: "\e056"; }

.glyphicon-indent-left:before {
  content: "\e057"; }

.glyphicon-indent-right:before {
  content: "\e058"; }

.glyphicon-facetime-video:before {
  content: "\e059"; }

.glyphicon-picture:before {
  content: "\e060"; }

.glyphicon-map-marker:before {
  content: "\e062"; }

.glyphicon-adjust:before {
  content: "\e063"; }

.glyphicon-tint:before {
  content: "\e064"; }

.glyphicon-edit:before {
  content: "\e065"; }

.glyphicon-share:before {
  content: "\e066"; }

.glyphicon-check:before {
  content: "\e067"; }

.glyphicon-move:before {
  content: "\e068"; }

.glyphicon-step-backward:before {
  content: "\e069"; }

.glyphicon-fast-backward:before {
  content: "\e070"; }

.glyphicon-backward:before {
  content: "\e071"; }

.glyphicon-play:before {
  content: "\e072"; }

.glyphicon-pause:before {
  content: "\e073"; }

.glyphicon-stop:before {
  content: "\e074"; }

.glyphicon-forward:before {
  content: "\e075"; }

.glyphicon-fast-forward:before {
  content: "\e076"; }

.glyphicon-step-forward:before {
  content: "\e077"; }

.glyphicon-eject:before {
  content: "\e078"; }

.glyphicon-chevron-left:before {
  content: "\e079"; }

.glyphicon-chevron-right:before {
  content: "\e080"; }

.glyphicon-plus-sign:before {
  content: "\e081"; }

.glyphicon-minus-sign:before {
  content: "\e082"; }

.glyphicon-remove-sign:before {
  content: "\e083"; }

.glyphicon-ok-sign:before {
  content: "\e084"; }

.glyphicon-question-sign:before {
  content: "\e085"; }

.glyphicon-info-sign:before {
  content: "\e086"; }

.glyphicon-screenshot:before {
  content: "\e087"; }

.glyphicon-remove-circle:before {
  content: "\e088"; }

.glyphicon-ok-circle:before {
  content: "\e089"; }

.glyphicon-ban-circle:before {
  content: "\e090"; }

.glyphicon-arrow-left:before {
  content: "\e091"; }

.glyphicon-arrow-right:before {
  content: "\e092"; }

.glyphicon-arrow-up:before {
  content: "\e093"; }

.glyphicon-arrow-down:before {
  content: "\e094"; }

.glyphicon-share-alt:before {
  content: "\e095"; }

.glyphicon-resize-full:before {
  content: "\e096"; }

.glyphicon-resize-small:before {
  content: "\e097"; }

.glyphicon-exclamation-sign:before {
  content: "\e101"; }

.glyphicon-gift:before {
  content: "\e102"; }

.glyphicon-leaf:before {
  content: "\e103"; }

.glyphicon-fire:before {
  content: "\e104"; }

.glyphicon-eye-open:before {
  content: "\e105"; }

.glyphicon-eye-close:before {
  content: "\e106"; }

.glyphicon-warning-sign:before {
  content: "\e107"; }

.glyphicon-plane:before {
  content: "\e108"; }

.glyphicon-calendar:before {
  content: "\e109"; }

.glyphicon-random:before {
  content: "\e110"; }

.glyphicon-comment:before {
  content: "\e111"; }

.glyphicon-magnet:before {
  content: "\e112"; }

.glyphicon-chevron-up:before {
  content: "\e113"; }

.glyphicon-chevron-down:before {
  content: "\e114"; }

.glyphicon-retweet:before {
  content: "\e115"; }

.glyphicon-shopping-cart:before {
  content: "\e116"; }

.glyphicon-folder-close:before {
  content: "\e117"; }

.glyphicon-folder-open:before {
  content: "\e118"; }

.glyphicon-resize-vertical:before {
  content: "\e119"; }

.glyphicon-resize-horizontal:before {
  content: "\e120"; }

.glyphicon-hdd:before {
  content: "\e121"; }

.glyphicon-bullhorn:before {
  content: "\e122"; }

.glyphicon-bell:before {
  content: "\e123"; }

.glyphicon-certificate:before {
  content: "\e124"; }

.glyphicon-thumbs-up:before {
  content: "\e125"; }

.glyphicon-thumbs-down:before {
  content: "\e126"; }

.glyphicon-hand-right:before {
  content: "\e127"; }

.glyphicon-hand-left:before {
  content: "\e128"; }

.glyphicon-hand-up:before {
  content: "\e129"; }

.glyphicon-hand-down:before {
  content: "\e130"; }

.glyphicon-circle-arrow-right:before {
  content: "\e131"; }

.glyphicon-circle-arrow-left:before {
  content: "\e132"; }

.glyphicon-circle-arrow-up:before {
  content: "\e133"; }

.glyphicon-circle-arrow-down:before {
  content: "\e134"; }

.glyphicon-globe:before {
  content: "\e135"; }

.glyphicon-wrench:before {
  content: "\e136"; }

.glyphicon-tasks:before {
  content: "\e137"; }

.glyphicon-filter:before {
  content: "\e138"; }

.glyphicon-briefcase:before {
  content: "\e139"; }

.glyphicon-fullscreen:before {
  content: "\e140"; }

.glyphicon-dashboard:before {
  content: "\e141"; }

.glyphicon-paperclip:before {
  content: "\e142"; }

.glyphicon-heart-empty:before {
  content: "\e143"; }

.glyphicon-link:before {
  content: "\e144"; }

.glyphicon-phone:before {
  content: "\e145"; }

.glyphicon-pushpin:before {
  content: "\e146"; }

.glyphicon-usd:before {
  content: "\e148"; }

.glyphicon-gbp:before {
  content: "\e149"; }

.glyphicon-sort:before {
  content: "\e150"; }

.glyphicon-sort-by-alphabet:before {
  content: "\e151"; }

.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152"; }

.glyphicon-sort-by-order:before {
  content: "\e153"; }

.glyphicon-sort-by-order-alt:before {
  content: "\e154"; }

.glyphicon-sort-by-attributes:before {
  content: "\e155"; }

.glyphicon-sort-by-attributes-alt:before {
  content: "\e156"; }

.glyphicon-unchecked:before {
  content: "\e157"; }

.glyphicon-expand:before {
  content: "\e158"; }

.glyphicon-collapse-down:before {
  content: "\e159"; }

.glyphicon-collapse-up:before {
  content: "\e160"; }

.glyphicon-log-in:before {
  content: "\e161"; }

.glyphicon-flash:before {
  content: "\e162"; }

.glyphicon-log-out:before {
  content: "\e163"; }

.glyphicon-new-window:before {
  content: "\e164"; }

.glyphicon-record:before {
  content: "\e165"; }

.glyphicon-save:before {
  content: "\e166"; }

.glyphicon-open:before {
  content: "\e167"; }

.glyphicon-saved:before {
  content: "\e168"; }

.glyphicon-import:before {
  content: "\e169"; }

.glyphicon-export:before {
  content: "\e170"; }

.glyphicon-send:before {
  content: "\e171"; }

.glyphicon-floppy-disk:before {
  content: "\e172"; }

.glyphicon-floppy-saved:before {
  content: "\e173"; }

.glyphicon-floppy-remove:before {
  content: "\e174"; }

.glyphicon-floppy-save:before {
  content: "\e175"; }

.glyphicon-floppy-open:before {
  content: "\e176"; }

.glyphicon-credit-card:before {
  content: "\e177"; }

.glyphicon-transfer:before {
  content: "\e178"; }

.glyphicon-cutlery:before {
  content: "\e179"; }

.glyphicon-header:before {
  content: "\e180"; }

.glyphicon-compressed:before {
  content: "\e181"; }

.glyphicon-earphone:before {
  content: "\e182"; }

.glyphicon-phone-alt:before {
  content: "\e183"; }

.glyphicon-tower:before {
  content: "\e184"; }

.glyphicon-stats:before {
  content: "\e185"; }

.glyphicon-sd-video:before {
  content: "\e186"; }

.glyphicon-hd-video:before {
  content: "\e187"; }

.glyphicon-subtitles:before {
  content: "\e188"; }

.glyphicon-sound-stereo:before {
  content: "\e189"; }

.glyphicon-sound-dolby:before {
  content: "\e190"; }

.glyphicon-sound-5-1:before {
  content: "\e191"; }

.glyphicon-sound-6-1:before {
  content: "\e192"; }

.glyphicon-sound-7-1:before {
  content: "\e193"; }

.glyphicon-copyright-mark:before {
  content: "\e194"; }

.glyphicon-registration-mark:before {
  content: "\e195"; }

.glyphicon-cloud-download:before {
  content: "\e197"; }

.glyphicon-cloud-upload:before {
  content: "\e198"; }

.glyphicon-tree-conifer:before {
  content: "\e199"; }

.glyphicon-tree-deciduous:before {
  content: "\e200"; }

.glyphicon-cd:before {
  content: "\e201"; }

.glyphicon-save-file:before {
  content: "\e202"; }

.glyphicon-open-file:before {
  content: "\e203"; }

.glyphicon-level-up:before {
  content: "\e204"; }

.glyphicon-copy:before {
  content: "\e205"; }

.glyphicon-paste:before {
  content: "\e206"; }

.glyphicon-alert:before {
  content: "\e209"; }

.glyphicon-equalizer:before {
  content: "\e210"; }

.glyphicon-king:before {
  content: "\e211"; }

.glyphicon-queen:before {
  content: "\e212"; }

.glyphicon-pawn:before {
  content: "\e213"; }

.glyphicon-bishop:before {
  content: "\e214"; }

.glyphicon-knight:before {
  content: "\e215"; }

.glyphicon-baby-formula:before {
  content: "\e216"; }

.glyphicon-tent:before {
  content: "\26fa"; }

.glyphicon-blackboard:before {
  content: "\e218"; }

.glyphicon-bed:before {
  content: "\e219"; }

.glyphicon-apple:before {
  content: "\f8ff"; }

.glyphicon-erase:before {
  content: "\e221"; }

.glyphicon-hourglass:before {
  content: "\231b"; }

.glyphicon-lamp:before {
  content: "\e223"; }

.glyphicon-duplicate:before {
  content: "\e224"; }

.glyphicon-piggy-bank:before {
  content: "\e225"; }

.glyphicon-scissors:before {
  content: "\e226"; }

.glyphicon-bitcoin:before {
  content: "\e227"; }

.glyphicon-btc:before {
  content: "\e227"; }

.glyphicon-xbt:before {
  content: "\e227"; }

.glyphicon-yen:before {
  content: "\00a5"; }

.glyphicon-jpy:before {
  content: "\00a5"; }

.glyphicon-ruble:before {
  content: "\20bd"; }

.glyphicon-rub:before {
  content: "\20bd"; }

.glyphicon-scale:before {
  content: "\e230"; }

.glyphicon-ice-lolly:before {
  content: "\e231"; }

.glyphicon-ice-lolly-tasted:before {
  content: "\e232"; }

.glyphicon-education:before {
  content: "\e233"; }

.glyphicon-option-horizontal:before {
  content: "\e234"; }

.glyphicon-option-vertical:before {
  content: "\e235"; }

.glyphicon-menu-hamburger:before {
  content: "\e236"; }

.glyphicon-modal-window:before {
  content: "\e237"; }

.glyphicon-oil:before {
  content: "\e238"; }

.glyphicon-grain:before {
  content: "\e239"; }

.glyphicon-sunglasses:before {
  content: "\e240"; }

.glyphicon-text-size:before {
  content: "\e241"; }

.glyphicon-text-color:before {
  content: "\e242"; }

.glyphicon-text-background:before {
  content: "\e243"; }

.glyphicon-object-align-top:before {
  content: "\e244"; }

.glyphicon-object-align-bottom:before {
  content: "\e245"; }

.glyphicon-object-align-horizontal:before {
  content: "\e246"; }

.glyphicon-object-align-left:before {
  content: "\e247"; }

.glyphicon-object-align-vertical:before {
  content: "\e248"; }

.glyphicon-object-align-right:before {
  content: "\e249"; }

.glyphicon-triangle-right:before {
  content: "\e250"; }

.glyphicon-triangle-left:before {
  content: "\e251"; }

.glyphicon-triangle-bottom:before {
  content: "\e252"; }

.glyphicon-triangle-top:before {
  content: "\e253"; }

.glyphicon-console:before {
  content: "\e254"; }

.glyphicon-superscript:before {
  content: "\e255"; }

.glyphicon-subscript:before {
  content: "\e256"; }

.glyphicon-menu-left:before {
  content: "\e257"; }

.glyphicon-menu-right:before {
  content: "\e258"; }

.glyphicon-menu-down:before {
  content: "\e259"; }

.glyphicon-menu-up:before {
  content: "\e260"; }

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

html {
  font-size: 10px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }

body {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  line-height: 1.428571429;
  color: #ffffff;
  background-color: #fff; }

input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit; }

a {
  color: #337ab7;
  text-decoration: none; }
  a:hover, a:focus {
    color: #23527c;
    text-decoration: underline; }
  a:focus {
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px; }

figure {
  margin: 0; }

img {
  vertical-align: middle; }

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto; }

.img-rounded {
  border-radius: 6px; }

.img-thumbnail {
  padding: 4px;
  line-height: 1.428571429;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto; }

.img-circle {
  border-radius: 50%; }

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eeeeee; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto; }

[role="button"] {
  cursor: pointer; }

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit; }
  h1 small,
  h1 .small, h2 small,
  h2 .small, h3 small,
  h3 .small, h4 small,
  h4 .small, h5 small,
  h5 .small, h6 small,
  h6 .small,
  .h1 small,
  .h1 .small, .h2 small,
  .h2 .small, .h3 small,
  .h3 .small, .h4 small,
  .h4 .small, .h5 small,
  .h5 .small, .h6 small,
  .h6 .small {
    font-weight: normal;
    line-height: 1;
    color: #777777; }

h1, .h1,
h2, .h2,
h3, .h3 {
  margin-top: 20px;
  margin-bottom: 10px; }
  h1 small,
  h1 .small, .h1 small,
  .h1 .small,
  h2 small,
  h2 .small, .h2 small,
  .h2 .small,
  h3 small,
  h3 .small, .h3 small,
  .h3 .small {
    font-size: 65%; }

h4, .h4,
h5, .h5,
h6, .h6 {
  margin-top: 10px;
  margin-bottom: 10px; }
  h4 small,
  h4 .small, .h4 small,
  .h4 .small,
  h5 small,
  h5 .small, .h5 small,
  .h5 .small,
  h6 small,
  h6 .small, .h6 small,
  .h6 .small {
    font-size: 75%; }

h1, .h1 {
  font-size: 36px; }

h2, .h2 {
  font-size: 30px; }

h3, .h3 {
  font-size: 24px; }

h4, .h4 {
  font-size: 18px; }

h5, .h5 {
  font-size: 14px; }

h6, .h6 {
  font-size: 12px; }

p {
  margin: 0 0 10px; }

.lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.4; }
  @media (min-width: 768px) {
    .lead {
      font-size: 21px; } }

small,
.small {
  font-size: 85%; }

mark,
.mark {
  background-color: #fcf8e3;
  padding: .2em; }

.text-left {
  text-align: left; }

.text-right {
  text-align: right; }

.text-center {
  text-align: center; }

.text-justify {
  text-align: justify; }

.text-nowrap {
  white-space: nowrap; }

.text-lowercase {
  text-transform: lowercase; }

.text-uppercase, .initialism {
  text-transform: uppercase; }

.text-capitalize {
  text-transform: capitalize; }

.text-muted {
  color: #777777; }

.text-primary {
  color: #337ab7; }

a.text-primary:hover,
a.text-primary:focus {
  color: #286090; }

.text-success {
  color: #3c763d; }

a.text-success:hover,
a.text-success:focus {
  color: #2b542c; }

.text-info {
  color: #31708f; }

a.text-info:hover,
a.text-info:focus {
  color: #245269; }

.text-warning {
  color: #8a6d3b; }

a.text-warning:hover,
a.text-warning:focus {
  color: #66512c; }

.text-danger {
  color: #a94442; }

a.text-danger:hover,
a.text-danger:focus {
  color: #843534; }

.bg-primary {
  color: #fff; }

.bg-primary {
  background-color: #337ab7; }

a.bg-primary:hover,
a.bg-primary:focus {
  background-color: #286090; }

.bg-success {
  background-color: #dff0d8; }

a.bg-success:hover,
a.bg-success:focus {
  background-color: #c1e2b3; }

.bg-info {
  background-color: #d9edf7; }

a.bg-info:hover,
a.bg-info:focus {
  background-color: #afd9ee; }

.bg-warning {
  background-color: #fcf8e3; }

a.bg-warning:hover,
a.bg-warning:focus {
  background-color: #f7ecb5; }

.bg-danger {
  background-color: #f2dede; }

a.bg-danger:hover,
a.bg-danger:focus {
  background-color: #e4b9b9; }

.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eeeeee; }

ul,
ol {
  margin-top: 0;
  margin-bottom: 10px; }
  ul ul,
  ul ol,
  ol ul,
  ol ol {
    margin-bottom: 0; }

.list-unstyled {
  padding-left: 0;
  list-style: none; }

.list-inline {
  padding-left: 0;
  list-style: none;
  margin-left: -5px; }
  .list-inline > li {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px; }

dl {
  margin-top: 0;
  margin-bottom: 20px; }

dt,
dd {
  line-height: 1.428571429; }

dt {
  font-weight: bold; }

dd {
  margin-left: 0; }

.dl-horizontal dd:before, .dl-horizontal dd:after {
  content: " ";
  display: table; }
.dl-horizontal dd:after {
  clear: both; }
@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
  .dl-horizontal dd {
    margin-left: 180px; } }

abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #777777; }

.initialism {
  font-size: 90%; }

blockquote {
  padding: 10px 20px;
  margin: 0 0 20px;
  font-size: 17.5px;
  border-left: 5px solid #eeeeee; }
  blockquote p:last-child,
  blockquote ul:last-child,
  blockquote ol:last-child {
    margin-bottom: 0; }
  blockquote footer,
  blockquote small,
  blockquote .small {
    display: block;
    font-size: 80%;
    line-height: 1.428571429;
    color: #777777; }
    blockquote footer:before,
    blockquote small:before,
    blockquote .small:before {
      content: '\2014 \00A0'; }

.blockquote-reverse,
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eeeeee;
  border-left: 0;
  text-align: right; }
  .blockquote-reverse footer:before,
  .blockquote-reverse small:before,
  .blockquote-reverse .small:before,
  blockquote.pull-right footer:before,
  blockquote.pull-right small:before,
  blockquote.pull-right .small:before {
    content: ''; }
  .blockquote-reverse footer:after,
  .blockquote-reverse small:after,
  .blockquote-reverse .small:after,
  blockquote.pull-right footer:after,
  blockquote.pull-right small:after,
  blockquote.pull-right .small:after {
    content: '\00A0 \2014'; }

address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.428571429; }

code,
kbd,
pre,
samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace; }

code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px; }

kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #fff;
  background-color: #333;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25); }
  kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: bold;
    box-shadow: none; }

pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.428571429;
  word-break: break-all;
  word-wrap: break-word;
  color: #333333;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px; }
  pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0; }

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll; }

.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px; }
  .container:before, .container:after {
    content: " ";
    display: table; }
  .container:after {
    clear: both; }
  @media (min-width: 768px) {
    .container {
      width: 750px; } }
  @media (min-width: 992px) {
    .container {
      width: 970px; } }
  @media (min-width: 1200px) {
    .container {
      width: 1170px; } }

.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px; }
  .container-fluid:before, .container-fluid:after {
    content: " ";
    display: table; }
  .container-fluid:after {
    clear: both; }

.row {
  margin-left: -15px;
  margin-right: -15px; }
  .row:before, .row:after {
    content: " ";
    display: table; }
  .row:after {
    clear: both; }

.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px; }

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
  float: left; }

.col-xs-1 {
  width: 8.3333333333%; }

.col-xs-2 {
  width: 16.6666666667%; }

.col-xs-3 {
  width: 25%; }

.col-xs-4 {
  width: 33.3333333333%; }

.col-xs-5 {
  width: 41.6666666667%; }

.col-xs-6 {
  width: 50%; }

.col-xs-7 {
  width: 58.3333333333%; }

.col-xs-8 {
  width: 66.6666666667%; }

.col-xs-9 {
  width: 75%; }

.col-xs-10 {
  width: 83.3333333333%; }

.col-xs-11 {
  width: 91.6666666667%; }

.col-xs-12 {
  width: 100%; }

.col-xs-pull-0 {
  right: auto; }

.col-xs-pull-1 {
  right: 8.3333333333%; }

.col-xs-pull-2 {
  right: 16.6666666667%; }

.col-xs-pull-3 {
  right: 25%; }

.col-xs-pull-4 {
  right: 33.3333333333%; }

.col-xs-pull-5 {
  right: 41.6666666667%; }

.col-xs-pull-6 {
  right: 50%; }

.col-xs-pull-7 {
  right: 58.3333333333%; }

.col-xs-pull-8 {
  right: 66.6666666667%; }

.col-xs-pull-9 {
  right: 75%; }

.col-xs-pull-10 {
  right: 83.3333333333%; }

.col-xs-pull-11 {
  right: 91.6666666667%; }

.col-xs-pull-12 {
  right: 100%; }

.col-xs-push-0 {
  left: auto; }

.col-xs-push-1 {
  left: 8.3333333333%; }

.col-xs-push-2 {
  left: 16.6666666667%; }

.col-xs-push-3 {
  left: 25%; }

.col-xs-push-4 {
  left: 33.3333333333%; }

.col-xs-push-5 {
  left: 41.6666666667%; }

.col-xs-push-6 {
  left: 50%; }

.col-xs-push-7 {
  left: 58.3333333333%; }

.col-xs-push-8 {
  left: 66.6666666667%; }

.col-xs-push-9 {
  left: 75%; }

.col-xs-push-10 {
  left: 83.3333333333%; }

.col-xs-push-11 {
  left: 91.6666666667%; }

.col-xs-push-12 {
  left: 100%; }

.col-xs-offset-0 {
  margin-left: 0%; }

.col-xs-offset-1 {
  margin-left: 8.3333333333%; }

.col-xs-offset-2 {
  margin-left: 16.6666666667%; }

.col-xs-offset-3 {
  margin-left: 25%; }

.col-xs-offset-4 {
  margin-left: 33.3333333333%; }

.col-xs-offset-5 {
  margin-left: 41.6666666667%; }

.col-xs-offset-6 {
  margin-left: 50%; }

.col-xs-offset-7 {
  margin-left: 58.3333333333%; }

.col-xs-offset-8 {
  margin-left: 66.6666666667%; }

.col-xs-offset-9 {
  margin-left: 75%; }

.col-xs-offset-10 {
  margin-left: 83.3333333333%; }

.col-xs-offset-11 {
  margin-left: 91.6666666667%; }

.col-xs-offset-12 {
  margin-left: 100%; }

@media (min-width: 768px) {
  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
    float: left; }

  .col-sm-1 {
    width: 8.3333333333%; }

  .col-sm-2 {
    width: 16.6666666667%; }

  .col-sm-3 {
    width: 25%; }

  .col-sm-4 {
    width: 33.3333333333%; }

  .col-sm-5 {
    width: 41.6666666667%; }

  .col-sm-6 {
    width: 50%; }

  .col-sm-7 {
    width: 58.3333333333%; }

  .col-sm-8 {
    width: 66.6666666667%; }

  .col-sm-9 {
    width: 75%; }

  .col-sm-10 {
    width: 83.3333333333%; }

  .col-sm-11 {
    width: 91.6666666667%; }

  .col-sm-12 {
    width: 100%; }

  .col-sm-pull-0 {
    right: auto; }

  .col-sm-pull-1 {
    right: 8.3333333333%; }

  .col-sm-pull-2 {
    right: 16.6666666667%; }

  .col-sm-pull-3 {
    right: 25%; }

  .col-sm-pull-4 {
    right: 33.3333333333%; }

  .col-sm-pull-5 {
    right: 41.6666666667%; }

  .col-sm-pull-6 {
    right: 50%; }

  .col-sm-pull-7 {
    right: 58.3333333333%; }

  .col-sm-pull-8 {
    right: 66.6666666667%; }

  .col-sm-pull-9 {
    right: 75%; }

  .col-sm-pull-10 {
    right: 83.3333333333%; }

  .col-sm-pull-11 {
    right: 91.6666666667%; }

  .col-sm-pull-12 {
    right: 100%; }

  .col-sm-push-0 {
    left: auto; }

  .col-sm-push-1 {
    left: 8.3333333333%; }

  .col-sm-push-2 {
    left: 16.6666666667%; }

  .col-sm-push-3 {
    left: 25%; }

  .col-sm-push-4 {
    left: 33.3333333333%; }

  .col-sm-push-5 {
    left: 41.6666666667%; }

  .col-sm-push-6 {
    left: 50%; }

  .col-sm-push-7 {
    left: 58.3333333333%; }

  .col-sm-push-8 {
    left: 66.6666666667%; }

  .col-sm-push-9 {
    left: 75%; }

  .col-sm-push-10 {
    left: 83.3333333333%; }

  .col-sm-push-11 {
    left: 91.6666666667%; }

  .col-sm-push-12 {
    left: 100%; }

  .col-sm-offset-0 {
    margin-left: 0%; }

  .col-sm-offset-1 {
    margin-left: 8.3333333333%; }

  .col-sm-offset-2 {
    margin-left: 16.6666666667%; }

  .col-sm-offset-3 {
    margin-left: 25%; }

  .col-sm-offset-4 {
    margin-left: 33.3333333333%; }

  .col-sm-offset-5 {
    margin-left: 41.6666666667%; }

  .col-sm-offset-6 {
    margin-left: 50%; }

  .col-sm-offset-7 {
    margin-left: 58.3333333333%; }

  .col-sm-offset-8 {
    margin-left: 66.6666666667%; }

  .col-sm-offset-9 {
    margin-left: 75%; }

  .col-sm-offset-10 {
    margin-left: 83.3333333333%; }

  .col-sm-offset-11 {
    margin-left: 91.6666666667%; }

  .col-sm-offset-12 {
    margin-left: 100%; } }
@media (min-width: 992px) {
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: left; }

  .col-md-1 {
    width: 8.3333333333%; }

  .col-md-2 {
    width: 16.6666666667%; }

  .col-md-3 {
    width: 25%; }

  .col-md-4 {
    width: 33.3333333333%; }

  .col-md-5 {
    width: 41.6666666667%; }

  .col-md-6 {
    width: 50%; }

  .col-md-7 {
    width: 58.3333333333%; }

  .col-md-8 {
    width: 66.6666666667%; }

  .col-md-9 {
    width: 75%; }

  .col-md-10 {
    width: 83.3333333333%; }

  .col-md-11 {
    width: 91.6666666667%; }

  .col-md-12 {
    width: 100%; }

  .col-md-pull-0 {
    right: auto; }

  .col-md-pull-1 {
    right: 8.3333333333%; }

  .col-md-pull-2 {
    right: 16.6666666667%; }

  .col-md-pull-3 {
    right: 25%; }

  .col-md-pull-4 {
    right: 33.3333333333%; }

  .col-md-pull-5 {
    right: 41.6666666667%; }

  .col-md-pull-6 {
    right: 50%; }

  .col-md-pull-7 {
    right: 58.3333333333%; }

  .col-md-pull-8 {
    right: 66.6666666667%; }

  .col-md-pull-9 {
    right: 75%; }

  .col-md-pull-10 {
    right: 83.3333333333%; }

  .col-md-pull-11 {
    right: 91.6666666667%; }

  .col-md-pull-12 {
    right: 100%; }

  .col-md-push-0 {
    left: auto; }

  .col-md-push-1 {
    left: 8.3333333333%; }

  .col-md-push-2 {
    left: 16.6666666667%; }

  .col-md-push-3 {
    left: 25%; }

  .col-md-push-4 {
    left: 33.3333333333%; }

  .col-md-push-5 {
    left: 41.6666666667%; }

  .col-md-push-6 {
    left: 50%; }

  .col-md-push-7 {
    left: 58.3333333333%; }

  .col-md-push-8 {
    left: 66.6666666667%; }

  .col-md-push-9 {
    left: 75%; }

  .col-md-push-10 {
    left: 83.3333333333%; }

  .col-md-push-11 {
    left: 91.6666666667%; }

  .col-md-push-12 {
    left: 100%; }

  .col-md-offset-0 {
    margin-left: 0%; }

  .col-md-offset-1 {
    margin-left: 8.3333333333%; }

  .col-md-offset-2 {
    margin-left: 16.6666666667%; }

  .col-md-offset-3 {
    margin-left: 25%; }

  .col-md-offset-4 {
    margin-left: 33.3333333333%; }

  .col-md-offset-5 {
    margin-left: 41.6666666667%; }

  .col-md-offset-6 {
    margin-left: 50%; }

  .col-md-offset-7 {
    margin-left: 58.3333333333%; }

  .col-md-offset-8 {
    margin-left: 66.6666666667%; }

  .col-md-offset-9 {
    margin-left: 75%; }

  .col-md-offset-10 {
    margin-left: 83.3333333333%; }

  .col-md-offset-11 {
    margin-left: 91.6666666667%; }

  .col-md-offset-12 {
    margin-left: 100%; } }
@media (min-width: 1200px) {
  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
    float: left; }

  .col-lg-1 {
    width: 8.3333333333%; }

  .col-lg-2 {
    width: 16.6666666667%; }

  .col-lg-3 {
    width: 25%; }

  .col-lg-4 {
    width: 33.3333333333%; }

  .col-lg-5 {
    width: 41.6666666667%; }

  .col-lg-6 {
    width: 50%; }

  .col-lg-7 {
    width: 58.3333333333%; }

  .col-lg-8 {
    width: 66.6666666667%; }

  .col-lg-9 {
    width: 75%; }

  .col-lg-10 {
    width: 83.3333333333%; }

  .col-lg-11 {
    width: 91.6666666667%; }

  .col-lg-12 {
    width: 100%; }

  .col-lg-pull-0 {
    right: auto; }

  .col-lg-pull-1 {
    right: 8.3333333333%; }

  .col-lg-pull-2 {
    right: 16.6666666667%; }

  .col-lg-pull-3 {
    right: 25%; }

  .col-lg-pull-4 {
    right: 33.3333333333%; }

  .col-lg-pull-5 {
    right: 41.6666666667%; }

  .col-lg-pull-6 {
    right: 50%; }

  .col-lg-pull-7 {
    right: 58.3333333333%; }

  .col-lg-pull-8 {
    right: 66.6666666667%; }

  .col-lg-pull-9 {
    right: 75%; }

  .col-lg-pull-10 {
    right: 83.3333333333%; }

  .col-lg-pull-11 {
    right: 91.6666666667%; }

  .col-lg-pull-12 {
    right: 100%; }

  .col-lg-push-0 {
    left: auto; }

  .col-lg-push-1 {
    left: 8.3333333333%; }

  .col-lg-push-2 {
    left: 16.6666666667%; }

  .col-lg-push-3 {
    left: 25%; }

  .col-lg-push-4 {
    left: 33.3333333333%; }

  .col-lg-push-5 {
    left: 41.6666666667%; }

  .col-lg-push-6 {
    left: 50%; }

  .col-lg-push-7 {
    left: 58.3333333333%; }

  .col-lg-push-8 {
    left: 66.6666666667%; }

  .col-lg-push-9 {
    left: 75%; }

  .col-lg-push-10 {
    left: 83.3333333333%; }

  .col-lg-push-11 {
    left: 91.6666666667%; }

  .col-lg-push-12 {
    left: 100%; }

  .col-lg-offset-0 {
    margin-left: 0%; }

  .col-lg-offset-1 {
    margin-left: 8.3333333333%; }

  .col-lg-offset-2 {
    margin-left: 16.6666666667%; }

  .col-lg-offset-3 {
    margin-left: 25%; }

  .col-lg-offset-4 {
    margin-left: 33.3333333333%; }

  .col-lg-offset-5 {
    margin-left: 41.6666666667%; }

  .col-lg-offset-6 {
    margin-left: 50%; }

  .col-lg-offset-7 {
    margin-left: 58.3333333333%; }

  .col-lg-offset-8 {
    margin-left: 66.6666666667%; }

  .col-lg-offset-9 {
    margin-left: 75%; }

  .col-lg-offset-10 {
    margin-left: 83.3333333333%; }

  .col-lg-offset-11 {
    margin-left: 91.6666666667%; }

  .col-lg-offset-12 {
    margin-left: 100%; } }
table {
  background-color: transparent; }

caption {
  padding-top: 8px;
  padding-bottom: 8px;
  color: #777777;
  text-align: left; }

th {
  text-align: left; }

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px; }
  .table > thead > tr > th,
  .table > thead > tr > td,
  .table > tbody > tr > th,
  .table > tbody > tr > td,
  .table > tfoot > tr > th,
  .table > tfoot > tr > td {
    padding: 8px;
    line-height: 1.428571429;
    vertical-align: top;
    border-top: 1px solid #ddd; }
  .table > thead > tr > th {
    vertical-align: bottom;
    border-bottom: 2px solid #ddd; }
  .table > caption + thead > tr:first-child > th,
  .table > caption + thead > tr:first-child > td,
  .table > colgroup + thead > tr:first-child > th,
  .table > colgroup + thead > tr:first-child > td,
  .table > thead:first-child > tr:first-child > th,
  .table > thead:first-child > tr:first-child > td {
    border-top: 0; }
  .table > tbody + tbody {
    border-top: 2px solid #ddd; }
  .table .table {
    background-color: #fff; }

.table-condensed > thead > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > th,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > th,
.table-condensed > tfoot > tr > td {
  padding: 5px; }

.table-bordered {
  border: 1px solid #ddd; }
  .table-bordered > thead > tr > th,
  .table-bordered > thead > tr > td,
  .table-bordered > tbody > tr > th,
  .table-bordered > tbody > tr > td,
  .table-bordered > tfoot > tr > th,
  .table-bordered > tfoot > tr > td {
    border: 1px solid #ddd; }
  .table-bordered > thead > tr > th,
  .table-bordered > thead > tr > td {
    border-bottom-width: 2px; }

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9; }

.table-hover > tbody > tr:hover {
  background-color: #f5f5f5; }

table col[class*="col-"] {
  position: static;
  float: none;
  display: table-column; }

table td[class*="col-"],
table th[class*="col-"] {
  position: static;
  float: none;
  display: table-cell; }

.table > thead > tr > td.active,
.table > thead > tr > th.active, .table > thead > tr.active > td, .table > thead > tr.active > th,
.table > tbody > tr > td.active,
.table > tbody > tr > th.active,
.table > tbody > tr.active > td,
.table > tbody > tr.active > th,
.table > tfoot > tr > td.active,
.table > tfoot > tr > th.active,
.table > tfoot > tr.active > td,
.table > tfoot > tr.active > th {
  background-color: #f5f5f5; }

.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover, .table-hover > tbody > tr.active:hover > td, .table-hover > tbody > tr:hover > .active, .table-hover > tbody > tr.active:hover > th {
  background-color: #e8e8e8; }

.table > thead > tr > td.success,
.table > thead > tr > th.success, .table > thead > tr.success > td, .table > thead > tr.success > th,
.table > tbody > tr > td.success,
.table > tbody > tr > th.success,
.table > tbody > tr.success > td,
.table > tbody > tr.success > th,
.table > tfoot > tr > td.success,
.table > tfoot > tr > th.success,
.table > tfoot > tr.success > td,
.table > tfoot > tr.success > th {
  background-color: #dff0d8; }

.table-hover > tbody > tr > td.success:hover,
.table-hover > tbody > tr > th.success:hover, .table-hover > tbody > tr.success:hover > td, .table-hover > tbody > tr:hover > .success, .table-hover > tbody > tr.success:hover > th {
  background-color: #d0e9c6; }

.table > thead > tr > td.info,
.table > thead > tr > th.info, .table > thead > tr.info > td, .table > thead > tr.info > th,
.table > tbody > tr > td.info,
.table > tbody > tr > th.info,
.table > tbody > tr.info > td,
.table > tbody > tr.info > th,
.table > tfoot > tr > td.info,
.table > tfoot > tr > th.info,
.table > tfoot > tr.info > td,
.table > tfoot > tr.info > th {
  background-color: #d9edf7; }

.table-hover > tbody > tr > td.info:hover,
.table-hover > tbody > tr > th.info:hover, .table-hover > tbody > tr.info:hover > td, .table-hover > tbody > tr:hover > .info, .table-hover > tbody > tr.info:hover > th {
  background-color: #c4e3f3; }

.table > thead > tr > td.warning,
.table > thead > tr > th.warning, .table > thead > tr.warning > td, .table > thead > tr.warning > th,
.table > tbody > tr > td.warning,
.table > tbody > tr > th.warning,
.table > tbody > tr.warning > td,
.table > tbody > tr.warning > th,
.table > tfoot > tr > td.warning,
.table > tfoot > tr > th.warning,
.table > tfoot > tr.warning > td,
.table > tfoot > tr.warning > th {
  background-color: #fcf8e3; }

.table-hover > tbody > tr > td.warning:hover,
.table-hover > tbody > tr > th.warning:hover, .table-hover > tbody > tr.warning:hover > td, .table-hover > tbody > tr:hover > .warning, .table-hover > tbody > tr.warning:hover > th {
  background-color: #faf2cc; }

.table > thead > tr > td.danger,
.table > thead > tr > th.danger, .table > thead > tr.danger > td, .table > thead > tr.danger > th,
.table > tbody > tr > td.danger,
.table > tbody > tr > th.danger,
.table > tbody > tr.danger > td,
.table > tbody > tr.danger > th,
.table > tfoot > tr > td.danger,
.table > tfoot > tr > th.danger,
.table > tfoot > tr.danger > td,
.table > tfoot > tr.danger > th {
  background-color: #f2dede; }

.table-hover > tbody > tr > td.danger:hover,
.table-hover > tbody > tr > th.danger:hover, .table-hover > tbody > tr.danger:hover > td, .table-hover > tbody > tr:hover > .danger, .table-hover > tbody > tr.danger:hover > th {
  background-color: #ebcccc; }

.table-responsive {
  overflow-x: auto;
  min-height: 0.01%; }
  @media screen and (max-width: 767px) {
    .table-responsive {
      width: 100%;
      margin-bottom: 15px;
      overflow-y: hidden;
      -ms-overflow-style: -ms-autohiding-scrollbar;
      border: 1px solid #ddd; }
      .table-responsive > .table {
        margin-bottom: 0; }
        .table-responsive > .table > thead > tr > th,
        .table-responsive > .table > thead > tr > td,
        .table-responsive > .table > tbody > tr > th,
        .table-responsive > .table > tbody > tr > td,
        .table-responsive > .table > tfoot > tr > th,
        .table-responsive > .table > tfoot > tr > td {
          white-space: nowrap; }
      .table-responsive > .table-bordered {
        border: 0; }
        .table-responsive > .table-bordered > thead > tr > th:first-child,
        .table-responsive > .table-bordered > thead > tr > td:first-child,
        .table-responsive > .table-bordered > tbody > tr > th:first-child,
        .table-responsive > .table-bordered > tbody > tr > td:first-child,
        .table-responsive > .table-bordered > tfoot > tr > th:first-child,
        .table-responsive > .table-bordered > tfoot > tr > td:first-child {
          border-left: 0; }
        .table-responsive > .table-bordered > thead > tr > th:last-child,
        .table-responsive > .table-bordered > thead > tr > td:last-child,
        .table-responsive > .table-bordered > tbody > tr > th:last-child,
        .table-responsive > .table-bordered > tbody > tr > td:last-child,
        .table-responsive > .table-bordered > tfoot > tr > th:last-child,
        .table-responsive > .table-bordered > tfoot > tr > td:last-child {
          border-right: 0; }
        .table-responsive > .table-bordered > tbody > tr:last-child > th,
        .table-responsive > .table-bordered > tbody > tr:last-child > td,
        .table-responsive > .table-bordered > tfoot > tr:last-child > th,
        .table-responsive > .table-bordered > tfoot > tr:last-child > td {
          border-bottom: 0; } }

fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0; }

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #333333;
  border: 0;
  border-bottom: 1px solid #e5e5e5; }

label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: bold; }

input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  line-height: normal; }

input[type="file"] {
  display: block; }

input[type="range"] {
  display: block;
  width: 100%; }

select[multiple],
select[size] {
  height: auto; }

input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px; }

output {
  display: block;
  padding-top: 7px;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555555; }

.form-control {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s; }
  .form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6); }
  .form-control::-moz-placeholder {
    color: #999;
    opacity: 1; }
  .form-control:-ms-input-placeholder {
    color: #999; }
  .form-control::-webkit-input-placeholder {
    color: #999; }
  .form-control::-ms-expand {
    border: 0;
    background-color: transparent; }
  .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    background-color: #eeeeee;
    opacity: 1; }
  .form-control[disabled], fieldset[disabled] .form-control {
    cursor: not-allowed; }

textarea.form-control {
  height: auto; }

input[type="search"] {
  -webkit-appearance: none; }

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"].form-control,
  input[type="time"].form-control,
  input[type="datetime-local"].form-control,
  input[type="month"].form-control {
    line-height: 34px; }
  input[type="date"].input-sm, .input-group-sm > input[type="date"].form-control,
  .input-group-sm > input[type="date"].input-group-addon,
  .input-group-sm > .input-group-btn > input[type="date"].btn, .input-group-sm input[type="date"],
  input[type="time"].input-sm,
  .input-group-sm > input[type="time"].form-control,
  .input-group-sm > input[type="time"].input-group-addon,
  .input-group-sm > .input-group-btn > input[type="time"].btn,
  .input-group-sm input[type="time"],
  input[type="datetime-local"].input-sm,
  .input-group-sm > input[type="datetime-local"].form-control,
  .input-group-sm > input[type="datetime-local"].input-group-addon,
  .input-group-sm > .input-group-btn > input[type="datetime-local"].btn,
  .input-group-sm input[type="datetime-local"],
  input[type="month"].input-sm,
  .input-group-sm > input[type="month"].form-control,
  .input-group-sm > input[type="month"].input-group-addon,
  .input-group-sm > .input-group-btn > input[type="month"].btn,
  .input-group-sm input[type="month"] {
    line-height: 30px; }
  input[type="date"].input-lg, .input-group-lg > input[type="date"].form-control,
  .input-group-lg > input[type="date"].input-group-addon,
  .input-group-lg > .input-group-btn > input[type="date"].btn, .input-group-lg input[type="date"], input[type="time"].input-lg, .input-group-lg > input[type="time"].form-control,
  .input-group-lg > input[type="time"].input-group-addon,
  .input-group-lg > .input-group-btn > input[type="time"].btn, .input-group-lg input[type="time"], input[type="datetime-local"].input-lg, .input-group-lg > input[type="datetime-local"].form-control,
  .input-group-lg > input[type="datetime-local"].input-group-addon,
  .input-group-lg > .input-group-btn > input[type="datetime-local"].btn, .input-group-lg input[type="datetime-local"], input[type="month"].input-lg, .input-group-lg > input[type="month"].form-control,
  .input-group-lg > input[type="month"].input-group-addon,
  .input-group-lg > .input-group-btn > input[type="month"].btn, .input-group-lg input[type="month"] {
    line-height: 46px; } }
.form-group {
  margin-bottom: 15px; }

.radio,
.checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px; }
  .radio label,
  .checkbox label {
    min-height: 20px;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer; }

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-left: -20px;
  margin-top: 4px \9; }

.radio + .radio,
.checkbox + .checkbox {
  margin-top: -5px; }

.radio-inline,
.checkbox-inline {
  position: relative;
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer; }

.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px; }

input[type="radio"][disabled], input[type="radio"].disabled, fieldset[disabled] input[type="radio"],
input[type="checkbox"][disabled],
input[type="checkbox"].disabled,
fieldset[disabled] input[type="checkbox"] {
  cursor: not-allowed; }

.radio-inline.disabled, fieldset[disabled] .radio-inline,
.checkbox-inline.disabled,
fieldset[disabled] .checkbox-inline {
  cursor: not-allowed; }

.radio.disabled label, fieldset[disabled] .radio label,
.checkbox.disabled label,
fieldset[disabled] .checkbox label {
  cursor: not-allowed; }

.form-control-static {
  padding-top: 7px;
  padding-bottom: 7px;
  margin-bottom: 0;
  min-height: 34px; }
  .form-control-static.input-lg, .input-group-lg > .form-control-static.form-control,
  .input-group-lg > .form-control-static.input-group-addon,
  .input-group-lg > .input-group-btn > .form-control-static.btn, .form-control-static.input-sm, .input-group-sm > .form-control-static.form-control,
  .input-group-sm > .form-control-static.input-group-addon,
  .input-group-sm > .input-group-btn > .form-control-static.btn {
    padding-left: 0;
    padding-right: 0; }

.input-sm, .input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }

select.input-sm, .input-group-sm > select.form-control,
.input-group-sm > select.input-group-addon,
.input-group-sm > .input-group-btn > select.btn {
  height: 30px;
  line-height: 30px; }

textarea.input-sm, .input-group-sm > textarea.form-control,
.input-group-sm > textarea.input-group-addon,
.input-group-sm > .input-group-btn > textarea.btn,
select[multiple].input-sm,
.input-group-sm > select[multiple].form-control,
.input-group-sm > select[multiple].input-group-addon,
.input-group-sm > .input-group-btn > select[multiple].btn {
  height: auto; }

.form-group-sm .form-control {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }
.form-group-sm select.form-control {
  height: 30px;
  line-height: 30px; }
.form-group-sm textarea.form-control,
.form-group-sm select[multiple].form-control {
  height: auto; }
.form-group-sm .form-control-static {
  height: 30px;
  min-height: 32px;
  padding: 6px 10px;
  font-size: 12px;
  line-height: 1.5; }

.input-lg, .input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px; }

select.input-lg, .input-group-lg > select.form-control,
.input-group-lg > select.input-group-addon,
.input-group-lg > .input-group-btn > select.btn {
  height: 46px;
  line-height: 46px; }

textarea.input-lg, .input-group-lg > textarea.form-control,
.input-group-lg > textarea.input-group-addon,
.input-group-lg > .input-group-btn > textarea.btn,
select[multiple].input-lg,
.input-group-lg > select[multiple].form-control,
.input-group-lg > select[multiple].input-group-addon,
.input-group-lg > .input-group-btn > select[multiple].btn {
  height: auto; }

.form-group-lg .form-control {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px; }
.form-group-lg select.form-control {
  height: 46px;
  line-height: 46px; }
.form-group-lg textarea.form-control,
.form-group-lg select[multiple].form-control {
  height: auto; }
.form-group-lg .form-control-static {
  height: 46px;
  min-height: 38px;
  padding: 11px 16px;
  font-size: 18px;
  line-height: 1.3333333; }

.has-feedback {
  position: relative; }
  .has-feedback .form-control {
    padding-right: 42.5px; }

.form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  pointer-events: none; }

.input-lg + .form-control-feedback, .input-group-lg > .form-control + .form-control-feedback,
.input-group-lg > .input-group-addon + .form-control-feedback,
.input-group-lg > .input-group-btn > .btn + .form-control-feedback,
.input-group-lg + .form-control-feedback,
.form-group-lg .form-control + .form-control-feedback {
  width: 46px;
  height: 46px;
  line-height: 46px; }

.input-sm + .form-control-feedback, .input-group-sm > .form-control + .form-control-feedback,
.input-group-sm > .input-group-addon + .form-control-feedback,
.input-group-sm > .input-group-btn > .btn + .form-control-feedback,
.input-group-sm + .form-control-feedback,
.form-group-sm .form-control + .form-control-feedback {
  width: 30px;
  height: 30px;
  line-height: 30px; }

.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline, .has-success.radio label, .has-success.checkbox label, .has-success.radio-inline label, .has-success.checkbox-inline label {
  color: #3c763d; }
.has-success .form-control {
  border-color: #3c763d;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-success .form-control:focus {
    border-color: #2b542c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168; }
.has-success .input-group-addon {
  color: #3c763d;
  border-color: #3c763d;
  background-color: #dff0d8; }
.has-success .form-control-feedback {
  color: #3c763d; }

.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline, .has-warning.radio label, .has-warning.checkbox label, .has-warning.radio-inline label, .has-warning.checkbox-inline label {
  color: #8a6d3b; }
.has-warning .form-control {
  border-color: #8a6d3b;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-warning .form-control:focus {
    border-color: #66512c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b; }
.has-warning .input-group-addon {
  color: #8a6d3b;
  border-color: #8a6d3b;
  background-color: #fcf8e3; }
.has-warning .form-control-feedback {
  color: #8a6d3b; }

.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline, .has-error.radio label, .has-error.checkbox label, .has-error.radio-inline label, .has-error.checkbox-inline label {
  color: #a94442; }
.has-error .form-control {
  border-color: #a94442;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-error .form-control:focus {
    border-color: #843534;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483; }
.has-error .input-group-addon {
  color: #a94442;
  border-color: #a94442;
  background-color: #f2dede; }
.has-error .form-control-feedback {
  color: #a94442; }

.has-feedback label ~ .form-control-feedback {
  top: 25px; }
.has-feedback label.sr-only ~ .form-control-feedback {
  top: 0; }

.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: white; }

@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle; }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle; }
  .form-inline .form-control-static {
    display: inline-block; }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle; }
    .form-inline .input-group .input-group-addon,
    .form-inline .input-group .input-group-btn,
    .form-inline .input-group .form-control {
      width: auto; }
  .form-inline .input-group > .form-control {
    width: 100%; }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle; }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle; }
    .form-inline .radio label,
    .form-inline .checkbox label {
      padding-left: 0; }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0; }
  .form-inline .has-feedback .form-control-feedback {
    top: 0; } }

.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px; }
.form-horizontal .radio,
.form-horizontal .checkbox {
  min-height: 27px; }
.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px; }
  .form-horizontal .form-group:before, .form-horizontal .form-group:after {
    content: " ";
    display: table; }
  .form-horizontal .form-group:after {
    clear: both; }
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right;
    margin-bottom: 0;
    padding-top: 7px; } }
.form-horizontal .has-feedback .form-control-feedback {
  right: 15px; }
@media (min-width: 768px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 11px;
    font-size: 18px; } }
@media (min-width: 768px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 6px;
    font-size: 12px; } }

.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }
  .btn:focus, .btn.focus, .btn:active:focus, .btn:active.focus, .btn.active:focus, .btn.active.focus {
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px; }
  .btn:hover, .btn:focus, .btn.focus {
    color: #353535;
    text-decoration: none; }
  .btn:active, .btn.active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }
  .btn.disabled, .btn[disabled], fieldset[disabled] .btn {
    cursor: not-allowed;
    opacity: 0.65;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none; }

a.btn.disabled, fieldset[disabled] a.btn {
  pointer-events: none; }

.btn-default {
  color: #353535;
  background-color: #fff;
  border-color: #ccc; }
  .btn-default:focus, .btn-default.focus {
    color: #353535;
    background-color: #e6e6e6;
    border-color: #8c8c8c; }
  .btn-default:hover {
    color: #353535;
    background-color: #e6e6e6;
    border-color: #adadad; }
  .btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
    color: #353535;
    background-color: #e6e6e6;
    border-color: #adadad; }
    .btn-default:active:hover, .btn-default:active:focus, .btn-default:active.focus, .btn-default.active:hover, .btn-default.active:focus, .btn-default.active.focus, .open > .btn-default.dropdown-toggle:hover, .open > .btn-default.dropdown-toggle:focus, .open > .btn-default.dropdown-toggle.focus {
      color: #353535;
      background-color: #d4d4d4;
      border-color: #8c8c8c; }
  .btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
    background-image: none; }
  .btn-default.disabled:hover, .btn-default.disabled:focus, .btn-default.disabled.focus, .btn-default[disabled]:hover, .btn-default[disabled]:focus, .btn-default[disabled].focus, fieldset[disabled] .btn-default:hover, fieldset[disabled] .btn-default:focus, fieldset[disabled] .btn-default.focus {
    background-color: #fff;
    border-color: #ccc; }
  .btn-default .badge {
    color: #fff;
    background-color: #353535; }

.btn-primary {
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4; }
  .btn-primary:focus, .btn-primary.focus {
    color: #fff;
    background-color: #286090;
    border-color: #122b40; }
  .btn-primary:hover {
    color: #fff;
    background-color: #286090;
    border-color: #204d74; }
  .btn-primary:active, .btn-primary.active, .open > .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #286090;
    border-color: #204d74; }
    .btn-primary:active:hover, .btn-primary:active:focus, .btn-primary:active.focus, .btn-primary.active:hover, .btn-primary.active:focus, .btn-primary.active.focus, .open > .btn-primary.dropdown-toggle:hover, .open > .btn-primary.dropdown-toggle:focus, .open > .btn-primary.dropdown-toggle.focus {
      color: #fff;
      background-color: #204d74;
      border-color: #122b40; }
  .btn-primary:active, .btn-primary.active, .open > .btn-primary.dropdown-toggle {
    background-image: none; }
  .btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled.focus, .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled].focus, fieldset[disabled] .btn-primary:hover, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary.focus {
    background-color: #337ab7;
    border-color: #2e6da4; }
  .btn-primary .badge {
    color: #337ab7;
    background-color: #fff; }

.btn-success {
  color: #fff;
  background-color: #5cb85c;
  border-color: #4cae4c; }
  .btn-success:focus, .btn-success.focus {
    color: #fff;
    background-color: #449d44;
    border-color: #255625; }
  .btn-success:hover {
    color: #fff;
    background-color: #449d44;
    border-color: #398439; }
  .btn-success:active, .btn-success.active, .open > .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #449d44;
    border-color: #398439; }
    .btn-success:active:hover, .btn-success:active:focus, .btn-success:active.focus, .btn-success.active:hover, .btn-success.active:focus, .btn-success.active.focus, .open > .btn-success.dropdown-toggle:hover, .open > .btn-success.dropdown-toggle:focus, .open > .btn-success.dropdown-toggle.focus {
      color: #fff;
      background-color: #398439;
      border-color: #255625; }
  .btn-success:active, .btn-success.active, .open > .btn-success.dropdown-toggle {
    background-image: none; }
  .btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled.focus, .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled].focus, fieldset[disabled] .btn-success:hover, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success.focus {
    background-color: #5cb85c;
    border-color: #4cae4c; }
  .btn-success .badge {
    color: #5cb85c;
    background-color: #fff; }

.btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #46b8da; }
  .btn-info:focus, .btn-info.focus {
    color: #fff;
    background-color: #31b0d5;
    border-color: #1b6d85; }
  .btn-info:hover {
    color: #fff;
    background-color: #31b0d5;
    border-color: #269abc; }
  .btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #31b0d5;
    border-color: #269abc; }
    .btn-info:active:hover, .btn-info:active:focus, .btn-info:active.focus, .btn-info.active:hover, .btn-info.active:focus, .btn-info.active.focus, .open > .btn-info.dropdown-toggle:hover, .open > .btn-info.dropdown-toggle:focus, .open > .btn-info.dropdown-toggle.focus {
      color: #fff;
      background-color: #269abc;
      border-color: #1b6d85; }
  .btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
    background-image: none; }
  .btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled.focus, .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled].focus, fieldset[disabled] .btn-info:hover, fieldset[disabled] .btn-info:focus, fieldset[disabled] .btn-info.focus {
    background-color: #5bc0de;
    border-color: #46b8da; }
  .btn-info .badge {
    color: #5bc0de;
    background-color: #fff; }

.btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236; }
  .btn-warning:focus, .btn-warning.focus {
    color: #fff;
    background-color: #ec971f;
    border-color: #985f0d; }
  .btn-warning:hover {
    color: #fff;
    background-color: #ec971f;
    border-color: #d58512; }
  .btn-warning:active, .btn-warning.active, .open > .btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #ec971f;
    border-color: #d58512; }
    .btn-warning:active:hover, .btn-warning:active:focus, .btn-warning:active.focus, .btn-warning.active:hover, .btn-warning.active:focus, .btn-warning.active.focus, .open > .btn-warning.dropdown-toggle:hover, .open > .btn-warning.dropdown-toggle:focus, .open > .btn-warning.dropdown-toggle.focus {
      color: #fff;
      background-color: #d58512;
      border-color: #985f0d; }
  .btn-warning:active, .btn-warning.active, .open > .btn-warning.dropdown-toggle {
    background-image: none; }
  .btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled.focus, .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled].focus, fieldset[disabled] .btn-warning:hover, fieldset[disabled] .btn-warning:focus, fieldset[disabled] .btn-warning.focus {
    background-color: #f0ad4e;
    border-color: #eea236; }
  .btn-warning .badge {
    color: #f0ad4e;
    background-color: #fff; }

.btn-danger {
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a; }
  .btn-danger:focus, .btn-danger.focus {
    color: #fff;
    background-color: #c9302c;
    border-color: #761c19; }
  .btn-danger:hover {
    color: #fff;
    background-color: #c9302c;
    border-color: #ac2925; }
  .btn-danger:active, .btn-danger.active, .open > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #c9302c;
    border-color: #ac2925; }
    .btn-danger:active:hover, .btn-danger:active:focus, .btn-danger:active.focus, .btn-danger.active:hover, .btn-danger.active:focus, .btn-danger.active.focus, .open > .btn-danger.dropdown-toggle:hover, .open > .btn-danger.dropdown-toggle:focus, .open > .btn-danger.dropdown-toggle.focus {
      color: #fff;
      background-color: #ac2925;
      border-color: #761c19; }
  .btn-danger:active, .btn-danger.active, .open > .btn-danger.dropdown-toggle {
    background-image: none; }
  .btn-danger.disabled:hover, .btn-danger.disabled:focus, .btn-danger.disabled.focus, .btn-danger[disabled]:hover, .btn-danger[disabled]:focus, .btn-danger[disabled].focus, fieldset[disabled] .btn-danger:hover, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .btn-danger.focus {
    background-color: #d9534f;
    border-color: #d43f3a; }
  .btn-danger .badge {
    color: #d9534f;
    background-color: #fff; }

.btn-link {
  color: #337ab7;
  font-weight: normal;
  border-radius: 0; }
  .btn-link, .btn-link:active, .btn-link.active, .btn-link[disabled], fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none; }
  .btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active {
    border-color: transparent; }
  .btn-link:hover, .btn-link:focus {
    color: #23527c;
    text-decoration: underline;
    background-color: transparent; }
  .btn-link[disabled]:hover, .btn-link[disabled]:focus, fieldset[disabled] .btn-link:hover, fieldset[disabled] .btn-link:focus {
    color: #777777;
    text-decoration: none; }

.btn-lg, .btn-group-lg > .btn {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px; }

.btn-sm, .btn-group-sm > .btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }

.btn-xs, .btn-group-xs > .btn {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px; }

.btn-block {
  display: block;
  width: 100%; }

.btn-block + .btn-block {
  margin-top: 5px; }

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%; }

.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear; }
  .fade.in {
    opacity: 1; }

.collapse {
  display: none; }
  .collapse.in {
    display: block; }

tr.collapse.in {
  display: table-row; }

tbody.collapse.in {
  display: table-row-group; }

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -webkit-transition-duration: 0.35s;
  transition-duration: 0.35s;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease; }

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid \9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent; }

.dropup,
.dropdown {
  position: relative; }

.dropdown-toggle:focus {
  outline: 0; }

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 14px;
  text-align: left;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box; }
  .dropdown-menu.pull-right {
    right: 0;
    left: auto; }
  .dropdown-menu .divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5; }
  .dropdown-menu > li > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.428571429;
    color: #333333;
    white-space: nowrap; }

.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5; }

.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background-color: #337ab7; }

.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
  color: #777777; }
.dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed; }

.open > .dropdown-menu {
  display: block; }
.open > a {
  outline: 0; }

.dropdown-menu-right {
  left: auto;
  right: 0; }

.dropdown-menu-left {
  left: 0;
  right: auto; }

.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.428571429;
  color: #777777;
  white-space: nowrap; }

.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990; }

.pull-right > .dropdown-menu {
  right: 0;
  left: auto; }

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  border-top: 0;
  border-bottom: 4px dashed;
  border-bottom: 4px solid \9;
  content: ""; }
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px; }

@media (min-width: 768px) {
  .navbar-right .dropdown-menu {
    right: 0;
    left: auto; }
  .navbar-right .dropdown-menu-left {
    left: 0;
    right: auto; } }
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle; }
  .btn-group > .btn,
  .btn-group-vertical > .btn {
    position: relative;
    float: left; }
    .btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,
    .btn-group-vertical > .btn:hover,
    .btn-group-vertical > .btn:focus,
    .btn-group-vertical > .btn:active,
    .btn-group-vertical > .btn.active {
      z-index: 2; }

.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -1px; }

.btn-toolbar {
  margin-left: -5px; }
  .btn-toolbar:before, .btn-toolbar:after {
    content: " ";
    display: table; }
  .btn-toolbar:after {
    clear: both; }
  .btn-toolbar .btn,
  .btn-toolbar .btn-group,
  .btn-toolbar .input-group {
    float: left; }
  .btn-toolbar > .btn,
  .btn-toolbar > .btn-group,
  .btn-toolbar > .input-group {
    margin-left: 5px; }

.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0; }

.btn-group > .btn:first-child {
  margin-left: 0; }
  .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0; }

.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.btn-group > .btn-group {
  float: left; }

.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0; }

.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0; }

.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0; }

.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px; }

.btn-group > .btn-lg + .dropdown-toggle, .btn-group-lg.btn-group > .btn + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px; }

.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }
  .btn-group.open .dropdown-toggle.btn-link {
    -webkit-box-shadow: none;
    box-shadow: none; }

.btn .caret {
  margin-left: 0; }

.btn-lg .caret, .btn-group-lg > .btn .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0; }

.dropup .btn-lg .caret, .dropup .btn-group-lg > .btn .caret {
  border-width: 0 5px 5px; }

.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group,
.btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%; }
.btn-group-vertical > .btn-group:before, .btn-group-vertical > .btn-group:after {
  content: " ";
  display: table; }
.btn-group-vertical > .btn-group:after {
  clear: both; }
.btn-group-vertical > .btn-group > .btn {
  float: none; }
.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0; }

.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0; }
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px; }

.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0; }

.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate; }
  .btn-group-justified > .btn,
  .btn-group-justified > .btn-group {
    float: none;
    display: table-cell;
    width: 1%; }
  .btn-group-justified > .btn-group .btn {
    width: 100%; }
  .btn-group-justified > .btn-group .dropdown-menu {
    left: auto; }

[data-toggle="buttons"] > .btn input[type="radio"],
[data-toggle="buttons"] > .btn input[type="checkbox"],
[data-toggle="buttons"] > .btn-group > .btn input[type="radio"],
[data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none; }

.input-group {
  position: relative;
  display: table;
  border-collapse: separate; }
  .input-group[class*="col-"] {
    float: none;
    padding-left: 0;
    padding-right: 0; }
  .input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
    margin-bottom: 0; }
    .input-group .form-control:focus {
      z-index: 3; }

.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell; }
  .input-group-addon:not(:first-child):not(:last-child),
  .input-group-btn:not(:first-child):not(:last-child),
  .input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0; }

.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle; }

.input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  color: #555555;
  text-align: center;
  background-color: #eeeeee;
  border: 1px solid #ccc;
  border-radius: 4px; }
  .input-group-addon.input-sm,
  .input-group-sm > .input-group-addon,
  .input-group-sm > .input-group-btn > .input-group-addon.btn {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 3px; }
  .input-group-addon.input-lg,
  .input-group-lg > .input-group-addon,
  .input-group-lg > .input-group-btn > .input-group-addon.btn {
    padding: 10px 16px;
    font-size: 18px;
    border-radius: 6px; }
  .input-group-addon input[type="radio"],
  .input-group-addon input[type="checkbox"] {
    margin-top: 0; }

.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0; }

.input-group-addon:first-child {
  border-right: 0; }

.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group > .btn,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child),
.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.input-group-addon:last-child {
  border-left: 0; }

.input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap; }
  .input-group-btn > .btn {
    position: relative; }
    .input-group-btn > .btn + .btn {
      margin-left: -1px; }
    .input-group-btn > .btn:hover, .input-group-btn > .btn:focus, .input-group-btn > .btn:active {
      z-index: 2; }
  .input-group-btn:first-child > .btn,
  .input-group-btn:first-child > .btn-group {
    margin-right: -1px; }
  .input-group-btn:last-child > .btn,
  .input-group-btn:last-child > .btn-group {
    z-index: 2;
    margin-left: -1px; }

.nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none; }
  .nav:before, .nav:after {
    content: " ";
    display: table; }
  .nav:after {
    clear: both; }
  .nav > li {
    position: relative;
    display: block; }
    .nav > li > a {
      position: relative;
      display: block;
      padding: 10px 15px; }
      .nav > li > a:hover, .nav > li > a:focus {
        text-decoration: none;
        background-color: #eeeeee; }
    .nav > li.disabled > a {
      color: #777777; }
      .nav > li.disabled > a:hover, .nav > li.disabled > a:focus {
        color: #777777;
        text-decoration: none;
        background-color: transparent;
        cursor: not-allowed; }
  .nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
    background-color: #eeeeee;
    border-color: #337ab7; }
  .nav .nav-divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5; }
  .nav > li > a > img {
    max-width: none; }

.nav-tabs {
  border-bottom: 1px solid #ddd; }
  .nav-tabs > li {
    float: left;
    margin-bottom: -1px; }
    .nav-tabs > li > a {
      margin-right: 2px;
      line-height: 1.428571429;
      border: 1px solid transparent;
      border-radius: 4px 4px 0 0; }
      .nav-tabs > li > a:hover {
        border-color: #eeeeee #eeeeee #ddd; }
    .nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
      color: #555555;
      background-color: #fff;
      border: 1px solid #ddd;
      border-bottom-color: transparent;
      cursor: default; }

.nav-pills > li {
  float: left; }
  .nav-pills > li > a {
    border-radius: 4px; }
  .nav-pills > li + li {
    margin-left: 2px; }
  .nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
    color: #fff;
    background-color: #337ab7; }

.nav-stacked > li {
  float: none; }
  .nav-stacked > li + li {
    margin-top: 2px;
    margin-left: 0; }

.nav-justified, .nav-tabs.nav-justified {
  width: 100%; }
  .nav-justified > li, .nav-tabs.nav-justified > li {
    float: none; }
    .nav-justified > li > a, .nav-tabs.nav-justified > li > a {
      text-align: center;
      margin-bottom: 5px; }
  .nav-justified > .dropdown .dropdown-menu {
    top: auto;
    left: auto; }
  @media (min-width: 768px) {
    .nav-justified > li, .nav-tabs.nav-justified > li {
      display: table-cell;
      width: 1%; }
      .nav-justified > li > a, .nav-tabs.nav-justified > li > a {
        margin-bottom: 0; } }

.nav-tabs-justified, .nav-tabs.nav-justified {
  border-bottom: 0; }
  .nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {
    margin-right: 0;
    border-radius: 4px; }
  .nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a,
  .nav-tabs-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs-justified > .active > a:focus,
  .nav-tabs.nav-justified > .active > a:focus {
    border: 1px solid #ddd; }
  @media (min-width: 768px) {
    .nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {
      border-bottom: 1px solid #ddd;
      border-radius: 4px 4px 0 0; }
    .nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a,
    .nav-tabs-justified > .active > a:hover,
    .nav-tabs.nav-justified > .active > a:hover,
    .nav-tabs-justified > .active > a:focus,
    .nav-tabs.nav-justified > .active > a:focus {
      border-bottom-color: #fff; } }

.tab-content > .tab-pane {
  display: none; }
.tab-content > .active {
  display: block; }

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.navbar {
  position: relative;
  min-height: 50px;
  margin-bottom: 20px;
  border: 1px solid transparent; }
  .navbar:before, .navbar:after {
    content: " ";
    display: table; }
  .navbar:after {
    clear: both; }
  @media (min-width: 768px) {
    .navbar {
      border-radius: 4px; } }

.navbar-header:before, .navbar-header:after {
  content: " ";
  display: table; }
.navbar-header:after {
  clear: both; }
@media (min-width: 768px) {
  .navbar-header {
    float: left; } }

.navbar-collapse {
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch; }
  .navbar-collapse:before, .navbar-collapse:after {
    content: " ";
    display: table; }
  .navbar-collapse:after {
    clear: both; }
  .navbar-collapse.in {
    overflow-y: auto; }
  @media (min-width: 768px) {
    .navbar-collapse {
      width: auto;
      border-top: 0;
      box-shadow: none; }
      .navbar-collapse.collapse {
        display: block !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important; }
      .navbar-collapse.in {
        overflow-y: visible; }
      .navbar-fixed-top .navbar-collapse, .navbar-static-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
        padding-left: 0;
        padding-right: 0; } }

.navbar-fixed-top .navbar-collapse,
.navbar-fixed-bottom .navbar-collapse {
  max-height: 340px; }
  @media (max-device-width: 480px) and (orientation: landscape) {
    .navbar-fixed-top .navbar-collapse,
    .navbar-fixed-bottom .navbar-collapse {
      max-height: 200px; } }

.container > .navbar-header,
.container > .navbar-collapse,
.container-fluid > .navbar-header,
.container-fluid > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px; }
  @media (min-width: 768px) {
    .container > .navbar-header,
    .container > .navbar-collapse,
    .container-fluid > .navbar-header,
    .container-fluid > .navbar-collapse {
      margin-right: 0;
      margin-left: 0; } }

.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px; }
  @media (min-width: 768px) {
    .navbar-static-top {
      border-radius: 0; } }

.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030; }
  @media (min-width: 768px) {
    .navbar-fixed-top,
    .navbar-fixed-bottom {
      border-radius: 0; } }

.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px; }

.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0; }

.navbar-brand {
  float: left;
  padding: 15px 15px;
  font-size: 18px;
  line-height: 20px;
  height: 50px; }
  .navbar-brand:hover, .navbar-brand:focus {
    text-decoration: none; }
  .navbar-brand > img {
    display: block; }
  @media (min-width: 768px) {
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand {
      margin-left: -15px; } }

.navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px; }
  .navbar-toggle:focus {
    outline: 0; }
  .navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px; }
  .navbar-toggle .icon-bar + .icon-bar {
    margin-top: 4px; }
  @media (min-width: 768px) {
    .navbar-toggle {
      display: none; } }

.navbar-nav {
  margin: 7.5px -15px; }
  .navbar-nav > li > a {
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 20px; }
  @media (max-width: 767px) {
    .navbar-nav .open .dropdown-menu {
      position: static;
      float: none;
      width: auto;
      margin-top: 0;
      background-color: transparent;
      border: 0;
      box-shadow: none; }
      .navbar-nav .open .dropdown-menu > li > a,
      .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 5px 15px 5px 25px; }
      .navbar-nav .open .dropdown-menu > li > a {
        line-height: 20px; }
        .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-nav .open .dropdown-menu > li > a:focus {
          background-image: none; } }
  @media (min-width: 768px) {
    .navbar-nav {
      float: left;
      margin: 0; }
      .navbar-nav > li {
        float: left; }
        .navbar-nav > li > a {
          padding-top: 15px;
          padding-bottom: 15px; } }

.navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 8px;
  margin-bottom: 8px; }
  @media (min-width: 768px) {
    .navbar-form .form-group {
      display: inline-block;
      margin-bottom: 0;
      vertical-align: middle; }
    .navbar-form .form-control {
      display: inline-block;
      width: auto;
      vertical-align: middle; }
    .navbar-form .form-control-static {
      display: inline-block; }
    .navbar-form .input-group {
      display: inline-table;
      vertical-align: middle; }
      .navbar-form .input-group .input-group-addon,
      .navbar-form .input-group .input-group-btn,
      .navbar-form .input-group .form-control {
        width: auto; }
    .navbar-form .input-group > .form-control {
      width: 100%; }
    .navbar-form .control-label {
      margin-bottom: 0;
      vertical-align: middle; }
    .navbar-form .radio,
    .navbar-form .checkbox {
      display: inline-block;
      margin-top: 0;
      margin-bottom: 0;
      vertical-align: middle; }
      .navbar-form .radio label,
      .navbar-form .checkbox label {
        padding-left: 0; }
    .navbar-form .radio input[type="radio"],
    .navbar-form .checkbox input[type="checkbox"] {
      position: relative;
      margin-left: 0; }
    .navbar-form .has-feedback .form-control-feedback {
      top: 0; } }
  @media (max-width: 767px) {
    .navbar-form .form-group {
      margin-bottom: 5px; }
      .navbar-form .form-group:last-child {
        margin-bottom: 0; } }
  @media (min-width: 768px) {
    .navbar-form {
      width: auto;
      border: 0;
      margin-left: 0;
      margin-right: 0;
      padding-top: 0;
      padding-bottom: 0;
      -webkit-box-shadow: none;
      box-shadow: none; } }

.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  margin-bottom: 0;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.navbar-btn {
  margin-top: 8px;
  margin-bottom: 8px; }
  .navbar-btn.btn-sm, .btn-group-sm > .navbar-btn.btn {
    margin-top: 10px;
    margin-bottom: 10px; }
  .navbar-btn.btn-xs, .btn-group-xs > .navbar-btn.btn {
    margin-top: 14px;
    margin-bottom: 14px; }

.navbar-text {
  margin-top: 15px;
  margin-bottom: 15px; }
  @media (min-width: 768px) {
    .navbar-text {
      float: left;
      margin-left: 15px;
      margin-right: 15px; } }

@media (min-width: 768px) {
  .navbar-left {
    float: left !important; }

  .navbar-right {
    float: right !important;
    margin-right: -15px; }
    .navbar-right ~ .navbar-right {
      margin-right: 0; } }
.navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7; }
  .navbar-default .navbar-brand {
    color: #777; }
    .navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
      color: #5e5e5e;
      background-color: transparent; }
  .navbar-default .navbar-text {
    color: #777; }
  .navbar-default .navbar-nav > li > a {
    color: #777; }
    .navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
      color: #333;
      background-color: transparent; }
  .navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
    color: #555;
    background-color: #e7e7e7; }
  .navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:hover, .navbar-default .navbar-nav > .disabled > a:focus {
    color: #ccc;
    background-color: transparent; }
  .navbar-default .navbar-toggle {
    border-color: #ddd; }
    .navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
      background-color: #ddd; }
    .navbar-default .navbar-toggle .icon-bar {
      background-color: #888; }
  .navbar-default .navbar-collapse,
  .navbar-default .navbar-form {
    border-color: #e7e7e7; }
  .navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
    background-color: #e7e7e7;
    color: #555; }
  @media (max-width: 767px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
      color: #777; }
      .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #333;
        background-color: transparent; }
    .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
      color: #555;
      background-color: #e7e7e7; }
    .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
      color: #ccc;
      background-color: transparent; } }
  .navbar-default .navbar-link {
    color: #777; }
    .navbar-default .navbar-link:hover {
      color: #333; }
  .navbar-default .btn-link {
    color: #777; }
    .navbar-default .btn-link:hover, .navbar-default .btn-link:focus {
      color: #333; }
    .navbar-default .btn-link[disabled]:hover, .navbar-default .btn-link[disabled]:focus, fieldset[disabled] .navbar-default .btn-link:hover, fieldset[disabled] .navbar-default .btn-link:focus {
      color: #ccc; }

.navbar-inverse {
  background-color: #222;
  border-color: #090909; }
  .navbar-inverse .navbar-brand {
    color: #9d9d9d; }
    .navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {
      color: #fff;
      background-color: transparent; }
  .navbar-inverse .navbar-text {
    color: #9d9d9d; }
  .navbar-inverse .navbar-nav > li > a {
    color: #9d9d9d; }
    .navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus {
      color: #fff;
      background-color: transparent; }
  .navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
    color: #fff;
    background-color: #090909; }
  .navbar-inverse .navbar-nav > .disabled > a, .navbar-inverse .navbar-nav > .disabled > a:hover, .navbar-inverse .navbar-nav > .disabled > a:focus {
    color: #444;
    background-color: transparent; }
  .navbar-inverse .navbar-toggle {
    border-color: #333; }
    .navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus {
      background-color: #333; }
    .navbar-inverse .navbar-toggle .icon-bar {
      background-color: #fff; }
  .navbar-inverse .navbar-collapse,
  .navbar-inverse .navbar-form {
    border-color: #101010; }
  .navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
    background-color: #090909;
    color: #fff; }
  @media (max-width: 767px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
      border-color: #090909; }
    .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
      background-color: #090909; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
      color: #9d9d9d; }
      .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #fff;
        background-color: transparent; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
      color: #fff;
      background-color: #090909; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
      color: #444;
      background-color: transparent; } }
  .navbar-inverse .navbar-link {
    color: #9d9d9d; }
    .navbar-inverse .navbar-link:hover {
      color: #fff; }
  .navbar-inverse .btn-link {
    color: #9d9d9d; }
    .navbar-inverse .btn-link:hover, .navbar-inverse .btn-link:focus {
      color: #fff; }
    .navbar-inverse .btn-link[disabled]:hover, .navbar-inverse .btn-link[disabled]:focus, fieldset[disabled] .navbar-inverse .btn-link:hover, fieldset[disabled] .navbar-inverse .btn-link:focus {
      color: #444; }

.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 4px; }
  .breadcrumb > li {
    display: inline-block; }
    .breadcrumb > li + li:before {
      content: "/ ";
      padding: 0 5px;
      color: #ccc; }
  .breadcrumb > .active {
    color: #777777; }

.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px; }
  .pagination > li {
    display: inline; }
    .pagination > li > a,
    .pagination > li > span {
      position: relative;
      float: left;
      padding: 6px 12px;
      line-height: 1.428571429;
      text-decoration: none;
      color: #337ab7;
      background-color: #fff;
      border: 1px solid #ddd;
      margin-left: -1px; }
    .pagination > li:first-child > a,
    .pagination > li:first-child > span {
      margin-left: 0;
      border-bottom-left-radius: 4px;
      border-top-left-radius: 4px; }
    .pagination > li:last-child > a,
    .pagination > li:last-child > span {
      border-bottom-right-radius: 4px;
      border-top-right-radius: 4px; }
  .pagination > li > a:hover, .pagination > li > a:focus,
  .pagination > li > span:hover,
  .pagination > li > span:focus {
    z-index: 2;
    color: #23527c;
    background-color: #eeeeee;
    border-color: #ddd; }
  .pagination > .active > a, .pagination > .active > a:hover, .pagination > .active > a:focus,
  .pagination > .active > span,
  .pagination > .active > span:hover,
  .pagination > .active > span:focus {
    z-index: 3;
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
    cursor: default; }
  .pagination > .disabled > span,
  .pagination > .disabled > span:hover,
  .pagination > .disabled > span:focus,
  .pagination > .disabled > a,
  .pagination > .disabled > a:hover,
  .pagination > .disabled > a:focus {
    color: #777777;
    background-color: #fff;
    border-color: #ddd;
    cursor: not-allowed; }

.pagination-lg > li > a,
.pagination-lg > li > span {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333; }
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px; }
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px; }

.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5; }
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px; }
.pagination-sm > li:last-child > a,
.pagination-sm > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px; }

.pager {
  padding-left: 0;
  margin: 20px 0;
  list-style: none;
  text-align: center; }
  .pager:before, .pager:after {
    content: " ";
    display: table; }
  .pager:after {
    clear: both; }
  .pager li {
    display: inline; }
    .pager li > a,
    .pager li > span {
      display: inline-block;
      padding: 5px 14px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 15px; }
    .pager li > a:hover,
    .pager li > a:focus {
      text-decoration: none;
      background-color: #eeeeee; }
  .pager .next > a,
  .pager .next > span {
    float: right; }
  .pager .previous > a,
  .pager .previous > span {
    float: left; }
  .pager .disabled > a,
  .pager .disabled > a:hover,
  .pager .disabled > a:focus,
  .pager .disabled > span {
    color: #777777;
    background-color: #fff;
    cursor: not-allowed; }

.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em; }
  .label:empty {
    display: none; }
  .btn .label {
    position: relative;
    top: -1px; }

a.label:hover, a.label:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer; }

.label-default {
  background-color: #777777; }
  .label-default[href]:hover, .label-default[href]:focus {
    background-color: #5e5e5e; }

.label-primary {
  background-color: #337ab7; }
  .label-primary[href]:hover, .label-primary[href]:focus {
    background-color: #286090; }

.label-success {
  background-color: #5cb85c; }
  .label-success[href]:hover, .label-success[href]:focus {
    background-color: #449d44; }

.label-info {
  background-color: #5bc0de; }
  .label-info[href]:hover, .label-info[href]:focus {
    background-color: #31b0d5; }

.label-warning {
  background-color: #f0ad4e; }
  .label-warning[href]:hover, .label-warning[href]:focus {
    background-color: #ec971f; }

.label-danger {
  background-color: #d9534f; }
  .label-danger[href]:hover, .label-danger[href]:focus {
    background-color: #c9302c; }

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  vertical-align: middle;
  white-space: nowrap;
  text-align: center;
  background-color: #777777;
  border-radius: 10px; }
  .badge:empty {
    display: none; }
  .btn .badge {
    position: relative;
    top: -1px; }
  .btn-xs .badge, .btn-group-xs > .btn .badge, .btn-group-xs > .btn .badge {
    top: 0;
    padding: 1px 5px; }
  .list-group-item.active > .badge, .nav-pills > .active > a > .badge {
    color: #337ab7;
    background-color: #fff; }
  .list-group-item > .badge {
    float: right; }
  .list-group-item > .badge + .badge {
    margin-right: 5px; }
  .nav-pills > li > a > .badge {
    margin-left: 3px; }

a.badge:hover, a.badge:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer; }

.jumbotron {
  padding-top: 30px;
  padding-bottom: 30px;
  margin-bottom: 30px;
  color: inherit;
  background-color: #eeeeee; }
  .jumbotron h1,
  .jumbotron .h1 {
    color: inherit; }
  .jumbotron p {
    margin-bottom: 15px;
    font-size: 21px;
    font-weight: 200; }
  .jumbotron > hr {
    border-top-color: #d5d5d5; }
  .container .jumbotron, .container-fluid .jumbotron {
    border-radius: 6px;
    padding-left: 15px;
    padding-right: 15px; }
  .jumbotron .container {
    max-width: 100%; }
  @media screen and (min-width: 768px) {
    .jumbotron {
      padding-top: 48px;
      padding-bottom: 48px; }
      .container .jumbotron, .container-fluid .jumbotron {
        padding-left: 60px;
        padding-right: 60px; }
      .jumbotron h1,
      .jumbotron .h1 {
        font-size: 63px; } }

.thumbnail {
  display: block;
  padding: 4px;
  margin-bottom: 20px;
  line-height: 1.428571429;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: border 0.2s ease-in-out;
  -o-transition: border 0.2s ease-in-out;
  transition: border 0.2s ease-in-out; }
  .thumbnail > img,
  .thumbnail a > img {
    display: block;
    max-width: 100%;
    height: auto;
    margin-left: auto;
    margin-right: auto; }
  .thumbnail .caption {
    padding: 9px;
    color: #ffffff; }

a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
  border-color: #337ab7; }

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px; }
  .alert h4 {
    margin-top: 0;
    color: inherit; }
  .alert .alert-link {
    font-weight: bold; }
  .alert > p,
  .alert > ul {
    margin-bottom: 0; }
  .alert > p + p {
    margin-top: 5px; }

.alert-dismissable,
.alert-dismissible {
  padding-right: 35px; }
  .alert-dismissable .close,
  .alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit; }

.alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
  color: #3c763d; }
  .alert-success hr {
    border-top-color: #c9e2b3; }
  .alert-success .alert-link {
    color: #2b542c; }

.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #31708f; }
  .alert-info hr {
    border-top-color: #a6e1ec; }
  .alert-info .alert-link {
    color: #245269; }

.alert-warning {
  background-color: #fcf8e3;
  border-color: #faebcc;
  color: #8a6d3b; }
  .alert-warning hr {
    border-top-color: #f7e1b5; }
  .alert-warning .alert-link {
    color: #66512c; }

.alert-danger {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #a94442; }
  .alert-danger hr {
    border-top-color: #e4b9c0; }
  .alert-danger .alert-link {
    color: #843534; }

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0; }
  to {
    background-position: 0 0; } }
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0; }
  to {
    background-position: 0 0; } }
.progress {
  overflow: hidden;
  height: 20px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1); }

.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  background-color: #337ab7;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  transition: width 0.6s ease; }

.progress-striped .progress-bar,
.progress-bar-striped {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px; }

.progress.active .progress-bar,
.progress-bar.active {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  -o-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite; }

.progress-bar-success {
  background-color: #5cb85c; }
  .progress-striped .progress-bar-success {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); }

.progress-bar-info {
  background-color: #5bc0de; }
  .progress-striped .progress-bar-info {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); }

.progress-bar-warning {
  background-color: #f0ad4e; }
  .progress-striped .progress-bar-warning {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); }

.progress-bar-danger {
  background-color: #d9534f; }
  .progress-striped .progress-bar-danger {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); }

.media {
  margin-top: 15px; }
  .media:first-child {
    margin-top: 0; }

.media,
.media-body {
  zoom: 1;
  overflow: hidden; }

.media-body {
  width: 10000px; }

.media-object {
  display: block; }
  .media-object.img-thumbnail {
    max-width: none; }

.media-right,
.media > .pull-right {
  padding-left: 10px; }

.media-left,
.media > .pull-left {
  padding-right: 10px; }

.media-left,
.media-right,
.media-body {
  display: table-cell;
  vertical-align: top; }

.media-middle {
  vertical-align: middle; }

.media-bottom {
  vertical-align: bottom; }

.media-heading {
  margin-top: 0;
  margin-bottom: 5px; }

.media-list {
  padding-left: 0;
  list-style: none; }

.list-group {
  margin-bottom: 20px;
  padding-left: 0; }

.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd; }
  .list-group-item:first-child {
    border-top-right-radius: 4px;
    border-top-left-radius: 4px; }
  .list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px; }

a.list-group-item,
button.list-group-item {
  color: #555; }
  a.list-group-item .list-group-item-heading,
  button.list-group-item .list-group-item-heading {
    color: #333; }
  a.list-group-item:hover, a.list-group-item:focus,
  button.list-group-item:hover,
  button.list-group-item:focus {
    text-decoration: none;
    color: #555;
    background-color: #f5f5f5; }

button.list-group-item {
  width: 100%;
  text-align: left; }

.list-group-item.disabled, .list-group-item.disabled:hover, .list-group-item.disabled:focus {
  background-color: #eeeeee;
  color: #777777;
  cursor: not-allowed; }
  .list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading {
    color: inherit; }
  .list-group-item.disabled .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text {
    color: #777777; }
.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {
  z-index: 2;
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7; }
  .list-group-item.active .list-group-item-heading,
  .list-group-item.active .list-group-item-heading > small,
  .list-group-item.active .list-group-item-heading > .small, .list-group-item.active:hover .list-group-item-heading,
  .list-group-item.active:hover .list-group-item-heading > small,
  .list-group-item.active:hover .list-group-item-heading > .small, .list-group-item.active:focus .list-group-item-heading,
  .list-group-item.active:focus .list-group-item-heading > small,
  .list-group-item.active:focus .list-group-item-heading > .small {
    color: inherit; }
  .list-group-item.active .list-group-item-text, .list-group-item.active:hover .list-group-item-text, .list-group-item.active:focus .list-group-item-text {
    color: #c7ddef; }

.list-group-item-success {
  color: #3c763d;
  background-color: #dff0d8; }

a.list-group-item-success,
button.list-group-item-success {
  color: #3c763d; }
  a.list-group-item-success .list-group-item-heading,
  button.list-group-item-success .list-group-item-heading {
    color: inherit; }
  a.list-group-item-success:hover, a.list-group-item-success:focus,
  button.list-group-item-success:hover,
  button.list-group-item-success:focus {
    color: #3c763d;
    background-color: #d0e9c6; }
  a.list-group-item-success.active, a.list-group-item-success.active:hover, a.list-group-item-success.active:focus,
  button.list-group-item-success.active,
  button.list-group-item-success.active:hover,
  button.list-group-item-success.active:focus {
    color: #fff;
    background-color: #3c763d;
    border-color: #3c763d; }

.list-group-item-info {
  color: #31708f;
  background-color: #d9edf7; }

a.list-group-item-info,
button.list-group-item-info {
  color: #31708f; }
  a.list-group-item-info .list-group-item-heading,
  button.list-group-item-info .list-group-item-heading {
    color: inherit; }
  a.list-group-item-info:hover, a.list-group-item-info:focus,
  button.list-group-item-info:hover,
  button.list-group-item-info:focus {
    color: #31708f;
    background-color: #c4e3f3; }
  a.list-group-item-info.active, a.list-group-item-info.active:hover, a.list-group-item-info.active:focus,
  button.list-group-item-info.active,
  button.list-group-item-info.active:hover,
  button.list-group-item-info.active:focus {
    color: #fff;
    background-color: #31708f;
    border-color: #31708f; }

.list-group-item-warning {
  color: #8a6d3b;
  background-color: #fcf8e3; }

a.list-group-item-warning,
button.list-group-item-warning {
  color: #8a6d3b; }
  a.list-group-item-warning .list-group-item-heading,
  button.list-group-item-warning .list-group-item-heading {
    color: inherit; }
  a.list-group-item-warning:hover, a.list-group-item-warning:focus,
  button.list-group-item-warning:hover,
  button.list-group-item-warning:focus {
    color: #8a6d3b;
    background-color: #faf2cc; }
  a.list-group-item-warning.active, a.list-group-item-warning.active:hover, a.list-group-item-warning.active:focus,
  button.list-group-item-warning.active,
  button.list-group-item-warning.active:hover,
  button.list-group-item-warning.active:focus {
    color: #fff;
    background-color: #8a6d3b;
    border-color: #8a6d3b; }

.list-group-item-danger {
  color: #a94442;
  background-color: #f2dede; }

a.list-group-item-danger,
button.list-group-item-danger {
  color: #a94442; }
  a.list-group-item-danger .list-group-item-heading,
  button.list-group-item-danger .list-group-item-heading {
    color: inherit; }
  a.list-group-item-danger:hover, a.list-group-item-danger:focus,
  button.list-group-item-danger:hover,
  button.list-group-item-danger:focus {
    color: #a94442;
    background-color: #ebcccc; }
  a.list-group-item-danger.active, a.list-group-item-danger.active:hover, a.list-group-item-danger.active:focus,
  button.list-group-item-danger.active,
  button.list-group-item-danger.active:hover,
  button.list-group-item-danger.active:focus {
    color: #fff;
    background-color: #a94442;
    border-color: #a94442; }

.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px; }

.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3; }

.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05); }

.panel-body {
  padding: 15px; }
  .panel-body:before, .panel-body:after {
    content: " ";
    display: table; }
  .panel-body:after {
    clear: both; }

.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px; }
  .panel-heading > .dropdown .dropdown-toggle {
    color: inherit; }

.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px;
  color: inherit; }
  .panel-title > a,
  .panel-title > small,
  .panel-title > .small,
  .panel-title > small > a,
  .panel-title > .small > a {
    color: inherit; }

.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px; }

.panel > .list-group,
.panel > .panel-collapse > .list-group {
  margin-bottom: 0; }
  .panel > .list-group .list-group-item,
  .panel > .panel-collapse > .list-group .list-group-item {
    border-width: 1px 0;
    border-radius: 0; }
  .panel > .list-group:first-child .list-group-item:first-child,
  .panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {
    border-top: 0;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px; }
  .panel > .list-group:last-child .list-group-item:last-child,
  .panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
    border-bottom: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px; }
.panel > .panel-heading + .panel-collapse > .list-group .list-group-item:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0; }

.list-group + .panel-footer {
  border-top-width: 0; }

.panel > .table,
.panel > .table-responsive > .table,
.panel > .panel-collapse > .table {
  margin-bottom: 0; }
  .panel > .table caption,
  .panel > .table-responsive > .table caption,
  .panel > .panel-collapse > .table caption {
    padding-left: 15px;
    padding-right: 15px; }
.panel > .table:first-child,
.panel > .table-responsive:first-child > .table:first-child {
  border-top-right-radius: 3px;
  border-top-left-radius: 3px; }
  .panel > .table:first-child > thead:first-child > tr:first-child,
  .panel > .table:first-child > tbody:first-child > tr:first-child,
  .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child,
  .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {
    border-top-left-radius: 3px;
    border-top-right-radius: 3px; }
    .panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
    .panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
    .panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
    .panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
    .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
    .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
    .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
    .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
      border-top-left-radius: 3px; }
    .panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
    .panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
    .panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
    .panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
    .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
    .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
    .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
    .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
      border-top-right-radius: 3px; }
.panel > .table:last-child,
.panel > .table-responsive:last-child > .table:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px; }
  .panel > .table:last-child > tbody:last-child > tr:last-child,
  .panel > .table:last-child > tfoot:last-child > tr:last-child,
  .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child,
  .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px; }
    .panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
    .panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
    .panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
    .panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
    .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
    .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
    .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
    .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
      border-bottom-left-radius: 3px; }
    .panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
    .panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
    .panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
    .panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
    .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
    .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
    .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
    .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
      border-bottom-right-radius: 3px; }
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive,
.panel > .table + .panel-body,
.panel > .table-responsive + .panel-body {
  border-top: 1px solid #ddd; }
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0; }
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
  border: 0; }
  .panel > .table-bordered > thead > tr > th:first-child,
  .panel > .table-bordered > thead > tr > td:first-child,
  .panel > .table-bordered > tbody > tr > th:first-child,
  .panel > .table-bordered > tbody > tr > td:first-child,
  .panel > .table-bordered > tfoot > tr > th:first-child,
  .panel > .table-bordered > tfoot > tr > td:first-child,
  .panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
  .panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
  .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0; }
  .panel > .table-bordered > thead > tr > th:last-child,
  .panel > .table-bordered > thead > tr > td:last-child,
  .panel > .table-bordered > tbody > tr > th:last-child,
  .panel > .table-bordered > tbody > tr > td:last-child,
  .panel > .table-bordered > tfoot > tr > th:last-child,
  .panel > .table-bordered > tfoot > tr > td:last-child,
  .panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
  .panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
  .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0; }
  .panel > .table-bordered > thead > tr:first-child > td,
  .panel > .table-bordered > thead > tr:first-child > th,
  .panel > .table-bordered > tbody > tr:first-child > td,
  .panel > .table-bordered > tbody > tr:first-child > th,
  .panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
  .panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
  .panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
  .panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
    border-bottom: 0; }
  .panel > .table-bordered > tbody > tr:last-child > td,
  .panel > .table-bordered > tbody > tr:last-child > th,
  .panel > .table-bordered > tfoot > tr:last-child > td,
  .panel > .table-bordered > tfoot > tr:last-child > th,
  .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
  .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
    border-bottom: 0; }
.panel > .table-responsive {
  border: 0;
  margin-bottom: 0; }

.panel-group {
  margin-bottom: 20px; }
  .panel-group .panel {
    margin-bottom: 0;
    border-radius: 4px; }
    .panel-group .panel + .panel {
      margin-top: 5px; }
  .panel-group .panel-heading {
    border-bottom: 0; }
    .panel-group .panel-heading + .panel-collapse > .panel-body,
    .panel-group .panel-heading + .panel-collapse > .list-group {
      border-top: 1px solid #ddd; }
  .panel-group .panel-footer {
    border-top: 0; }
    .panel-group .panel-footer + .panel-collapse .panel-body {
      border-bottom: 1px solid #ddd; }

.panel-default {
  border-color: #ddd; }
  .panel-default > .panel-heading {
    color: #333333;
    background-color: #f5f5f5;
    border-color: #ddd; }
    .panel-default > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ddd; }
    .panel-default > .panel-heading .badge {
      color: #f5f5f5;
      background-color: #333333; }
  .panel-default > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ddd; }

.panel-primary {
  border-color: #337ab7; }
  .panel-primary > .panel-heading {
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7; }
    .panel-primary > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #337ab7; }
    .panel-primary > .panel-heading .badge {
      color: #337ab7;
      background-color: #fff; }
  .panel-primary > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #337ab7; }

.panel-success {
  border-color: #d6e9c6; }
  .panel-success > .panel-heading {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6; }
    .panel-success > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #d6e9c6; }
    .panel-success > .panel-heading .badge {
      color: #dff0d8;
      background-color: #3c763d; }
  .panel-success > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #d6e9c6; }

.panel-info {
  border-color: #bce8f1; }
  .panel-info > .panel-heading {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1; }
    .panel-info > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #bce8f1; }
    .panel-info > .panel-heading .badge {
      color: #d9edf7;
      background-color: #31708f; }
  .panel-info > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #bce8f1; }

.panel-warning {
  border-color: #faebcc; }
  .panel-warning > .panel-heading {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc; }
    .panel-warning > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #faebcc; }
    .panel-warning > .panel-heading .badge {
      color: #fcf8e3;
      background-color: #8a6d3b; }
  .panel-warning > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #faebcc; }

.panel-danger {
  border-color: #ebccd1; }
  .panel-danger > .panel-heading {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1; }
    .panel-danger > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ebccd1; }
    .panel-danger > .panel-heading .badge {
      color: #f2dede;
      background-color: #a94442; }
  .panel-danger > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ebccd1; }

.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden; }
  .embed-responsive .embed-responsive-item,
  .embed-responsive iframe,
  .embed-responsive embed,
  .embed-responsive object,
  .embed-responsive video {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    border: 0; }

.embed-responsive-16by9 {
  padding-bottom: 56.25%; }

.embed-responsive-4by3 {
  padding-bottom: 75%; }

.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05); }
  .well blockquote {
    border-color: #ddd;
    border-color: rgba(0, 0, 0, 0.15); }

.well-lg {
  padding: 24px;
  border-radius: 6px; }

.well-sm {
  padding: 9px;
  border-radius: 3px; }

.close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
  filter: alpha(opacity=20); }
  .close:hover, .close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    opacity: 0.5;
    filter: alpha(opacity=50); }

button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none; }

.modal-open {
  overflow: hidden; }

.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  -webkit-overflow-scrolling: touch;
  outline: 0; }
  .modal.fade .modal-dialog {
    -webkit-transform: translate(0, -25%);
    -ms-transform: translate(0, -25%);
    -o-transform: translate(0, -25%);
    transform: translate(0, -25%);
    -webkit-transition: -webkit-transform 0.3s ease-out;
    -moz-transition: -moz-transform 0.3s ease-out;
    -o-transition: -o-transform 0.3s ease-out;
    transition: transform 0.3s ease-out; }
  .modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0); }

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto; }

.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px; }

.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0; }

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000; }
  .modal-backdrop.fade {
    opacity: 0;
    filter: alpha(opacity=0); }
  .modal-backdrop.in {
    opacity: 0.5;
    filter: alpha(opacity=50); }

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5; }
  .modal-header:before, .modal-header:after {
    content: " ";
    display: table; }
  .modal-header:after {
    clear: both; }

.modal-header .close {
  margin-top: -2px; }

.modal-title {
  margin: 0;
  line-height: 1.428571429; }

.modal-body {
  position: relative;
  padding: 15px; }

.modal-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5; }
  .modal-footer:before, .modal-footer:after {
    content: " ";
    display: table; }
  .modal-footer:after {
    clear: both; }
  .modal-footer .btn + .btn {
    margin-left: 5px;
    margin-bottom: 0; }
  .modal-footer .btn-group .btn + .btn {
    margin-left: -1px; }
  .modal-footer .btn-block + .btn-block {
    margin-left: 0; }

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll; }

@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto; }

  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5); }

  .modal-sm {
    width: 300px; } }
@media (min-width: 992px) {
  .modal-lg {
    width: 900px; } }
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.428571429;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 12px;
  opacity: 0;
  filter: alpha(opacity=0); }
  .tooltip.in {
    opacity: 0.9;
    filter: alpha(opacity=90); }
  .tooltip.top {
    margin-top: -3px;
    padding: 5px 0; }
  .tooltip.right {
    margin-left: 3px;
    padding: 0 5px; }
  .tooltip.bottom {
    margin-top: 3px;
    padding: 5px 0; }
  .tooltip.left {
    margin-left: -3px;
    padding: 0 5px; }

.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 4px; }

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid; }

.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  right: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  left: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000; }
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000; }
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  right: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  left: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: none;
  max-width: 276px;
  padding: 1px;
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.428571429;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 14px;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2); }
  .popover.top {
    margin-top: -10px; }
  .popover.right {
    margin-left: 10px; }
  .popover.bottom {
    margin-top: 10px; }
  .popover.left {
    margin-left: -10px; }

.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0; }

.popover-content {
  padding: 9px 14px; }

.popover > .arrow, .popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid; }

.popover > .arrow {
  border-width: 11px; }

.popover > .arrow:after {
  border-width: 10px;
  content: ""; }

.popover.top > .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px; }
  .popover.top > .arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -10px;
    border-bottom-width: 0;
    border-top-color: #fff; }
.popover.right > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25); }
  .popover.right > .arrow:after {
    content: " ";
    left: 1px;
    bottom: -10px;
    border-left-width: 0;
    border-right-color: #fff; }
.popover.bottom > .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px; }
  .popover.bottom > .arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #fff; }
.popover.left > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25); }
  .popover.left > .arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-color: #fff;
    bottom: -10px; }

.carousel {
  position: relative; }

.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%; }
  .carousel-inner > .item {
    display: none;
    position: relative;
    -webkit-transition: 0.6s ease-in-out left;
    -o-transition: 0.6s ease-in-out left;
    transition: 0.6s ease-in-out left; }
    .carousel-inner > .item > img,
    .carousel-inner > .item > a > img {
      display: block;
      max-width: 100%;
      height: auto;
      line-height: 1; }
    @media all and (transform-3d), (-webkit-transform-3d) {
      .carousel-inner > .item {
        -webkit-transition: -webkit-transform 0.6s ease-in-out;
        -moz-transition: -moz-transform 0.6s ease-in-out;
        -o-transition: -o-transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out;
        -webkit-backface-visibility: hidden;
        -moz-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000px;
        -moz-perspective: 1000px;
        perspective: 1000px; }
        .carousel-inner > .item.next, .carousel-inner > .item.active.right {
          -webkit-transform: translate3d(100%, 0, 0);
          transform: translate3d(100%, 0, 0);
          left: 0; }
        .carousel-inner > .item.prev, .carousel-inner > .item.active.left {
          -webkit-transform: translate3d(-100%, 0, 0);
          transform: translate3d(-100%, 0, 0);
          left: 0; }
        .carousel-inner > .item.next.left, .carousel-inner > .item.prev.right, .carousel-inner > .item.active {
          -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
          left: 0; } }
  .carousel-inner > .active,
  .carousel-inner > .next,
  .carousel-inner > .prev {
    display: block; }
  .carousel-inner > .active {
    left: 0; }
  .carousel-inner > .next,
  .carousel-inner > .prev {
    position: absolute;
    top: 0;
    width: 100%; }
  .carousel-inner > .next {
    left: 100%; }
  .carousel-inner > .prev {
    left: -100%; }
  .carousel-inner > .next.left,
  .carousel-inner > .prev.right {
    left: 0; }
  .carousel-inner > .active.left {
    left: -100%; }
  .carousel-inner > .active.right {
    left: 100%; }

.carousel-control {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 15%;
  opacity: 0.5;
  filter: alpha(opacity=50);
  font-size: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  background-color: rgba(0, 0, 0, 0); }
  .carousel-control.left {
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1); }
  .carousel-control.right {
    left: auto;
    right: 0;
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1); }
  .carousel-control:hover, .carousel-control:focus {
    outline: 0;
    color: #fff;
    text-decoration: none;
    opacity: 0.9;
    filter: alpha(opacity=90); }
  .carousel-control .icon-prev,
  .carousel-control .icon-next,
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right {
    position: absolute;
    top: 50%;
    margin-top: -10px;
    z-index: 5;
    display: inline-block; }
  .carousel-control .icon-prev,
  .carousel-control .glyphicon-chevron-left {
    left: 50%;
    margin-left: -10px; }
  .carousel-control .icon-next,
  .carousel-control .glyphicon-chevron-right {
    right: 50%;
    margin-right: -10px; }
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 20px;
    height: 20px;
    line-height: 1;
    font-family: serif; }
  .carousel-control .icon-prev:before {
    content: '\2039'; }
  .carousel-control .icon-next:before {
    content: '\203a'; }

.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  margin-left: -30%;
  padding-left: 0;
  list-style: none;
  text-align: center; }
  .carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    border: 1px solid #fff;
    border-radius: 10px;
    cursor: pointer;
    background-color: #000 \9;
    background-color: rgba(0, 0, 0, 0); }
  .carousel-indicators .active {
    margin: 0;
    width: 12px;
    height: 12px;
    background-color: #fff; }

.carousel-caption {
  position: absolute;
  left: 15%;
  right: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6); }
  .carousel-caption .btn {
    text-shadow: none; }

@media screen and (min-width: 768px) {
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -10px;
    font-size: 30px; }
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .icon-prev {
    margin-left: -10px; }
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-next {
    margin-right: -10px; }

  .carousel-caption {
    left: 20%;
    right: 20%;
    padding-bottom: 30px; }

  .carousel-indicators {
    bottom: 20px; } }
.clearfix:before, .clearfix:after {
  content: " ";
  display: table; }
.clearfix:after {
  clear: both; }

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto; }

.pull-right {
  float: right !important; }

.pull-left {
  float: left !important; }

.hide {
  display: none !important; }

.show {
  display: block !important; }

.invisible {
  visibility: hidden; }

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0; }

.hidden {
  display: none !important; }

.affix {
  position: fixed; }

@-ms-viewport {
  width: device-width; }
.visible-xs {
  display: none !important; }

.visible-sm {
  display: none !important; }

.visible-md {
  display: none !important; }

.visible-lg {
  display: none !important; }

.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block {
  display: none !important; }

@media (max-width: 767px) {
  .visible-xs {
    display: block !important; }

  table.visible-xs {
    display: table !important; }

  tr.visible-xs {
    display: table-row !important; }

  th.visible-xs,
  td.visible-xs {
    display: table-cell !important; } }
@media (max-width: 767px) {
  .visible-xs-block {
    display: block !important; } }

@media (max-width: 767px) {
  .visible-xs-inline {
    display: inline !important; } }

@media (max-width: 767px) {
  .visible-xs-inline-block {
    display: inline-block !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important; }

  table.visible-sm {
    display: table !important; }

  tr.visible-sm {
    display: table-row !important; }

  th.visible-sm,
  td.visible-sm {
    display: table-cell !important; } }
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-block {
    display: block !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline {
    display: inline !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline-block {
    display: inline-block !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important; }

  table.visible-md {
    display: table !important; }

  tr.visible-md {
    display: table-row !important; }

  th.visible-md,
  td.visible-md {
    display: table-cell !important; } }
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-block {
    display: block !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline {
    display: inline !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline-block {
    display: inline-block !important; } }

@media (min-width: 1200px) {
  .visible-lg {
    display: block !important; }

  table.visible-lg {
    display: table !important; }

  tr.visible-lg {
    display: table-row !important; }

  th.visible-lg,
  td.visible-lg {
    display: table-cell !important; } }
@media (min-width: 1200px) {
  .visible-lg-block {
    display: block !important; } }

@media (min-width: 1200px) {
  .visible-lg-inline {
    display: inline !important; } }

@media (min-width: 1200px) {
  .visible-lg-inline-block {
    display: inline-block !important; } }

@media (max-width: 767px) {
  .hidden-xs {
    display: none !important; } }
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important; } }
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md {
    display: none !important; } }
@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important; } }
.visible-print {
  display: none !important; }

@media print {
  .visible-print {
    display: block !important; }

  table.visible-print {
    display: table !important; }

  tr.visible-print {
    display: table-row !important; }

  th.visible-print,
  td.visible-print {
    display: table-cell !important; } }
.visible-print-block {
  display: none !important; }
  @media print {
    .visible-print-block {
      display: block !important; } }

.visible-print-inline {
  display: none !important; }
  @media print {
    .visible-print-inline {
      display: inline !important; } }

.visible-print-inline-block {
  display: none !important; }
  @media print {
    .visible-print-inline-block {
      display: inline-block !important; } }

@media print {
  .hidden-print {
    display: none !important; } }
.header-sky {
  position: fixed;
  z-index: 999;
  right: 0;
  left: 0;
  top: 0; }
  .header-sky .bg-menu {
    background: #232323;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    opacity: 0.9; }
  .header-sky.header-top-sky .header-top {
    margin-top: -90px;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out; }
  .header-sky.header-top-sky .navbar {
    padding-top: 0; }
    .header-sky.header-top-sky .navbar .navbar-nav li .submenu-hover1 .dropdown-margin li a {
      color: #FFF; }
    .header-sky.header-top-sky .navbar .navbar-nav .dropdown-menu {
      background: #232323;
      border: none; }
      .header-sky.header-top-sky .navbar .navbar-nav .dropdown-menu li a {
        color: #FFF; }
  .header-sky.header-top-sky .navbar {
    box-shadow: 0px 2px 3px #353333;
    background: #080303;
    opacity: 0.9; }
  .header-sky .header-top {
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    display: flow-root;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    line-height: 40px; }
    .header-sky .header-top.header-top-v3 {
      border: none; }
      .header-sky .header-top.header-top-v3 .logo-v3 {
        display: none; }
    .header-sky .header-top.no-border {
      border: none; }
    .header-sky .header-top ul li a {
      color: #FFF;
      font-size: 12px;
      font-weight: 400;
      padding: 0px 10px; }
      .header-sky .header-top ul li a:hover, .header-sky .header-top ul li a:focus {
        background: transparent; }
    .header-sky .header-top .header-top-left {
      float: left;
      color: white; }
      .header-sky .header-top .header-top-left span {
        font-size: 12px;
        margin-right: 15px;
        font-weight: 400; }
        .header-sky .header-top .header-top-left span i {
          font-size: 15px;
          padding-right: 5px; }
    .header-sky .header-top .header-top-right {
      float: right; }
      .header-sky .header-top .header-top-right .icon-v3-top {
        display: inline-block;
        margin-right: 5px; }
        .header-sky .header-top .header-top-right .icon-v3-top a {
          padding: 10px 0px; }
          .header-sky .header-top .header-top-right .icon-v3-top a:hover {
            background: #FFF; }
            .header-sky .header-top .header-top-right .icon-v3-top a:hover .fa {
              color: #232323; }
        .header-sky .header-top .header-top-right .icon-v3-top .fa {
          font-size: 15px;
          color: #FFF;
          padding: 0px 7px;
          display: inline-block; }
          @media (min-width: 768px) and (max-width: 991px) {
            .header-sky .header-top .header-top-right .icon-v3-top .fa {
              padding: 0px 5px; } }
      @media all and (min-width: 1200px) {
        .header-sky .header-top .header-top-right .dropdown:hover .caret {
          border-top: 0px dashed;
          border-bottom: 4px dashed; }
        .header-sky .header-top .header-top-right .dropdown:hover:hover .dropdown-menu {
          visibility: visible;
          opacity: 1;
          transform: translate(0px);
          -moz-transform: translateY(0px);
          -webkit-transform: translateY(0px);
          -ms-transform: translateY(0px);
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          -ms-transition: all 0.3s ease-in-out;
          -o-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out; }
        .header-sky .header-top .header-top-right .dropdown-menu {
          padding: 20px;
          transform: translateY(50px);
          -moz-transform: translateY(50px);
          -ms-transform: translateY(50px);
          -webkit-transform: translateY(50px);
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          -ms-transition: all 0.3s ease-in-out;
          -o-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out;
          padding: 15px 0px;
          visibility: hidden;
          opacity: 0;
          display: block; } }
      .header-sky .header-top .header-top-right .dropdown-menu {
        padding: 0;
        border-radius: 0px;
        min-width: 100px;
        right: 0px;
        left: auto;
        margin-top: 0px;
        border: none; }
        .header-sky .header-top .header-top-right .dropdown-menu li a {
          color: #232323;
          padding: 6px 15px;
          text-transform: uppercase; }
          .header-sky .header-top .header-top-right .dropdown-menu li a:hover {
            background: #8E7037;
            color: #FFF !important; }
        .header-sky .header-top .header-top-right .dropdown-menu .active a {
          background: #8E7037;
          color: #FFF !important; }
      .header-sky .header-top .header-top-right ul {
        padding: 0;
        list-style: none;
        margin-bottom: 0;
        float: right; }
        .header-sky .header-top .header-top-right ul .dropdown {
          display: inline-block;
          margin-left: -4px; }
          .header-sky .header-top .header-top-right ul .dropdown:hover {
            background: #fff; }
            .header-sky .header-top .header-top-right ul .dropdown:hover a {
              color: #232323; }
          .header-sky .header-top .header-top-right ul .dropdown.open > a,
          .header-sky .header-top .header-top-right ul .dropdown a:hover {
            text-decoration: none;
            color: #232323; }
          .header-sky .header-top .header-top-right ul .dropdown .dropdown-toggle {
            display: inline;
            padding: 11px 10px; }
            @media (min-width: 768px) and (max-width: 991px) {
              .header-sky .header-top .header-top-right ul .dropdown .dropdown-toggle {
                padding: 11px 8px; } }
  .header-sky .nav .open > a,
  .header-sky .nav .open > a:hover,
  .header-sky .nav .open > a:focus {
    background: transparent; }
  .header-sky .navbar {
    border-radius: 0px;
    border: none;
    margin: 0; }
    .header-sky .navbar.navbar-fixed-top {
      top: initial;
      z-index: 999; }
    .header-sky .navbar .navbar-header .hamburger-icon {
      padding: 12px 10px; }
      .header-sky .navbar .navbar-header .hamburger-icon span:nth-child(2) {
        transition: 0.2s ease-in-out;
        transform: translateY(4px) rotate(42deg); }
      .header-sky .navbar .navbar-header .hamburger-icon span:nth-child(3) {
        display: none;
        transition: 0.2s ease-in-out; }
      .header-sky .navbar .navbar-header .hamburger-icon span:nth-child(4) {
        transition: 0.2s ease-in-out;
        transform: translateY(-2px) rotate(-42deg); }
    .header-sky .navbar .navbar-header .navbar-brand {
      padding-top: 14px;
      padding-right: 0px; }
      .header-sky .navbar .navbar-header .navbar-brand img {
        max-width: 100%;
        height: auto;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden; }
    .header-sky .navbar .navbar-nav .submenu-hover1 a .caret {
      margin: 0px;
      text-transform: none;
      background: transparent;
      position: absolute;
      right: -3%;
      top: 44% !important; }
      @media (max-width: 768px) {
        .header-sky .navbar .navbar-nav .submenu-hover1 a .caret {
          margin-top: 0px !important; } }
    .header-sky .navbar .navbar-nav .submenu-hover1:hover .dropdown-margin li a {
      color: #232323; }
      .header-sky .navbar .navbar-nav .submenu-hover1:hover .dropdown-margin li a:hover {
        color: #8E7037; }
    .header-sky .navbar .navbar-nav .active a {
      background: red;
      color: #ffffff; }
    .header-sky .navbar .navbar-nav li {
      padding: 35px 25px; }
      .header-sky .navbar .navbar-nav li .caret {
        margin-left: 5px; }
      .header-sky .navbar .navbar-nav li:hover a {
        background: transparent;
        color: #8E7037; }
      .header-sky .navbar .navbar-nav li a {
        font-weight: 600;
        font-size: 14px;
        color: #fff;
        font-family: 'Poppins', sans-serif;
        padding: 0px;
        text-transform: uppercase;
        letter-spacing: 1px; }
        @media (max-width: 767px) {
          .header-sky .navbar .navbar-nav li a:hover {
            color: #8E7037; } }
        .header-sky .navbar .navbar-nav li a:focus {
          background: transparent; }
      .header-sky .navbar .navbar-nav li .dropdown-menu {
        -webkit-border-radius: 0px;
        border-radius: 0px;
        left: 0;
        right: auto; }
        .header-sky .navbar .navbar-nav li .dropdown-menu li {
          position: relative;
          padding-bottom: 6px !important;
          padding-top: 6px !important; }
          .header-sky .navbar .navbar-nav li .dropdown-menu li:hover a, .header-sky .navbar .navbar-nav li .dropdown-menu li:focus a {
            background: transparent;
            color: #8E7037; }
            .header-sky .navbar .navbar-nav li .dropdown-menu li:hover a .caret, .header-sky .navbar .navbar-nav li .dropdown-menu li:focus a .caret {
              color: #8E7037; }
          .header-sky .navbar .navbar-nav li .dropdown-menu li a {
            padding: 0px !important;
            color: #232323;
            text-transform: none;
            background: transparent;
            position: relative; }
            .header-sky .navbar .navbar-nav li .dropdown-menu li a:before {
              display: none; }
    @media all and (min-width: 1200px) {
      .header-sky .navbar .navbar-nav .dropdown:hover .submenu-hover {
        visibility: visible;
        opacity: 1;
        transform: translate(0px);
        -moz-transform: translateY(0px);
        -webkit-transform: translateY(0px);
        -ms-transform: translateY(0px);
        transition: opacity 0.4s ease 0s, -moz-transform 0.4s ease 0s;
        -webkit-transition: opacity 0.4s ease 0s, -webkit-transform 0.4s ease 0s;
        -o-transition: opacity 0.4s ease 0s, -o-transform 0.4s ease 0s;
        -ms-transition: opacity 0.4s ease 0s, -ms-transform 0.4s ease 0s; }
      .header-sky .navbar .navbar-nav .submenu-hover {
        width: 190px;
        filter: alpha(opacity=0);
        padding: 20px;
        transform: translateY(50px);
        -moz-transform: translateY(50px);
        -ms-transform: translateY(50px);
        -webkit-transform: translateY(50px);
        transition: opacity 0.4s ease 0s, -moz-transform 0.4s ease 0s, visibility 0.4s linear 0s;
        -webkit-transition: opacity 0.4s ease 0s, -webkit-transform 0.4s ease 0s, visibility 0.4s linear 0s;
        -ms-transition: opacity 0.4s ease 0s, -ms-transform 0.4s ease 0s, visibility 0.4s linear 0s;
        -o-transition: opacity 0.4s ease 0s, -o-transform 0.4s ease 0s, visibility 0.4s linear 0s;
        padding: 10px 0px;
        visibility: hidden;
        opacity: 0;
        display: block;
        position: absolute; }
      .header-sky .navbar .navbar-nav .submenu-hover1 {
        position: relative; }
        .header-sky .navbar .navbar-nav .submenu-hover1:hover .dropdown-margin {
          visibility: visible;
          opacity: 1;
          transform: translate(0px);
          -moz-transform: translateY(0px);
          -webkit-transform: translateY(0px);
          -ms-transform: translateY(0px);
          transition: opacity 0.4s ease 0s, -moz-transform 0.4s ease 0s;
          -webkit-transition: opacity 0.4s ease 0s, -webkit-transform 0.4s ease 0s;
          -o-transition: opacity 0.4s ease 0s, -o-transform 0.4s ease 0s;
          -ms-transition: opacity 0.4s ease 0s, -ms-transform 0.4s ease 0s; }
          .header-sky .navbar .navbar-nav .submenu-hover1:hover .dropdown-margin li a {
            color: #232323; }
            .header-sky .navbar .navbar-nav .submenu-hover1:hover .dropdown-margin li a:hover {
              color: #8E7037; }
        .header-sky .navbar .navbar-nav .submenu-hover1 .dropdown-margin {
          visibility: hidden;
          opacity: 0;
          display: block;
          position: absolute;
          left: 100% !important;
          top: 0;
          margin: 0;
          filter: alpha(opacity=0);
          transform: translateY(50px);
          -moz-transform: translateY(50px);
          -ms-transform: translateY(50px);
          -webkit-transform: translateY(50px);
          transition: opacity 0.4s ease 0s, -moz-transform 0.4s ease 0s, visibility 0.4s linear 0s;
          -webkit-transition: opacity 0.4s ease 0s, -webkit-transform 0.4s ease 0s, visibility 0.4s linear 0s;
          -ms-transition: opacity 0.4s ease 0s, -ms-transform 0.4s ease 0s, visibility 0.4s linear 0s;
          -o-transition: opacity 0.4s ease 0s, -o-transform 0.4s ease 0s, visibility 0.4s linear 0s; } }

/* 
2.4 Content
    2.4.1 Map
    2.4.2 ROOMS
    2.4.3 GALLERY
    2.4.4 NEW
    2.4.5 EVENTS
    2.4.6 SCROLL TOP
    2.4.7 TESTIMONIALS
    2.4.8 BEST
    2.4.9 ABOUT 
    2.4.10 BODY-PAGE
    2.4.11 BANNER-TEMS
    2.4.12 BODY-ROOM 
    2.4.13 CHECK-OUT 
    2.4.14 RESTAURANTS
    2.4.15 CONTACT
    2.4.16 BLOG
    2.4.17 SIDEBAR
    2.4.18 WIDGETSEARCH
    2.4.19 POST
    2.4.20 NAVIGATION
    2.4.21 VIDEO
    2.4.22 COMMENT
    2.4.23 AttRACTIONS
    2.4.24 SECTION-TEAM
    2.4.25 RESERVATION
    2.4.26 DATEPICKER
    2.4.27 PRODUCT
    2.4.28 GUEST-BOOK */
/*   2.4.1 Map */
.section-map-v4 {
  margin-bottom: -10px; }
  .section-map-v4 iframe {
    width: 100%;
    border: none; }

.section-map {
  height: 470px;
  position: relative; }
  .section-map iframe {
    width: 100%;
    border: none; }

#map {
  height: 685px;
  width: 100%; }
  @media (max-width: 1200px) {
    #map {
      height: 500px; } }
  @media (max-width: 992px) {
    #map {
      height: 410px; } }
  @media (max-width: 768px) {
    #map {
      height: 320px; } }

/*2.4.2 ROOMS*/
.rooms {
  text-align: center;
  padding-top: 60px;
  padding-bottom: 60px;
  background: #F2F2F2; }
  .rooms.rooms-v2 .outline {
    margin-top: 22px;
    margin-bottom: 30px; }
  .rooms.rooms-v2 .wrap-rooms .wrap-box {
    margin-left: 15px;
    margin-right: 15px; }
    @media (max-width: 480px) {
      .rooms.rooms-v2 .wrap-rooms .wrap-box {
        margin-left: 0;
        margin-right: 0; } }
    .rooms.rooms-v2 .wrap-rooms .wrap-box:hover .img-item img {
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out; }
    .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img {
      overflow: hidden;
      display: block; }
      .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img .rooms-content {
        position: absolute;
        bottom: 0%;
        left: 50%;
        transform: translate(-50%, 70%);
        right: 50%;
        width: 100%;
        height: 100%;
        z-index: 99; }
        @media (max-width: 480px) {
          .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img .rooms-content {
            bottom: 0%; } }
        @media (max-width: 768px) and (min-width: 600px) {
          .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img .rooms-content {
            transform: translate(-50%, 65%); } }
        .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img .rooms-content .sky-h4 {
          font-size: 30px;
          color: #FFF;
          font-weight: 500;
          margin-bottom: 5px; }
        .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img .rooms-content .price {
          font-weight: 500;
          font-size: 16px;
          color: #FFF; }
      .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img .img-item {
        position: relative;
        overflow: hidden; }
        .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img .img-item img {
          -webkit-transition: all 0.5s ease-in-out;
          -moz-transition: all 0.5s ease-in-out;
          -ms-transition: all 0.5s ease-in-out;
          -o-transition: all 0.5s ease-in-out;
          transition: all 0.5s ease-in-out; }
        .rooms.rooms-v2 .wrap-rooms .wrap-box .box-img .img-item:before {
          bottom: 0;
          left: 0;
          right: 0;
          top: 50%;
          position: absolute;
          content: '';
          pointer-events: none;
          background-color: transparent;
          background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(5, 5, 5, 0.6)));
          background: -webkit-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
          background: -moz-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
          background: -ms-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
          background: -o-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
          z-index: 9; }
    .rooms.rooms-v2 .wrap-rooms .wrap-box .content {
      padding: 22px 25px 28px; }
      @media (max-width: 768px) and (min-width: 600px) {
        .rooms.rooms-v2 .wrap-rooms .wrap-box .content {
          padding: 10px; } }
      @media (max-width: 480px) {
        .rooms.rooms-v2 .wrap-rooms .wrap-box .content {
          padding: 22px 5px 28px; } }
      .rooms.rooms-v2 .wrap-rooms .wrap-box .content p {
        font-weight: 300;
        font-size: 16px; }
      .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room {
        overflow: hidden;
        margin-top: 20px; }
        .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul {
          padding: 0; }
          .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul li {
            position: relative;
            font-size: 14px;
            width: calc(50% - 0%);
            vertical-align: middle;
            font-weight: 400;
            color: #959595;
            border-right: 1px solid #ececec;
            border-bottom: 1px solid #ececec;
            float: left;
            list-style: none;
            text-align: left;
            padding: 13px 0 15px 17px; }
            @media (min-width: 992px) and (max-width: 1200px) {
              .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul li {
                padding-left: 35px; } }
            @media (min-width: 768px) and (max-width: 991px) {
              .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul li {
                font-size: 12px; } }
            .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul li img {
              width: auto;
              display: inline-block;
              padding-right: 6px;
              position: relative;
              vertical-align: inherit; }
            .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul li:nth-child(2), .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul li:nth-child(4) {
              border-right: none; }
            .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul li:nth-child(3), .rooms.rooms-v2 .wrap-rooms .wrap-box .content .bottom-room ul li:nth-child(4) {
              border-bottom: none; }
  .rooms.rooms-v3 {
    padding-top: 40px; }
    @media (max-width: 991px) {
      .rooms.rooms-v3 {
        padding-bottom: 30px; } }
    .rooms.rooms-v3 .wrap-rooms .owl-dots {
      opacity: 0; }
    .rooms.rooms-v3 .outline {
      margin-top: 22px;
      margin-bottom: 30px; }
  .rooms.rooms-v4 {
    padding-bottom: 55px; }
  .rooms .title-room {
    color: #232323; }
  .rooms .outline {
    width: 100%;
    height: 25px;
    background: url("../images/Home-1/icon-our.png") no-repeat center center;
    margin-top: 10px; }
  .rooms .wrap-rooms {
    margin-top: 25px; }
    .rooms .wrap-rooms .item {
      margin-top: 20px; }
    .rooms .wrap-rooms .wrap-box {
      background: white;
      margin-bottom: 30px;
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out; }
      .rooms .wrap-rooms .wrap-box.wrap-box-v4 {
        position: relative;
        overflow: hidden;
        display: block;
        transform: scale(1, 1);
        margin-left: 0px;
        margin-right: 0px;
        -webkit-box-shadow: none !important;
        box-shadow: none !important; }
        .rooms .wrap-rooms .wrap-box.wrap-box-v4:hover .box-img img {
          transform: none; }
        .rooms .wrap-rooms .wrap-box.wrap-box-v4:hover .rooms-content {
          overflow: hidden;
          opacity: 0;
          transition: all 0.7s ease-in-out; }
        .rooms .wrap-rooms .wrap-box.wrap-box-v4:hover .content {
          opacity: 1; }
          .rooms .wrap-rooms .wrap-box.wrap-box-v4:hover .content .wrap-content {
            margin-top: 0px;
            transition: all 0.8s ease-in-out;
            opacity: 1; }
        .rooms .wrap-rooms .wrap-box.wrap-box-v4 .rooms-content {
          position: absolute;
          bottom: 0;
          background: #FFF;
          width: 100%;
          transition: all 0.7s ease-in-out;
          opacity: 1; }
          @media (min-width: 992px) and (max-width: 1200px) {
            .rooms .wrap-rooms .wrap-box.wrap-box-v4 .rooms-content {
              padding-bottom: 0;
              padding-top: 0; }
              .rooms .wrap-rooms .wrap-box.wrap-box-v4 .rooms-content .sky-h4 {
                font-size: 21px; } }
          @media (min-width: 768px) and (max-width: 992px) {
            .rooms .wrap-rooms .wrap-box.wrap-box-v4 .rooms-content {
              padding-bottom: 10;
              padding-top: 10; } }
        .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content {
          bottom: 0%;
          position: absolute;
          left: 0;
          right: 0;
          background-color: rgba(20, 19, 19, 0.85);
          overflow: hidden;
          width: 100%;
          height: 100%;
          opacity: 0;
          -webkit-transition: all 0.7s ease-in-out;
          -moz-transition: all 0.7s ease-in-out;
          -ms-transition: all 0.7s ease-in-out;
          -o-transition: all 0.7s ease-in-out;
          transition: all 0.7s ease-in-out; }
          .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .wrap-content {
            -webkit-transition: all 0.5s ease-in-out;
            -moz-transition: all 0.5s ease-in-out;
            -ms-transition: all 0.5s ease-in-out;
            -o-transition: all 0.5s ease-in-out;
            transition: all 0.5s ease-in-out;
            margin-top: 60px;
            opacity: 0; }
            @media (max-width: 480px) {
              .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .wrap-content {
                top: 50%;
                position: absolute;
                transform: translate(0%, -50%); } }
          .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content p {
            font-size: 14px;
            font-weight: 300;
            color: #FFF; }
          .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .bottom-room {
            margin-top: -10px;
            padding: 23px 35px 25px 35px; }
            .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .bottom-room ul li {
              color: #c9c9c9;
              border-right: 1px solid #c9c9c9;
              border-bottom: 1px solid #c9c9c9;
              font-size: 11px;
              padding: 5px 0px 10px 13px; }
          .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .rooms-content1 .sky-h4 {
            color: #FFF;
            text-transform: uppercase;
            margin-bottom: 6px; }
          .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .rooms-content1 .price {
            color: #8E7037;
            font-size: 16px;
            font-weight: 500; }
          .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .btn {
            font-size: 14px;
            color: #c9c9c9;
            font-weight: 400;
            height: 43px;
            width: 163px;
            line-height: 30px;
            background: transparent;
            border: 1px solid white; }
            @media (max-width: 1200px) {
              .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .btn {
                height: 35px;
                width: 140px;
                line-height: 21px;
                margin-top: 0; } }
            .rooms .wrap-rooms .wrap-box.wrap-box-v4 .content .btn:hover {
              background: #8E7037;
              border: 1px solid #8E7037;
              color: #FFF; }
      .rooms .wrap-rooms .wrap-box:hover {
        -webkit-box-shadow: 0px 0px 10px 5px rgba(0, 0, 0, 0.15);
        box-shadow: 0px 0px 12px 3px rgba(0, 0, 0, 0.15); }
        .rooms .wrap-rooms .wrap-box:hover .rooms-content .sky-h4 {
          color: #8E7037; }
        .rooms .wrap-rooms .wrap-box:hover .box-img img {
          transform: scale(1.1);
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          -ms-transition: all 0.3s ease-in-out;
          -o-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out; }
      .rooms .wrap-rooms .wrap-box .rooms-content {
        padding-bottom: 25px;
        padding-top: 15px;
        text-transform: uppercase;
        cursor: pointer; }
        .rooms .wrap-rooms .wrap-box .rooms-content .price {
          color: #8E7037;
          font-size: 16px;
          font-weight: 500;
          letter-spacing: 1px; }
      .rooms .wrap-rooms .wrap-box .box-img {
        position: relative;
        overflow: hidden; }
        .rooms .wrap-rooms .wrap-box .box-img img {
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          -ms-transition: all 0.3s ease-in-out;
          -o-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out; }
    .rooms .wrap-rooms .owl-nav {
      display: none; }
    .rooms .wrap-rooms .owl-dots {
      margin-top: 2px;
      display: block; }
      .rooms .wrap-rooms .owl-dots .owl-dot {
        width: 8px;
        height: 8px;
        display: inline-block;
        border-radius: 100%;
        border: 2px solid #555555;
        margin: 0px 3px; }
        .rooms .wrap-rooms .owl-dots .owl-dot.active {
          background: #555555; }
    .rooms .wrap-rooms .owl-carousel.owl-grab {
      cursor: auto; }
  .rooms .rooms-p {
    color: #ABABAB;
    font-weight: 300;
    font-size: 16px;
    width: 48%;
    margin: 0 auto;
    margin-top: 15px; }

/*2.4.3 GALLERY */
.gallery-our {
  text-align: center;
  background: url("../images/Home-1/bg-gallery.jpg");
  padding-top: 50px;
  padding-bottom: 80px; }
  .gallery-our.wrap-gallery-restaurant {
    background: none;
    padding-bottom: 50px; }
    .gallery-our.wrap-gallery-restaurant .gallery-restaurant {
      margin: auto; }
      .gallery-our.wrap-gallery-restaurant .gallery-restaurant .gallery_product {
        padding: 0 15px;
        margin-bottom: 30px; }
      .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul {
        margin-bottom: 35px; }
        @media (max-width: 767px) {
          .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul {
            margin-bottom: 25px;
            margin-top: 35px; } }
        .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul li.active > a {
          border: 1px solid transparent;
          color: #fff;
          background: #8e7037; }
          .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul li.active > a:hover, .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul li.active > a:focus {
            color: #fff;
            background-color: #8e7037;
            border: 1px solid transparent;
            cursor: pointer; }
        .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul li a {
          font-weight: 600;
          color: #232323;
          margin: 0px 5px;
          font-size: 16px;
          padding: 6px 18px;
          border: 1px solid transparent; }
          @media (max-width: 767px) {
            .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul li a {
              margin: 0;
              font-size: 14px; } }
          @media (max-width: 480px) {
            .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul li a {
              padding: 6px 10px; } }
          .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul li a:hover, .gallery-our.wrap-gallery-restaurant .gallery-restaurant ul li a:focus {
            background: #8E7037;
            outline: none;
            color: #fff;
            border: 1px solid transparent; }
  .gallery-our .gallery {
    margin: 0 160px; }
    .gallery-our .gallery .title-gallery {
      color: #FFF; }
    .gallery-our .gallery .h2-rooms {
      color: #000;
      font-size: 36px; }
    .gallery-our .gallery .nav-tabs {
      display: inline-block;
      border: none;
      margin-bottom: 30px;
      margin-top: 25px; }
      .gallery-our .gallery .nav-tabs li.active > a {
        border: 1px solid #8e7037;
        color: #8e7037;
        background: transparent; }
        .gallery-our .gallery .nav-tabs li.active > a:hover, .gallery-our .gallery .nav-tabs li.active > a:focus {
          color: #8e7037;
          background-color: 0;
          border: 1px solid #8e7037;
          cursor: pointer; }
      .gallery-our .gallery .nav-tabs li a {
        margin: 0px 8px;
        color: #ababab;
        font-size: 16px;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        -webkit-border-radius: 0px;
        border-radius: 0px;
        padding: 6px 13px; }
        .gallery-our .gallery .nav-tabs li a:hover, .gallery-our .gallery .nav-tabs li a:focus {
          background: 0;
          outline: none;
          color: #8e7037;
          border: 1px solid transparent; }
    .gallery-our .gallery .outline {
      width: 100%;
      height: 25px;
      background: url("../images/Home-1/icon-our.png") no-repeat center center;
      margin-top: 20px; }
    .gallery-our .gallery .gallery-title {
      font-size: 48px;
      color: #232323;
      text-align: center;
      font-weight: 400;
      font-family: 'Playfair Display', serif;
      position: relative; }
    .gallery-our .gallery .gallery_product {
      padding: 0; }
      .gallery-our .gallery .gallery_product .wrap-box {
        position: relative;
        display: block;
        overflow: hidden;
        text-align: center;
        cursor: pointer;
        transition: all 4s ease-in-out; }
        .gallery-our .gallery .gallery_product .wrap-box .box-img {
          width: 100%;
          height: 100%; }
        .gallery-our .gallery .gallery_product .wrap-box:hover .gallery-box-main {
          overflow: visible;
          opacity: 1;
          transform: translate(-50%, -50%); }
        .gallery-our .gallery .gallery_product .wrap-box img {
          transition: all 0.3s ease-in-out;
          width: 101%;
          max-width: 101%;
          height: 100%;
          display: block; }
        .gallery-our .gallery .gallery_product .wrap-box:hover img {
          transform: scale(1.2);
          opacity: 0.8; }
        .gallery-our .gallery .gallery_product .wrap-box .gallery-box-main {
          position: absolute;
          top: 50%;
          left: 50%;
          overflow: hidden;
          opacity: 0;
          transition: all 0.4s ease-in-out;
          transform: translate(-340%, -49%); }
          .gallery-our .gallery .gallery_product .wrap-box .gallery-box-main .gallery-icon {
            transition: all 0.3s ease-in-out; }
            .gallery-our .gallery .gallery_product .wrap-box .gallery-box-main .gallery-icon .ion-ios-plus-empty {
              font-size: 40px;
              width: 60px;
              height: 60px;
              background: #ffffff;
              display: inline-block;
              border-radius: 100%;
              line-height: 60px;
              color: #8e7037;
              border: 1px solid #ffffff; }
              .gallery-our .gallery .gallery_product .wrap-box .gallery-box-main .gallery-icon .ion-ios-plus-empty:hover {
                background: transparent;
                color: #FFF;
                border: 1px solid #ffffff; }
          .gallery-our .gallery .gallery_product .wrap-box .gallery-box-main .gallery-content .gallery-box-h4 {
            font-family: 'Poppins', sans-serif;
            font-size: 18px;
            color: #FFF;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 1px; }
          .gallery-our .gallery .gallery_product .wrap-box .gallery-box-main .gallery-content .gallery-box-p {
            color: #E09300;
            font-family: 'Playfair Display', serif;
            font-style: italic;
            font-size: 15px;
            margin-top: -8px; }
    .gallery-our .gallery .btn-our {
      margin-top: 60px;
      width: 200px;
      height: 44px;
      -webkit-border-radius: 0px;
      border-radius: 0px;
      color: #ffffff;
      font-family: 'Poppins', sans-serif;
      font-weight: 500;
      font-size: 14px;
      line-height: 30px;
      border: 2px solid #ffffff;
      letter-spacing: 2px;
      outline: none;
      background: transparent; }
      .gallery-our .gallery .btn-our:hover {
        color: #ffffff;
        text-shadow: none;
        background: #8e7037;
        border: 1px solid #8e7037; }
      .gallery-our .gallery .btn-our:focus {
        color: #FFF;
        background: #8e7037;
        outline: none;
        border: 1px solid #8e7037; }
  .gallery-our.gallery_1 {
    padding-top: 25px;
    padding-bottom: 50px; }
    @media (max-width: 767px) {
      .gallery-our.gallery_1 {
        padding-bottom: 20px;
        padding-top: 0; } }
    .gallery-our.gallery_1 .wrap-box-1 {
      position: relative;
      overflow: hidden;
      display: block; }
      .gallery-our.gallery_1 .wrap-box-1:hover .box-img img {
        transform: scale(1.1);
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out; }
      .gallery-our.gallery_1 .wrap-box-1:hover .box-img p {
        overflow: visible;
        opacity: 1;
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
        bottom: 0; }
      .gallery-our.gallery_1 .wrap-box-1:hover .box-img:before {
        opacity: 1;
        overflow: visible;
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
      .gallery-our.gallery_1 .wrap-box-1 .box-img p {
        position: absolute;
        bottom: -30%;
        padding: 0px 25px 6px 25px;
        text-align: left;
        color: #FFF;
        font-size: 18px;
        line-height: 23px;
        opacity: 0;
        overflow: hidden;
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
        max-width: 260px; }
      .gallery-our.gallery_1 .wrap-box-1 .box-img img {
        width: 100%;
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out; }
      .gallery-our.gallery_1 .wrap-box-1 .box-img:before {
        bottom: 0;
        left: 0;
        right: 0;
        top: 35%;
        position: absolute;
        content: '';
        pointer-events: none;
        background-color: transparent;
        background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(5, 5, 5, 0.6)));
        background: -webkit-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
        background: -moz-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
        background: -ms-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
        background: -o-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
        opacity: 0;
        overflow: hidden;
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
  .gallery-our.gallery_2 {
    padding-top: 55px;
    padding-bottom: 75px; }
    @media screen and (max-width: 768px) {
      .gallery-our.gallery_2 {
        padding-top: 0px;
        padding-bottom: 50px; }
        .gallery-our.gallery_2 .gallery-cat .list-inline {
          margin-bottom: 20px; }
          .gallery-our.gallery_2 .gallery-cat .list-inline li {
            line-height: 35px;
            padding: 0; } }
    .gallery-our.gallery_2 .gallery-content {
      margin-top: -13px; }

@media screen and (max-width: 1200px) {
  .gallery-isotope.col-3 .item-isotope, .gallery-isotope.col-3 .item-size {
    width: 33.33333% !important; }
  .gallery-isotope.col-4 .item-isotope,
  .gallery-isotope.col-4 .item-size,
  .gallery-isotope.col-4 .width-33 {
    width: 33.33333% !important; } }
@media screen and (max-width: 768px) {
  .gallery-isotope {
    /* GALLERY */ }
    .gallery-isotope.col-3 .item-isotope,
    .gallery-isotope.col-3 .item-size {
      width: 50% !important; }
    .gallery-isotope.col-4 .item-isotope,
    .gallery-isotope.col-4 .item-size,
    .gallery-isotope.col-4 .width-33 {
      width: 50% !important; } }
@media screen and (max-width: 480px) {
  .gallery-isotope {
    /* GALLERY */ }
    .gallery-isotope.col-3 .item-isotope,
    .gallery-isotope.col-3 .item-size {
      width: 100% !important; }
    .gallery-isotope.col-4 .item-isotope,
    .gallery-isotope.col-4 .item-size,
    .gallery-isotope.col-4 .width-33 {
      width: 100% !important; } }
.gallery-isotope .item-isotope {
  float: left;
  margin-top: 30px;
  padding-left: 15px;
  padding-right: 15px; }
.gallery-isotope.col-3 .item-isotope,
.gallery-isotope.col-3 .item-size {
  width: 25%; }
.gallery-isotope.col-4 .item-isotope,
.gallery-isotope.col-4 .item-size {
  width: 25%; }
.gallery-isotope.col-4 .width-50 {
  width: 50%; }
.gallery-isotope.col-4 .width-25 {
  width: 25%; }
.gallery-isotope.col-4 .item-isotope {
  margin-top: 30px; }

/* 2.4.4 NEW */
.news {
  padding-top: 70px;
  padding-bottom: 95px; }
  .news.news-v3 {
    padding-bottom: 75px; }
  .news.news-v4 {
    padding-bottom: 100px; }
  .news .line {
    width: 170px;
    height: 1px;
    background: #8e7037;
    margin-top: 20px;
    margin-bottom: -9px; }
  .news .line-v2 {
    width: 170px;
    height: 2px;
    background: #8e7037;
    margin-top: 20px;
    margin-bottom: -10px;
    margin-left: 0; }
  .news .new-title {
    color: #232323;
    position: relative; }
  .news .title-news {
    font-size: 45.71px;
    color: #232323;
    font-family: 'Playfair Display', serif; }
  .news .line-news {
    width: 170px;
    height: 1px;
    background: #8e7037; }
  .news .substance {
    margin-top: 50px;
    border-left: 1px solid #ebebeb;
    padding-left: 25px; }
    .news .substance .date .day {
      font-size: 45px;
      font-family: 'Playfair', serif;
      color: #898989;
      display: inline-block;
      padding-right: 7px;
      font-weight: 400; }
    .news .substance .date .year {
      color: #898989;
      font-size: 14px;
      display: inline-block;
      margin-left: 5px;
      line-height: 20px;
      border-left: 1px solid #ebebeb;
      padding-left: 16px;
      letter-spacing: 1px;
      font-weight: 500; }
    .news .substance .text {
      margin-top: 8px;
      margin-bottom: 22px; }
      .news .substance .text a {
        color: #232323;
        font-size: 26px;
        font-weight: 400; }
        .news .substance .text a:hover {
          text-decoration: none;
          color: #8E7037; }
    .news .substance .read-more {
      font-size: 13px;
      color: #8e7037;
      font-weight: 300; }
      .news .substance .read-more:hover {
        text-decoration: none;
        color: #8E7037; }

/* 2.4.5 EVENTS */
.events {
  margin-top: 33px; }
  .events.events-v3 {
    margin-top: 60px; }
    @media (max-width: 767px) {
      .events.events-v3 {
        margin-top: 25px; } }
  .events.events-v4 {
    margin-bottom: -30px; }
    .events.events-v4 .events-item {
      margin-bottom: 30px;
      position: relative; }
      .events.events-v4 .events-item .v4 {
        font-size: 48px; }
      .events.events-v4 .events-item img {
        z-index: -1; }
      .events.events-v4 .events-item:before {
        bottom: 0;
        left: 0;
        right: 0;
        top: 50%;
        position: absolute;
        content: '';
        pointer-events: none;
        background-color: transparent;
        background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(5, 5, 5, 0.6)));
        background: -webkit-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
        background: -moz-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
        background: -ms-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
        background: -o-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
        z-index: 1; }
      .events.events-v4 .events-item .content-v4 {
        bottom: -58%;
        text-align: left;
        left: 8%; }
  .events .line-v2 {
    margin: auto;
    width: 300px;
    height: 2px;
    background: #8e7037;
    margin-top: 22px;
    margin-bottom: 45px; }
  .events .owl-carousel {
    margin-top: 40px; }
    .events .owl-carousel .owl-nav {
      display: flex;
      position: absolute;
      right: 15px;
      top: -105px; }
      .events .owl-carousel .owl-nav .owl-prev {
        font-size: 0; }
        .events .owl-carousel .owl-nav .owl-prev:hover:before {
          background: url("../images/Home-1/even-l-icon-color.png") no-repeat; }
        .events .owl-carousel .owl-nav .owl-prev:before {
          content: "";
          background: url("../images/Home-1/even-l-icon.png") no-repeat;
          position: absolute;
          width: 17px;
          height: 30px;
          margin-left: -40px; }
      .events .owl-carousel .owl-nav .owl-next {
        font-size: 0; }
        .events .owl-carousel .owl-nav .owl-next:before {
          content: "";
          background: url("../images/Home-1/even-r-icon.png") no-repeat;
          position: absolute;
          width: 17px;
          height: 30px; }
        .events .owl-carousel .owl-nav .owl-next:hover:before {
          background: url("../images/Home-1/even-l-icon-color1.png") no-repeat; }
  .events .line {
    width: 300px;
    height: 1px;
    background: #8e7037;
    margin-top: 22px;
    margin-bottom: 45px; }
  .events .events-title {
    color: #232323; }
  .events .events-item {
    position: relative;
    text-align: center;
    overflow: hidden;
    display: block;
    cursor: pointer; }
    .events .events-item:before {
      background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(5, 5, 5, 0.6)));
      background: -webkit-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
      background: -moz-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
      background: -ms-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
      background: -o-linear-gradient(top, transparent, rgba(5, 5, 5, 0.6));
      bottom: 0;
      left: 0;
      right: 0;
      top: 50%;
      position: absolute;
      content: '';
      pointer-events: none;
      z-index: 1; }
    .events .events-item img {
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out; }
    .events .events-item:hover img {
      transform: scale(1.2);
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out; }
    .events .events-item .events-content {
      position: absolute;
      bottom: -73%;
      width: 100%;
      height: 100%;
      z-index: 1; }
      .events .events-item .events-content a:hover {
        text-decoration: none; }
      @media (max-width: 1199px) {
        .events .events-item .events-content .sky-h3 {
          font-size: 27px; } }

.section-event-restaurant {
  padding-top: 73px;
  padding-bottom: 73px;
  background: url("../images/Restaurants/Restaurants-60.jpg") no-repeat center center;
  background-size: cover; }
  .section-event-restaurant .event-restaurant .event-restaurant_header {
    color: #fff;
    margin-top: 50px;
    padding-right: 20px; }
    .section-event-restaurant .event-restaurant .event-restaurant_header .icon {
      font-size: 50px; }
    .section-event-restaurant .event-restaurant .event-restaurant_header h2 {
      text-transform: uppercase;
      font-size: 36px;
      color: #fff;
      font-weight: bold;
      line-height: 1;
      margin-top: 10px;
      margin-bottom: -4px; }
    .section-event-restaurant .event-restaurant .event-restaurant_header p {
      margin-bottom: 0;
      font-size: 16px;
      margin-top: 20px; }
  .section-event-restaurant .event-restaurant .event-restaurant_content {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0; }
    .section-event-restaurant .event-restaurant .event-restaurant_content li {
      overflow: hidden;
      margin-top: 45px; }
      .section-event-restaurant .event-restaurant .event-restaurant_content li:first-child {
        margin-top: 0; }
      .section-event-restaurant .event-restaurant .event-restaurant_content li .event-date {
        width: 70px;
        padding: 5px 0;
        margin-right: 30px;
        float: left;
        border: 2px solid #fff;
        display: block;
        text-align: center;
        color: #fff;
        font-size: 16px;
        text-transform: uppercase;
        line-height: 1; }
        .section-event-restaurant .event-restaurant .event-restaurant_content li .event-date strong {
          display: block;
          font-weight: 600;
          font-size: 34px;
          margin-bottom: 5px; }
      .section-event-restaurant .event-restaurant .event-restaurant_content li .text {
        overflow: hidden; }
        .section-event-restaurant .event-restaurant .event-restaurant_content li .text h2 {
          color: #fff;
          font-size: 18px;
          text-transform: uppercase;
          font-weight: 600;
          line-height: 1.4em;
          margin-top: -3px;
          margin-bottom: 0px; }
        .section-event-restaurant .event-restaurant .event-restaurant_content li .text a {
          color: #fff;
          display: inline-block;
          margin-top: 5px;
          font-size: 13px; }
          .section-event-restaurant .event-restaurant .event-restaurant_content li .text a:hover {
            color: #8E7037;
            text-decoration: none; }

/* 2.4.6 SCROLL TOP*/
.scrollToTop {
  width: 42px;
  height: 42px;
  position: fixed;
  bottom: 5%;
  right: 2%;
  display: none;
  font-size: 40px;
  background: #232323;
  color: #ebebeb;
  border-radius: 3px;
  text-align: center;
  line-height: 42px;
  z-index: 999; }
  .scrollToTop:hover {
    color: #FFF; }
  .scrollToTop:focus {
    color: #FFF; }

/*2.4.7 TESTIMONIALS*/
.testimonials {
  background: url("../images/Home-1/Testimonials.jpg");
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  text-align: center;
  position: relative;
  width: 100%; }
  .testimonials.testimonials-v2 {
    background: url("../images/Home-2/Testimonials-bg.jpg");
    background-size: cover;
    background-position: center center;
    background-attachment: fixed;
    position: relative;
    text-align: left;
    width: 100%;
    color: white; }
    .testimonials.testimonials-v2 .testimonials-h .owl-dots {
      text-align: center; }
    .testimonials.testimonials-v2 .testimonials-h .testimonials-img {
      margin-right: 10px;
      float: right;
      border-radius: 100%;
      overflow: hidden; }
      .testimonials.testimonials-v2 .testimonials-h .testimonials-img img {
        -webkit-border-radius: 50%;
        border-radius: 50%; }
    .testimonials.testimonials-v2 .testimonials-h .content-testimonials {
      padding-right: 5%; }
      .testimonials.testimonials-v2 .testimonials-h .content-testimonials .line {
        border-left: 2px solid #88949e;
        color: white;
        padding-left: 15px;
        background: 0;
        margin-left: 15px; }
      .testimonials.testimonials-v2 .testimonials-h .content-testimonials .testimonials-pp {
        position: relative;
        margin-top: 23px;
        margin-bottom: 12%; }
        .testimonials.testimonials-v2 .testimonials-h .content-testimonials .testimonials-pp:before {
          content: "​‌“​‌";
          font-size: 40px;
          font-family: 'Montserrat', sans-serif;
          vertical-align: middle;
          margin-right: 5px;
          line-height: 0;
          padding-top: 10px;
          position: relative; }
        .testimonials.testimonials-v2 .testimonials-h .content-testimonials .testimonials-pp:after {
          content: "”";
          font-size: 40px;
          font-family: 'Montserrat', sans-serif;
          line-height: 0px;
          vertical-align: middle;
          margin-left: 5px;
          padding-top: 10px;
          position: relative;
          top: 2px; }
    .testimonials.testimonials-v2 .testimonials-h .testimonials-content {
      top: 35%; }
  .testimonials .testimonials-h {
    min-height: 480px;
    position: relative;
    background: rgba(0, 0, 0, 0.75); }
    .testimonials .testimonials-h.testimonials-v3 {
      background: rgba(6, 6, 14, 0.87);
      opacity: 1; }
    .testimonials .testimonials-h .testimonials-content {
      position: absolute;
      top: 14%;
      width: 100%; }
      .testimonials .testimonials-h .testimonials-content .img-testimonials {
        display: inline-block;
        min-width: 115px;
        min-height: 115px;
        margin-bottom: 30px;
        border-radius: 100%; }
        .testimonials .testimonials-h .testimonials-content .img-testimonials img {
          max-width: 100%;
          text-align: center;
          border-radius: 100%; }
      .testimonials .testimonials-h .testimonials-content .owl-nav {
        display: none; }
      .testimonials .testimonials-h .testimonials-content .owl-dot {
        width: 8px;
        height: 8px;
        display: inline-block;
        border-radius: 100%;
        border: 2px solid white;
        margin: 0px 3px; }
        .testimonials .testimonials-h .testimonials-content .owl-dot.active {
          background: white; }
      .testimonials .testimonials-h .testimonials-content .owl-carousel {
        cursor: col-resize; }
        .testimonials .testimonials-h .testimonials-content .owl-carousel.owl-grab {
          cursor: auto; }
      .testimonials .testimonials-h .testimonials-content .testimonials-p {
        font-weight: 300;
        font-size: 16px;
        color: #FFF;
        padding: 0px 8%;
        margin-bottom: 26px; }
        .testimonials .testimonials-h .testimonials-content .testimonials-p span {
          font-family: 'Montserrat', sans-serif;
          font-size: 40px;
          line-height: 0;
          position: relative;
          top: 12px; }
      .testimonials .testimonials-h .testimonials-content .testimonials-p1 {
        font-weight: 400;
        font-size: 14px;
        color: #FFF;
        margin-top: -4px;
        margin-bottom: 42px; }

/* 2.4.8 BEST */
.best {
  margin-bottom: 70px;
  text-align: center; }
  .best .wrap-best {
    -webkit-box-shadow: 0 3px 20px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0 3px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 3px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    padding: 25px 0px;
    text-transform: uppercase; }
    .best .wrap-best img {
      margin: 0 auto; }

/* 2.4.9 ABOUT  */
.about {
  margin-top: 85px;
  margin-bottom: 155px; }
  .about.about-v2 {
    text-align: center;
    margin-bottom: 210px; }
    .about.about-v2 .line-v2 {
      width: 100px; }
    .about.about-v2 .about-img-v3 {
      overflow: hidden; }
      .about.about-v2 .about-img-v3 img {
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
      .about.about-v2 .about-img-v3:hover img {
        transform: scale(1.1, 1.1);
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
    .about.about-v2 .about-img {
      float: none;
      left: -55px;
      position: relative; }
      .about.about-v2 .about-img .img-1 img {
        box-shadow: 0px 0px 8px #f0f0f0; }
      .about.about-v2 .about-img .img-1 .img-2 {
        position: absolute;
        top: 16%;
        right: -11%;
        box-shadow: none;
        left: initial; }
    .about.about-v2 .about-p,
    .about.about-v2 .about-p1 {
      font-size: 18px; }
  .about.about-v3 {
    margin-bottom: 60px; }
  .about.about-v4 {
    margin-bottom: 6rem; }
    .about.about-v4 img {
      width: 100%; }
    .about.about-v4 .img-v4 {
      margin-top: 50px; }
      @media (max-width: 767px) {
        .about.about-v4 .img-v4 {
          display: none; } }
  .about .line {
    width: 250px;
    height: 1px;
    background: #8e7037;
    margin-top: 20px;
    margin-bottom: 45px; }
  .about .about-title {
    color: #232323;
    position: relative;
    margin-top: 0; }
  .about .about-p {
    color: #333;
    font-size: 16px;
    font-weight: 300;
    margin-top: 0px;
    padding-right: 50px; }
  .about .about-p1 {
    color: #333;
    font-size: 16px;
    font-weight: 300;
    margin-top: 30px;
    margin-bottom: 55px;
    padding-right: 70px; }
  .about .read-more {
    color: #232323;
    font-size: 12px;
    font-weight: 600;
    border: 2px solid #232323;
    padding: 9px 27px; }
    .about .read-more:hover {
      text-decoration: none;
      background: #8e7037;
      border: 2px solid #8e7037;
      color: #FFF; }
  .about .about-img {
    float: right; }
    .about .about-img .img-1 {
      position: relative; }
      .about .about-img .img-1 .img-2 {
        position: absolute;
        top: 50%;
        left: -15%;
        box-shadow: -4px 4px 20px 0px #f0f0f0; }
      .about .about-img .img-1 .img-3 {
        position: absolute;
        top: 84%;
        left: 27%;
        box-shadow: 0px 5px 20px 0px #f0f0f0; }

.section-about {
  background-color: #fff;
  padding-bottom: 70px;
  padding-top: 45px; }
  @media (max-width: 767px) {
    .section-about {
      padding-top: 20px;
      padding-bottom: 35px; } }
  .section-about .wrap-about {
    margin-top: -40px; }
    .section-about .wrap-about .about-item {
      margin-top: 45px; }

.about-item {
  overflow: hidden; }
  .about-item.about-right .img {
    float: right;
    width: 100%; }
  .about-item.about-right .text {
    float: right;
    padding-left: 0;
    padding-right: 100px;
    margin-top: 55px; }
  .about-item .img img {
    width: 100%; }
  .about-item .img .owl-pagination {
    display: none; }
  .about-item .text {
    padding-left: 40px;
    margin-top: 35px;
    padding-right: 52px; }
    .about-item .text h2 {
      line-height: 1.2em;
      font-size: 36px;
      color: #333333; }
    .about-item .text .desc {
      margin-top: 20px;
      color: #333333;
      font-size: 15px; }
      @media (max-width: 767px) {
        .about-item .text .desc {
          font-size: 14px; } }
      .about-item .text .desc p {
        line-height: 1.5em;
        margin: 0; }
  .about-item:hover .img:before {
    opacity: .9; }

/* 2.4.10 BODY-PAGE*/
.body-page {
  background: url("../images/404page/404page.jpg") no-repeat center center;
  width: 100%;
  text-align: center;
  background-size: cover;
  position: relative; }
  .body-page.page-v1 {
    background: url("../images/Login/login.jpg") no-repeat center center;
    background-size: cover; }
    .body-page.page-v1 .content {
      max-width: 450px;
      width: 100%;
      padding-top: 290px; }
      @media (max-width: 480px) {
        .body-page.page-v1 .content {
          padding-top: 120px;
          padding-bottom: 15px; } }
      .body-page.page-v1 .content .sky-h3 {
        font-family: "Poppins", Arial, sans-serif; }
      .body-page.page-v1 .content .p-v1 {
        font-size: 18px;
        font-weight: 400;
        font-family: "Poppins", Arial, sans-serif;
        margin-top: 17px;
        margin-bottom: 48px;
        color: #FFF;
        line-height: 23px; }
      .body-page.page-v1 .content .form-control {
        width: 430px;
        height: 43px;
        background: transparent;
        border: 2px solid white;
        -webkit-border-radius: 0px;
        border-radius: 0px;
        color: #FFF;
        padding-left: 17px;
        padding-right: 17px;
        margin: 0 auto; }
        .body-page.page-v1 .content .form-control::placeholder {
          color: white;
          font-size: 14px;
          font-weight: 400;
          font-family: "Poppins", Arial, sans-serif; }
      .body-page.page-v1 .content .btn {
        margin-bottom: 28px;
        margin-top: 15px;
        width: 200px;
        height: 42px;
        background: transparent;
        border: 2px solid white;
        -webkit-border-radius: 0px;
        border-radius: 0px;
        color: white;
        font-size: 16px;
        font-weight: 600; }
        .body-page.page-v1 .content .btn:hover {
          background: #8e7037;
          border: 1px solid #8e7037; }
      .body-page.page-v1 .content p {
        font-size: 14px;
        color: #FFF; }
      .body-page.page-v1 .content .field-icon {
        top: 14px;
        position: absolute;
        z-index: 2;
        color: white;
        cursor: pointer;
        right: 4%; }
  .body-page.page-v2 .content {
    max-width: 560px; }
  .body-page.page-v2 .btn {
    margin-top: 7px !important; }
  .body-page.page-v3 {
    background: url("../images/404page/Comming.jpg") no-repeat center center; }
    .body-page.page-v3 .content {
      width: 100%;
      max-width: 100%;
      padding-top: 325px;
      padding-bottom: 280px;
      background-size: cover; }
      @media (max-width: 768px) {
        .body-page.page-v3 .content {
          padding-top: 40%;
          padding-bottom: 20%; } }
      .body-page.page-v3 .content .h1-v3 {
        font-size: 72px;
        font-family: "Poppins", Arial, sans-serif;
        font-weight: 700;
        word-spacing: 5px;
        color: #FFF;
        margin-bottom: 20px; }
      .body-page.page-v3 .content .h3-v3 {
        font-size: 34px;
        font-weight: 400;
        font-family: "Poppins", Arial, sans-serif;
        margin-bottom: 35px;
        margin-top: -10px;
        color: #FFF; }
      .body-page.page-v3 .content .p-v3 {
        font-size: 20px;
        color: #FFF; }
      .body-page.page-v3 .content .pp-v3 {
        color: #C4C4C4;
        font-size: 20px; }
      .body-page.page-v3 .content .wrap {
        margin: 27px 0; }
        .body-page.page-v3 .content .wrap #timer {
          border: 2px solid #fff;
          display: inline-block;
          padding: 0; }
          .body-page.page-v3 .content .wrap #timer .padding {
            padding-right: 105px; }
            .body-page.page-v3 .content .wrap #timer .padding:last-child {
              padding: 0px;
              padding-right: 15px !important; }
          .body-page.page-v3 .content .wrap #timer #days {
            font-size: 50px;
            color: #fff;
            font-weight: 600; }
          .body-page.page-v3 .content .wrap #timer #hours {
            font-size: 50px;
            color: #fff;
            font-weight: 600;
            position: relative; }
            .body-page.page-v3 .content .wrap #timer #hours:before {
              left: -25%;
              top: 53%;
              width: 4px;
              height: 4px;
              content: "";
              position: absolute;
              background: #FFF; }
            .body-page.page-v3 .content .wrap #timer #hours:after {
              width: 4px;
              height: 4px;
              content: "";
              position: absolute;
              background: #FFF;
              top: 45%;
              left: -25%; }
          .body-page.page-v3 .content .wrap #timer #minutes {
            font-size: 50px;
            color: #fff;
            font-weight: 600;
            position: relative; }
            .body-page.page-v3 .content .wrap #timer #minutes:before {
              left: -21%;
              top: 53%;
              width: 4px;
              height: 4px;
              content: "";
              position: absolute;
              background: #FFF; }
            .body-page.page-v3 .content .wrap #timer #minutes:after {
              width: 4px;
              height: 4px;
              content: "";
              position: absolute;
              background: #FFF;
              top: 45%;
              left: -21%; }
          .body-page.page-v3 .content .wrap #timer #seconds {
            font-size: 50px;
            color: #fff;
            font-weight: 600;
            position: relative; }
            .body-page.page-v3 .content .wrap #timer #seconds:before {
              left: -50%;
              top: 56%;
              width: 4px;
              height: 4px;
              content: "";
              position: absolute;
              background: #FFF; }
            .body-page.page-v3 .content .wrap #timer #seconds:after {
              width: 4px;
              height: 4px;
              content: "";
              position: absolute;
              background: #FFF;
              top: 43%;
              left: -50%; }
          .body-page.page-v3 .content .wrap #timer div {
            display: inline-block;
            line-height: 1;
            padding: 23px;
            font-size: 40px; }
          .body-page.page-v3 .content .wrap #timer span {
            display: block;
            font-size: 20px;
            color: white;
            font-weight: 400;
            font-family: "Poppins", Arial, sans-serif;
            text-transform: uppercase;
            margin-top: 10px; }
      .body-page.page-v3 .content .v3-icon a {
        display: inline-block;
        border: 2px solid #C4C4C4;
        color: #C4C4C4;
        font-size: 16px;
        height: 30px;
        width: 30px;
        line-height: 26px;
        text-align: center;
        margin: 3px 7px 0 7px;
        -webkit-border-radius: 100%;
        -moz-border-radius: 100%;
        border-radius: 100%;
        -webkit-transition: all .3s ease;
        -moz-transition: all .3s ease;
        -ms-transition: all .3s ease;
        -o-transition: all .3s ease; }
        .body-page.page-v3 .content .v3-icon a:hover {
          border: 2px solid #fff;
          color: #fff; }
  .body-page .content {
    padding-top: 287px;
    padding-bottom: 340px;
    max-width: 580px;
    margin: 0 auto; }
    @media (max-width: 480px) {
      .body-page .content {
        padding-top: 110px;
        padding-bottom: 60px; } }
    .body-page .content .form-group {
      position: relative;
      width: auto;
      display: inline-block; }
      @media (max-width: 480px) {
        .body-page .content .form-group {
          display: block; } }
    .body-page .content .no-padding {
      padding: 0; }
    .body-page .content .page404 {
      font-size: 178px;
      font-weight: 300;
      letter-spacing: 6px;
      color: #FFF; }
    .body-page .content .h3-404 {
      font-size: 27px;
      font-weight: 400;
      margin-top: -30px;
      margin-bottom: 37px;
      color: #FFF; }
    .body-page .content .p-404 {
      color: #cccccc;
      font-size: 18px; }
      .body-page .content .p-404.size {
        font-size: 16px; }
      .body-page .content .p-404 a {
        color: #fff;
        font-weight: 600; }
      .body-page .content .p-404 a:hover {
        text-decoration: none;
        color: #8E7037; }
    .body-page .content .navbar-form {
      margin-top: 43px;
      margin-bottom: 20px;
      border: none;
      -webkit-box-shadow: none;
      box-shadow: none; }
      @media (max-width: 480px) {
        .body-page .content .navbar-form {
          margin-top: 15px; } }
      .body-page .content .navbar-form .form-control {
        width: 372px;
        height: 42px;
        padding-left: 20px;
        border-radius: 0px;
        background: transparent;
        border: 2px solid white;
        color: #FFF; }
        .body-page .content .navbar-form .form-control::placeholder {
          color: white;
          font-size: 14px;
          font-weight: 400;
          font-family: "Poppins", Arial, sans-serif; }
      .body-page .content .navbar-form .btn {
        width: 124px;
        height: 42px;
        -webkit-border-radius: 0px;
        border-radius: 0px;
        background: transparent;
        border: 2px solid white;
        color: #FFF;
        font-weight: 600;
        letter-spacing: 1px;
        margin-left: 10px;
        box-shadow: none; }
        @media (max-width: 480px) {
          .body-page .content .navbar-form .btn {
            margin-left: 0 !important;
            margin-top: 10px; } }
        .body-page .content .navbar-form .btn:hover {
          background: #8e7037;
          border: 1px solid #8e7037; }

/*2.4.11 BANNER-TEMS*/
.banner-tems {
  padding-top: 160px;
  padding-bottom: 40px;
  background: url("../images/Reservation/header.jpg") no-repeat;
  background-size: cover;
  background-position: center; }
  .banner-tems.bg-restaurants {
    background: url("../images/Restaurants/Restaurants.jpg") no-repeat;
    background-size: cover;
    background-position: center; }
  .banner-tems .banner-content h2 {
    font-size: 36px;
    font-weight: 700;
    color: white;
    text-transform: uppercase;
    font-family: 'Poppins', sans-serif;
    letter-spacing: 2px; }
  .banner-tems .banner-content p {
    font-size: 16px;
    color: white;
    font-weight: 300; }

/* 2.4.12 BODY-TEMS*/
.body-tems {
  padding-bottom: 69px;
  margin-top: 78px; }
  @media (max-width: 767px) {
    .body-tems {
      padding-bottom: 40px;
      margin-top: 55px; } }
  .body-tems .wrap-content {
    color: #333;
    margin-top: -72px; }
    .body-tems .wrap-content .title-h3 {
      font-size: 24px;
      font-weight: 700;
      text-transform: uppercase;
      margin-top: 38px;
      margin-bottom: 23px; }
      @media (max-width: 767px) {
        .body-tems .wrap-content .title-h3 {
          font-size: 20px; } }
    .body-tems .wrap-content .p-tems {
      font-size: 15px;
      margin: 0; }
      @media (max-width: 767px) {
        .body-tems .wrap-content .p-tems {
          font-size: 14px; } }
    .body-tems .wrap-content ul {
      font-size: 15px;
      margin-top: 15px;
      padding-left: 15px; }
      .body-tems .wrap-content ul li {
        padding-left: 18px; }
    .body-tems .wrap-content h6 {
      font-size: 16px;
      font-weight: 700;
      margin-top: 25px;
      margin-bottom: 15px; }

/*2.4.13 BODY-ROOM */
.body-room-1 {
  padding-bottom: 85px; }
  @media (max-width: 767px) {
    .body-room-1 {
      padding-bottom: 40px; } }
  .body-room-1 .room-wrap-1 .room-item-1 {
    position: relative;
    margin-top: 45px; }
    @media (max-width: 767px) {
      .body-room-1 .room-wrap-1 .room-item-1 {
        margin-top: 27px; } }
    .body-room-1 .room-wrap-1 .room-item-1 h2 {
      text-transform: uppercase;
      font-size: 26px;
      font-weight: bold; }
      @media (max-width: 767px) {
        .body-room-1 .room-wrap-1 .room-item-1 h2 {
          font-size: 20px; } }
      .body-room-1 .room-wrap-1 .room-item-1 h2 a {
        color: #333; }
        .body-room-1 .room-wrap-1 .room-item-1 h2 a:hover {
          color: #8E7037;
          text-decoration: none; }
    .body-room-1 .room-wrap-1 .room-item-1 .img {
      position: relative;
      margin-top: 25px;
      overflow: hidden; }
      .body-room-1 .room-wrap-1 .room-item-1 .img img {
        width: 100%;
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out; }
    .body-room-1 .room-wrap-1 .room-item-1 .content {
      color: #333;
      font-weight: 400;
      font-size: 14px;
      margin-top: 15px; }
      .body-room-1 .room-wrap-1 .room-item-1 .content p {
        margin-bottom: 0;
        font-weight: 400;
        line-height: 1.5em;
        font-size: 14px; }
      .body-room-1 .room-wrap-1 .room-item-1 .content ul {
        font-size: 0;
        padding-left: 0;
        margin-bottom: 0;
        margin-top: 15px; }
        .body-room-1 .room-wrap-1 .room-item-1 .content ul li {
          position: relative;
          padding-left: 20px;
          font-size: 14px;
          width: calc(50% - 14%);
          display: inline-block;
          vertical-align: top;
          margin: 3px 0;
          font-weight: 400;
          color: #232323; }
          .body-room-1 .room-wrap-1 .room-item-1 .content ul li:before {
            width: 5px;
            height: 5px;
            background-color: #232323;
            content: '';
            position: absolute;
            top: 8px;
            left: 0;
            -webkit-border-radius: 100%;
            border-radius: 100%; }
    .body-room-1 .room-wrap-1 .room-item-1 .bottom {
      border-top: 1px solid #e4e4e4;
      border-bottom: 1px solid #e4e4e4;
      overflow: hidden;
      padding: 10px 0;
      margin-top: 20px; }
      .body-room-1 .room-wrap-1 .room-item-1 .bottom .price {
        display: inline-block;
        color: #898989;
        font-size: 14px;
        line-height: 1; }
        .body-room-1 .room-wrap-1 .room-item-1 .bottom .price .amout {
          color: #333333;
          font-weight: bold;
          font-size: 26px;
          margin-left: 5px; }
      .body-room-1 .room-wrap-1 .room-item-1 .bottom .btn {
        float: right;
        color: #FFF;
        font-size: 12px;
        width: 127px;
        height: 38px;
        background: #8E7037;
        line-height: 25px;
        border-radius: 0px;
        font-weight: 600; }
        .body-room-1 .room-wrap-1 .room-item-1 .bottom .btn:hover {
          border: 2px solid #8E7037;
          background: transparent;
          color: #8E7037; }
    .body-room-1 .room-wrap-1 .room-item-1:hover .img img {
      transform: scale(1.1);
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out; }

.body-room-2 {
  padding: 50px 0px; }
  @media (max-width: 767px) {
    .body-room-2 {
      padding: 30px 0px; } }
  .body-room-2 .wrap-room2 {
    margin-bottom: 100px; }
    .body-room-2 .wrap-room2 .img:after,
    .body-room-2 .wrap-room2 .img1:after {
      bottom: 0;
      left: 0;
      right: 0;
      top: 50%;
      position: absolute;
      content: '';
      opacity: 0;
      pointer-events: none;
      background-color: transparent;
      background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(#1e1818));
      background: -webkit-linear-gradient(top, transparent, #1e1818);
      background: -moz-linear-gradient(top, transparent, #1e1818);
      background: -ms-linear-gradient(top, transparent, #1e1818);
      background: -o-linear-gradient(top, transparent, #1e1818);
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out;
      overflow: hidden; }
    .body-room-2 .wrap-room2:hover .img:after,
    .body-room-2 .wrap-room2:hover .img1:after {
      opacity: 0.7;
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out;
      overflow: visible; }
    .body-room-2 .wrap-room2:last-child {
      margin-bottom: 0; }
    .body-room-2 .wrap-room2 .img {
      position: relative; }
      .body-room-2 .wrap-room2 .img:before {
        content: '';
        display: block;
        position: absolute;
        width: 40px;
        top: 37px;
        bottom: 43px;
        right: 0px;
        background-color: #fff;
        z-index: 2;
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out; }
      .body-room-2 .wrap-room2 .img img {
        width: 100%; }
    .body-room-2 .wrap-room2 .img1 {
      position: relative; }
      .body-room-2 .wrap-room2 .img1:before {
        content: '';
        display: block;
        position: absolute;
        width: 40px;
        top: 37px;
        bottom: 43px;
        background-color: #fff;
        z-index: 2;
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out; }
      .body-room-2 .wrap-room2 .img1 img {
        width: 100%; }
    .body-room-2 .wrap-room2 .text {
      padding-top: 36px; }
      .body-room-2 .wrap-room2 .text h2 {
        font-size: 36px; }
        .body-room-2 .wrap-room2 .text h2:hover {
          color: #8E7037; }
      .body-room-2 .wrap-room2 .text h5 {
        margin-bottom: 35px;
        margin-top: 20px; }
      .body-room-2 .wrap-room2 .text p {
        color: #898989;
        font-size: 15px; }
        .body-room-2 .wrap-room2 .text p b {
          color: #333333;
          font-weight: 600;
          font-size: 15px; }
      .body-room-2 .wrap-room2 .text .view-dateails {
        margin-top: 20px;
        width: 118px; }
        @media (max-width: 767px) {
          .body-room-2 .wrap-room2 .text .view-dateails {
            margin-top: 10px; } }

.body-room-3 .wrap-room-3 {
  padding-bottom: 40px;
  padding-top: 10px; }
  @media (max-width: 767px) {
    .body-room-3 .wrap-room-3 {
      padding: 0;
      padding-bottom: 40px; } }
  .body-room-3 .wrap-room-3 .wrap-item {
    margin-top: 40px; }
    .body-room-3 .wrap-room-3 .wrap-item:hover .img1,
    .body-room-3 .wrap-room-3 .wrap-item:hover .img2 {
      transform: scale(1.2, 1.2);
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out; }
    .body-room-3 .wrap-room-3 .wrap-item:hover .img:before {
      opacity: 0.7;
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out;
      overflow: visible; }
    @media (max-width: 480px) {
      .body-room-3 .wrap-room-3 .wrap-item {
        margin-top: 30px; } }
    .body-room-3 .wrap-room-3 .wrap-item .img {
      position: relative; }
      .body-room-3 .wrap-room-3 .wrap-item .img:before {
        bottom: 0;
        left: 0;
        right: 0;
        top: 50%;
        position: absolute;
        content: '';
        opacity: 0;
        pointer-events: none;
        background-color: transparent;
        background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(#1e1818));
        background: -webkit-linear-gradient(top, transparent, #1e1818);
        background: -moz-linear-gradient(top, transparent, #1e1818);
        background: -ms-linear-gradient(top, transparent, #1e1818);
        background: -o-linear-gradient(top, transparent, #1e1818);
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
        overflow: hidden; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 {
      margin-left: 69px;
      margin-right: 69px;
      position: relative; }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img1 {
        float: left;
        margin-top: 60px;
        margin-right: 70px;
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img2 {
        float: right;
        margin-top: 60px;
        margin-left: 70px;
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text {
        overflow: hidden;
        padding: 8px 20px 0 27px;
        background-color: #fff;
        position: relative;
        -webkit-transform: translateY(-60px);
        -moz-transform: translateY(-60px);
        -ms-transform: translateY(-60px);
        -o-transform: translateY(-60px);
        width: 530px;
        float: left; }
        .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text .line {
          width: 70px;
          margin-top: 80px;
          height: 2px;
          background: #333; }
        .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text .view-dateails {
          margin-top: 25px;
          width: 119px;
          position: relative; }
          @media (max-width: 767px) {
            .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text .view-dateails {
              margin-top: 10px; } }
        .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text h2 {
          font-size: 36px; }
          .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text h2:hover {
            color: #8E7037; }
        .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text h5 {
          font-size: 14px;
          margin-top: 10px;
          margin-bottom: 33px; }
        .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text p {
          color: #898989;
          font-size: 15px;
          font-weight: 400; }
          .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text p b {
            font-weight: 600;
            color: #333; }

.body-room-4 {
  padding-top: 50px;
  padding-bottom: 95px; }
  @media (max-width: 767px) {
    .body-room-4 {
      padding-top: 30px;
      padding-bottom: 40px; } }
  .body-room-4 .wrap-room-4 .wrap-item {
    margin-bottom: 100px; }
    .body-room-4 .wrap-room-4 .wrap-item .img1 {
      position: relative;
      overflow: hidden; }
      .body-room-4 .wrap-room-4 .wrap-item .img1 img {
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
    .body-room-4 .wrap-room-4 .wrap-item:hover .img1 {
      overflow: hidden; }
      .body-room-4 .wrap-room-4 .wrap-item:hover .img1 img {
        transform: scale(1.2, 1.2);
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
    .body-room-4 .wrap-room-4 .wrap-item:hover .img:before {
      opacity: 0.7;
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out;
      overflow: visible; }
    .body-room-4 .wrap-room-4 .wrap-item .img {
      position: relative; }
      .body-room-4 .wrap-room-4 .wrap-item .img:before {
        bottom: 0;
        left: 0;
        right: 0;
        top: 50%;
        position: absolute;
        content: '';
        opacity: 0;
        pointer-events: none;
        background-color: transparent;
        background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(#1e1818));
        background: -webkit-linear-gradient(top, transparent, #1e1818);
        background: -moz-linear-gradient(top, transparent, #1e1818);
        background: -ms-linear-gradient(top, transparent, #1e1818);
        background: -o-linear-gradient(top, transparent, #1e1818);
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
        overflow: hidden; }
    .body-room-4 .wrap-room-4 .wrap-item:last-child {
      margin-bottom: 0px; }
    .body-room-4 .wrap-room-4 .wrap-item .text-room-4 {
      padding-left: 0px;
      margin-left: -30px; }
      .body-room-4 .wrap-room-4 .wrap-item .text-room-4.room-right {
        padding-right: 0px;
        margin-right: -30px;
        margin-left: 0px; }
      .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text {
        padding: 25px 60px 85px 100px; }
        .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text .view-dateails {
          margin-top: 15px;
          width: 119px;
          position: relative; }
        .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text h2 {
          font-size: 36px; }
        .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text h5 {
          font-size: 14px;
          margin-top: 20px;
          margin-bottom: 33px; }
        .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text p {
          color: #898989;
          font-size: 15px;
          font-weight: 400; }
          .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text p b {
            font-weight: 600;
            color: #333; }

.body-room-5 {
  padding-top: 50px;
  padding-bottom: 35px; }
  @media (max-width: 767px) {
    .body-room-5 {
      padding-top: 30px;
      padding-bottom: 0px; } }
  .body-room-5 .wrap-room-5 .wrap-item {
    position: relative;
    overflow: hidden;
    display: block;
    margin-bottom: 30px;
    background-position: 50% 50%;
    background-size: cover;
    background-repeat: no-repeat; }
    .body-room-5 .wrap-room-5 .wrap-item:before {
      position: absolute;
      pointer-events: none;
      content: '';
      z-index: 0;
      top: 50%;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(21, 21, 21, 0);
      background: -webkit-gradient(linear, left top, left bottom, from(rgba(21, 21, 21, 0)), to(rgba(21, 21, 21, 0.7)));
      background: -webkit-linear-gradient(top, rgba(21, 21, 21, 0), rgba(21, 21, 21, 0.7));
      background: -moz-linear-gradient(top, rgba(21, 21, 21, 0), rgba(21, 21, 21, 0.7));
      background: -ms-linear-gradient(top, rgba(21, 21, 21, 0), rgba(21, 21, 21, 0.7));
      background: -o-linear-gradient(top, rgba(21, 21, 21, 0), rgba(21, 21, 21, 0.7)); }
    .body-room-5 .wrap-room-5 .wrap-item:hover .text-hover {
      position: absolute;
      top: 0px;
      background: rgba(255, 255, 255, 0.94);
      width: 100%;
      height: 100%;
      opacity: 1;
      border: 2px solid #3d3b38;
      display: visible;
      transform: scale(1, 1);
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out;
      right: 0;
      left: 0;
      bottom: 0; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 {
      position: absolute;
      bottom: 28px;
      left: 50px; }
      .body-room-5 .wrap-room-5 .wrap-item .text-room-5 h2 {
        color: #ffffff; }
      .body-room-5 .wrap-room-5 .wrap-item .text-room-5 h5 {
        color: #ffffff;
        font-size: 14px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-hover {
      display: hidden;
      opacity: 0;
      position: absolute;
      top: 0px;
      right: 0;
      transform: scale(0.9, 0.9);
      border: 2px solid #3d3b38;
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out; }
      .body-room-5 .wrap-room-5 .wrap-item .text-hover .text {
        padding: 16px 47px; }
        .body-room-5 .wrap-room-5 .wrap-item .text-hover .text .view-dateails {
          margin-top: 15px;
          width: 119px; }
          .body-room-5 .wrap-room-5 .wrap-item .text-hover .text .view-dateails:hover {
            background: #FFF; }
        .body-room-5 .wrap-room-5 .wrap-item .text-hover .text h2 {
          font-size: 30px; }
        .body-room-5 .wrap-room-5 .wrap-item .text-hover .text h5 {
          font-size: 14px;
          margin-top: 10px;
          margin-bottom: 33px; }
        .body-room-5 .wrap-room-5 .wrap-item .text-hover .text p {
          color: #898989;
          font-size: 15px;
          font-weight: 400; }
          .body-room-5 .wrap-room-5 .wrap-item .text-hover .text p b {
            font-weight: 600;
            color: #333; }

.body-room-6 {
  padding-top: 50px;
  padding-bottom: 25px; }
  @media (max-width: 767px) {
    .body-room-6 {
      padding-top: 30px;
      padding-bottom: 0px; } }
  .body-room-6 .wrap-room-6 .wrap-item {
    position: relative;
    overflow: hidden;
    display: block;
    margin-bottom: 30px;
    background-position: 50% 50%;
    background-size: cover;
    background-repeat: no-repeat;
    overflow: hidden; }
    .body-room-6 .wrap-room-6 .wrap-item img {
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out; }
    .body-room-6 .wrap-room-6 .wrap-item .img:before {
      content: "";
      position: absolute;
      background: -webkit-gradient(linear, left top, left bottom, from(rgba(21, 21, 21, 0)), to(rgba(21, 21, 21, 0.7)));
      background: -webkit-linear-gradient(top, rgba(21, 21, 21, 0), rgba(21, 21, 21, 0.7));
      background: -moz-linear-gradient(top, rgba(21, 21, 21, 0), rgba(21, 21, 21, 0.7));
      background: -ms-linear-gradient(top, rgba(21, 21, 21, 0), rgba(21, 21, 21, 0.7));
      background: -o-linear-gradient(top, rgba(21, 21, 21, 0), rgba(21, 21, 21, 0.7));
      height: 100%;
      width: 100%;
      z-index: 1;
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out;
      opacity: 0;
      overflow: hidden; }
    .body-room-6 .wrap-room-6 .wrap-item:hover .img {
      position: relative;
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out; }
      .body-room-6 .wrap-room-6 .wrap-item:hover .img img {
        transform: scale(1.1, 1.1);
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out;
        background-color: rgba(21, 21, 21, 0);
        opacity: 0.9; }
      .body-room-6 .wrap-room-6 .wrap-item:hover .img:before {
        height: 100%;
        width: 100%;
        opacity: 1;
        overflow: visible;
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
    .body-room-6 .wrap-room-6 .wrap-item .text {
      position: absolute;
      top: 0;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      opacity: 0.9;
      left: 0px;
      max-width: 470px;
      padding: 12px 30px 30px 30px;
      z-index: 2; }
      .body-room-6 .wrap-room-6 .wrap-item .text .view-dateails {
        margin-top: 15px;
        width: 119px; }
        .body-room-6 .wrap-room-6 .wrap-item .text .view-dateails:focus {
          color: #ffffff; }
        .body-room-6 .wrap-room-6 .wrap-item .text .view-dateails:hover {
          background: #FFF;
          color: #8E7037; }
      .body-room-6 .wrap-room-6 .wrap-item .text h2 {
        font-size: 32px;
        color: #ffffff; }
      .body-room-6 .wrap-room-6 .wrap-item .text h5 {
        font-size: 14px;
        margin-top: 10px;
        margin-bottom: 22px;
        color: #8E7037; }
      .body-room-6 .wrap-room-6 .wrap-item .text p {
        color: #ffffff;
        font-size: 15px;
        font-weight: 400; }
        .body-room-6 .wrap-room-6 .wrap-item .text p b {
          font-weight: 600;
          color: #ffffff; }
      .body-room-6 .wrap-room-6 .wrap-item .text ul {
        padding-left: 14px;
        margin-top: 17px; }
        .body-room-6 .wrap-room-6 .wrap-item .text ul li {
          font-size: 14px;
          color: #ffffff;
          font-weight: 500;
          line-height: 1.9;
          padding-left: 10px; }
    .body-room-6 .wrap-room-6 .wrap-item .text-1 {
      position: absolute;
      top: 0;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      opacity: 0.9;
      right: 0px;
      max-width: 470px;
      padding: 12px 30px 30px 30px;
      z-index: 2; }
      .body-room-6 .wrap-room-6 .wrap-item .text-1 .view-dateails {
        margin-top: 15px;
        width: 119px; }
        .body-room-6 .wrap-room-6 .wrap-item .text-1 .view-dateails:focus {
          color: #ffffff; }
        .body-room-6 .wrap-room-6 .wrap-item .text-1 .view-dateails:hover {
          background: #FFF;
          color: #8E7037; }
      .body-room-6 .wrap-room-6 .wrap-item .text-1 h2 {
        font-size: 32px;
        color: #ffffff; }
      .body-room-6 .wrap-room-6 .wrap-item .text-1 h5 {
        font-size: 14px;
        margin-top: 10px;
        margin-bottom: 22px;
        color: #8E7037; }
      .body-room-6 .wrap-room-6 .wrap-item .text-1 p {
        color: #ffffff;
        font-size: 15px;
        font-weight: 400; }
        .body-room-6 .wrap-room-6 .wrap-item .text-1 p b {
          font-weight: 600;
          color: #ffffff; }
      .body-room-6 .wrap-room-6 .wrap-item .text-1 ul {
        padding-left: 14px;
        margin-top: 17px; }
        .body-room-6 .wrap-room-6 .wrap-item .text-1 ul li {
          font-size: 14px;
          color: #ffffff;
          font-weight: 500;
          line-height: 1.9;
          padding-left: 10px; }

/* 2.4.14 CHECK-OUT  */
.check-out {
  padding-bottom: 60px;
  padding-top: 38px; }
  .check-out h2 {
    font-weight: 700; }
    @media (max-width: 767px) {
      .check-out h2 {
        font-size: 20px; } }
  @media (max-width: 767px) {
    .check-out {
      padding-bottom: 60px; } }
  .check-out .check-p {
    color: #898989;
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 17px; }
    .check-out .check-p a {
      color: #232323;
      font-weight: 600; }
      .check-out .check-p a:hover {
        text-decoration: none; }
  .check-out .check-left label {
    margin-bottom: 7px;
    color: #232323;
    font-weight: 500; }
  .check-out .check-left a {
    line-height: 26px !important;
    text-decoration: none; }
    .check-out .check-left a:hover {
      text-decoration: none; }
  .check-out .check-left .color {
    color: #232323 !important;
    font-size: 14px; }
  .check-out .check-left .ion-chevron-down {
    color: #333333;
    float: right; }
  .check-out .check-left .dropdown-menu {
    z-index: 1;
    width: 100%;
    border-radius: 0px;
    padding: 10px 0px;
    border: 2px solid white;
    cursor: pointer; }
    .check-out .check-left .dropdown-menu .active {
      background: #8e7037;
      color: #FFF; }
    .check-out .check-left .dropdown-menu li {
      padding: 7px 13px; }
      .check-out .check-left .dropdown-menu li:hover {
        background: #8e7037;
        color: #FFF; }
  .check-out .check-left .form-group span {
    color: #ff0000; }
  .check-out .check-left .form-group textarea {
    height: 85px; }
  .check-out .check-left .form-control {
    border: 2px solid #333333;
    border-radius: 0px;
    height: 42px;
    line-height: 37px;
    margin-bottom: 15px; }
    .check-out .check-left .form-control::placeholder {
      color: #232323;
      font-weight: 400;
      font-size: 14px; }
  .check-out .check-left p {
    color: #000;
    font-size: 16px;
    margin-top: 15px;
    margin-bottom: 30px; }
  .check-out .check-left .click {
    color: #898989;
    font-size: 14px;
    font-weight: 400;
    height: auto; }
    .check-out .check-left .click a {
      color: #232323;
      font-weight: 600; }
  .check-out .check-left .b {
    margin-bottom: 22px;
    padding-left: 10px; }
    .check-out .check-left .b input {
      margin-top: 6px;
      margin-left: -30px; }
  .check-out .col-lg-offset-1 {
    margin-left: 5.6%; }
  .check-out .col-lg-5 {
    width: 44.3%; }
  .check-out .check-right .checkout_cart {
    margin-bottom: 33px;
    margin-top: 25px;
    border: 2px solid #333333;
    padding: 15px 30px 25px 30px; }
    .check-out .check-right .checkout_cart .cart-item {
      overflow: hidden;
      margin-top: 20px;
      position: relative; }
      .check-out .check-right .checkout_cart .cart-item .img {
        width: 100px;
        float: left;
        position: relative;
        margin-right: 20px; }
        .check-out .check-right .checkout_cart .cart-item .img img {
          width: 100%; }
        .check-out .check-right .checkout_cart .cart-item .img:before {
          bottom: 0;
          left: 0;
          right: 0;
          top: 50%;
          position: absolute;
          content: '';
          opacity: 0;
          z-index: 10;
          pointer-events: none; }
      .check-out .check-right .checkout_cart .cart-item .text {
        margin-right: 15px;
        overflow: hidden; }
        .check-out .check-right .checkout_cart .cart-item .text a {
          font-weight: 600;
          color: #232323;
          font-size: 14px;
          text-transform: uppercase; }
          .check-out .check-right .checkout_cart .cart-item .text a:hover {
            color: #8E7037;
            text-decoration: none; }
        .check-out .check-right .checkout_cart .cart-item .text p {
          margin: 0;
          font-weight: 400;
          font-size: 14px; }
          .check-out .check-right .checkout_cart .cart-item .text p span {
            display: block; }
          .check-out .check-right .checkout_cart .cart-item .text p b {
            font-size: 16px; }
      .check-out .check-right .checkout_cart .cart-item .remove {
        position: absolute;
        top: 0;
        right: 0;
        color: #a7a7a7; }
      .check-out .check-right .checkout_cart .cart-item:hover .img:before {
        opacity: .5;
        top: 0; }
  .check-out .check-right .checkout-cartinfo span {
    display: inline-block;
    min-width: 195px;
    font-weight: 500; }
  .check-out .check-right .checkout-cartinfo p {
    margin-bottom: 15px;
    color: #000;
    font-size: 20px;
    font-weight: 700; }
    @media (max-width: 767px) {
      .check-out .check-right .checkout-cartinfo p {
        font-size: 16px; } }
    .check-out .check-right .checkout-cartinfo p strong {
      color: #EE242A; }
  .check-out .check-right .checkout-option {
    margin-bottom: 32px;
    margin-top: 30px; }
    .check-out .check-right .checkout-option .card {
      display: inline; }
      .check-out .check-right .checkout-option .card .img-responsive {
        display: inline-block; }
    .check-out .check-right .checkout-option .margin-bottom {
      margin-bottom: 22px; }
    .check-out .check-right .checkout-option label {
      font-weight: 500;
      font-size: 15px;
      color: #232323;
      padding-left: 30px; }
      .check-out .check-right .checkout-option label input {
        margin-top: 6px;
        margin-left: -30px; }
      .check-out .check-right .checkout-option label span {
        font-size: 12px;
        font-weight: 400;
        margin-top: 5px;
        display: block; }
      .check-out .check-right .checkout-option label.padding-right {
        padding-right: 20px; }
    .check-out .check-right .checkout-option img {
      padding-left: 6px;
      cursor: pointer; }
  .check-out .check-right .checkout-btn {
    width: 213px;
    height: 54px;
    font-size: 16px;
    color: #ffffff;
    background: #8E7037;
    line-height: 40px;
    border-radius: 0px;
    font-weight: 600; }
    .check-out .check-right .checkout-btn:hover {
      border: 2px solid #8E7037;
      background: transparent;
      color: #8E7037; }

/* 2.4.15 RESTAURANTS */
.body-restaurant {
  padding-bottom: 95px;
  padding-top: 40px; }
  @media (max-width: 767px) {
    .body-restaurant {
      padding: 30px 0; } }
  .body-restaurant .restaurant-lager {
    margin-top: -40px; }
    .body-restaurant .restaurant-lager .restaurant_content {
      margin-top: 40px; }
      .body-restaurant .restaurant-lager .restaurant_content .hr {
        margin-top: 45px;
        height: 1px;
        width: 100%; }
        @media (max-width: 480px) {
          .body-restaurant .restaurant-lager .restaurant_content .hr {
            margin-top: 25px; } }
        .body-restaurant .restaurant-lager .restaurant_content .hr:before {
          width: 50%;
          content: '';
          height: 1px;
          float: left;
          background-color: #red;
          background-image: -webkit-gradient(linear, left top, right top, from(#fff), to(#e3e3e3));
          background-image: -webkit-linear-gradient(left, #fff, #e3e3e3);
          background-image: -moz-linear-gradient(left, #fff, #e3e3e3);
          background-image: -ms-linear-gradient(left, #fff, #e3e3e3);
          background-image: -o-linear-gradient(left, #fff, #e3e3e3); }
        .body-restaurant .restaurant-lager .restaurant_content .hr:after {
          float: right;
          background-color: #red;
          background-image: -webkit-gradient(linear, left top, right top, from(#e3e3e3), to(#fff));
          background-image: -webkit-linear-gradient(left, #e3e3e3, #fff);
          background-image: -moz-linear-gradient(left, #e3e3e3, #fff);
          background-image: -ms-linear-gradient(left, #e3e3e3, #fff);
          background-image: -o-linear-gradient(left, #e3e3e3, #fff);
          width: 50%;
          content: '';
          height: 1px; }
      .body-restaurant .restaurant-lager .restaurant_content .restaurant_title .heading {
        font-size: 36px;
        font-weight: 700;
        color: #232323;
        font-family: "Poppins", sans-serif; }
        @media (max-width: 480px) {
          .body-restaurant .restaurant-lager .restaurant_content .restaurant_title .heading {
            font-size: 25px; } }
      .body-restaurant .restaurant-lager .restaurant_content .restaurant_title .time {
        color: #898989;
        font-size: 18px;
        margin-top: -8px;
        display: block; }
      .body-restaurant .restaurant-lager .restaurant_content .content-restaurant {
        margin-top: 20px; }
        .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item {
          margin-top: 30px; }
          @media (max-width: 767px) {
            .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item {
              margin-top: 5px; } }
          .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item:hover img {
            transform: scale(1.1, 1.1);
            opacity: 0.7;
            -webkit-transition: all 0.5s ease-in-out;
            -moz-transition: all 0.5s ease-in-out;
            -ms-transition: all 0.5s ease-in-out;
            -o-transition: all 0.5s ease-in-out;
            transition: all 0.5s ease-in-out; }
          .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item .img {
            overflow: hidden; }
            .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item .img img {
              -webkit-transition: all 0.5s ease-in-out;
              -moz-transition: all 0.5s ease-in-out;
              -ms-transition: all 0.5s ease-in-out;
              -o-transition: all 0.5s ease-in-out;
              transition: all 0.5s ease-in-out;
              width: 100%; }
          .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item .text h2 {
            font-size: 18px;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 7px; }
            .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item .text h2 a {
              color: #444444; }
              .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item .text h2 a:hover, .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item .text h2 a:focus {
                text-decoration: none;
                color: #8E7037; }
          .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item .text .desc {
            color: #444;
            margin-bottom: 0;
            font-size: 15px;
            margin-top: 5px;
            line-height: 1.4em; }
          .body-restaurant .restaurant-lager .restaurant_content .content-restaurant .restaurant_item .text .price {
            font-size: 30px;
            margin-bottom: 0;
            margin-top: 10px;
            color: #8E7037;
            font-weight: 600; }

.body-restaurant-1 {
  padding-bottom: 30px;
  padding-top: 20px; }
  @media (max-width: 767px) {
    .body-restaurant-1 {
      padding-bottom: 0; } }
  .body-restaurant-1 .restaurant_title .heading {
    font-size: 36px; }
    @media (max-width: 480px) {
      .body-restaurant-1 .restaurant_title .heading {
        font-size: 25px; } }
  .body-restaurant-1 .restaurant_title .time {
    font-size: 18px; }
  .body-restaurant-1 .wrap-content .restaurant_content {
    margin-bottom: 55px; }
    .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item {
      margin-top: 30px;
      overflow: hidden; }
      .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item:hover img {
        transform: scale(1.1, 1.1);
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out;
        opacity: 0.7; }
      .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .img {
        float: left;
        width: 120px;
        margin-right: 30px;
        position: relative;
        overflow: hidden; }
        .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .img img {
          width: 100%;
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          -webkit-transition: all 0.5s ease-in-out;
          -moz-transition: all 0.5s ease-in-out;
          -ms-transition: all 0.5s ease-in-out;
          -o-transition: all 0.5s ease-in-out;
          transition: all 0.5s ease-in-out; }
      .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .text {
        padding-right: 60px;
        position: relative;
        overflow: hidden;
        margin-top: 5px; }
        .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .text h2 {
          font-size: 18px;
          text-transform: uppercase;
          font-weight: 600;
          margin-top: 0; }
          .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .text h2 a {
            color: #444444; }
            .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .text h2 a:hover, .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .text h2 a:focus {
              text-decoration: none;
              color: #8E7037; }
        .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .text .desc {
          color: #444;
          margin-bottom: 0;
          font-size: 15px;
          margin-top: 5px;
          line-height: 1.4em; }
        .body-restaurant-1 .wrap-content .restaurant_content .restaurant_item .text .price {
          text-align: right;
          font-size: 18px;
          line-height: 1.428em;
          position: absolute;
          top: 0;
          right: 0;
          margin-bottom: 0;
          line-height: 1;
          color: #8E7037;
          font-size: 18px;
          font-weight: 600; }

.body-restaurant-2 {
  text-align: center;
  padding-top: 45px;
  padding-bottom: 50px; }
  @media (max-width: 767px) {
    .body-restaurant-2 {
      padding-bottom: 0; } }
  .body-restaurant-2 .restaurant_item .wrap_item:hover img {
    transform: scale(1.1, 1.1);
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    opacity: 0.8; }
  .body-restaurant-2 .restaurant_item .img {
    float: left;
    width: 110px;
    margin-right: 30px;
    position: relative;
    overflow: hidden; }
    .body-restaurant-2 .restaurant_item .img img {
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out;
      overflow: hidden;
      width: 100%; }
    .body-restaurant-2 .restaurant_item .img .sales {
      font-family: 'Montserrat', sans-serif;
      font-size: 12px;
      position: absolute;
      top: 0;
      background: #f9b82c;
      color: #ffffff;
      padding: 5px 7px;
      left: 0; }
  .body-restaurant-2 .restaurant_item .text {
    padding-right: 60px;
    position: relative;
    overflow: hidden;
    margin-top: 5px;
    margin-bottom: 32px; }
    .body-restaurant-2 .restaurant_item .text h2 {
      font-size: 18px;
      text-transform: uppercase;
      font-weight: 600;
      margin-top: 0; }
      .body-restaurant-2 .restaurant_item .text h2 a {
        color: #444444; }
        .body-restaurant-2 .restaurant_item .text h2 a:hover, .body-restaurant-2 .restaurant_item .text h2 a:focus {
          text-decoration: none;
          color: #8E7037; }
    .body-restaurant-2 .restaurant_item .text .desc {
      color: #444;
      margin-bottom: 0;
      font-size: 15px;
      margin-top: 5px;
      line-height: 1.4em; }
    .body-restaurant-2 .restaurant_item .text .price {
      text-align: right;
      font-size: 18px;
      line-height: 1.428em;
      position: absolute;
      top: 0;
      right: 0px;
      margin-bottom: 0;
      line-height: 1;
      color: #8E7037;
      font-size: 18px;
      font-weight: 600; }
      .body-restaurant-2 .restaurant_item .text .price span {
        display: block; }
      .body-restaurant-2 .restaurant_item .text .price .del {
        margin-top: 9px;
        color: #999;
        text-decoration: line-through; }
  .body-restaurant-2 .time, .body-restaurant-2 .body-restaurant-1 .restaurant_title .time, .body-restaurant-1 .restaurant_title .body-restaurant-2 .time {
    margin-top: 4px;
    font-size: 16px;
    font-weight: 400; }
  .body-restaurant-2 .product {
    text-align: initial; }
  .body-restaurant-2 .shape {
    width: 100%; }
  .body-restaurant-2 .nav-tabs {
    display: block;
    border-bottom: 2px solid #e7e7e7;
    margin-bottom: 25px;
    margin-top: 10px; }
    .body-restaurant-2 .nav-tabs li {
      width: 25%; }
      .body-restaurant-2 .nav-tabs li.active > a {
        color: #8e7037;
        background: transparent;
        border: none;
        position: relative; }
        .body-restaurant-2 .nav-tabs li.active > a:before {
          content: "";
          position: absolute;
          background: 0;
          outline: none;
          color: #8e7037;
          border-bottom: 2px solid #8e7037;
          border-top: transparent;
          border-left: transparent;
          border-right: transparent;
          border-bottom: 2px solid #8e7037;
          -webkit-transition: all 0.5s ease-in-out;
          -moz-transition: all 0.5s ease-in-out;
          -ms-transition: all 0.5s ease-in-out;
          -o-transition: all 0.5s ease-in-out;
          transition: all 0.5s ease-in-out;
          width: 100%;
          bottom: -1px;
          left: 0;
          opacity: 1;
          overflow: visible; }
        .body-restaurant-2 .nav-tabs li.active > a:hover, .body-restaurant-2 .nav-tabs li.active > a:focus {
          background: 0;
          outline: none;
          color: #8e7037;
          border-top: transparent;
          border-left: transparent;
          border-right: transparent; }
          .body-restaurant-2 .nav-tabs li.active > a:hover:before, .body-restaurant-2 .nav-tabs li.active > a:focus:before {
            -webkit-transition: all 0.5s ease-in-out;
            -moz-transition: all 0.5s ease-in-out;
            -ms-transition: all 0.5s ease-in-out;
            -o-transition: all 0.5s ease-in-out;
            transition: all 0.5s ease-in-out;
            width: 100%;
            opacity: 1;
            overflow: visible; }
      .body-restaurant-2 .nav-tabs li a {
        color: #232323;
        font-size: 23px;
        font-family: 'Poppins', sans-serif;
        font-weight: 700;
        -webkit-border-radius: 0px;
        border-radius: 0px;
        padding: 6px 13px;
        border: none;
        margin: 0;
        padding-bottom: 23px;
        position: relative; }
        .body-restaurant-2 .nav-tabs li a:before {
          content: "";
          position: absolute;
          background: 0;
          outline: none;
          color: #8e7037;
          border-bottom: 2px solid #8e7037;
          border-top: transparent;
          border-left: transparent;
          border-right: transparent;
          border-bottom: 2px solid #8e7037;
          -webkit-transition: all 0.5s ease-in-out;
          -moz-transition: all 0.5s ease-in-out;
          -ms-transition: all 0.5s ease-in-out;
          -o-transition: all 0.5s ease-in-out;
          transition: all 0.5s ease-in-out;
          overflow: hidden;
          opacity: -1;
          width: 0%;
          bottom: -1px;
          left: 0; }
        .body-restaurant-2 .nav-tabs li a:hover, .body-restaurant-2 .nav-tabs li a:focus {
          background: 0;
          outline: none;
          color: #8e7037;
          border-top: transparent;
          border-left: transparent;
          border-right: transparent; }
          .body-restaurant-2 .nav-tabs li a:hover:before, .body-restaurant-2 .nav-tabs li a:focus:before {
            -webkit-transition: all 0.5s ease-in-out;
            -moz-transition: all 0.5s ease-in-out;
            -ms-transition: all 0.5s ease-in-out;
            -o-transition: all 0.5s ease-in-out;
            transition: all 0.5s ease-in-out;
            width: 100%;
            opacity: 1;
            overflow: visible; }

.body-restaurant-3 {
  background: url("../images/Restaurants/Restaurants-47.jpg") no-repeat center center;
  padding: 50px 0px;
  background-size: cover; }
  .body-restaurant-3 .reservation {
    position: relative;
    z-index: 9;
    max-width: 770px;
    margin-left: auto;
    margin-right: auto;
    padding: 25px 70px; }
    .body-restaurant-3 .reservation:after {
      content: '';
      display: block;
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.87);
      opacity: .9;
      top: 0;
      left: 0;
      z-index: -1; }
    .body-restaurant-3 .reservation h2 {
      text-transform: uppercase;
      color: #fff;
      font-weight: bold;
      margin: 0;
      line-height: 1;
      text-align: center; }
    .body-restaurant-3 .reservation .reservation_form {
      margin-top: 40px; }
      .body-restaurant-3 .reservation .reservation_form .form-control {
        -webkit-border-radius: 0px;
        border-radius: 0px; }
        .body-restaurant-3 .reservation .reservation_form .form-control.wrap-box {
          height: 42px;
          width: 100%;
          background: transparent;
          border: 2px solid white;
          border-right: none;
          color: #FFF;
          font-size: 12px;
          font-weight: 400;
          -webkit-box-shadow: none;
          box-shadow: none;
          padding-right: 0; }
          .body-restaurant-3 .reservation .reservation_form .form-control.wrap-box::placeholder {
            color: #fff; }
      .body-restaurant-3 .reservation .reservation_form .input-group-addon {
        border-radius: 0px;
        background: transparent;
        color: white;
        border: 2px solid white;
        border-left: none;
        padding-left: 0; }
      .body-restaurant-3 .reservation .reservation_form .awe-input {
        border: 2px solid #fff;
        background-color: transparent;
        width: 100%;
        color: #fff;
        height: 42px;
        padding-left: 14px;
        padding-right: 14px;
        font-weight: 400; }
        .body-restaurant-3 .reservation .reservation_form .awe-input::placeholder {
          font-size: 12px;
          font-weight: 400;
          color: #fff; }
      .body-restaurant-3 .reservation .reservation_form .dropdown .form-control {
        color: #FFF;
        height: 42px;
        background: transparent;
        border: 2px solid white;
        border-radius: 0;
        font-size: 12px;
        line-height: 27px;
        text-decoration: none; }
        .body-restaurant-3 .reservation .reservation_form .dropdown .form-control .color {
          color: #fff; }
      .body-restaurant-3 .reservation .reservation_form .dropdown .dropdown-menu {
        border: none;
        min-width: 100%;
        border-radius: 0px; }
        .body-restaurant-3 .reservation .reservation_form .dropdown .dropdown-menu li {
          padding: 5px 15px; }
          .body-restaurant-3 .reservation .reservation_form .dropdown .dropdown-menu li:hover {
            background: #8E7037;
            color: #FFF; }
      .body-restaurant-3 .reservation .reservation_form .dropdown .ion-chevron-down {
        float: right;
        color: #FFF; }
      .body-restaurant-3 .reservation .reservation_form .awe-teaxtarea {
        margin-top: 20px;
        background-color: transparent;
        width: 100%;
        border: 2px solid #fff;
        color: #fff;
        height: 135px;
        padding: 14px;
        padding-top: 10px;
        vertical-align: middle;
        resize: none; }
        .body-restaurant-3 .reservation .reservation_form .awe-teaxtarea::placeholder {
          color: #fff; }
      .body-restaurant-3 .reservation .reservation_form .btn {
        width: 189px;
        height: 42px;
        font-size: 16px;
        margin-top: 20px; }
        .body-restaurant-3 .reservation .reservation_form .btn:hover {
          background: #fff; }

/*  2.4.16 CONTACT */
.section-contact {
  padding-top: 30px;
  padding-bottom: 65px; }
  @media (max-width: 767px) {
    .section-contact {
      padding-top: 0px;
      padding-bottom: 40px; } }
  .section-contact .contact .text {
    margin-top: 15px; }
    .section-contact .contact .text h2 {
      text-transform: uppercase;
      font-weight: bold;
      font-size: 28px;
      color: #232323; }
      @media (max-width: 767px) {
        .section-contact .contact .text h2 {
          font-size: 20px; } }
    .section-contact .contact .text p {
      margin-top: 30px;
      font-size: 15px;
      line-height: 1.428em; }
      @media (max-width: 767px) {
        .section-contact .contact .text p {
          font-size: 14px;
          margin-top: 15px; } }
    .section-contact .contact .text ul {
      list-style: none;
      padding-left: 0;
      margin-bottom: 0;
      margin-top: 30px; }
      .section-contact .contact .text ul li {
        padding: 4px 0; }
        .section-contact .contact .text ul li .fa {
          color: #8e7037;
          font-size: 17px;
          width: 8%; }
  .section-contact .contact .contact-form .field-textarea {
    padding-top: 7px; }
  .section-contact .contact .contact-form .field-text,
  .section-contact .contact .contact-form .field-textarea {
    height: 42px;
    border: 2px solid #232323;
    width: 100%;
    margin-top: 15px;
    padding-left: 15px;
    padding-right: 15px; }
    .section-contact .contact .contact-form .field-text::placeholder,
    .section-contact .contact .contact-form .field-textarea::placeholder {
      color: #232323;
      font-size: 14px;
      font-weight: 400;
      font-family: "Poppins", sans-serif; }
    .section-contact .contact .contact-form .field-text:focus,
    .section-contact .contact .contact-form .field-textarea:focus {
      border: 2px solid #8E7037; }
  .section-contact .contact .contact-form .field-textarea {
    resize: none;
    height: 210px; }
  .section-contact .contact .contact-form .btn {
    margin-top: 15px;
    width: 200px;
    font-size: 16px;
    height: 42px;
    width: 200px; }

/* 2.4.17 BLOG */
.section-blog {
  padding-bottom: 95px;
  padding-top: 5px; }
  .section-blog .post .wrap {
    position: absolute;
    background: #997E4A;
    bottom: 5%;
    text-align: center;
    opacity: 0.9;
    left: 0; }
    .section-blog .post .wrap .timer {
      padding: 0 30px !important; }
    .section-blog .post .wrap div {
      display: inline-block;
      line-height: 1;
      padding: 15px; }
    .section-blog .post .wrap span {
      display: block;
      font-size: 12px;
      color: white;
      font-weight: 400;
      text-transform: uppercase;
      margin-top: 5px; }
    .section-blog .post .wrap #hours,
    .section-blog .post .wrap #days,
    .section-blog .post .wrap #minutes,
    .section-blog .post .wrap #seconds {
      font-size: 30px;
      color: #ffffff;
      font-weight: 600; }
  .section-blog.blog-detail {
    padding-bottom: 125px; }
    .section-blog.blog-detail .hover-zoom-1 img {
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out; }
    .section-blog.blog-detail .hover-zoom-1:hover img {
      opacity: .8;
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out; }
    .section-blog.blog-detail .share-tag {
      margin-left: 100px;
      margin-top: 31px;
      border-top: 1px solid #cacaca;
      padding-top: 15px; }
      .section-blog.blog-detail .share-tag i {
        padding: 0px 8px;
        font-size: 14px; }
      .section-blog.blog-detail .share-tag span {
        padding-right: 23px; }
      .section-blog.blog-detail .share-tag a {
        color: #333333;
        font-size: 12px;
        font-weight: 500; }
        .section-blog.blog-detail .share-tag a:hover {
          color: #8E7037;
          text-decoration: none; }

.blog-content .widget_search {
  margin-top: 40px; }
.blog-content.events-content .post .entry-footer {
  margin-top: 15px; }
.blog-content .post {
  margin-top: 40px; }
  @media (max-width: 480px) {
    .blog-content .post {
      margin-top: 25px; } }
.blog-content .page-navigation {
  margin-top: 60px; }
.blog-content .comments,
.blog-content .comment-respond {
  margin-left: 100px; }

/* 2.4.18 SIDEBAR */
.sidebar {
  margin-right: 70px; }
  .sidebar .widget {
    margin-top: 40px; }
    .sidebar .widget .widget-title {
      font-size: 18px;
      margin-top: 0;
      margin-bottom: 20px;
      font-weight: bold;
      text-transform: uppercase; }
    .sidebar .widget ul {
      list-style: none;
      padding-left: 0;
      margin-bottom: 0; }
    .sidebar .widget.widget_check_availability {
      background-color: #f1f1f1; }
      .sidebar .widget.widget_check_availability .widget-title {
        margin: 0;
        font-size: 16px;
        text-align: center;
        color: #333333;
        line-height: 50px;
        font-weight: 700; }
      .sidebar .widget.widget_check_availability .check_availability {
        padding: 0 20px 30px 20px;
        border-top: 1px solid #e4e4e4; }
        .sidebar .widget.widget_check_availability .check_availability .check_availability_title {
          margin-top: 25px;
          font-size: 14px;
          text-transform: uppercase;
          font-weight: bold;
          color: #8E7037; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability_title:before {
            content: '';
            display: inline-block;
            height: 10px;
            border-left: 2px solid #8E7037;
            margin-right: 10px; }
        .sidebar .widget.widget_check_availability .check_availability .check_availability-field {
          margin-top: 15px; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability-field .date {
            width: 100%; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability-field .form-control {
            color: #898989;
            width: 100%; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability-field .wrap-box {
            -webkit-border-radius: 0;
            border-radius: 0;
            border: none;
            height: 30px;
            font-weight: 300; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability-field .input-group-addon {
            border-radius: 0;
            background: transparent;
            border: none;
            position: absolute;
            z-index: 11;
            right: 13px;
            top: 7%;
            height: 30px;
            color: #8E7037; }
            .sidebar .widget.widget_check_availability .check_availability .check_availability-field .input-group-addon .fa-calendar {
              color: #919191; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability-field label {
            display: block;
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 10px;
            color: #333333;
            text-transform: uppercase; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability-field .awe-calendar-wrapper {
            width: 100%; }
            .sidebar .widget.widget_check_availability .check_availability .check_availability-field .awe-calendar-wrapper .awe-calendar {
              background-color: #fff;
              border-width: 0;
              height: 30px;
              line-height: 30px;
              color: #898989;
              font-weight: 500; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select {
            width: 100%; }
            .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-toggle {
              background-color: #fff;
              height: 30px;
              border-width: 0;
              line-height: 20px;
              -webkit-box-shadow: none;
              box-shadow: none;
              -webkit-border-radius: 0;
              border-radius: 0; }
              .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-toggle:focus {
                outline: none !important; }
              .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-toggle .filter-option,
              .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-toggle .caret {
                color: #898989; }
            .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-menu {
              -webkit-border-radius: 0;
              border-radius: 0;
              border: none; }
              .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-menu ul li a {
                padding: 7px 10px;
                outline: none;
                color: #898989;
                font-size: 14px;
                font-weight: 500; }
                .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-menu ul li a:hover, .sidebar .widget.widget_check_availability .check_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-menu ul li a:focus {
                  background: #8E7037;
                  color: white; }
        .sidebar .widget.widget_check_availability .check_availability .check_availability_group {
          padding-left: 70px;
          position: relative; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability_group:after {
            display: table;
            content: '';
            clear: both; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability_group .label-group {
            position: absolute;
            left: 0;
            bottom: 5px;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability_group .check_availability-field_group {
            margin-left: -4px;
            margin-right: -4px; }
          .sidebar .widget.widget_check_availability .check_availability .check_availability_group .check_availability-field {
            width: 50%;
            padding-left: 10px;
            padding-right: 10px;
            float: left; }
        .sidebar .widget.widget_check_availability .check_availability .btn-room {
          margin-top: 30px;
          width: 100%;
          font-size: 14px;
          color: #ffffff;
          background: #8E7037;
          height: 40px;
          max-width: 260px;
          margin-right: auto;
          margin-left: auto;
          display: block; }
          .sidebar .widget.widget_check_availability .check_availability .btn-room:hover {
            border: 2px solid #8E7037;
            background: transparent;
            color: #8E7037; }
      .sidebar .widget.widget_check_availability.widget-1 {
        margin-top: 30px; }
    .sidebar .widget .widget-deal {
      cursor: w-resize; }
      .sidebar .widget .widget-deal .item {
        position: relative; }
        .sidebar .widget .widget-deal .item:after {
          content: '';
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          position: absolute;
          pointer-events: none; }
        .sidebar .widget .widget-deal .item .img {
          position: relative; }
          .sidebar .widget .widget-deal .item .img img {
            width: 100%; }
        .sidebar .widget .widget-deal .item .text {
          text-align: center;
          position: absolute;
          bottom: 30px;
          left: 0;
          right: 0;
          padding: 0 10px;
          z-index: 10; }
          .sidebar .widget .widget-deal .item .text h2 {
            color: #ffffff;
            font-size: 20px;
            text-transform: uppercase;
            margin: 0;
            font-weight: normal;
            font-weight: 600; }
          .sidebar .widget .widget-deal .item .text .price {
            color: #ffffff;
            font-weight: 600;
            font-size: 39px;
            margin-bottom: 0;
            line-height: 1;
            margin-top: 5px; }
          .sidebar .widget .widget-deal .item .text .btn {
            margin-top: 13px;
            font-size: 12px;
            color: #ffffff;
            width: 110px;
            height: 35px;
            border: 2px solid #ffffff;
            background: transparent;
            line-height: 20px; }
            .sidebar .widget .widget-deal .item .text .btn:hover {
              border: 2px solid #8E7037;
              background: #8E7037; }
      .sidebar .widget .widget-deal .owl-nav {
        display: none; }
      .sidebar .widget .widget-deal .owl-dots {
        position: absolute;
        right: 15px;
        bottom: 5px; }
        .sidebar .widget .widget-deal .owl-dots .owl-dot {
          width: 8px;
          height: 8px;
          display: inline-block;
          border-radius: 100%;
          border: 2px solid white;
          margin: 0px 3px; }
          .sidebar .widget .widget-deal .owl-dots .owl-dot.active {
            background: white; }
    .sidebar .widget.widget_categories ul {
      margin-top: -7px; }
      .sidebar .widget.widget_categories ul li a {
        color: #333333;
        display: inline-block;
        padding: 7px 0; }
      .sidebar .widget.widget_categories ul li:hover > a {
        color: #8E7037;
        text-decoration: none; }
    .sidebar .widget.widget_upcoming_events ul {
      margin-top: -10px; }
      .sidebar .widget.widget_upcoming_events ul li {
        overflow: hidden;
        padding: 10px 0; }
        .sidebar .widget.widget_upcoming_events ul li .event-date {
          text-align: center;
          display: block;
          width: 70px;
          height: 68px;
          float: left;
          padding: 5px 10px;
          font-size: 16px;
          color: #000;
          margin-right: 20px;
          line-height: 1;
          border: 2px solid #232323; }
          .sidebar .widget.widget_upcoming_events ul li .event-date strong {
            margin-bottom: 3px;
            display: block;
            font-size: 34px;
            font-weight: 400; }
        .sidebar .widget.widget_upcoming_events ul li .text {
          margin-top: 5px;
          overflow: hidden; }
          .sidebar .widget.widget_upcoming_events ul li .text a {
            font-weight: bold;
            text-transform: uppercase;
            color: #000; }
            .sidebar .widget.widget_upcoming_events ul li .text a:hover {
              color: #8E7037;
              text-decoration: none; }
          .sidebar .widget.widget_upcoming_events ul li .text .date {
            display: block;
            color: #444444;
            margin-top: 5px;
            width: 100%; }
    .sidebar .widget.widget_recent_entries ul {
      margin-top: -7px; }
      .sidebar .widget.widget_recent_entries ul li {
        overflow: hidden;
        padding: 10px 0; }
        .sidebar .widget.widget_recent_entries ul li:hover img {
          transform: scale(1.1, 1.1);
          opacity: .8;
          -webkit-transition: all 0.5s ease-in-out;
          -moz-transition: all 0.5s ease-in-out;
          -ms-transition: all 0.5s ease-in-out;
          -o-transition: all 0.5s ease-in-out;
          transition: all 0.5s ease-in-out; }
        .sidebar .widget.widget_recent_entries ul li .img {
          float: left;
          width: 70px;
          width: 70px;
          margin-right: 20px;
          overflow: hidden; }
          .sidebar .widget.widget_recent_entries ul li .img img {
            width: 100%;
            -webkit-transition: all 0.5s ease-in-out;
            -moz-transition: all 0.5s ease-in-out;
            -ms-transition: all 0.5s ease-in-out;
            -o-transition: all 0.5s ease-in-out;
            transition: all 0.5s ease-in-out; }
        .sidebar .widget.widget_recent_entries ul li a {
          text-transform: uppercase;
          font-weight: bold;
          color: #000; }
          .sidebar .widget.widget_recent_entries ul li a:hover {
            color: #8E7037;
            text-decoration: none; }
        .sidebar .widget.widget_recent_entries ul li .date {
          display: block;
          color: #444444;
          margin-top: 5px; }
    .sidebar .widget.widget_tag_cloud .tagcloud {
      font-size: 0;
      margin-top: -10px; }
      .sidebar .widget.widget_tag_cloud .tagcloud a {
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
        padding: 5px 13px;
        margin-right: 10px;
        margin-top: 10px;
        border: 2px solid #232323;
        color: #232323; }
        .sidebar .widget.widget_tag_cloud .tagcloud a:hover {
          color: #ffffff;
          background: #8E7037;
          text-decoration: none;
          border: 2px solid #ffffff; }
    .sidebar .widget.widget_social .widget-social {
      font-size: 0;
      margin-top: -10px; }
      .sidebar .widget.widget_social .widget-social a {
        font-size: 18px;
        margin-top: 10px;
        display: inline-block;
        width: 32px;
        height: 32px;
        margin-right: 25px;
        text-align: center;
        line-height: 30px;
        color: #8E7037;
        -webkit-border-radius: 100%;
        border-radius: 100%;
        border: 2px solid #8E7037; }
  .sidebar .reservation-room-selected .content-room,
  .sidebar .reservation-room-selected .total {
    padding: 20px; }
    .sidebar .reservation-room-selected .content-room.border-top,
    .sidebar .reservation-room-selected .total.border-top {
      border-top: 1px solid #e4e4e4;
      padding-top: 15px; }
  .sidebar .reservation-room-selected .room {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px; }
    .sidebar .reservation-room-selected .room span {
      font-size: 12px;
      font-weight: 400;
      padding-left: 20px; }
    .sidebar .reservation-room-selected .room.room-1 {
      font-size: 12px;
      color: #ACACAC; }
  .sidebar .reservation-room-selected .room1 {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px; }
    .sidebar .reservation-room-selected .room1 span {
      font-weight: 600;
      color: #8E7037;
      float: right; }
  .sidebar .reservation-room-selected a {
    color: #8E7037;
    font-size: 12px;
    text-decoration: underline; }
  .sidebar .total {
    font-size: 16px;
    font-weight: 600;
    color: #333; }
    .sidebar .total.total-3 {
      background: #8E7037;
      color: #FFF; }
      .sidebar .total.total-3 span {
        color: #FFF; }
    .sidebar .total span {
      color: #8E7037;
      font-size: 18px;
      float: right; }

/* 2.4.19 WIDGETSEARCH */
.widgetsearch {
  position: relative;
  border: 2px solid #232323;
  font-size: 14px; }
  .widgetsearch:before {
    content: '\f002';
    font-family: 'FontAwesome';
    position: absolute;
    right: 15px;
    font-size: 18px;
    top: 50%;
    -webkit-transform: translate(0, -50%);
    -moz-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    -o-transform: translate(0, -50%); }
  .widgetsearch .input-text {
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding-right: 50px;
    border-width: 0;
    background-color: #fff;
    color: #333;
    font-weight: 500;
    padding-left: 15px; }
    .widgetsearch .input-text::placeholder {
      color: #333333; }
  .widgetsearch .input-submit {
    display: none; }

/* 2.4.20 POST */
.post {
  position: relative; }
  .post .entry-media {
    position: relative; }
    .post .entry-media .hover-zoom {
      overflow: hidden;
      display: block; }
      .post .entry-media .hover-zoom:hover img {
        opacity: 0.8;
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
    .post .entry-media img {
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out;
      width: 100%; }
    .post .entry-media .count-date {
      background-color: rgba(225, 189, 133, 0.85);
      position: absolute;
      right: 0;
      bottom: 0;
      z-index: 20;
      color: #fff;
      padding: 15px 30px; }
      .post .entry-media .count-date span {
        font-size: 30px;
        display: inline-block;
        text-align: center;
        padding: 0px 10px;
        line-height: 1; }
        .post .entry-media .count-date span span {
          display: block;
          font-size: 12px;
          text-transform: uppercase;
          margin-top: 5px; }
    .post .entry-media .post-format {
      position: absolute;
      display: block;
      color: #fff;
      text-align: center;
      top: 50%;
      left: 50%;
      font-size: 50px;
      line-height: 1;
      pointer-events: none;
      transform: translate(-50%, -50%);
      width: 64px;
      height: 64px;
      background: url("../images/Blog/Shape-101.png") no-repeat scroll 0 0; }
    .post .entry-media .posted-on {
      position: absolute;
      bottom: -23px;
      left: 0;
      border: 2px solid #232323;
      text-align: center;
      color: #000;
      font-size: 16px;
      text-transform: uppercase;
      line-height: 1;
      width: 70px;
      height: 70px;
      padding: 10px;
      -webkit-transform: translate(0, 100%);
      -moz-transform: translate(0, 100%);
      -ms-transform: translate(0, 100%);
      -o-transform: translate(0, 100%); }
      .post .entry-media .posted-on strong {
        display: block;
        font-weight: 600;
        font-size: 34px; }
    .post .entry-media .owl-pagination {
      display: none; }
  .post .entry-header {
    margin-top: 20px;
    padding-left: 100px; }
    .post .entry-header h2 {
      font-weight: bold;
      margin: 0;
      font-size: 18px;
      line-height: 23px; }
      @media (max-width: 767px) {
        .post .entry-header h2 {
          font-size: 16px; } }
      .post .entry-header h2 a {
        text-transform: uppercase;
        color: #000; }
        .post .entry-header h2 a:hover, .post .entry-header h2 a:focus {
          color: #8E7037;
          text-decoration: none; }
    .post .entry-header .entry-meta {
      margin-top: 10px;
      margin-bottom: 0; }
  .post.post-single .entry-header h2 {
    font-weight: bold;
    margin: 0;
    font-size: 22px;
    text-transform: uppercase; }
    @media (max-width: 767px) {
      .post.post-single .entry-header h2 {
        font-size: 16px; } }
  .post .entry-meta {
    font-size: 14px;
    color: #9e9e9e; }
    .post .entry-meta:before {
      content: '';
      font-size: 10px;
      position: relative;
      top: -1px;
      margin-right: 10px;
      border-left: 2px solid #232323; }
    .post .entry-meta .posted-on {
      display: none; }
    .post .entry-meta .entry-categories:before,
    .post .entry-meta .entry-comments-link:before {
      content: '-';
      padding: 0 2px; }
    .post .entry-meta .entry-tags {
      margin-left: 20px; }
      .post .entry-meta .entry-tags .screen-reader-text {
        color: #444444;
        margin-right: 5px; }
    .post .entry-meta a {
      color: #9E9E9E; }
      .post .entry-meta a:hover {
        color: #8E7037;
        text-decoration: none; }
      .post .entry-meta a .entry-author-name {
        font-weight: bold;
        color: #444444; }
        .post .entry-meta a .entry-author-name:hover {
          color: #8E7037; }
  .post .entry-content {
    color: #444444;
    font-size: 15px;
    margin-top: 12px;
    padding-left: 100px; }
    @media (max-width: 767px) {
      .post .entry-content {
        font-size: 14px; } }
    .post .entry-content p {
      line-height: 1.5em;
      margin-bottom: 0; }
    .post .entry-content a {
      color: #0072bc;
      text-decoration: underline; }
      .post .entry-content a:hover {
        color: #8E7037; }
    .post .entry-content blockquote {
      border: 2px solid #232323;
      padding: 17px 40px;
      margin: 0; }
      .post .entry-content blockquote p {
        color: #444;
        font-style: italic;
        font-size: 14px;
        position: relative; }
        .post .entry-content blockquote p::before {
          content: '“';
          font-family: 'Montserrat';
          font-size: 28px;
          font-style: inherit;
          font-weight: 700;
          vertical-align: middle;
          padding-right: 10px;
          line-height: 0; }
        .post .entry-content blockquote p:after {
          content: '”';
          font-size: 28px;
          font-style: inherit;
          font-family: 'Montserrat';
          font-weight: 700;
          padding-top: 5px;
          vertical-align: middle;
          padding-left: 5px;
          line-height: 0; }
    .post .entry-content h2 {
      margin-bottom: 15px !important;
      font-weight: 700;
      font-size: 18px; }
  .post .entry-footer {
    padding-left: 100px;
    margin-top: 30px; }
    @media (max-width: 480px) {
      .post .entry-footer {
        margin-top: 15px; } }
  .post:hover .entry-media:before {
    opacity: .7;
    top: 0; }
  .post.post-single .entry-content {
    color: #444444;
    margin-top: 20px;
    font-size: 15px; }
    @media (max-width: 767px) {
      .post.post-single .entry-content {
        font-size: 14px; } }
    .post.post-single .entry-content h1,
    .post.post-single .entry-content h2,
    .post.post-single .entry-content h3,
    .post.post-single .entry-content h4,
    .post.post-single .entry-content h5,
    .post.post-single .entry-content h6 {
      text-transform: uppercase;
      margin-bottom: 10px; }
    .post.post-single .entry-content .wp-caption {
      max-width: 100%; }
    .post.post-single .entry-content .aligncenter {
      margin-left: auto;
      margin-right: auto; }
    .post.post-single .entry-content .alignleft {
      float: left;
      margin-right: 20px; }
    .post.post-single .entry-content .alignright {
      float: right;
      margin-left: 20px; }
    .post.post-single .entry-content p {
      margin-bottom: 0; }
      .post.post-single .entry-content p em {
        font-size: 13px;
        line-height: 1.4em;
        color: #6e6e6e; }

/* 2.4.21 NAVIGATION */
.page-navigation {
  margin-top: 75px;
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
  font-size: 0; }
  @media (max-width: 480px) {
    .page-navigation {
      margin-top: 50px; } }
  .page-navigation li {
    display: inline-block;
    font-size: 15px;
    margin-right: 20px;
    vertical-align: middle; }
    .page-navigation li a {
      color: #444444;
      font-weight: 500;
      font-size: 15px; }
      .page-navigation li a:hover {
        color: #8E7037;
        text-decoration: none; }
    .page-navigation li.first a, .page-navigation li.last a {
      font-size: 23px;
      display: block;
      width: 23px;
      height: 23px;
      line-height: 22px;
      text-align: center; }
    .page-navigation li.first {
      margin-right: 40px; }
    .page-navigation li.last {
      margin-left: 18px; }
    .page-navigation li.current-page a {
      color: #8E7037; }

/* 2.4.22 VIDEO */
.section-video .btn-play {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  transition: all .3s;
  background: url("../images/Blog/Shape-101.png");
  width: 64px;
  height: 64px;
  background-repeat: no-repeat;
  background-size: cover; }
.section-video .modal-video {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  transform: scale(2);
  transition: all .5s;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999; }
  .section-video .modal-video iframe {
    position: relative;
    top: 150px;
    width: 700px;
    max-width: 90%;
    display: block;
    margin: 0 auto;
    border: none; }
    .section-video .modal-video iframe .ytp-autohide .ytp-watermark {
      display: none !important; }
  .section-video .modal-video.opened {
    visibility: visible;
    opacity: 1;
    transform: scale(1); }
.section-video:hover .btn-play {
  width: 70px;
  height: 70px;
  background: url("../images/Blog/Shape-1101.png");
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 2; }

.video-v4 iframe {
  border: none; }

/*  2.4.23 COMMENT */
.comments {
  margin-top: 10px;
  border-top: 1px solid #cacaca;
  padding-top: 15px; }
  .comments .comment-title {
    font-weight: bold;
    text-transform: uppercase; }
  .comments .commentlist {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0; }
    .comments .commentlist li {
      position: relative;
      padding-left: 90px;
      margin-top: 25px; }
      @media (max-width: 480px) {
        .comments .commentlist li {
          padding-left: 60px; } }
      .comments .commentlist li .comment-body {
        border-bottom: 1px solid #cacaca;
        padding-bottom: 25px;
        overflow: hidden; }
        .comments .commentlist li .comment-body .comment-avatar {
          position: absolute;
          width: 70px;
          display: block;
          top: 5px;
          left: 0;
          border-radius: 50%;
          overflow: hidden; }
          .comments .commentlist li .comment-body .comment-avatar img {
            width: 100%; }
          @media (max-width: 480px) {
            .comments .commentlist li .comment-body .comment-avatar {
              width: 50px; } }
        .comments .commentlist li .comment-body .comment-subject {
          margin: 0;
          font-size: 14px;
          color: #444444;
          font-weight: bold; }
        .comments .commentlist li .comment-body p {
          color: #444444;
          font-size: 15px;
          line-height: 1.428em;
          margin-bottom: 0;
          margin-top: 10px; }
          @media (max-width: 767px) {
            .comments .commentlist li .comment-body p {
              font-size: 14px; } }
        .comments .commentlist li .comment-body .comment-meta {
          float: left;
          margin-top: 20px;
          color: #898989; }
          .comments .commentlist li .comment-body .comment-meta a {
            color: #444;
            font-weight: bold;
            text-transform: capitalize; }
            .comments .commentlist li .comment-body .comment-meta a:hover {
              color: #8E7037;
              text-decoration: none; }
        .comments .commentlist li .comment-body .action {
          float: right;
          margin-top: 18px; }
          .comments .commentlist li .comment-body .action .btn {
            min-width: inherit;
            margin-left: 10px;
            height: 27px;
            width: 63px;
            font-weight: 600;
            font-size: 12px;
            line-height: 12px;
            color: #232323;
            background: transparent;
            border: 2px solid #000; }
            .comments .commentlist li .comment-body .action .btn:hover {
              color: #8E7037;
              border: 2px solid #8E7037; }
        .comments .commentlist li .comment-body .children {
          overflow: hidden;
          clear: both;
          list-style: none;
          padding-left: 0; }
          .comments .commentlist li .comment-body .children li:last-child .comment-body {
            padding-bottom: 0;
            border-width: 0; }
      .comments .commentlist li:last-child .comment-body {
        border-bottom-width: 0;
        padding-bottom: 0; }

.comment-respond {
  border-top: 1px solid #cacaca;
  margin-top: 50px;
  padding-top: 20px; }
  .comment-respond .comment-reply-title {
    text-transform: uppercase;
    color: #000;
    font-size: 18px;
    margin: 0;
    font-weight: bold; }
  .comment-respond .comment-form {
    margin-top: 10px; }
    .comment-respond .comment-form .row {
      margin-left: -10px;
      margin-right: -10px; }
      .comment-respond .comment-form .row [class*="col-"] {
        padding-left: 10px;
        padding-right: 10px; }
    .comment-respond .comment-form .field-text,
    .comment-respond .comment-form .field-textarea {
      width: 100%;
      border: 2px solid #000;
      padding: 0 15px;
      margin-top: 15px;
      height: 43px; }
      .comment-respond .comment-form .field-text::placeholder,
      .comment-respond .comment-form .field-textarea::placeholder {
        color: #232323; }
      .comment-respond .comment-form .field-text:focus,
      .comment-respond .comment-form .field-textarea:focus {
        border: 2px solid #8E7037; }
    .comment-respond .comment-form .field-textarea {
      resize: none;
      height: 200px;
      padding: 10px 15px;
      line-height: 1.428em; }
    .comment-respond .comment-form .btn {
      font-size: 14px;
      padding-left: 20px;
      padding-right: 20px;
      margin-top: 20px;
      font-weight: 600;
      border: 2px solid #000;
      color: #232323;
      background: transparent; }
      .comment-respond .comment-form .btn:hover {
        color: #8E7037;
        border: 2px solid #8E7037; }

/* 2.4.24 AttRACTIONS */
.section-attractions {
  padding-bottom: 80px; }
  @media (max-width: 767px) {
    .section-attractions {
      padding-bottom: 50px; } }
  .section-attractions .attraction {
    padding-top: 60px;
    margin-top: -60px; }
    .section-attractions .attraction .attraction_sidebar {
      background-color: #f1f1f1;
      margin-top: 60px; }
      .section-attractions .attraction .attraction_sidebar .attraction_heading {
        color: #333333;
        font-size: 16px;
        text-transform: uppercase;
        font-weight: bold;
        border-bottom: 1px solid #e4e4e4;
        padding: 15px 20px;
        text-align: center;
        position: relative;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        user-select: none; }
        .section-attractions .attraction .attraction_sidebar .attraction_heading .attraction-icon-drop {
          display: none;
          float: right;
          position: absolute;
          right: 20px;
          font-size: 20px;
          top: 50%; }
      .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content {
        padding-left: 20px;
        padding-right: 20px;
        padding-bottom: 25px; }
        .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_title {
          color: #fff;
          background: #8E7037;
          font-size: 12px;
          text-transform: uppercase;
          font-weight: bold;
          margin-top: 30px;
          height: 40px;
          line-height: 43px;
          padding: 0 20px; }
          .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_title i {
            margin-right: 15px;
            font-size: 16px; }
        .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select {
          width: 100%;
          margin-top: 20px; }
          .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select .btn {
            background-color: #fff;
            height: 40px;
            border-width: 0;
            line-height: 20px;
            -webkit-box-shadow: none;
            box-shadow: none;
            -webkit-border-radius: 0;
            border-radius: 0;
            padding-left: 18px; }
            .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select .btn:focus {
              outline: none !important; }
            .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select .btn .filter-option,
            .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select .btn .caret {
              color: #898989; }
          .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select .dropdown-menu {
            -webkit-border-radius: 0;
            border-radius: 0;
            border: none;
            width: 100%; }
            .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select .dropdown-menu ul li a {
              padding: 7px 10px;
              outline: none;
              color: #898989;
              font-size: 14px;
              font-weight: 400; }
              .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select .dropdown-menu ul li a:hover, .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .bootstrap-select.attraction_select.awe-select .dropdown-menu ul li a:focus {
                background: #8E7037;
                color: white; }
        .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_location {
          list-style: none;
          padding-left: 0;
          margin-top: 25px;
          margin-bottom: 0; }
          .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_location li {
            color: #898989;
            font-weight: 400;
            position: relative;
            padding-left: 25px;
            margin-top: 14px; }
            .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_location li:first-child {
              margin-top: 0; }
            .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_location li i {
              color: #333333;
              position: absolute;
              left: 0;
              line-height: 24px; }
            .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_location li a {
              color: #898989; }
              .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_location li a:hover, .section-attractions .attraction .attraction_sidebar .attraction_sidebar-content .attraction_location li a:focus {
                color: #8E7037;
                text-decoration: none;
                outline: none; }
  .section-attractions .attraction_detail {
    margin-top: 60px; }
    .section-attractions .attraction_detail .attraction_detail-title {
      color: #333333;
      text-transform: uppercase;
      font-weight: bold;
      font-size: 20px; }
      @media (max-width: 767px) {
        .section-attractions .attraction_detail .attraction_detail-title {
          font-size: 16px; } }
      .section-attractions .attraction_detail .attraction_detail-title i {
        color: #8E7037;
        margin-right: 20px; }
    .section-attractions .attraction_detail ul {
      color: #333;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4em;
      list-style: none;
      padding-left: 0;
      margin-bottom: 0;
      margin-top: 20px; }
      .section-attractions .attraction_detail ul li {
        margin-top: 10px; }
        .section-attractions .attraction_detail ul li span {
          font-weight: bold;
          text-transform: uppercase;
          margin-right: 20px; }
        .section-attractions .attraction_detail ul li a {
          color: #8E7037;
          text-decoration: none; }
  .section-attractions .attraction_content {
    border-top: 1px solid #e4e4e4;
    padding-top: 7px;
    color: #898989;
    font-size: 14px;
    margin-top: 26px; }
    .section-attractions .attraction_content .attraction_content-title {
      text-transform: uppercase;
      color: #333;
      font-size: 20px;
      margin-bottom: 17px;
      font-weight: bold; }
      @media (max-width: 767px) {
        .section-attractions .attraction_content .attraction_content-title {
          font-size: 16px; } }
    .section-attractions .attraction_content p {
      margin-bottom: 0;
      line-height: 1.5em;
      font-size: 14px; }
      .section-attractions .attraction_content p b {
        color: #333;
        font-weight: 600; }
      .section-attractions .attraction_content p a {
        text-decoration: underline;
        font-weight: 600;
        color: #8E7037; }
    .section-attractions .attraction_content .aligncenter,
    .section-attractions .attraction_content .alignleft {
      text-align: center;
      overflow: hidden; }
      .section-attractions .attraction_content .aligncenter:hover img,
      .section-attractions .attraction_content .alignleft:hover img {
        transform: scale(1.1, 1.1);
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out;
        opacity: .8; }
      .section-attractions .attraction_content .aligncenter img,
      .section-attractions .attraction_content .alignleft img {
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
    .section-attractions .attraction_content .alignright {
      float: right;
      margin-left: 30px; }
    .section-attractions .attraction_content .alignleft {
      float: left;
      margin-right: 30px;
      width: 100%; }

.section-statistics {
  padding-top: 55px;
  padding-bottom: 80px;
  background: url("../images/About/about-2.jpg") no-repeat;
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  text-align: center;
  position: relative;
  width: 100%; }
  .section-statistics:before {
    background: #363635;
    content: '';
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    top: 0px;
    right: 0;
    left: 0;
    bottom: 0;
    opacity: 0.6; }
  .section-statistics .statistics {
    z-index: 99;
    position: relative; }
    .section-statistics .statistics .col {
      position: relative; }
      .section-statistics .statistics .col:before {
        top: 35%;
        right: 0;
        bottom: 0;
        position: absolute;
        content: '';
        border-right: 1px solid #a5a4a4; }
      .section-statistics .statistics .col:last-child:before {
        border: none; }
  .section-statistics .item {
    margin-top: 45px;
    text-align: center;
    color: #fff;
    font-size: 18px;
    text-transform: uppercase;
    position: relative; }
    .section-statistics .item .count {
      font-size: 60px;
      font-weight: 500;
      color: #fff;
      display: block;
      line-height: 1.3em; }
  .section-statistics .heading, .section-statistics .body-restaurant-1 .restaurant_title .heading, .body-restaurant-1 .restaurant_title .section-statistics .heading {
    color: #ffffff;
    font-size: 36px;
    text-transform: uppercase; }

/* 2.4.25 SECTION-TEAM*/
.section-team {
  background-color: #fff;
  padding-bottom: 75px;
  padding-top: 41px; }
  .section-team .heading, .section-team .body-restaurant-1 .restaurant_title .heading, .body-restaurant-1 .restaurant_title .section-team .heading {
    color: #333333;
    text-transform: uppercase;
    font-size: 36px; }
    @media (max-width: 480px) {
      .section-team .heading, .section-team .body-restaurant-1 .restaurant_title .heading, .body-restaurant-1 .restaurant_title .section-team .heading {
        font-size: 25px; } }
  .section-team .sub-heading {
    color: #333333;
    font-size: 20px; }

.team_item {
  margin-top: 70px;
  position: relative; }
  .team_item:before {
    position: absolute;
    left: 0;
    right: 0;
    bottom: -25px;
    margin: auto;
    width: 0;
    content: '';
    border-bottom: 1px solid #f1f1f1;
    -webkit-transition: all .5s ease;
    -moz-transition: all .5s ease;
    -ms-transition: all .5s ease;
    -o-transition: all .5s ease; }
  .team_item:hover .img img {
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    transform: scale(1, 1); }
  .team_item:hover:before {
    width: 100%; }
  .team_item .img {
    max-width: 200px;
    overflow: hidden;
    margin: auto;
    position: relative;
    border-radius: 100%;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    -ms-border-radius: 100%;
    -o-border-radius: 100%; }
    .team_item .img img {
      width: 100%;
      -webkit-transition: all 0.5s ease-in-out;
      -moz-transition: all 0.5s ease-in-out;
      -ms-transition: all 0.5s ease-in-out;
      -o-transition: all 0.5s ease-in-out;
      transition: all 0.5s ease-in-out;
      transform: scale(1.1, 1.1); }
  .team_item .text {
    margin-top: 35px; }
    .team_item .text h2 {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
      text-transform: uppercase;
      color: #444; }
      .team_item .text h2:hover {
        color: #8E7037; }
    .team_item .text span {
      color: #898989;
      font-size: 16px;
      display: block;
      margin-top: 7px; }
    .team_item .text p {
      margin-top: 15px;
      margin-bottom: 0;
      color: #30373b;
      line-height: 1.5em;
      font-size: 15px; }
    .team_item .text .team-share {
      font-size: 0;
      margin-top: 23px; }
      .team_item .text .team-share a {
        font-size: 18px;
        display: inline-block;
        color: #898989;
        margin: 0 13px; }
        .team_item .text .team-share a:hover {
          color: #8E7037; }
  .team_item:hover:before {
    width: 100%; }

.counter {
  background-color: #eaecf0;
  text-align: center; }

.employees,
.customer,
.design,
.order {
  margin-top: 70px;
  margin-bottom: 70px; }

.counter-count {
  font-size: 18px;
  background-color: #00b3e7;
  border-radius: 50%;
  position: relative;
  color: #ffffff;
  text-align: center;
  line-height: 92px;
  width: 92px;
  height: 92px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: inline-block; }

/*  2.4.26 RESERVATION */
.section-reservation-page {
  padding-bottom: 50px;
  padding-top: 20px; }
  @media (max-width: 992px) {
    .section-reservation-page {
      padding-bottom: 40px; } }
  .section-reservation-page .sidebar {
    margin-right: 0; }
    .section-reservation-page .sidebar .check_availability_title {
      font-weight: 600 !important; }
      .section-reservation-page .sidebar .check_availability_title:before {
        content: "\f363" !important;
        font-family: "Ionicons";
        border-left: none !important; }
    .section-reservation-page .sidebar .check_availability-field label {
      font-weight: 600 !important; }
    .section-reservation-page .sidebar .check_availability_group .label-group {
      font-weight: 600 !important; }
    .section-reservation-page .sidebar .widget-title {
      font-weight: 600 !important; }

.reservation_step ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
  font-size: 0;
  text-align: center;
  border-bottom: 1px solid #e4e4e4; }
  .reservation_step ul li {
    display: inline-block;
    font-size: 15px;
    position: relative;
    margin: 0px 25px; }
    @media (max-width: 767px) {
      .reservation_step ul li {
        display: block;
        margin: 0; } }
    .reservation_step ul li:before {
      content: '';
      left: 0;
      right: 0;
      bottom: -1px;
      height: 2px;
      position: absolute;
      background-color: transparent;
      width: 0;
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out; }
    .reservation_step ul li a {
      color: #333;
      display: block;
      padding: 15px 0;
      font-size: 14px; }
      .reservation_step ul li a span {
        font-weight: bold;
        font-size: 16px; }
    .reservation_step ul li.active:before, .reservation_step ul li:hover:before {
      background: #8E7037;
      width: 100%;
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out; }

.reservation-date {
  margin-top: 30px;
  background: #f1f1f1; }
  .reservation-date ul {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
    padding: 20px 20px; }
    .reservation-date ul li {
      color: #333;
      font-size: 12px;
      overflow: hidden;
      font-weight: 500;
      padding: 5px 0; }
      .reservation-date ul li span:last-child {
        float: right;
        font-weight: 600; }

.reservation-room-selected {
  background-color: #f1f1f1;
  margin-top: 45px; }
  .reservation-room-selected.selected-4 {
    margin-top: 30px; }
  .reservation-room-selected .reservation-room-seleted_item {
    padding: 20px 20px 20px 20px;
    border-bottom: 1px solid #e4e4e4; }
    .reservation-room-selected .reservation-room-seleted_item:last-child {
      border-bottom-width: 0; }
    .reservation-room-selected .reservation-room-seleted_item h6 {
      display: inline-block;
      vertical-align: middle;
      font-size: 14px;
      text-transform: uppercase;
      font-weight: 600;
      margin: 0 20px 0 0;
      line-height: 1.428em; }
    .reservation-room-selected .reservation-room-seleted_item .reservation-option {
      display: inline-block;
      font-size: 12px;
      font-weight: 400; }
    .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_name {
      overflow: hidden;
      margin-top: 10px; }
      .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_name h2 {
        text-transform: uppercase;
        font-size: 12px;
        font-weight: 600;
        margin-right: 80px;
        float: left;
        margin: 0; }
        .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_name h2 a {
          color: #8E7037; }
      .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_name .reservation-amout {
        float: right;
        font-weight: bold;
        font-size: 14px; }
      .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_name.has-package h2 {
        font-size: 16px; }
    .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_change {
      color: #898989;
      font-size: 12px;
      font-style: italic;
      text-decoration: underline;
      display: inline-block;
      margin-top: 7px; }
    .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_package {
      border-top: 1px solid #e4e4e4;
      margin-top: 15px;
      padding-top: 5px; }
      .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_package ul {
        list-style: none;
        padding-left: 0;
        margin-bottom: 0;
        padding-bottom: 2px;
        margin-top: 5px; }
        .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_package ul li {
          color: #333;
          font-size: 12px;
          overflow: hidden;
          font-weight: 400;
          padding: 5px 0; }
          .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_package ul li span:last-child {
            float: right;
            font-weight: 600;
            text-transform: uppercase; }
        .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_package ul + ul {
          border-top: 1px solid #e4e4e4;
          padding-top: 8px;
          border-bottom: 1px solid #e4e4e4;
          padding-bottom: 8px; }
    .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_total-room {
      color: #333;
      font-size: 14px;
      font-weight: 600;
      text-transform: uppercase;
      padding-top: 15px;
      padding-bottom: 10px; }
      .reservation-room-selected .reservation-room-seleted_item .reservation-room-seleted_total-room .reservation-amout {
        float: right;
        color: #8E7037; }
    .reservation-room-selected .reservation-room-seleted_item.reservation_disable {
      background-color: #f1f1f1; }
      .reservation-room-selected .reservation-room-seleted_item.reservation_disable h6 {
        opacity: 0.4;
        font-size: 12px; }
      .reservation-room-selected .reservation-room-seleted_item.reservation_disable span {
        opacity: 0.3; }
  .reservation-room-selected .reservation-room-seleted_current {
    overflow: hidden;
    color: #fff;
    padding: 15px 20px 13px 40px;
    background: #8E7037; }
    .reservation-room-selected .reservation-room-seleted_current h6 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 14px;
      text-transform: uppercase;
      font-weight: 600;
      line-height: 1.428em;
      position: relative;
      color: #fff; }
      .reservation-room-selected .reservation-room-seleted_current h6:before {
        content: '\f0da';
        font-family: 'FontAwesome';
        left: -20px;
        position: absolute;
        font-size: 12px;
        font-weight: normal; }
    .reservation-room-selected .reservation-room-seleted_current span {
      font-size: 13px;
      font-weight: 400; }
    .reservation-room-selected .reservation-room-seleted_current.reservation-bg_blue {
      color: #fff; }
  .reservation-room-selected .reservation-room-seleted_total {
    padding: 20px 20px;
    overflow: hidden;
    font-size: 16px;
    font-weight: 600;
    background: #8E7037;
    color: #FFF; }
    .reservation-room-selected .reservation-room-seleted_total label {
      font-weight: 600;
      text-transform: uppercase;
      margin: 0; }
    .reservation-room-selected .reservation-room-seleted_total .reservation-total {
      font-size: 16px;
      font-weight: 600;
      float: right; }

.reservation-sidebar {
  padding-top: 10px; }

.reservation-heading {
  font-size: 16px;
  color: #333333;
  margin-top: 0px;
  line-height: 30px;
  text-transform: uppercase;
  padding: 12px 20px;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid #e4e4e4;
  margin-bottom: 0; }

.reservation-sidebar_availability {
  padding: 0 20px 20px 20px;
  margin-top: 30px; }
  .reservation-sidebar_availability .reservation-heading {
    margin-left: -20px;
    margin-right: -20px; }
  .reservation-sidebar_availability .check_availability_title {
    margin-top: 20px;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: bold; }
  .reservation-sidebar_availability .check_availability-field {
    margin-top: 15px; }
    .reservation-sidebar_availability .check_availability-field label {
      display: block;
      font-size: 12px;
      margin-bottom: 7px;
      text-transform: uppercase; }
    .reservation-sidebar_availability .check_availability-field .awe-calendar-wrapper {
      width: 100%; }
      .reservation-sidebar_availability .check_availability-field .awe-calendar-wrapper .awe-calendar {
        background-color: #fff;
        border-width: 0;
        height: 30px;
        line-height: 30px;
        color: #898989;
        font-weight: 500; }
    .reservation-sidebar_availability .check_availability-field .bootstrap-select.btn-group.awe-select {
      width: 100%; }
      .reservation-sidebar_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-toggle {
        background-color: #fff;
        height: 30px;
        border-width: 0;
        line-height: 30px; }
        .reservation-sidebar_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-toggle .filter-option,
        .reservation-sidebar_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-toggle .caret {
          color: #898989;
          font-weight: 500; }
      .reservation-sidebar_availability .check_availability-field .bootstrap-select.btn-group.awe-select .dropdown-menu ul li a {
        padding: 7px 10px; }
  .reservation-sidebar_availability .check_availability_group {
    padding-left: 70px;
    position: relative; }
    .reservation-sidebar_availability .check_availability_group:after {
      display: table;
      content: '';
      clear: both; }
    .reservation-sidebar_availability .check_availability_group .label-group {
      position: absolute;
      left: 0;
      bottom: 5px;
      font-weight: bold;
      font-size: 12px;
      text-transform: uppercase; }
    .reservation-sidebar_availability .check_availability_group .check_availability-field_group {
      margin-left: -4px;
      margin-right: -4px; }
    .reservation-sidebar_availability .check_availability_group .check_availability-field {
      width: 50%;
      padding-left: 4px;
      padding-right: 4px;
      float: left; }

.reservation_content {
  padding-bottom: 20px;
  margin-top: 45px;
  margin-left: 70px; }
  @media (max-width: 991px) {
    .reservation_content {
      margin-left: 0; } }
  .reservation_content.bg-gray {
    background: #f1f1f1; }
  .reservation_content:before, .reservation_content:after {
    content: '';
    display: table;
    clear: both; }

.reservation-calendar_custom .reservation-calendar_title {
  text-align: center;
  color: #333333;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 600;
  margin: 0;
  padding-top: 7px;
  padding-bottom: 16px;
  position: relative;
  border-bottom: 1px solid #e4e4e4; }
  .reservation-calendar_custom .reservation-calendar_title .reservation-calendar_corner {
    display: block;
    text-align: center;
    color: #cbcbcb;
    font-size: 35px;
    position: absolute;
    top: -20%; }
    .reservation-calendar_custom .reservation-calendar_title .reservation-calendar_corner:hover {
      color: #333333; }
    .reservation-calendar_custom .reservation-calendar_title .reservation-calendar_corner.reservation-calendar_prev {
      left: 0; }
    .reservation-calendar_custom .reservation-calendar_title .reservation-calendar_corner.reservation-calendar_next {
      right: 0; }
.reservation-calendar_custom .reservation-calendar_tabel {
  width: 100%;
  vertical-align: middle;
  text-align: center;
  table-layout: fixed;
  margin-top: 15px;
  font-family: 'Hind', sans-serif;
  font-size: 12px;
  font-weight: 700;
  color: #333333; }
  .reservation-calendar_custom .reservation-calendar_tabel th {
    text-transform: uppercase;
    text-align: center;
    font-size: 12px;
    padding-top: 10px;
    padding-bottom: 10px; }
  .reservation-calendar_custom .reservation-calendar_tabel td {
    background-color: #fff;
    background-clip: content-box;
    padding: 3px; }
    .reservation-calendar_custom .reservation-calendar_tabel td.active {
      background-color: #8E7037; }
      .reservation-calendar_custom .reservation-calendar_tabel td.active a small,
      .reservation-calendar_custom .reservation-calendar_tabel td.active a span {
        color: #FFF; }
    .reservation-calendar_custom .reservation-calendar_tabel td a {
      position: relative;
      display: block;
      padding-top: 90%;
      font-size: 12px;
      border: 2px solid transparent; }
      .reservation-calendar_custom .reservation-calendar_tabel td a:hover {
        border: 2px solid #8E7037; }
      .reservation-calendar_custom .reservation-calendar_tabel td a:before {
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        content: '';
        border: 2px solid transparent;
        position: absolute; }
      .reservation-calendar_custom .reservation-calendar_tabel td a small {
        position: absolute;
        top: 5px;
        right: 10px;
        font-size: 12px;
        font-family: 'Hind', sans-serif;
        color: #333333;
        font-weight: 500; }
      .reservation-calendar_custom .reservation-calendar_tabel td a span {
        position: absolute;
        top: 50%;
        left: 50%;
        font-family: 'Hind', sans-serif;
        color: #333333;
        font-weight: 500;
        -webkit-transform: translate(-50%, 3px);
        -moz-transform: translate(-50%, 3px);
        -ms-transform: translate(-50%, 3px);
        -o-transform: translate(-50%, 3px); }
        @media (max-width: 480px) {
          .reservation-calendar_custom .reservation-calendar_tabel td a span {
            font-size: 8px; } }
    .reservation-calendar_custom .reservation-calendar_tabel td.current-select a {
      color: #fff; }

.reservation-room .reservation-room_item {
  margin-top: 45px; }
  .reservation-room .reservation-room_item:first-child {
    margin-top: 0; }

.reservation-room_item {
  overflow: hidden; }
  .reservation-room_item .reservation-room_name {
    text-transform: uppercase;
    font-weight: bold;
    margin: 0;
    font-size: 26px; }
    @media (max-width: 767px) {
      .reservation-room_item .reservation-room_name {
        font-size: 20px; } }
    .reservation-room_item .reservation-room_name a {
      color: #333; }
      .reservation-room_item .reservation-room_name a:hover {
        color: #8E7037; }
  .reservation-room_item .reservation-room_img {
    float: left;
    margin-right: 30px;
    width: 370px;
    margin-top: 25px; }
    @media (min-width: 992px) and (max-width: 1200px) {
      .reservation-room_item .reservation-room_img {
        float: none;
        width: 100%; } }
    @media (min-width: 768px) and (max-width: 991px) {
      .reservation-room_item .reservation-room_img {
        float: none;
        width: 100%; } }
    @media (max-width: 767px) {
      .reservation-room_item .reservation-room_img {
        float: none;
        width: 100%; } }
    .reservation-room_item .reservation-room_img img {
      width: 100%; }
  .reservation-room_item .reservation-room_text {
    overflow: hidden;
    margin-top: 25px; }
    .reservation-room_item .reservation-room_text .reservation-room_desc {
      color: #333333; }
      .reservation-room_item .reservation-room_text .reservation-room_desc p {
        margin-bottom: 0;
        font-size: 14px; }
      .reservation-room_item .reservation-room_text .reservation-room_desc ul {
        padding-left: 18px;
        margin-bottom: 0;
        margin-top: 15px; }
        .reservation-room_item .reservation-room_text .reservation-room_desc ul li {
          padding: 3px 0;
          font-size: 14px;
          padding-left: 8px; }
    .reservation-room_item .reservation-room_text .reservation-room_view-more {
      display: inline-block;
      clear: both;
      margin-top: 10px;
      font-size: 12px;
      font-weight: 300;
      margin-bottom: 25px;
      text-decoration: underline;
      color: #333;
      font-style: italic; }
      .reservation-room_item .reservation-room_text .reservation-room_view-more:hover {
        color: #8E7037; }
    .reservation-room_item .reservation-room_text .reservation-room_price {
      display: inline-block;
      vertical-align: middle;
      color: #898989;
      font-size: 14px;
      margin: 0;
      line-height: 1; }
      .reservation-room_item .reservation-room_text .reservation-room_price .reservation-room_amout {
        font-weight: bold;
        color: #333333;
        font-size: 30px; }
    .reservation-room_item .reservation-room_text .btn {
      margin-left: 35px;
      height: 35px;
      width: 124px;
      font-weight: 700;
      color: #333;
      background: transparent;
      border: 2px solid #333;
      line-height: 20px; }
      .reservation-room_item .reservation-room_text .btn:hover {
        background: #8E7037;
        border: 2px solid #8E7037;
        color: white; }
  .reservation-room_item .reservation-room_package {
    clear: both;
    margin-top: 30px; }
    .reservation-room_item .reservation-room_package .reservation-room_package-more {
      display: block;
      color: #333333;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
      padding: 15px 30px;
      background-color: #f1f1f1; }
      .reservation-room_item .reservation-room_package .reservation-room_package-more:after {
        content: '\f067';
        font-family: 'FontAwesome';
        float: right;
        font-size: 12px; }
      .reservation-room_item .reservation-room_package .reservation-room_package-more[aria-expanded="true"]:after {
        content: '\f068'; }

.reservation-package_item {
  overflow: hidden;
  padding: 20px 30px;
  background-color: #fbfbfb; }
  .reservation-package_item .reservation-package_img {
    float: left;
    width: 100px;
    margin-right: 30px; }
    @media (max-width: 480px) {
      .reservation-package_item .reservation-package_img {
        float: none;
        width: 100%; } }
    .reservation-package_item .reservation-package_img img {
      width: 100%; }
  .reservation-package_item .reservation-package_text {
    overflow: hidden;
    padding-right: 130px;
    position: relative;
    min-height: 72px; }
    @media (max-width: 480px) {
      .reservation-package_item .reservation-package_text {
        padding-right: 0;
        margin-top: 15px; } }
    .reservation-package_item .reservation-package_text h4 {
      text-transform: uppercase;
      font-weight: bold;
      font-size: 14px;
      margin: 0; }
      .reservation-package_item .reservation-package_text h4 a {
        color: #333; }
        .reservation-package_item .reservation-package_text h4 a:hover {
          color: #8E7037; }
    .reservation-package_item .reservation-package_text p {
      color: #898989;
      font-size: 12px;
      margin-bottom: 0;
      margin-top: 10px; }
    .reservation-package_item .reservation-package_text .reservation-package_book-price {
      position: absolute;
      right: 0;
      top: 0; }
      @media (max-width: 480px) {
        .reservation-package_item .reservation-package_text .reservation-package_book-price {
          position: initial;
          margin-top: 10px; }
          .reservation-package_item .reservation-package_text .reservation-package_book-price .reservation-package_price {
            float: right; } }
      .reservation-package_item .reservation-package_text .reservation-package_book-price .reservation-package_price {
        color: #333333;
        text-align: right; }
        .reservation-package_item .reservation-package_text .reservation-package_book-price .reservation-package_price .amout {
          font-weight: bold;
          font-size: 20px;
          line-height: 1; }
      .reservation-package_item .reservation-package_text .reservation-package_book-price .btn {
        padding: 5px 10px;
        min-width: inherit;
        font-size: 10px;
        margin-top: 5px;
        color: #333;
        font-weight: 700;
        background: transparent;
        border: 2px solid #333;
        height: 30px;
        line-height: 15px;
        width: 100px; }
        .reservation-package_item .reservation-package_text .reservation-package_book-price .btn:hover {
          color: #FFF;
          background: #8E7037;
          border: 2px solid #8E7037; }
  .reservation-package_item:hover, .reservation-package_item.current-select {
    background-color: #f1f1f1; }

.reservation-chosen-message {
  background-color: #f1f1f1;
  text-align: center;
  padding-bottom: 45px;
  padding-top: 80px; }
  @media (max-width: 767px) {
    .reservation-chosen-message {
      padding-left: 10px;
      padding-right: 10px;
      padding-top: 45px; } }
  .reservation-chosen-message h4 {
    font-weight: bold;
    text-transform: uppercase;
    margin: 0;
    font-size: 20px;
    color: #333; }
  .reservation-chosen-message p {
    color: #898989;
    font-size: 14px;
    margin-top: 20px;
    margin-bottom: 0; }
  .reservation-chosen-message .btn {
    width: 150px;
    color: #fff;
    padding: 10px;
    height: 40px;
    margin-top: 30px;
    line-height: 17px; }

.reservation-billing-detail {
  max-width: 570px; }
  @media (max-width: 1200px) {
    .reservation-billing-detail {
      max-width: 100%; } }
  .reservation-billing-detail .reservation-login {
    margin-bottom: 0;
    color: #898989; }
    .reservation-billing-detail .reservation-login a {
      font-weight: 600;
      color: #232323;
      font-family: "Poppins", sans-serif; }
      .reservation-billing-detail .reservation-login a:hover {
        color: #8E7037; }
  .reservation-billing-detail h4 {
    color: #000;
    text-transform: uppercase;
    margin-top: 30px;
    margin-bottom: 0;
    padding-bottom: 10px;
    font-weight: bold;
    letter-spacing: 5px;
    font-size: 30px; }
    @media (max-width: 480px) {
      .reservation-billing-detail h4 {
        font-size: 24px; } }
  .reservation-billing-detail label {
    color: #232323;
    font-size: 12px;
    text-transform: uppercase;
    display: block;
    margin-top: 20px;
    margin-bottom: 10px; }
    .reservation-billing-detail label sup {
      color: #ff0000;
      font-size: 13px;
      line-height: 0px;
      top: 0px;
      left: 4px; }
    .reservation-billing-detail label.label-radio .input-radio {
      margin: 0;
      display: inline-block;
      vertical-align: middle;
      margin-right: 15px; }
  .reservation-billing-detail .bootstrap-select.awe-select.btn-group {
    width: 100%; }
    .reservation-billing-detail .bootstrap-select.awe-select.btn-group .btn {
      border: 2px solid #e4e4e4;
      font-weight: 400;
      height: 43px;
      -webkit-border-radius: 0;
      border-radius: 0;
      padding: 0px 20px;
      background: transparent;
      -webkit-box-shadow: none;
      box-shadow: none;
      outline: none;
      font-size: 14px; }
      .reservation-billing-detail .bootstrap-select.awe-select.btn-group .btn:hover, .reservation-billing-detail .bootstrap-select.awe-select.btn-group .btn:focus {
        background-color: transparent;
        outline: none !important; }
      .reservation-billing-detail .bootstrap-select.awe-select.btn-group .btn .filter-option,
      .reservation-billing-detail .bootstrap-select.awe-select.btn-group .btn .caret {
        color: #333; }
    .reservation-billing-detail .bootstrap-select.awe-select.btn-group .dropdown-menu {
      -webkit-border-radius: 0;
      border-radius: 0; }
      .reservation-billing-detail .bootstrap-select.awe-select.btn-group .dropdown-menu li.selected a {
        background-color: #8E7037;
        color: #FFF; }
      .reservation-billing-detail .bootstrap-select.awe-select.btn-group .dropdown-menu li a {
        padding: 7px 20px; }
        .reservation-billing-detail .bootstrap-select.awe-select.btn-group .dropdown-menu li a:hover {
          background: #8E7037;
          color: #FFF; }
  .reservation-billing-detail .input-text,
  .reservation-billing-detail .input-textarea {
    border: 2px solid #e4e4e4;
    width: 100%;
    font-size: 14px;
    color: #333333;
    font-weight: normal;
    height: 43px;
    -webkit-border-radius: 0;
    border-radius: 0;
    padding: 20px; }
    .reservation-billing-detail .input-text::placeholder,
    .reservation-billing-detail .input-textarea::placeholder {
      color: #333; }
  .reservation-billing-detail .input-textarea {
    height: 85px; }
  .reservation-billing-detail .reservation-code {
    background-color: #333333;
    color: #ffffff;
    font-size: 14px;
    padding: 15px 20px;
    margin-top: 35px; }
    .reservation-billing-detail .reservation-code a {
      color: #fff;
      font-weight: 600; }
      .reservation-billing-detail .reservation-code a:hover {
        color: #8E7037; }
  .reservation-billing-detail .option-bank {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
    padding-top: 10px; }
    .reservation-billing-detail .option-bank li {
      position: relative;
      padding-left: 30px;
      margin-top: 25px; }
      .reservation-billing-detail .option-bank li a {
        padding-right: 6px;
        display: inline-block; }
        @media (max-width: 580px) {
          .reservation-billing-detail .option-bank li a img {
            max-width: 33px; } }
      .reservation-billing-detail .option-bank li .label-radio {
        font-size: 14px;
        margin: 0 40px 0 0;
        display: inline-block; }
        @media (max-width: 580px) {
          .reservation-billing-detail .option-bank li .label-radio {
            display: block; } }
        .reservation-billing-detail .option-bank li .label-radio .input-radio {
          position: absolute;
          top: 4px;
          left: 0; }
      .reservation-billing-detail .option-bank li p {
        font-size: 11px;
        margin-bottom: 0;
        margin-top: 5px; }
      .reservation-billing-detail .option-bank li:last-child .label-radio .input-radio {
        top: 7px; }
  .reservation-billing-detail .btn4 {
    font-weight: 700;
    font-size: 14px;
    margin-top: 40px;
    width: 170px;
    height: 48px; }
    .reservation-billing-detail .btn4:hover {
      border: 2px solid #8E7037; }

/* 2.4.27 DATEPICKER */
.datepicker.dropdown-menu {
  font-family: "Poppins", sans-serif !important;
  z-index: 99 !important;
  min-width: 239px !important;
  border: none !important;
  -webkit-border-radius: 0 !important;
  border-radius: 0 !important; }
  .datepicker.dropdown-menu td {
    border: 1px solid transparent !important;
    -webkit-border-radius: 0;
    border-radius: 0; }
    .datepicker.dropdown-menu td.day {
      padding: 5px 10px; }

.datepicker table tr td.day:hover,
.datepicker table tr td.day.focused {
  border: 1px solid #8E7037 !important;
  -webkit-border-radius: 0px;
  border-radius: 0px;
  background: transparent; }

.datepicker table tr td.active.active {
  background: #8E7037 !important;
  -webkit-border-radius: 0px;
  border-radius: 0px;
  border: 1px solid transparent !important; }

.datepicker {
  padding: 0 !important; }

.table-condensed {
  border-collapse: initial;
  border-spacing: 0px; }
  .table-condensed .today {
    background-color: #8f7037 !important;
    background-image: none !important;
    color: white !important; }
    .table-condensed .today:hover {
      background-color: #8f7037 !important;
      color: white !important; }
  .table-condensed thead tr .prev,
  .table-condensed thead tr .datepicker-switch,
  .table-condensed thead tr .next {
    background: #8f7037 !important;
    -webkit-border-radius: 0;
    border-radius: 0;
    color: #FFF;
    padding: 10px 0px; }
  .table-condensed thead tr .prev,
  .table-condensed thead tr .next {
    font-size: 0; }
  .table-condensed thead tr .datepicker-switch {
    text-transform: uppercase;
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    vertical-align: middle; }
  .table-condensed thead tr .prev:before {
    content: "\f3d5";
    display: inline-block;
    font-family: Ionicons;
    font-size: 20px;
    vertical-align: middle; }
  .table-condensed thead tr .next:before {
    content: "\f3d6";
    display: inline-block;
    font-family: Ionicons;
    font-size: 20px;
    vertical-align: middle; }
  .table-condensed thead tr .dow {
    background: #977A42;
    -webkit-border-radius: 0;
    border-radius: 0;
    color: #FFF;
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    padding: 6px 0px;
    text-transform: uppercase;
    font-size: 12px; }

/* 2.4.28 PRODUCT */
.section-product-detail {
  padding-bottom: 100px; }
  @media (max-width: 767px) {
    .section-product-detail {
      padding-bottom: 60px; } }

.product-detail_book {
  background-color: #f1f1f1; }
  .product-detail_book .product-detail_total {
    padding: 20px 30px;
    text-align: center; }
    .product-detail_book .product-detail_total h6 {
      font-size: 12px;
      font-weight: bold;
      margin-top: 10px;
      color: #333; }
    .product-detail_book .product-detail_total .price {
      margin-bottom: 0;
      margin-top: 10px;
      color: #898989;
      font-size: 18px;
      line-height: 1; }
      .product-detail_book .product-detail_total .price .amout {
        font-weight: bold;
        font-size: 40px;
        color: #333; }
  .product-detail_book .product-detail_form {
    border-top: 1px solid #e4e4e4;
    padding: 0 30px 27px 30px; }
    .product-detail_book .product-detail_form .sidebar {
      margin: 0; }
      .product-detail_book .product-detail_form .sidebar .widget {
        margin-top: 25px; }
      .product-detail_book .product-detail_form .sidebar .check_availability {
        padding: 0;
        border: none; }
        .product-detail_book .product-detail_form .sidebar .check_availability .check_availability-field {
          margin-top: 23px; }
          .product-detail_book .product-detail_form .sidebar .check_availability .check_availability-field .btn {
            font-weight: 400;
            padding: 0px 12px; }
          .product-detail_book .product-detail_form .sidebar .check_availability .check_availability-field i {
            color: #898989; }
    .product-detail_book .product-detail_form .btn-product {
      width: 100%;
      font-weight: bold;
      font-size: 14px;
      padding: 10px;
      height: 45px;
      text-transform: uppercase;
      line-height: 20px;
      margin-top: 30px; }

.product-detail_tab {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e4e4e4;
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 20px; }

.product-detail_tab-header {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
  margin-right: 70px; }
  @media (max-width: 992px) {
    .product-detail_tab-header {
      margin-right: 0; } }
  .product-detail_tab-header li {
    border-bottom: 2px solid #e4e4e4;
    position: relative; }
    @media (max-width: 992px) {
      .product-detail_tab-header li {
        text-align: center; }
        .product-detail_tab-header li a {
          display: block !important; } }
    .product-detail_tab-header li a {
      padding: 20px 25px 10px 0;
      color: #232323;
      text-transform: uppercase;
      font-weight: bold;
      position: relative;
      display: inline-block; }
      .product-detail_tab-header li a:before {
        height: 2px;
        content: '';
        width: 0;
        position: absolute;
        left: 0;
        bottom: -2px;
        background: #8E7037;
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out; }
    .product-detail_tab-header li.active a, .product-detail_tab-header li:hover a {
      color: #8E7037; }
      .product-detail_tab-header li.active a:before, .product-detail_tab-header li:hover a:before {
        width: 100%;
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out; }

.product-detail_tab-content {
  padding-top: 15px;
  position: relative; }
  .product-detail_tab-content:before {
    border-left: 1px solid #e4e4e4;
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0; }
    @media (max-width: 992px) {
      .product-detail_tab-content:before {
        border: none; } }

.product-detail_amenities,
.product-detail_overview {
  font-size: 14px;
  font-weight: 400;
  color: #333; }
  .product-detail_amenities h5,
  .product-detail_overview h5 {
    font-size: 18px;
    font-weight: bold;
    line-height: 25px; }
    @media (max-width: 480px) {
      .product-detail_amenities h5,
      .product-detail_overview h5 {
        font-size: 14px; } }
  .product-detail_amenities p,
  .product-detail_overview p {
    font-size: 14px;
    margin-bottom: 0; }
  .product-detail_amenities h6,
  .product-detail_overview h6 {
    text-transform: uppercase;
    color: #333;
    font-weight: bold;
    font-size: 14px;
    margin-top: 37px; }
  .product-detail_amenities ul,
  .product-detail_overview ul {
    padding-left: 17px;
    margin-bottom: 0;
    margin-top: 5px; }
    .product-detail_amenities ul li,
    .product-detail_overview ul li {
      padding: 4px 0;
      padding-left: 10px; }

.product-detail_package .product-package_item {
  padding: 25px 0;
  border-bottom: 1px solid #e4e4e4; }
  @media (max-width: 480px) {
    .product-detail_package .product-package_item .text {
      padding-right: 0; }
      .product-detail_package .product-package_item .text .product-package_price {
        position: initial; }
        .product-detail_package .product-package_item .text .product-package_price .price {
          margin-top: 10px;
          vertical-align: middle;
          display: inline-block;
          margin-right: 5px; } }
  .product-detail_package .product-package_item:first-child {
    padding-top: 0; }
  .product-detail_package .product-package_item:last-child {
    border-bottom: 0; }

.product-package_item {
  overflow: hidden; }
  .product-package_item .img {
    float: left;
    width: 100px;
    margin-right: 30px; }
    .product-package_item .img img {
      width: 100%; }
  .product-package_item .text {
    overflow: hidden;
    padding-right: 170px;
    position: relative;
    min-height: 70px; }
    .product-package_item .text h4 {
      text-transform: uppercase;
      font-weight: bold;
      font-size: 14px;
      margin: 0; }
      .product-package_item .text h4 a {
        color: #333; }
        .product-package_item .text h4 a:hover {
          color: #8E7037; }
    .product-package_item .text p {
      color: #333;
      font-size: 13px;
      line-height: 1.428em;
      margin-bottom: 0;
      margin-top: 10px; }
    .product-package_item .text .product-package_price {
      position: absolute;
      right: 0;
      top: 0; }
      .product-package_item .text .product-package_price .price {
        color: #898989;
        font-size: 12px;
        margin: 0;
        text-align: right; }
        .product-package_item .text .product-package_price .price .amout {
          font-weight: 700;
          font-size: 20px;
          color: #333; }
      .product-package_item .text .product-package_price .btn {
        margin-top: 10px;
        font-size: 10px;
        font-weight: bold;
        text-transform: uppercase;
        color: #333;
        background: transparent;
        border: 2px solid #333;
        height: 30px;
        line-height: 27px;
        width: 100px;
        padding: 0; }
        .product-package_item .text .product-package_price .btn:hover {
          background: #8E7037;
          color: #FFF;
          border: 2px solid #8E7037; }

.product-detail_rates {
  padding-bottom: 20px; }
  @media (max-width: 768px) {
    .product-detail_rates {
      -ms-overflow-y: auto;
      overflow-y: auto; }
      .product-detail_rates table {
        width: 720px !important; }
        .product-detail_rates table th {
          font-size: 11px !important; } }
  .product-detail_rates table {
    width: 100%; }
    .product-detail_rates table th {
      font-size: 12px;
      font-family: 'Montserrat', sans-serif;
      text-transform: uppercase;
      font-weight: bold;
      background-color: #f1f1f1;
      padding: 15px 20px; }
      @media (min-width: 992px) and (max-width: 1200px) {
        .product-detail_rates table th {
          font-size: 11px; } }
      @media (min-width: 768px) and (max-width: 991px) {
        .product-detail_rates table th {
          font-size: 11px; } }
    .product-detail_rates table td {
      padding: 15px 20px;
      border-bottom: 1px solid #e4e4e4;
      vertical-align: top; }
      .product-detail_rates table td h6 {
        text-transform: uppercase;
        font-size: 14px;
        font-family: 'Hind', sans-serif;
        font-weight: bold; }
      .product-detail_rates table td ul {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
        margin-top: 10px; }
        .product-detail_rates table td ul li {
          padding: 2px 0;
          color: #898989;
          font-family: 'Hind', sans-serif;
          font-weight: 500; }
      .product-detail_rates table td .price {
        font-family: 'Montserrat', sans-serif;
        font-size: 14px;
        margin-bottom: 0;
        font-weight: 400; }

.calendar_custom {
  max-width: 390px; }
  @media (max-width: 768px) {
    .calendar_custom {
      max-width: 100%; } }
  .calendar_custom .calendar_title {
    text-align: center;
    color: #333;
    font-size: 14px;
    text-transform: uppercase;
    margin: 0;
    font-weight: 600;
    padding-top: 15px;
    padding-bottom: 15px;
    position: relative;
    border-bottom: 1px solid #e4e4e4;
    margin-left: 3px;
    margin-right: 3px; }
    .calendar_custom .calendar_title .calendar_corner {
      display: block;
      width: 24px;
      height: 24px;
      text-align: center;
      color: #cbcbcb;
      position: absolute;
      line-height: 0px;
      font-size: 35px;
      top: 5px; }
      .calendar_custom .calendar_title .calendar_corner:hover {
        color: #333; }
      .calendar_custom .calendar_title .calendar_corner.calendar_prev {
        left: 0; }
      .calendar_custom .calendar_title .calendar_corner.calendar_next {
        right: 0; }
  .calendar_custom .calendar_tabel {
    width: 100%;
    vertical-align: middle;
    text-align: center;
    table-layout: fixed; }
    .calendar_custom .calendar_tabel th {
      text-transform: uppercase;
      text-align: center;
      color: #333;
      font-size: 12px;
      padding-top: 10px;
      padding-bottom: 10px;
      font-weight: bold;
      font-family: 'Hind', sans-serif; }
    .calendar_custom .calendar_tabel td {
      background-color: #fff;
      background-clip: content-box;
      padding: 3px; }
      .calendar_custom .calendar_tabel td a {
        position: relative;
        display: block;
        padding-top: 100%;
        color: #333;
        font-size: 12px; }
        .calendar_custom .calendar_tabel td a:before {
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          content: '';
          border: 1px solid #333;
          position: absolute; }
        .calendar_custom .calendar_tabel td a:hover:before {
          border: 1px solid #8E7037; }
        .calendar_custom .calendar_tabel td a small {
          position: absolute;
          top: 5px;
          right: 10px;
          font-size: 12px; }
        .calendar_custom .calendar_tabel td a span {
          position: absolute;
          top: 60%;
          left: 50%; }
      .calendar_custom .calendar_tabel td.not-available {
        background-color: #333; }
        .calendar_custom .calendar_tabel td.not-available a {
          color: #fff; }

.product-detail_calendar-wrap .calendar_status {
  margin-top: 30px; }
  .product-detail_calendar-wrap .calendar_status span {
    color: #333333;
    font-size: 12px;
    font-weight: 400;
    padding: 0 10px; }
    .product-detail_calendar-wrap .calendar_status span:before {
      width: 20px;
      height: 20px;
      border: 1px solid #333;
      display: inline-block;
      vertical-align: middle;
      content: '';
      margin-right: 10px; }
    .product-detail_calendar-wrap .calendar_status span.not-available:before {
      background-color: #333; }

.product-detail {
  padding-top: 30px; }
  .product-detail.margin {
    margin-top: 15px; }
    @media (max-width: 767px) {
      .product-detail.margin {
        margin: 0; } }
  .product-detail .product-detail_title {
    font-weight: bold;
    margin: 0;
    font-size: 22px;
    line-height: 1;
    text-transform: uppercase; }
    @media (max-width: 767px) {
      .product-detail .product-detail_title {
        font-size: 19px; } }
  .product-detail .product-detail_item {
    margin-top: 30px; }

.product-detail_item {
  position: relative; }
  .product-detail_item .img {
    position: relative; }
    .product-detail_item .img img {
      width: 100%; }
  .product-detail_item .text {
    margin-top: 20px; }
    .product-detail_item .text h2 {
      font-size: 19px;
      font-weight: bold;
      margin: 0;
      text-transform: uppercase; }
      @media (min-width: 992px) and (max-width: 1200px) {
        .product-detail_item .text h2 {
          font-size: 17px; } }
      .product-detail_item .text h2 a {
        color: #333; }
        .product-detail_item .text h2 a:hover {
          color: #8E7037; }
    .product-detail_item .text ul {
      color: #898989;
      margin-top: 10px;
      font-size: 14px;
      margin-bottom: 0;
      color: #333333;
      padding-left: 0;
      list-style: none; }
      @media (min-width: 992px) and (max-width: 1200px) {
        .product-detail_item .text ul {
          font-size: 12px; }
          .product-detail_item .text ul li i {
            font-size: 14px;
            width: 25px; } }
      .product-detail_item .text ul li {
        padding: 4px 0; }
        .product-detail_item .text ul li i {
          font-size: 16px;
          width: 30px;
          display: inline-block; }
    .product-detail_item .text .btn {
      font-size: 10px;
      margin-top: 20px;
      font-weight: bold;
      background: transparent;
      border: 2px solid #333333;
      color: #333;
      line-height: 19px;
      width: 100px;
      height: 35px; }
      .product-detail_item .text .btn:hover {
        background: #8E7037;
        border: 2px solid #8E7037;
        color: #FFF; }

/*2.4.29 GUEST-BOOK  */
.section-guest-book {
  padding-top: 50px;
  padding-bottom: 60px; }
  @media (max-width: 768px) {
    .section-guest-book {
      padding-top: 10px;
      padding-bottom: 50px; } }
  .section-guest-book .guest-book .guest-book_head {
    background-size: cover;
    overflow: hidden; }
    .section-guest-book .guest-book .guest-book_head .text {
      max-width: 660px;
      width: auto;
      margin-left: auto;
      margin-right: auto; }
      .section-guest-book .guest-book .guest-book_head .text h2 {
        color: #333;
        font-weight: bold;
        font-size: 40px;
        text-transform: uppercase;
        letter-spacing: 5px;
        line-height: 52px;
        font-family: "Poppins", sans-serif; }
        @media (max-width: 768px) {
          .section-guest-book .guest-book .guest-book_head .text h2 {
            font-size: 30px;
            letter-spacing: 2px; } }
        @media (max-width: 480px) {
          .section-guest-book .guest-book .guest-book_head .text h2 {
            font-size: 20px;
            letter-spacing: 0px;
            line-height: 35px; } }
      .section-guest-book .guest-book .guest-book_head .text p {
        color: #898989;
        font-size: 15px;
        line-height: 1.7em;
        margin-bottom: 0;
        margin-top: 20px; }
        @media (max-width: 480px) {
          .section-guest-book .guest-book .guest-book_head .text p {
            margin-top: 0px; } }
        @media (max-width: 767px) {
          .section-guest-book .guest-book .guest-book_head .text p {
            font-size: 14px; } }
      .section-guest-book .guest-book .guest-book_head .text .btn {
        font-size: 14px;
        height: 42px;
        width: 220px;
        margin-top: 40px;
        font-weight: 700;
        line-height: 28px; }
        @media (max-width: 480px) {
          .section-guest-book .guest-book .guest-book_head .text .btn {
            margin-top: 25px; } }
        .section-guest-book .guest-book .guest-book_head .text .btn:focus {
          color: #8E7037;
          background: transparent;
          border: 2px solid #8E7037;
          outline: none; }
      .section-guest-book .guest-book .guest-book_head .text .modal {
        overflow-x: hidden;
        overflow-y: scroll; }
        .section-guest-book .guest-book .guest-book_head .text .modal.in .modal-dialog {
          -webkit-transform: translate(0, 25%);
          -ms-transform: translate(0, 25%);
          -o-transform: translate(0, 25%);
          transform: translate(0, 25%); }
        .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog {
          max-width: 370px;
          margin: 0 auto; }
          .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content {
            padding: 35px;
            border-radius: 0;
            text-align: initial;
            background: #f1f1f1; }
            .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-header .close {
              font-size: 40px;
              position: absolute;
              top: 0px;
              right: 10px;
              color: #8E7037;
              opacity: 1; }
            .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-header .modal-title {
              color: #333333;
              font-size: 26px;
              text-transform: uppercase;
              font-weight: bold;
              line-height: 1.3em;
              text-align: center;
              margin-top: -5px;
              letter-spacing: 2px; }
            .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-header,
            .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-body,
            .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-footer {
              padding: 0;
              border: none; }
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-header .btn,
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-body .btn,
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-footer .btn {
                width: 100%; }
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-header p,
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-body p,
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-footer p {
                margin-top: 15px; }
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-header .form-group,
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-body .form-group,
              .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-footer .form-group {
                margin-bottom: -9px;
                margin-top: 30px; }
                .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-header .form-group .form-control,
                .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-body .form-group .form-control,
                .section-guest-book .guest-book .guest-book_head .text .modal .modal-dialog .modal-content .modal-footer .form-group .form-control {
                  height: 38px;
                  border-radius: 0;
                  margin-top: 20px;
                  border: none;
                  box-shadow: none;
                  color: #898989; }
  .section-guest-book .guest-book .guest-book_content {
    margin-top: 40px; }
    @media (max-width: 480px) {
      .section-guest-book .guest-book .guest-book_content {
        margin-top: 0px; } }
    .section-guest-book .guest-book .guest-book_content .guest-book_mansory .item {
      padding: 0 15px; }
    @media (max-width: 480px) {
      .section-guest-book .guest-book .guest-book_content .page-navigation .first {
        margin-right: 20px; }
      .section-guest-book .guest-book .guest-book_content .page-navigation li {
        margin-right: 15px; }
      .section-guest-book .guest-book .guest-book_content .page-navigation .last {
        margin-left: 5px;
        margin-right: 0; } }
    .section-guest-book .guest-book .guest-book_content .guest-book_item {
      position: relative;
      cursor: pointer;
      margin-top: 30px;
      text-align: center;
      padding: 25px 35px 25px 35px;
      background: #F9F9F9; }
      .section-guest-book .guest-book .guest-book_content .guest-book_item:before, .section-guest-book .guest-book .guest-book_content .guest-book_item:after {
        content: '';
        display: block;
        position: absolute;
        z-index: 1;
        pointer-events: none;
        top: -2px;
        right: -2px;
        bottom: -2px;
        left: -2px;
        -webkit-transform: scale(0);
        -moz-transform: scale(0);
        -ms-transform: scale(0);
        -o-transform: scale(0);
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
      .section-guest-book .guest-book .guest-book_content .guest-book_item:before {
        border-right: 2px solid #e1bd85;
        border-bottom: 2px solid #e1bd85;
        -webkit-transform-origin: bottom right;
        -moz-transform-origin: bottom right;
        -ms-transform-origin: bottom right;
        -o-transform-origin: bottom right; }
      .section-guest-book .guest-book .guest-book_content .guest-book_item:after {
        border-left: 2px solid #e1bd85;
        border-top: 2px solid #e1bd85;
        -webkit-transform-origin: top left;
        -moz-transform-origin: top left;
        -ms-transform-origin: top left;
        -o-transform-origin: top left; }
      .section-guest-book .guest-book .guest-book_content .guest-book_item:hover:before, .section-guest-book .guest-book .guest-book_content .guest-book_item:hover:after {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1); }
      .section-guest-book .guest-book .guest-book_content .guest-book_item:hover .avatar img {
        transform: scale(1.1, 1.1);
        -webkit-transition: all 0.5s ease-in-out;
        -moz-transition: all 0.5s ease-in-out;
        -ms-transition: all 0.5s ease-in-out;
        -o-transition: all 0.5s ease-in-out;
        transition: all 0.5s ease-in-out; }
      @media (max-width: 768px) {
        .section-guest-book .guest-book .guest-book_content .guest-book_item {
          padding: 1px 5px 25px 5px; }
          .section-guest-book .guest-book .guest-book_content .guest-book_item p {
            font-size: 13px; } }
      .section-guest-book .guest-book .guest-book_content .guest-book_item .avatar {
        width: 60px;
        height: 60px;
        margin-left: auto;
        margin-right: auto;
        margin-top: 20px;
        overflow: hidden;
        position: relative;
        border-radius: 100%; }
        .section-guest-book .guest-book .guest-book_content .guest-book_item .avatar img {
          transform: scale(1, 1);
          -webkit-transition: all 0.5s ease-in-out;
          -moz-transition: all 0.5s ease-in-out;
          -ms-transition: all 0.5s ease-in-out;
          -o-transition: all 0.5s ease-in-out;
          transition: all 0.5s ease-in-out; }
      .section-guest-book .guest-book .guest-book_content .guest-book_item h2 {
        margin-top: 30px;
        font-size: 20px;
        text-transform: uppercase;
        color: #444444;
        font-weight: 600;
        font-family: "Poppins", sans-serif;
        letter-spacing: 4px; }
      .section-guest-book .guest-book .guest-book_content .guest-book_item p {
        color: #333;
        font-size: 15px;
        margin-top: 15px;
        margin-bottom: 0; }
      .section-guest-book .guest-book .guest-book_content .guest-book_item span {
        display: block;
        color: #898989;
        font-size: 14px;
        font-weight: 400;
        text-transform: capitalize;
        margin-top: 20px; }
        .section-guest-book .guest-book .guest-book_content .guest-book_item span b {
          color: #444444;
          font-weight: 600;
          text-transform: uppercase; }

.footer-sky.footer-sky-v4 {
  padding-top: 45px;
  background: url("../images/Home-4/footer.jpg") no-repeat;
  width: 100%;
  background-size: cover;
  background-position: center center; }
  .footer-sky.footer-sky-v4 .footer-top {
    background: 0; }
    .footer-sky.footer-sky-v4 .footer-top h2 {
      font-weight: 700;
      color: #FFF;
      font-size: 40px;
      letter-spacing: 2px; }
    .footer-sky.footer-sky-v4 .footer-top p {
      font-size: 16px;
      font-weight: 300;
      color: #FFF; }
  .footer-sky.footer-sky-v4 .icon-email {
    display: inline-block; }
  .footer-sky.footer-sky-v4 .footer-bottom .footer-icon a {
    padding: 0px 17px;
    color: #FFF;
    font-size: 18px; }
    .footer-sky.footer-sky-v4 .footer-bottom .footer-icon a:hover {
      color: #8E7037; }
  .footer-sky.footer-sky-v4 .footer-bottom .footer-bottom-l {
    color: #ebebeb;
    font-size: 13px;
    font-weight: 300; }
    .footer-sky.footer-sky-v4 .footer-bottom .footer-bottom-l a {
      font-weight: 400;
      color: #8E7037; }
      .footer-sky.footer-sky-v4 .footer-bottom .footer-bottom-l a:hover {
        color: #8E7037; }
  .footer-sky.footer-sky-v4 .textbox {
    margin-bottom: 50px;
    margin-top: 45px; }
    .footer-sky.footer-sky-v4 .textbox .input-group {
      width: 570px !important;
      border: 2px solid white; }
      .footer-sky.footer-sky-v4 .textbox .input-group .form-control {
        background: transparent !important; }
      .footer-sky.footer-sky-v4 .textbox .input-group .btn {
        background: transparent !important; }
        .footer-sky.footer-sky-v4 .textbox .input-group .btn .ion-android-send {
          color: #FFF !important;
          border-left: 1px solid #FFF !important; }
.footer-sky .footer-top {
  padding: 32px 0;
  margin: -2px 0;
  background: #2e2e2e; }
  .footer-sky .footer-top.footer-top-v3 {
    background: 0;
    padding-bottom: 100px; }
    .footer-sky .footer-top.footer-top-v3 .bg-v3 {
      background: url("../images/Home-3/news.jpg") no-repeat;
      padding: 43px 60px;
      background-size: cover;
      background-position: center center; }
      .footer-sky .footer-top.footer-top-v3 .bg-v3 .form-control {
        background: rgba(212, 206, 206, 0.31) !important; }
        .footer-sky .footer-top.footer-top-v3 .bg-v3 .form-control::placeholder {
          color: #FFF; }
      .footer-sky .footer-top.footer-top-v3 .bg-v3 .btn {
        background: transparent !important; }
        .footer-sky .footer-top.footer-top-v3 .bg-v3 .btn .ion-android-send {
          border-left: 1px solid #747d86 !important;
          color: #FFF !important; }
  .footer-sky .footer-top .textbox .form-inline {
    margin-top: 2px; }
    .footer-sky .footer-top .textbox .form-inline .form-group .input-group {
      width: 450px; }
      .footer-sky .footer-top .textbox .form-inline .form-group .input-group .form-control {
        font-size: 14px;
        font-weight: 300;
        color: #ffffff;
        font-family: 'Poppins', sans-serif;
        background: #3E3E3E;
        border: none;
        -webkit-border-radius: 0px;
        border-radius: 0px;
        padding: 6px 25px;
        height: 60px; }
        .footer-sky .footer-top .textbox .form-inline .form-group .input-group .form-control::placeholder {
          color: #FFF; }
        .footer-sky .footer-top .textbox .form-inline .form-group .input-group .form-control:focus {
          border: none;
          -webkit-box-shadow: none;
          box-shadow: none; }
      .footer-sky .footer-top .textbox .form-inline .form-group .input-group .btn {
        width: 80px;
        height: 60px;
        border: none;
        -webkit-border-radius: 0px;
        border-radius: 0px;
        background: #3E3E3E;
        -webkit-box-shadow: none;
        box-shadow: none;
        position: absolute;
        right: 10px;
        z-index: 99; }
        .footer-sky .footer-top .textbox .form-inline .form-group .input-group .btn:hover .ion-android-send {
          color: #ffffff; }
        .footer-sky .footer-top .textbox .form-inline .form-group .input-group .btn .ion-android-send {
          color: #B2B2B2;
          font-size: 30px;
          line-height: 0px;
          border-left: 1px solid #4c4c4c;
          padding-left: 25px; }
  .footer-sky .footer-top .footer-icon-l .fa {
    line-height: 64px;
    color: #ffffff;
    font-size: 20px;
    padding-left: 43px; }
    .footer-sky .footer-top .footer-icon-l .fa:hover {
      color: #8e7037; }
.footer-sky .footer-bottom {
  border-top: 1px solid #2e2e2e;
  font-size: 13px;
  font-weight: 300;
  color: #ebebeb;
  line-height: 100px; }
  .footer-sky .footer-bottom.bottom-v3 {
    line-height: 85px;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    color: #ebebeb;
    font-weight: 300;
    font-size: 13px; }
    .footer-sky .footer-bottom.bottom-v3 a {
      color: #8E7037; }
  .footer-sky .footer-bottom .no-padding {
    padding: 0; }
  .footer-sky .footer-bottom a {
    font-weight: 500;
    color: #8E7037; }
    .footer-sky .footer-bottom a:hover {
      color: #8E7037; }
  .footer-sky .footer-bottom .payments ul {
    padding: 0;
    margin: 0; }
    .footer-sky .footer-bottom .payments ul li {
      list-style: none;
      display: inline-block;
      padding-left: 25px; }
.footer-sky .footer-mid {
  background: #232323; }
  .footer-sky .footer-mid.footer-v3 {
    background: url("../images/Home-3/footer.jpg") no-repeat;
    width: 100%;
    background-size: cover;
    background-position: center center; }
    .footer-sky .footer-mid.footer-v3 .padding-footer-mid {
      padding: 95px 0px 67px 0px; }
      @media (max-width: 991px) {
        .footer-sky .footer-mid.footer-v3 .padding-footer-mid {
          padding: 20px 0px 30px 0px; } }
  .footer-sky .footer-mid .padding-footer-mid {
    padding: 85px 0px 30px 0px; }
    .footer-sky .footer-mid .padding-footer-mid .list-content.content-v3 ul li {
      margin-bottom: 12px; }
      .footer-sky .footer-mid .padding-footer-mid .list-content.content-v3 ul li a {
        color: #FFF; }
    .footer-sky .footer-mid .padding-footer-mid .list-content ul {
      padding: 0; }
      .footer-sky .footer-mid .padding-footer-mid .list-content ul li {
        list-style: none;
        margin-bottom: 16px; }
        .footer-sky .footer-mid .padding-footer-mid .list-content ul li a {
          color: #ebebeb;
          font-size: 14px;
          font-weight: 400;
          font-family: 'Poppins', sans-serif; }
          .footer-sky .footer-mid .padding-footer-mid .list-content ul li a:hover {
            color: #8e7037;
            text-decoration: none; }

.section-slider {
  position: relative; }
  .section-slider.height-v .item img {
    width: 100%;
    height: 100%; }
  .section-slider.height-v-about .item img {
    width: 100%; }
  .section-slider .check-avail {
    position: absolute;
    right: 0;
    left: 0;
    bottom: 6%;
    z-index: 9; }
    .section-slider .check-avail.check-avail-v3 {
      position: initial;
      padding: 50px 0; }
      .section-slider .check-avail.check-avail-v3 .date-title {
        border: 1px solid #ebebeb; }
    .section-slider .check-avail .date-title {
      height: 130px;
      margin-right: 15px; }
      .section-slider .check-avail .date-title:last-child {
        margin-right: 0px; }
      .section-slider .check-avail .date-title label {
        color: #8E7037;
        font-size: 14px;
        font-weight: 400;
        text-transform: uppercase;
        letter-spacing: 2px; }
    .section-slider .check-avail .container {
      display: flex;
      flex-direction: row; }
      .section-slider .check-avail .container .arrival,
      .section-slider .check-avail .container .departure {
        width: 27.2%;
        background: #fff;
        padding: 24px 26px 30px 35px;
        cursor: pointer;
        overflow: hidden; }
        .section-slider .check-avail .container .arrival input,
        .section-slider .check-avail .container .departure input {
          height: 55px;
          border: none;
          box-shadow: none;
          padding-left: 0;
          color: #b3b3b3;
          font-family: 'Playfair', serif;
          font-size: 37px;
          padding-right: 0;
          margin-bottom: 10px; }
          @media (max-width: 1200px) {
            .section-slider .check-avail .container .arrival input,
            .section-slider .check-avail .container .departure input {
              height: 47px;
              font-size: 28px; } }
          @media (max-width: 992px) {
            .section-slider .check-avail .container .arrival input,
            .section-slider .check-avail .container .departure input {
              font-size: 25px; } }
        .section-slider .check-avail .container .arrival .input-group-addon,
        .section-slider .check-avail .container .departure .input-group-addon {
          background: #fff;
          border: none;
          padding-top: 0;
          padding-right: 0;
          padding-bottom: 20px; }
          @media (max-width: 1200px) {
            .section-slider .check-avail .container .arrival .input-group-addon,
            .section-slider .check-avail .container .departure .input-group-addon {
              padding-bottom: 10px; }
              .section-slider .check-avail .container .arrival .input-group-addon img,
              .section-slider .check-avail .container .departure .input-group-addon img {
                width: 45px; } }
          @media (max-width: 992px) {
            .section-slider .check-avail .container .arrival .input-group-addon,
            .section-slider .check-avail .container .departure .input-group-addon {
              padding: 0px 10px 18px 0px; }
              .section-slider .check-avail .container .arrival .input-group-addon img,
              .section-slider .check-avail .container .departure .input-group-addon img {
                width: 35px; } }
      .section-slider .check-avail .container .children,
      .section-slider .check-avail .container .adults {
        background: #fff;
        width: 12%;
        text-align: center;
        padding-top: 24px; }
        .section-slider .check-avail .container .children .btn-group,
        .section-slider .check-avail .container .adults .btn-group {
          margin-top: -17px; }
          .section-slider .check-avail .container .children .btn-group .btn,
          .section-slider .check-avail .container .adults .btn-group .btn {
            font-size: 60px;
            padding: 0;
            border: none;
            -webkit-box-shadow: none;
            box-shadow: none;
            color: #353535;
            font-weight: 400;
            font-family: 'Playfair', serif;
            width: 141px; }
          .section-slider .check-avail .container .children .btn-group .dropdown-menu,
          .section-slider .check-avail .container .adults .btn-group .dropdown-menu {
            border: none;
            -webkit-border-radius: 0px;
            border-radius: 0px;
            min-width: 141px;
            top: 115%;
            left: 0px; }
            .section-slider .check-avail .container .children .btn-group .dropdown-menu .active > a,
            .section-slider .check-avail .container .adults .btn-group .dropdown-menu .active > a {
              background: #8E7037; }
            .section-slider .check-avail .container .children .btn-group .dropdown-menu a:hover,
            .section-slider .check-avail .container .adults .btn-group .dropdown-menu a:hover {
              background: #8E7037;
              color: white; }
      .section-slider .check-avail .container .find_btn {
        color: white;
        background: #8e7037;
        width: 16.3%; }
        .section-slider .check-avail .container .find_btn:hover {
          background: #fff;
          border: 2px solid #8E7037; }
          .section-slider .check-avail .container .find_btn:hover .text-find {
            color: #8E7037; }
        .section-slider .check-avail .container .find_btn .text-find {
          text-align: center;
          font-weight: 600;
          font-size: 14px;
          text-transform: uppercase;
          letter-spacing: 3px;
          padding-top: 45px;
          color: white;
          cursor: pointer; }
          .section-slider .check-avail .container .find_btn .text-find:hover {
            color: #8E7037; }
  .section-slider:hover .owl-nav {
    opacity: 1; }
  .section-slider.section-slider-v3 {
    margin-bottom: 60px; }
    .section-slider.section-slider-v3 .owl-nav {
      display: block !important;
      transition: all 0.3s ease-in-out;
      opacity: 1; }
      .section-slider.section-slider-v3 .owl-nav .owl-prev {
        left: 0%;
        top: 37%;
        font-size: 0;
        position: absolute; }
        .section-slider.section-slider-v3 .owl-nav .owl-prev:hover::before {
          opacity: 1;
          background: url("../images/Home-3/icon-color.png ") no-repeat;
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          -ms-transition: all 0.3s ease-in-out;
          -o-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out; }
        .section-slider.section-slider-v3 .owl-nav .owl-prev:before {
          content: "";
          position: absolute;
          background: url("../images/Home-3/icon.png") no-repeat;
          padding: 20px;
          opacity: 1;
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          -ms-transition: all 0.3s ease-in-out;
          -o-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out; }
      .section-slider.section-slider-v3 .owl-nav .owl-next {
        right: 0%;
        top: 50%;
        font-size: 0;
        position: absolute; }
        .section-slider.section-slider-v3 .owl-nav .owl-next:before {
          content: "";
          background: url("../images/Home-3/icon1.png ") no-repeat;
          padding: 19px;
          opacity: 1;
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          -ms-transition: all 0.3s ease-in-out;
          -o-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out; }
        .section-slider.section-slider-v3 .owl-nav .owl-next:hover::before {
          opacity: 1;
          background: url("../images/Home-3/icon1-color.png ") no-repeat;
          -webkit-transition: all 0.3s ease-in-out;
          -moz-transition: all 0.3s ease-in-out;
          -ms-transition: all 0.3s ease-in-out;
          -o-transition: all 0.3s ease-in-out;
          transition: all 0.3s ease-in-out; }
      @media all and (min-width: 1300px) {
        .section-slider.section-slider-v3 .owl-nav .owl-prev {
          left: -5.3%; }
        .section-slider.section-slider-v3 .owl-nav .owl-next {
          right: -5.3%; } }
  .section-slider .owl-nav {
    display: block !important;
    transition: all 0.3s ease-in-out;
    opacity: 0; }
    .section-slider .owl-nav .owl-prev {
      left: 5%;
      top: 50%;
      font-size: 0;
      position: absolute; }
      .section-slider .owl-nav .owl-prev:hover::before {
        opacity: 1; }
      .section-slider .owl-nav .owl-prev:before {
        content: "";
        background: url("../images/Home-1/icon-slider-l.png") no-repeat;
        padding: 20px;
        opacity: 0.6; }
    .section-slider .owl-nav .owl-next {
      right: 5%;
      top: 50%;
      font-size: 0;
      position: absolute; }
      .section-slider .owl-nav .owl-next:before {
        content: "";
        background: url("../images/Home-1/icon-slider-r.png ") no-repeat;
        padding: 20px;
        opacity: 0.6; }
      .section-slider .owl-nav .owl-next:hover::before {
        opacity: 1; }
  .section-slider .item {
    cursor: default; }
    .section-slider .item .wrap-best-v3 {
      border: 1px solid #ebebeb;
      padding: 25px 0 20px 0px;
      margin-right: 1px; }
      @media (max-width: 480px) {
        .section-slider .item .wrap-best-v3 .sky-h6 {
          font-size: 11px; } }
    .section-slider .item img {
      width: auto;
      margin: 0 auto; }
    .section-slider .item .carousel-caption {
      position: absolute;
      top: 50%;
      left: 50%;
      right: inherit;
      bottom: inherit;
      transform: translate(-50%, -75%);
      display: block;
      width: 100%; }
      .section-slider .item .carousel-caption h1 {
        font-size: 69px;
        font-weight: 400;
        font-family: 'Playfair Display', serif;
        color: #FFF;
        margin-bottom: 17px;
        text-shadow: none; }
      .section-slider .item .carousel-caption .v3 {
        font-size: 24px;
        font-weight: 300; }
      .section-slider .item .carousel-caption p {
        font-size: 24.75px;
        font-weight: 400;
        font-family: 'Poppins', sans-serif;
        letter-spacing: 5px;
        text-transform: uppercase;
        text-shadow: none;
        display: inline-flex;
        align-items: center; }
        .section-slider .item .carousel-caption p .line-t,
        .section-slider .item .carousel-caption p .line-b {
          width: 74px;
          height: 1px;
          background: #ffffff; }
        .section-slider .item .carousel-caption p .line-t {
          margin-right: 15px; }
        .section-slider .item .carousel-caption p .line-b {
          margin-left: 10px; }

body {
  color: #232323;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  padding: 0px;
  margin: 0px;
  font-weight: 400;
  line-height: 1.6; }

.sky-h1 {
  font-weight: 400;
  font-family: 'Playfair Display', serif; }

.sky-h2, .rooms .title-room, .gallery-our .gallery .title-gallery, .news .new-title, .events .events-title, .about .about-title {
  font-size: 48px;
  font-weight: 400;
  font-family: 'Playfair Display', serif; }

.sky-h3 {
  color: #fff;
  font-size: 36px;
  font-weight: 700;
  font-family: 'Playfair Display', serif;
  margin-top: 15px; }

.sky-h4 {
  color: #232323;
  font-size: 24px;
  font-weight: 500;
  font-family: "Poppins", Arial, sans-serif; }

.sky-h5 {
  font-family: 'Poppins', Arial, sans-serif;
  font-size: 20px;
  color: #FFF;
  font-weight: 500;
  letter-spacing: 1px; }

.sky-h6 {
  font-size: 14px;
  font-weight: 600;
  color: #232323;
  font-family: 'Poppins', Arial, sans-serif;
  letter-spacing: 1px; }

.sky-p {
  font-size: 14px;
  color: #FFF;
  font-family: 'Poppins', Arial, sans-serif; }

.no-padding {
  padding: 0; }

.no-padding-left {
  padding-left: 0; }

.no-padding-right {
  padding-right: 0; }

.testimonials-pp {
  font-weight: 300;
  font-size: 16px;
  color: #FFF; }

.line {
  background: #8E7037; }

.btn-room {
  color: #FFF;
  font-size: 12px;
  height: 38px;
  background: #8E7037;
  line-height: 24px;
  border-radius: 0px;
  font-weight: 600;
  box-shadow: none; }
  .btn-room:hover {
    border: 2px solid #8E7037;
    background: transparent;
    color: #8E7037; }

.h2-rooms {
  text-transform: uppercase;
  font-weight: bold;
  font-family: 'Poppins', Arial, sans-serif;
  color: #333; }

.h5-room {
  font-size: 14px;
  font-family: 'Poppins', Arial, sans-serif;
  font-weight: 600;
  color: #333;
  text-transform: uppercase; }

.heading, .body-restaurant-1 .restaurant_title .heading {
  font-weight: 700;
  color: #232323;
  font-family: "Poppins", sans-serif;
  margin-bottom: 13px; }

.time, .body-restaurant-1 .restaurant_title .time {
  color: #898989;
  margin-top: -8px;
  display: block; }

input, textarea, .form-control, button, .btn {
  outline: none; }

.line-v2 {
  margin: auto;
  height: 2px;
  background: #8e7037;
  margin-top: 22px;
  margin-bottom: 40px; }

a {
  text-decoration: none; }
  a:hover, a:focus {
    text-decoration: none; }

@keyframes myAnimation {
  from {
    right: 50px; }
  to {
    right: 0px; } }
/* Firefox */
@-moz-keyframes myAnimation {
  from {
    right: 50px; }
  to {
    right: 0px; } }
/* Chrome */
@-webkit-keyframes myAnimation {
  from {
    right: 50px; }
  to {
    right: 0px; } }
/* Opera */
@-o-keyframes myAnimation {
  from {
    right: 50px; }
  to {
    right: 0px; } }
/* CONTAINER */
@media (min-width: 1200px) {
  .container {
    width: 1200px !important; } }
/* END/CONTAINER */
@media (min-width: 992px) and (max-width: 1200px) {
  /* NAV HEADER */
  .header-sky .navbar .navbar-header .navbar-brand img {
    width: 90%; }
  .header-sky .navbar .navbar-nav li {
    padding: 35px 18px; }
    .header-sky .navbar .navbar-nav li a {
      font-size: 12px; }
    .header-sky .navbar .navbar-nav li .dropdown-menu {
      min-width: 160px; }

  /* END / NAV HEADER */
  /* NEW */
  .news .text a {
    font-size: 25px !important; }

  /* END/NEW */
  /* MAP */
  .section-map-v4 iframe {
    height: 40rem; }

  /* END/MAP */
  /* FOOTRER */
  .footer-sky.footer-sky-v4 .footer-bottom .payments li {
    padding-left: 19px; }
  .footer-sky .footer-top {
    padding: 20px; }
    .footer-sky .footer-top.footer-top-v3 {
      padding-bottom: 90px; }
      .footer-sky .footer-top.footer-top-v3 .bg-v3 .icon-email {
        margin: 0; }
      .footer-sky .footer-top.footer-top-v3 .bg-v3 .textbox {
        margin-left: -40px; }
      .footer-sky .footer-top.footer-top-v3 .bg-v3 .footer-icon-l .fa {
        padding-left: 40px; }
    .footer-sky .footer-top .icon-email {
      margin-top: 8px; }

  /* END/FOOTER */
  /* GALLERY */
  .gallery-our .gallery {
    margin: 0; }

  /* END/GALLERY */
  /*TESTIMONIALS */
  .testimonials .testimonials-h .testimonials-content .testimonials-p {
    padding: 0; }

  /* END / TESTIMONIALS */
  .best {
    margin-bottom: 40px; }

  /* SECTION-SLIDER  */
  .section-slider .check-avail .date-title {
    height: 100px; }
  .section-slider .check-avail .arrival,
  .section-slider .check-avail .departure {
    padding: 15px 20px 10px 20px !important; }
  .section-slider .check-avail .adults,
  .section-slider .check-avail .children {
    padding-top: 15px !important; }
    .section-slider .check-avail .adults .btn-group,
    .section-slider .check-avail .children .btn-group {
      margin-top: -28px !important; }
      .section-slider .check-avail .adults .btn-group .dropdown-menu,
      .section-slider .check-avail .children .btn-group .dropdown-menu {
        min-width: 111px !important;
        top: 94% !important;
        left: 0px; }
      .section-slider .check-avail .adults .btn-group .btn,
      .section-slider .check-avail .children .btn-group .btn {
        width: 111px !important;
        margin-top: 8px; }
  .section-slider .check-avail .find_btn .text-find {
    padding-top: 28px !important; }
  .section-slider .item .carousel-caption {
    transform: translate(-50%, -55%); }
    .section-slider .item .carousel-caption h1 {
      font-size: 57px;
      margin-bottom: 5px; }
    .section-slider .item .carousel-caption p {
      font-size: 25px; }
      .section-slider .item .carousel-caption p .line-t {
        width: 50px; }
      .section-slider .item .carousel-caption p .line-b {
        width: 50px; }

  .section-slider.height-v .item img {
    width: 170%;
    max-width: 300%; }

  /* END /SECTION-SLIDER*/
  /* ROOMS */
  .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .rooms-content1 .sky-h4 {
    font-size: 20px;
    margin-top: 0; }
  .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .rooms-content1 .price {
    font-size: 14px; }
  .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content p {
    font-size: 12px; }
  .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room {
    padding: 15px 5px 20px; }
    .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li {
      padding: 3px 0px 3px 12px; }
      .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li img {
        vertical-align: middle;
        width: 20px; }
  .rooms .rooms-p {
    color: #ABABAB;
    font-weight: 300;
    font-size: 16px;
    width: 60%;
    margin: 0 auto;
    margin-top: 0px;
    margin-top: 15px; }

  /* END/ROOMS */
  /*ABOUT */
  .about {
    margin-bottom: 6em; }
    .about.about-v4 {
      margin-bottom: 6rem !important; }
    .about.about-v2 {
      margin-bottom: 17%; }
      .about.about-v2 .about-img {
        margin-top: 0;
        text-align: center;
        left: initial;
        right: initial; }
        .about.about-v2 .about-img .img-1 {
          max-width: 100%;
          float: none; }
          .about.about-v2 .about-img .img-1 img {
            display: block; }
          .about.about-v2 .about-img .img-1 .img-2 {
            right: -22%; }
    .about.about-v3 {
      margin-bottom: 6%; }
    .about .about-img {
      margin-top: 35px;
      float: none; }
      .about .about-img .img-1 {
        float: right;
        width: 82%; }
        .about .about-img .img-1 .img-2 {
          top: 50%;
          left: -22%;
          width: 70%; }
        .about .about-img .img-1 .img-3 {
          top: 84%;
          left: 27%;
          width: 50%; }
    .about .about-centent p {
      padding: 0; }

  /* END / ABOUT */
  /* BODY-ROOM */
  .body-room-1 .room-wrap-1 .content ul li {
    width: calc(50% - 0%) !important; }

  .body-room-2 .wrap-room2 .text {
    padding-top: 0px; }
    .body-room-2 .wrap-room2 .text p {
      font-size: 14px; }
    .body-room-2 .wrap-room2 .text .h5-room {
      margin-bottom: 17px;
      margin-top: 15px; }
    .body-room-2 .wrap-room2 .text .h2-rooms {
      font-size: 30px; }

  .body-room-3 .wrap-room-3 .wrap-item .text-room-3 {
    margin-left: 50px;
    margin-right: 50px; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img1 {
      margin-right: 40px; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img2 {
      margin-left: 0; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text {
      width: 570px; }

  .body-room-5 .wrap-room-5 .wrap-item .text-room-5 {
    bottom: 10px;
    left: 30px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 .h2-rooms {
      font-size: 20px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 .h5-room {
      font-size: 13px; }
  .body-room-5 .wrap-room-5 .wrap-item .text-hover .text .h2-rooms {
    font-size: 20px; }
  .body-room-5 .wrap-room-5 .wrap-item .text-hover .text .h5-room {
    font-size: 13px;
    margin-bottom: 10px; }
  .body-room-5 .wrap-room-5 .wrap-item .text-hover .text p {
    font-size: 13px; }
  .body-room-5 .wrap-room-5 .wrap-item .text-hover .text .view-dateails {
    margin-top: 0; }

  .checkout-option .card {
    display: block !important;
    margin-left: 25px;
    margin-top: 10px; }

  .body-restaurant .restaurant_item .text h2 {
    margin-top: 0; }

  /*  END/BODY-ROOM */
  /*  RESTAURANT */
  .section-event-restaurant .event-restaurant .event-restaurant_header {
    margin-top: 0; }

  /* END/RETAURANT */
  /*  BLOG */
  .section-blog .blog .sidebar {
    margin-right: 0px; }

  /*  END/BLOG */
  /* ABOUT */
  .section-about .wrap-about .text {
    margin-top: 0px;
    padding-right: 25px; }
    .section-about .wrap-about .text .heading {
      margin: 0; }
    .section-about .wrap-about .text .desc {
      font-size: 14px; }

  /* END/ABOUT */
  /* RESERVATION */
  .reservation-calendar_custom .reservation-calendar_tabel td a span {
    top: 45%;
    font-size: 9px; }

  /* END/RESERVATION */ }
@media all and (min-width: 1200px) {
  /* MAP */
  .section-map-v4 iframe {
    height: 43rem; }

  /* END/MAP */
  .navbar-header-v3 {
    text-align: center;
    background: rgba(6, 6, 6, 0.33); }
    .navbar-header-v3 .navbar-header {
      display: none; }
    .navbar-header-v3 .navbar-right {
      float: initial !important;
      display: inline-block;
      vertical-align: middle; }
      .navbar-header-v3 .navbar-right li {
        padding: 15px 25px !important; }

  /* HEADER */
  .header-top-v3 {
    border: none;
    text-align: center;
    padding: 45px 0px 37px 0px; }
    .header-top-v3 .logo-v3 {
      width: auto;
      display: block !important; }
      .header-top-v3 .logo-v3 img {
        max-width: 100%;
        height: auto;
        vertical-align: middle;
        border: 0;
        display: inline-block;
        margin-top: -13px; }
    .header-top-v3 .header-top-right-v3 .dropdown:hover .dropdown-menu {
      transform: translate(0px) !important;
      -moz-transform: translateY(0px) !important;
      -webkit-transform: translateY(0px) !important;
      -ms-transform: translateY(0px) !important; }
    .header-top-v3 .header-top-right-v3 .dropdown .dropdown-menu {
      transform: translate(40px) !important;
      -moz-transform: translateY(40px) !important;
      -webkit-transform: translateY(40px) !important;
      -ms-transform: translateY(40px) !important; }

  .header-top-sky .bg-top-v {
    background: #080303;
    opacity: 0.9; }
  .header-top-sky .navbar-header-v3 {
    text-align: center;
    background: rgba(6, 6, 6, 0.47); }
    .header-top-sky .navbar-header-v3 .navbar {
      min-height: 35px; }
    .header-top-sky .navbar-header-v3 .navbar-header {
      display: none; }
    .header-top-sky .navbar-header-v3 .navbar-right {
      float: initial !important;
      display: inline-block;
      vertical-align: middle; }
      .header-top-sky .navbar-header-v3 .navbar-right ul li a {
        padding: 10px 25px !important; }
  .header-top-sky .header-top {
    margin-top: -90px;
    border: none; }
    .header-top-sky .header-top.header-top-v3 {
      padding-bottom: 0; }
      .header-top-sky .header-top.header-top-v3 .logo-v3 {
        margin-top: 55px; }
        .header-top-sky .header-top.header-top-v3 .logo-v3 img {
          margin: 0;
          width: 150px; }

  /* END/HEADER */
  /* SECTION-SLIDER  */
  .section-slider .item .carousel-caption h1.v2 {
    font-size: 50px; }

  /* END /SECTION-SLIDER*/ }
@media all and (min-width: 1920px) {
  /* SECTION-SLIDER  */
  .section-slider .item .carousel-caption .v2 {
    font-size: 72px !important; }
  .section-slider .item .carousel-caption h1 {
    font-size: 85px; }
  .section-slider .item .carousel-caption p {
    font-size: 26px; }
    .section-slider .item .carousel-caption p.p-v2:after {
      right: 23%; }
    .section-slider .item .carousel-caption p.p-v2:before {
      left: 23%; }

  /* END /SECTION-SLIDER*/
  /* MAP */
  .section-map-v4 iframe {
    height: 685px; }

  /* END/MAP */ }
@media (min-width: 768px) and (max-width: 991px) {
  /* FOOTER */
  .footer-sky.footer-sky-v4 .footer-bottom {
    line-height: 35px !important; }
  .footer-sky .footer-top {
    padding: 25px 0px; }
    .footer-sky .footer-top.footer-top-v3 {
      padding-bottom: 50px; }
      .footer-sky .footer-top.footer-top-v3 .bg-v3 {
        padding: 25px 60px; }
    .footer-sky .footer-top .textbox {
      text-align: center; }
      .footer-sky .footer-top .textbox .form-inline .form-group .input-group {
        width: 450px; }
    .footer-sky .footer-top .icon-email img {
      margin: 0 auto;
      margin-bottom: 20px; }
    .footer-sky .footer-top .footer-icon-l {
      text-align: center;
      margin-top: 30px; }
      .footer-sky .footer-top .footer-icon-l .fa {
        line-height: 0px;
        padding: 0px 18px; }
  .footer-sky .footer-bottom {
    padding-top: 10px; }
    .footer-sky .footer-bottom .no-padding {
      line-height: 27px; }
    .footer-sky .footer-bottom .payments {
      text-align: center;
      padding-bottom: 13px; }
      .footer-sky .footer-bottom .payments ul li {
        padding: 0px 9px; }
  .footer-sky .footer-mid .padding-footer-mid {
    padding: 15px 0px 25px 0px; }
    .footer-sky .footer-mid .padding-footer-mid .list-content {
      text-align: center;
      margin-top: 35px;
      margin-bottom: 10px; }
      .footer-sky .footer-mid .padding-footer-mid .list-content img {
        max-width: 170px;
        vertical-align: middle; }
      .footer-sky .footer-mid .padding-footer-mid .list-content ul li {
        text-align: left; }

  /* END/FOOTER*/
  .section-map-v4 iframe {
    height: 40rem; }

  /* SECTION-SLIDER  */
  .section-slider .check-avail .date-title {
    height: 85px; }
    .section-slider .check-avail .date-title label {
      font-size: 13px; }
  .section-slider .check-avail .arrival,
  .section-slider .check-avail .departure {
    padding: 10px 5px 27px 15px !important; }
  .section-slider .check-avail .adults,
  .section-slider .check-avail .children {
    padding-top: 10px !important; }
    .section-slider .check-avail .adults .btn-group,
    .section-slider .check-avail .children .btn-group {
      margin-top: -28px !important; }
      .section-slider .check-avail .adults .btn-group .dropdown-menu,
      .section-slider .check-avail .children .btn-group .dropdown-menu {
        min-width: 82px !important;
        top: 100% !important;
        left: 0 !important; }
      .section-slider .check-avail .adults .btn-group .btn,
      .section-slider .check-avail .children .btn-group .btn {
        width: 82px !important;
        font-size: 50px !important;
        margin-top: 10px; }
  .section-slider .check-avail .find_btn .text-find {
    padding-top: 23px !important;
    font-size: 12px !important;
    letter-spacing: 2px !important; }
  .section-slider .item .carousel-caption {
    transform: translate(-50%, -55%); }
    .section-slider .item .carousel-caption h1 {
      font-size: 44px;
      margin-bottom: 5px; }
    .section-slider .item .carousel-caption p {
      font-size: 22px;
      letter-spacing: 3px; }
      .section-slider .item .carousel-caption p .line-t {
        width: 40px; }
      .section-slider .item .carousel-caption p .line-b {
        width: 40px; }

  .section-slider.height-v .item img {
    width: 200%;
    max-width: 300%; }

  /* END/SECTION-SLIDER */
  /* UOR  GALLERY */
  .gallery-our .gallery {
    margin: 0; }

  /* END/OUR GALLERY*/
  /* NEWS */
  .news {
    padding-top: 0px;
    padding-bottom: 75px; }
    .news .text a {
      font-size: 18px !important; }

  /* END/NEWS*/
  /* BEST */
  .best {
    margin-bottom: 0px; }
    .best .sky-h6 {
      font-size: 12px; }

  /* END/BEST*/
  /*ABOUT */
  .about {
    margin-bottom: 5em; }
    .about.about-v4 .about-centent p {
      text-align: center; }
    .about.about-v2 {
      margin-bottom: 75px; }
      .about.about-v2 .about-img {
        margin-top: 0;
        text-align: center;
        left: initial;
        right: initial; }
        .about.about-v2 .about-img .img-1 {
          width: 100%; }
          .about.about-v2 .about-img .img-1 img {
            display: inline-block;
            width: 100%; }
          .about.about-v2 .about-img .img-1 .img-2 {
            display: none; }
    .about .about-img {
      margin-top: 125px;
      float: none; }
      .about .about-img .img-1 {
        float: right;
        width: 82%; }
        .about .about-img .img-1 .img-2 {
          top: 50%;
          left: -22%;
          width: 70%; }
        .about .about-img .img-1 .img-3 {
          top: 84%;
          left: 27%;
          width: 50%; }
    .about .about-centent p {
      padding: 0;
      text-align: justify; }

  /* END / ABOUT */
  /*ROOMS */
  .rooms {
    padding-top: 25px; }
    .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room {
      padding: 23px 5px 25px 5px; }
      .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li {
        padding: 7px 0px 7px 20px !important; }
    .rooms .rooms-p {
      width: 75%; }

  /* END / ROOMS */
  .events {
    margin-bottom: 40px; }
    .events.events-v3 .events-content {
      bottom: -74%; }
      .events.events-v3 .events-content p {
        margin: 0;
        font-size: 12px; }
      .events.events-v3 .events-content h3 {
        margin: 0;
        font-size: 30px; }

  /*TESTIMONIALS */
  .testimonials.testimonials-v2 .testimonials-content {
    top: 25% !important; }
    .testimonials.testimonials-v2 .testimonials-content .content-testimonials p {
      margin-bottom: 19% !important; }
  .testimonials .testimonials-h .testimonials-content .testimonials-p {
    padding: 0; }

  /* END / TESTIMONIALS */
  /* NAV-HEADER */
  .header-sky.header-top-sky .menu-header {
    height: 80px !important; }
  .header-sky .navbar .navbar-header .navbar-brand img {
    width: 150px; }
  .header-sky .navbar .navbar-nav li {
    padding: 30px 10px; }
    .header-sky .navbar .navbar-nav li a {
      font-size: 10px; }
      .header-sky .navbar .navbar-nav li a .caret {
        border-top: 3px dashed;
        border-top: 3px solid \9;
        border-right: 3px solid transparent;
        border-left: 3px solid transparent; }
    .header-sky .navbar .navbar-nav li .dropdown-menu {
      min-width: 120px; }

  /* END / NAV-HEADER */
  .body-room-1 .room-wrap-1 .content ul li {
    width: calc(50% - -50%) !important; }

  .body-room-2 .wrap-room2 .img:before,
  .body-room-2 .wrap-room2 .img1:before {
    right: 7%;
    width: 86%;
    bottom: 0;
    height: 10%;
    top: auto; }
  .body-room-2 .wrap-room2 .text {
    padding-left: 80px;
    padding-right: 80px;
    padding-top: 0px;
    margin-top: -27px; }

  .body-room-3 .wrap-room-3 .wrap-item .text-room-3 {
    margin-left: 40px;
    margin-right: 40px; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img1,
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img2 {
      display: none; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text {
      width: auto; }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text .line {
        display: none; }

  .body-room-4 .wrap-room-4 .wrap-item .text-room-4 {
    margin-left: auto; }
    .body-room-4 .wrap-room-4 .wrap-item .text-room-4.room-right {
      margin-right: auto; }
    .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text {
      padding: 0; }
    .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .img1 {
      display: none; }

  .body-room-5 .wrap-room-5 .wrap-item .text-room-5 {
    bottom: 10px;
    left: 30px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 .h2-rooms {
      font-size: 20px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 .h5-room {
      font-size: 13px; }
  .body-room-5 .wrap-room-5 .wrap-item .text-hover .text {
    padding: 0px 30px;
    padding-bottom: 11px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-hover .text .h2-rooms {
      font-size: 16px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-hover .text .h5-room {
      font-size: 10px;
      margin-bottom: 11px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-hover .text p {
      font-size: 10px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-hover .text .view-dateails {
      margin-top: 0;
      width: 80px;
      height: 25px;
      font-size: 8px;
      line-height: 10px; }

  .check-out .col-lg-offset-1 {
    margin-left: auto; }
  .check-out .col-lg-5 {
    width: auto; }

  .page-v3 .content .wrap #timer #hours:before, .page-v3 .content .wrap #timer #hours:after {
    left: -20% !important; }
  .page-v3 .content .wrap #timer #minutes:before, .page-v3 .content .wrap #timer #minutes:after {
    left: -15% !important; }
  .page-v3 .content .wrap #timer #seconds:before, .page-v3 .content .wrap #timer #seconds:after {
    left: -35% !important; }
  .page-v3 .content .wrap #timer .padding {
    padding-right: 70px !important; }

  .body-restaurant-3 .reservation .reservation_form .date {
    margin-bottom: 20px; }

  .section-event-restaurant .event-restaurant .event-restaurant_header {
    margin-top: -20px;
    text-align: center;
    margin-bottom: 50px;
    padding-right: 0px; }

  .section-blog .blog .sidebar {
    margin-right: 0px; }

  .section-about .wrap-about .about-item .no-padding-right {
    padding-right: 15px; }
    .section-about .wrap-about .about-item .no-padding-right .text {
      padding: 0;
      margin: 0; }
  .section-about .wrap-about .about-item .no-padding-left {
    padding-left: 15px; }
    .section-about .wrap-about .about-item .no-padding-left .text {
      padding: 0; }

  .section-team {
    padding-bottom: 80px; } }
/* End-min-width:768px */
@media (max-width: 767px) {
  /* BODY-RESTAURANT */
  .body-restaurant .restaurant_item .text h2 {
    margin-top: 0; }

  .body-restaurant-2 .nav {
    text-align: center; }
    .body-restaurant-2 .nav li {
      float: none;
      width: 100%;
      padding-top: 12px;
      display: block;
      border-bottom: 2px solid #e7e7e7; }
      .body-restaurant-2 .nav li:last-child {
        border-bottom: none; }

  .body-restaurant-3 .reservation .reservation_form .date,
  .body-restaurant-3 .reservation .reservation_form .awe-input {
    margin-bottom: 20px; }

  /* END BODY-RESTAURANT */
  .header-top {
    text-align: center; }
    .header-top .header-top-left {
      float: none !important;
      border-bottom: 1px solid rgba(245, 240, 240, 0.23); }
    .header-top .header-top-right {
      float: none !important;
      display: inline-block;
      line-height: 38px;
      margin-bottom: -8px; }
      .header-top .header-top-right .icon-v3-top {
        float: left; }
      .header-top .header-top-right ul {
        float: left; }

  .events-v3.events-v4 {
    margin-top: 30px; }
    .events-v3.events-v4 .events-item .v4 {
      font-size: 30px; }
  .events-v3 .events-content {
    bottom: -68%; }
    .events-v3 .events-content p {
      margin: 0;
      font-size: 12px; }
    .events-v3 .events-content h3 {
      margin: 0;
      font-size: 30px; }

  .best {
    margin-bottom: 30px; }

  .navbar-header-v3 {
    background: rgba(9, 8, 8, 0.79); }

  /* UOR  GALLERY */
  .gallery-our {
    padding-top: 20px; }
    .gallery-our .gallery {
      margin: 0; }
      .gallery-our .gallery .nav-tabs li {
        float: none;
        display: inherit;
        margin-bottom: 5px; }

  /* END/OUR GALLERY*/
  .gallery__description-block {
    padding: 10px 0; }
    .gallery__description-block .gallery__description-block__description {
      font-size: 12px;
      margin: 0;
      padding: 0 20px; }

  /*TESTIMONIALS */
  .testimonials.testimonials-v2 .testimonials-content {
    top: 20% !important; }
    .testimonials.testimonials-v2 .testimonials-content .testimonials-img {
      padding: 0; }
    .testimonials.testimonials-v2 .testimonials-content .content-testimonials p {
      font-size: 13px;
      padding: 0;
      margin-bottom: 11% !important; }
  .testimonials .testimonials-h {
    min-height: 330px; }
    .testimonials .testimonials-h .testimonials-content {
      top: 8%; }
      .testimonials .testimonials-h .testimonials-content .img-testimonials {
        margin-bottom: 10px; }
      .testimonials .testimonials-h .testimonials-content .testimonials-p {
        font-size: 12px;
        padding: 0;
        margin-bottom: 0; }
        .testimonials .testimonials-h .testimonials-content .testimonials-p span {
          font-size: 28px; }
      .testimonials .testimonials-h .testimonials-content .testimonials-p1 {
        font-size: 13px;
        margin-bottom: 13px; }

  /* END / TESTIMONIALS */
  /* NEWS */
  .news {
    padding-top: 30px;
    padding-bottom: 60px !important; }

  /* END/NEWS*/
  /* FOOTER */
  .footer-sky.footer-sky-v4 .footer-top {
    padding: 0; }
  .footer-sky.footer-sky-v4 .footer-bottom {
    line-height: 35px !important; }
  .footer-sky.footer-sky-v4 .textbox .input-group {
    width: 100% !important; }
  .footer-sky .footer-top {
    padding: 25px 0px; }
    .footer-sky .footer-top.footer-top-v3 {
      padding-bottom: 15px; }
      .footer-sky .footer-top.footer-top-v3 .footer-icon-l .fa {
        padding: 0 10px; }
      .footer-sky .footer-top.footer-top-v3 .bg-v3 {
        padding: 20px 15px; }
      .footer-sky .footer-top.footer-top-v3 .input-group {
        width: 420px !important; }
    .footer-sky .footer-top .textbox .form-inline .form-group .input-group {
      width: 450px;
      margin: 0 auto; }
    .footer-sky .footer-top .icon-email img {
      margin: 0 auto;
      margin-bottom: 10px; }
    .footer-sky .footer-top .footer-icon-l {
      text-align: center;
      margin-top: 10px; }
      .footer-sky .footer-top .footer-icon-l .fa {
        line-height: 0px;
        padding: 0px 18px; }
  .footer-sky .footer-bottom {
    text-align: center;
    padding-top: 10px; }
    .footer-sky .footer-bottom .no-padding {
      line-height: 30px; }
    .footer-sky .footer-bottom .payments {
      text-align: center;
      padding-bottom: 13px; }
      .footer-sky .footer-bottom .payments ul li {
        padding: 0px 9px; }
  .footer-sky .footer-mid .padding-footer-mid {
    padding: 15px 0px 20px 0px; }
    .footer-sky .footer-mid .padding-footer-mid .list-content {
      text-align: center;
      margin-top: 35px;
      margin-bottom: 10px; }
      .footer-sky .footer-mid .padding-footer-mid .list-content img {
        max-width: 170px;
        vertical-align: middle; }
      .footer-sky .footer-mid .padding-footer-mid .list-content ul li {
        text-align: left; }

  /* END/FOOTER*/
  /* ROOMS */
  .rooms {
    padding-top: 30px;
    padding-bottom: 50px; }
    .rooms .rooms-p {
      width: 100%; }
    .rooms .wrap-rooms .sky-h4 {
      font-size: 20px !important; }
    .rooms .wrap-rooms .price {
      font-size: 14px !important; }
    .rooms.rooms-v2 .content p {
      font-size: 14px !important; }
    .rooms.rooms-v2 .content .bottom-room ul li {
      font-size: 10px !important; }
      .rooms.rooms-v2 .content .bottom-room ul li img {
        width: 20px !important; }

  /* END/ROOMS */
  /*ABOUT */
  .about {
    margin-top: 50px;
    margin-bottom: 7rem; }
    .about.about-v2 {
      margin-bottom: 60px; }
      .about.about-v2 .about-img {
        text-align: center;
        left: initial;
        right: initial; }
        .about.about-v2 .about-img .img-1 {
          width: 100%; }
          .about.about-v2 .about-img .img-1 img {
            display: inline-block; }
          .about.about-v2 .about-img .img-1 .img-2 {
            display: none; }
      .about.about-v2 .about-centent p {
        font-size: 14px; }
    .about .about-img {
      margin-top: 35px;
      float: none; }
      .about .about-img .img-1 {
        width: 100%; }
        .about .about-img .img-1 img {
          width: 100%; }
        .about .about-img .img-1 .img-2,
        .about .about-img .img-1 .img-3 {
          display: none; }
    .about .about-centent {
      text-align: center; }
      .about .about-centent .line {
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 30px; }
      .about .about-centent p {
        padding: 0;
        font-size: 14px;
        margin-bottom: 30px; }

  /* END / ABOUT */
  /* section-slider  */
  .section-slider.height-v .item img {
    width: 290%;
    max-width: 300%; }
  .section-slider .check-avail {
    position: inherit;
    background: rgba(2, 2, 2, 0.78); }
    .section-slider .check-avail.check-avail-v3 {
      padding: 0; }
    .section-slider .check-avail .container {
      flex-direction: row;
      display: inline-block; }
      .section-slider .check-avail .container .date-title {
        height: 85px;
        width: 49%;
        margin-top: 10px;
        float: left;
        margin-right: 0px; }
        .section-slider .check-avail .container .date-title:nth-child(2), .section-slider .check-avail .container .date-title:nth-child(4) {
          float: right; }
        .section-slider .check-avail .container .date-title:nth-child(5) {
          width: 100%;
          margin-bottom: 5px;
          border: none; }
        .section-slider .check-avail .container .date-title label {
          font-size: 13px; }
        .section-slider .check-avail .container .date-title img {
          width: 40px; }
      .section-slider .check-avail .container .arrival,
      .section-slider .check-avail .container .departure {
        padding: 10px 5px 27px 15px; }
      .section-slider .check-avail .container .adults,
      .section-slider .check-avail .container .children {
        padding-top: 10px; }
        .section-slider .check-avail .container .adults .btn-group,
        .section-slider .check-avail .container .children .btn-group {
          margin-top: -28px; }
          .section-slider .check-avail .container .adults .btn-group .dropdown-menu,
          .section-slider .check-avail .container .children .btn-group .dropdown-menu {
            min-width: 80px;
            top: 100%;
            left: 0; }
          .section-slider .check-avail .container .adults .btn-group .btn,
          .section-slider .check-avail .container .children .btn-group .btn {
            width: 80px;
            font-size: 50px;
            margin-top: 10px; }
      .section-slider .check-avail .container .find_btn .text-find {
        padding-top: 23px;
        font-size: 12px; }
  .section-slider .item .carousel-caption {
    transform: translate(-50%, -55%); }
    .section-slider .item .carousel-caption h1 {
      font-size: 30px;
      margin-bottom: 0px; }
    .section-slider .item .carousel-caption p {
      letter-spacing: 2px;
      font-size: 16px;
      margin-top: 7px; }
      .section-slider .item .carousel-caption p .line-t {
        width: 15px;
        margin-right: 7px; }
      .section-slider .item .carousel-caption p .line-b {
        width: 15px;
        margin-left: 5px; }

  /* end-section-slider */
  .header-sky.header-top-sky .navbar .navbar-nav {
    margin-top: 0px !important; }
    .header-sky.header-top-sky .navbar .navbar-nav .dropdown-menu {
      background: transparent;
      -webkit-box-shadow: none;
      box-shadow: none; }
  .header-sky.header-top-sky .navbar-toggle {
    margin-top: 15px;
    margin-bottom: 15px; }
  .header-sky .header-top {
    line-height: 28px;
    border: none; }
    .header-sky .header-top .header-top-left span {
      margin-right: 15px; }
      .header-sky .header-top .header-top-left span i {
        font-size: 10px;
        padding-right: 3px; }
    .header-sky .header-top .header-top-right .icon-v3-top {
      margin-right: 0; }
      .header-sky .header-top .header-top-right .icon-v3-top .fa {
        font-size: 12px; }
    .header-sky .header-top .header-top-right .dropdown-menu {
      min-width: 90px; }
    .header-sky .header-top ul li a {
      font-size: 12px;
      padding: 6px 10px; }
  .header-sky .navbar-toggle {
    margin-top: 12px;
    border: 1px solid white;
    border-radius: 0px; }
    .header-sky .navbar-toggle .icon-bar {
      background: #FFF; }
  .header-sky .navbar {
    height: 65px; }
    .header-sky .navbar .navbar-header .navbar-brand {
      padding-top: 10px !important; }
      .header-sky .navbar .navbar-header .navbar-brand img {
        width: 70%; }
    .header-sky .navbar .page {
      margin-top: 8px !important; }
    .header-sky .navbar .navbar-collapse {
      border: none;
      -webkit-box-shadow: none;
      box-shadow: none;
      margin-right: -15px;
      margin-left: -15px;
      height: 100vh; }
    .header-sky .navbar .navbar-nav {
      background: rgba(9, 8, 8, 0.79);
      margin-top: 11px; }
      .header-sky .navbar .navbar-nav li {
        padding: 10px 15px !important;
        width: 100%;
        display: inline-block; }
        .header-sky .navbar .navbar-nav li .submenu-hover {
          min-width: 160px;
          margin-left: 20px;
          -webkit-box-shadow: none !important;
          box-shadow: none !important;
          margin-bottom: -15px; }
          .header-sky .navbar .navbar-nav li .submenu-hover .submenu-hover1 .caret {
            top: -20px; }
          .header-sky .navbar .navbar-nav li .submenu-hover .dropdown-margin {
            margin-left: 20px;
            margin-top: 0px; }
          .header-sky .navbar .navbar-nav li .submenu-hover li a {
            position: relative;
            padding: 5px 20px;
            color: #FFF !important;
            display: block; }
          .header-sky .navbar .navbar-nav li .submenu-hover li:hover a {
            background: transparent;
            color: #8E7037 !important; }
          .header-sky .navbar .navbar-nav li .submenu-hover li:hover .dropdown-margin li a {
            color: #FFF !important; }
            .header-sky .navbar .navbar-nav li .submenu-hover li:hover .dropdown-margin li a:hover {
              color: #8E7037 !important; }
      .header-sky .navbar .navbar-nav .dropdown.open .dropdown-toggle .caret {
        border-bottom: 0px dashed;
        border-top: 4px dashed; }
      .header-sky .navbar .navbar-nav .dropdown .dropdown-toggle .caret {
        float: right;
        margin-top: 7px;
        right: 0px; }

  /* 404-page */
  .body-page.page-v3 .content .h1-v3 {
    font-size: 55px !important; }
  .body-page.page-v3 .content .h3-v3 {
    font-size: 30px !important; }
  .body-page.page-v3 .content .pp-v3 {
    color: #C4C4C4;
    font-size: 20px; }
  .body-page.page-v3 .content .wrap {
    margin: 27px 0; }
    .body-page.page-v3 .content .wrap #timer {
      border: 2px solid #fff;
      display: inline-block;
      padding: 0; }
      .body-page.page-v3 .content .wrap #timer .padding {
        padding-right: 15px; }
        .body-page.page-v3 .content .wrap #timer .padding:last-child {
          padding: 0px;
          padding-right: 15px !important; }
      .body-page.page-v3 .content .wrap #timer #days {
        font-size: 27px; }
      .body-page.page-v3 .content .wrap #timer #hours {
        font-size: 27px; }
        .body-page.page-v3 .content .wrap #timer #hours:before, .body-page.page-v3 .content .wrap #timer #hours:after {
          left: -5%;
          height: 3px;
          width: 3px; }
      .body-page.page-v3 .content .wrap #timer #minutes {
        font-size: 27px; }
        .body-page.page-v3 .content .wrap #timer #minutes:before, .body-page.page-v3 .content .wrap #timer #minutes:after {
          left: 0%;
          height: 3px;
          width: 3px; }
      .body-page.page-v3 .content .wrap #timer #seconds {
        font-size: 27px; }
        .body-page.page-v3 .content .wrap #timer #seconds:before, .body-page.page-v3 .content .wrap #timer #seconds:after {
          left: -15%;
          height: 3px;
          width: 3px;
          top: 39%; }
        .body-page.page-v3 .content .wrap #timer #seconds:after {
          top: 56%; }
      .body-page.page-v3 .content .wrap #timer span {
        font-size: 12px; }
  .body-page.page-login {
    height: 70rem; }
  .body-page .content .navbar-form .form-control {
    width: 100%; }
  .body-page .content .navbar-form .btn {
    margin-left: 10px; }

  .page-v1 .content .sky-h3 {
    font-size: 30px; }
  .page-v1 .content .p-v1 {
    font-size: 14px !important; }

  /* END/404-page */
  .section-map-v4 iframe {
    height: 27rem; }

  .section-map {
    height: 300px; }
    .section-map iframe {
      height: 30rem; }

  .video-v4 .section-video .btn-play {
    width: 40px;
    height: 40px; }

  .body-room-1 .room-wrap-1 .content ul li {
    width: calc(50% - -50%) !important; }

  .body-room-2 .wrap-room2 .img:before,
  .body-room-2 .wrap-room2 .img1:before {
    right: 7%;
    width: 86%;
    bottom: 0;
    height: 10%;
    top: auto; }
  .body-room-2 .wrap-room2 .text {
    padding-left: 80px;
    padding-right: 80px;
    padding-top: 0px;
    margin-top: -25px; }
    .body-room-2 .wrap-room2 .text .h2-rooms {
      font-size: 30px; }
    .body-room-2 .wrap-room2 .text p {
      font-size: 14px; }
    .body-room-2 .wrap-room2 .text .h5-room {
      margin-bottom: 16px;
      margin-top: 12px; }

  .body-room-3 .wrap-room-3 .wrap-item .text-room-3 {
    margin-left: 40px;
    margin-right: 40px; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img1,
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img2 {
      display: none; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text {
      width: auto; }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text .line {
        display: none; }

  .body-room-4 .wrap-room-4 .wrap-item .text-room-4 {
    margin-left: auto; }
    .body-room-4 .wrap-room-4 .wrap-item .text-room-4.room-right {
      margin-right: auto; }
    .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text {
      padding: 0; }
      .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text .h2-rooms {
        font-size: 20px;
        margin-top: 0; }
      .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text .h5-room {
        font-size: 12px;
        margin-top: 10px;
        margin-bottom: 15px; }
      .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text p {
        font-size: 12px; }
    .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .img1 {
      display: none; }

  .body-room-5 .wrap-room-5 .wrap-item:hover .img:before {
    opacity: 0.7;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    overflow: visible; }
  .body-room-5 .wrap-room-5 .wrap-item .img {
    position: relative; }
    .body-room-5 .wrap-room-5 .wrap-item .img:before {
      bottom: 0;
      left: 0;
      right: 0;
      top: 50%;
      position: absolute;
      content: '';
      opacity: 0;
      pointer-events: none;
      background-color: transparent;
      background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(#1e1818));
      background: -webkit-linear-gradient(top, transparent, #1e1818);
      background: -moz-linear-gradient(top, transparent, #1e1818);
      background: -ms-linear-gradient(top, transparent, #1e1818);
      background: -o-linear-gradient(top, transparent, #1e1818);
      -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
      -ms-transition: all 0.3s ease-in-out;
      -o-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out;
      overflow: hidden; }
  .body-room-5 .wrap-room-5 .wrap-item .text-room-5 {
    bottom: 10px;
    left: 10px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 .h2-rooms {
      font-size: 15px;
      margin: 0;
      margin-bottom: 3px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 .h5-room {
      font-size: 10px;
      margin: 0; }
  .body-room-5 .wrap-room-5 .wrap-item .text-hover {
    display: none; }

  .banner-tems {
    padding-top: 130px; }

  .check-out .col-lg-offset-1 {
    margin-left: auto; }
  .check-out .col-lg-5 {
    width: auto; }
  .check-out .card {
    display: block !important;
    margin-left: 25px;
    margin-top: 10px; }

  .section-event-restaurant .event-restaurant .event-restaurant_header {
    margin-top: -20px;
    text-align: center;
    margin-bottom: 50px;
    padding-right: 0px; }
    .section-event-restaurant .event-restaurant .event-restaurant_header p {
      font-size: 15px; }
  .section-event-restaurant .event-restaurant .event-restaurant_content .text h2 {
    font-size: 15px !important; }

  .section-blog,
  .blog-detail {
    padding-bottom: 35px !important; }
    .section-blog.blog-detail .blog-content .share-tag,
    .section-blog.blog-detail .blog-content .comments,
    .section-blog.blog-detail .blog-content .comment-respond,
    .blog-detail.blog-detail .blog-content .share-tag,
    .blog-detail.blog-detail .blog-content .comments,
    .blog-detail.blog-detail .blog-content .comment-respond {
      margin-left: 0px; }
    .section-blog .blog .sidebar,
    .blog-detail .blog .sidebar {
      margin-right: 0px; }

  .section-team {
    padding-bottom: 60px;
    padding-top: 30px; }

  .section-about .wrap-about .about-item .no-padding-right {
    padding-right: 15px; }
    .section-about .wrap-about .about-item .no-padding-right .text {
      padding: 0;
      margin: 0; }
  .section-about .wrap-about .about-item .no-padding-left {
    padding-left: 15px; }
    .section-about .wrap-about .about-item .no-padding-left .text {
      padding: 0;
      margin-top: 20px; }

  .section-statistics {
    padding-top: 30px;
    padding-bottom: 50px; }
    .section-statistics .statistics .statistics_content {
      margin-top: -20px; }
      .section-statistics .statistics .statistics_content .item .count {
        font-size: 35px; }
      .section-statistics .statistics .statistics_content .item span {
        font-size: 13px; } }
/* END/768PX*/
@media (max-width: 480px) {
  .gallery__controls-buttons {
    top: 40%; }

  .body-room-2 .wrap-room2 {
    margin-bottom: 40px; }
    .body-room-2 .wrap-room2 .img:before,
    .body-room-2 .wrap-room2 .img1:before {
      content: initial; }
    .body-room-2 .wrap-room2 .text {
      padding: 0;
      margin: 0; }
      .body-room-2 .wrap-room2 .text .h2-rooms {
        font-size: 20px; }
      .body-room-2 .wrap-room2 .text p {
        font-size: 14px; }
      .body-room-2 .wrap-room2 .text .h5-room {
        margin-bottom: 10px;
        margin-top: 12px;
        font-size: 14px; }

  .body-room-1 .room-wrap-1 .room-item-1 .bottom .btn {
    width: 115px; }

  .body-room-3 .wrap-room-3 .wrap-item .text-room-3 {
    margin-left: 0px;
    margin-right: 0px; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img1,
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .img2 {
      display: none; }
    .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text {
      width: auto;
      padding: 0;
      -webkit-transform: translateY(0px);
      -moz-transform: translateY(0px);
      -ms-transform: translateY(0px);
      -o-transform: translateY(0px); }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text .h2-rooms {
        font-size: 20px; }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text .h5-room {
        margin-bottom: 10px;
        margin-top: 12px;
        font-size: 14px; }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text p {
        font-size: 14px; }
      .body-room-3 .wrap-room-3 .wrap-item .text-room-3 .text .line {
        display: none; }

  .body-room-4 .wrap-room-4 .wrap-item {
    margin-bottom: 60px; }
    .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text {
      padding: 17px 0 0 0; }
      .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text .h2-rooms {
        font-size: 20px; }
      .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text .h5-room {
        font-size: 14px; }
      .body-room-4 .wrap-room-4 .wrap-item .text-room-4 .text p {
        font-size: 14px; }

  .body-room-5 .wrap-room-5 .wrap-item .text-room-5 {
    bottom: 15px;
    left: 15px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 .h2-rooms {
      font-size: 20px; }
    .body-room-5 .wrap-room-5 .wrap-item .text-room-5 .h5-room {
      font-size: 14px; }

  .body-room-6 .wrap-room-6 .wrap-item .text h2,
  .body-room-6 .wrap-room-6 .wrap-item .text-1 h2 {
    font-size: 24px; }
  .body-room-6 .wrap-room-6 .wrap-item .text p,
  .body-room-6 .wrap-room-6 .wrap-item .text-1 p {
    font-size: 13px; }
  .body-room-6 .wrap-room-6 .wrap-item .text ul li,
  .body-room-6 .wrap-room-6 .wrap-item .text-1 ul li {
    font-size: 12px; }

  .page-v1 .content .form-group {
    width: 100% !important; }
  .page-v1 .content .form-control {
    width: 100% !important; }
  .page-v1 .content .sky-h3 {
    font-size: 24px; }

  .col-xs-pull-6 {
    right: 0; }

  .col-xs-push-6 {
    left: 0; }

  .col-xs-6 {
    width: 100%; }

  .events .owl-carousel .owl-nav .owl-prev::before {
    margin-left: -25px; }

  .events-v2 .events-title {
    text-align: initial; }
  .events-v2 .line-v2 {
    width: 290px;
    margin-left: 0; }

  .header-sky.header-top-sky .navbar-toggle {
    margin-top: 15px;
    margin-bottom: 15px; }
  .header-sky .header-top {
    margin: 0; }
    .header-sky .header-top .header-top-left {
      display: none; }
    .header-sky .header-top ul li a {
      font-size: 11px; }
      .header-sky .header-top ul li a .caret {
        border-top: 3px dashed;
        border-top: 3px solid \9;
        border-right: 3px solid transparent;
        border-left: 3px solid transparent; }
    .header-sky .header-top .header-top-right {
      margin: 0;
      text-align: center; }
      .header-sky .header-top .header-top-right .icon-v3-top,
      .header-sky .header-top .header-top-right ul {
        float: none; }
  .header-sky .navbar {
    height: 65px;
    margin-top: 0px; }
    .header-sky .navbar .navbar-header .navbar-brand {
      padding-top: 10px !important; }
      .header-sky .navbar .navbar-header .navbar-brand img {
        width: 70%; }
    .header-sky .navbar .page-404 {
      margin-top: 8px !important; }
    .header-sky .navbar .navbar-nav {
      margin-top: 8px; }
      .header-sky .navbar .navbar-nav li a {
        font-size: 12px; }
      .header-sky .navbar .navbar-nav li .dropdown-menu {
        min-width: 160px;
        margin-top: auto; }
        .header-sky .navbar .navbar-nav li .dropdown-menu li a {
          padding: 5px 20px; }
  .header-sky .navbar-toggle {
    margin-top: 15px; }

  .footer-sky.footer-sky-v4 .footer-top {
    padding: 0; }
    .footer-sky.footer-sky-v4 .footer-top h2 {
      font-size: 30px; }
    .footer-sky.footer-sky-v4 .footer-top p {
      font-size: 12px; }
  .footer-sky .footer-top.footer-top-v3 .input-group {
    width: 100% !important; }
  .footer-sky .footer-top .textbox .form-inline .form-group .input-group {
    width: 100%; }
  .footer-sky .footer-mid .padding-footer-mid {
    padding: 15px 0px 40px 0px; }
    .footer-sky .footer-mid .padding-footer-mid .col-xs-4 {
      width: 100%; }
    .footer-sky .footer-mid .padding-footer-mid .list-content ul li {
      padding-left: 10%;
      text-align: left; }
  .footer-sky .footer-mid.footer-v3 .padding-footer-mid {
    padding: 30px 0px 40px 0px; }

  .about.about-v4 {
    margin-bottom: 6rem; }
    .about.about-v4 .img-v4 {
      display: none; }

  /*TESTIMONIALS */
  .testimonials.testimonials-v2 .testimonials-content {
    top: 20% !important; }
    .testimonials.testimonials-v2 .testimonials-content .content-testimonials {
      padding: 0; }
      .testimonials.testimonials-v2 .testimonials-content .content-testimonials p {
        font-size: 10px;
        padding: 0;
        text-align: justify;
        margin-bottom: 20% !important; }
      .testimonials.testimonials-v2 .testimonials-content .content-testimonials .testimonials-pp:before, .testimonials.testimonials-v2 .testimonials-content .content-testimonials .testimonials-pp:after {
        font-size: 30px; }
      .testimonials.testimonials-v2 .testimonials-content .content-testimonials .title-tes .line {
        font-size: 9px;
        padding-left: 7px;
        margin-left: 5px; }
      .testimonials.testimonials-v2 .testimonials-content .content-testimonials .title-tes .sky-h5 {
        font-size: 14px; }
  .testimonials .testimonials-h .testimonials-content .img-testimonials {
    margin-bottom: 0px; }
  .testimonials .testimonials-h .testimonials-content .testimonials-p {
    font-size: 10px;
    padding: 0;
    margin-bottom: 0; }
  .testimonials .testimonials-h .testimonials-content .sky-h5 {
    font-size: 14px; }
  .testimonials .testimonials-h .testimonials-content .testimonials-p1 {
    font-size: 13px;
    margin-bottom: 5px; }

  /* END / TESTIMONIALS */
  .section-slider .check-avail {
    position: inherit;
    background: rgba(2, 2, 2, 0.78); }
    .section-slider .check-avail .container {
      flex-direction: column; }
      .section-slider .check-avail .container .date-title {
        height: 85px;
        width: 100%;
        margin-top: 10px; }
        .section-slider .check-avail .container .date-title label {
          font-size: 14px; }
        .section-slider .check-avail .container .date-title img {
          width: 40px; }
      .section-slider .check-avail .container .arrival,
      .section-slider .check-avail .container .departure {
        padding: 10px 5px 27px 15px; }
      .section-slider .check-avail .container .adults,
      .section-slider .check-avail .container .children {
        padding-top: 10px; }
        .section-slider .check-avail .container .adults .btn-group,
        .section-slider .check-avail .container .children .btn-group {
          margin-top: -28px; }
          .section-slider .check-avail .container .adults .btn-group .dropdown-menu,
          .section-slider .check-avail .container .children .btn-group .dropdown-menu {
            min-width: 80px;
            top: 101%;
            left: 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.47); }
          .section-slider .check-avail .container .adults .btn-group .btn,
          .section-slider .check-avail .container .children .btn-group .btn {
            width: 80px;
            font-size: 50px;
            margin-top: 10px; }
      .section-slider .check-avail .container .find_btn .text-find {
        padding-top: 23px;
        font-size: 12px; }
  .section-slider .item .carousel-caption {
    transform: translate(-50%, -55%); }
    .section-slider .item .carousel-caption h1 {
      font-size: 32px;
      margin-bottom: 0px; }
    .section-slider .item .carousel-caption p {
      font-size: 14px !important;
      letter-spacing: 2px; }
      .section-slider .item .carousel-caption p .line-t {
        width: 10px;
        margin-right: 5px; }
      .section-slider .item .carousel-caption p .line-b {
        width: 10px;
        margin-left: 3px; }

  /* ROOMS */
  .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .rooms-content {
    padding-top: 0px;
    padding-bottom: 0px; }
    .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .rooms-content .sky-h4 {
      margin-bottom: 3px;
      font-size: 20px !important; }
    .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .rooms-content .price {
      font-size: 13px !important; }
  .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content {
    padding: 10px 10px 26px; }
    .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .btn {
      font-size: 12px;
      height: 30px;
      width: 110px;
      line-height: 18px;
      margin-top: 5px; }
    .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .rooms-content1 .sky-h4 {
      margin-bottom: 5px;
      font-size: 20px !important; }
    .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .rooms-content1 .price {
      margin-bottom: 5px; }
    .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room {
      padding: 20px 10px; }
      .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li {
        padding: 6px 0px 6px 10px; }
        .rooms.rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li img {
          vertical-align: middle;
          width: 18px !important; }
  .rooms.rooms-v2 .content p {
    font-size: 13px !important; }
  .rooms.rooms-v2 .content .bottom-room ul li {
    padding-left: 11% !important;
    font-size: 10px !important; }
    .rooms.rooms-v2 .content .bottom-room ul li img {
      width: 20px !important; }

  /* END/ROOMS */
  .body-page.page-v3 .content .h1-v3 {
    font-size: 35px !important; }
  .body-page.page-v3 .content .h3-v3 {
    margin-bottom: 20px !important;
    font-size: 18px !important; }
  .body-page.page-v3 .content .p-v3 {
    font-size: 15px !important; }
  .body-page.page-v3 .content .wrap {
    margin: 27px 0; }
    .body-page.page-v3 .content .wrap #timer {
      border: 2px solid #fff;
      display: inline-block;
      padding: 0; }
      .body-page.page-v3 .content .wrap #timer .padding {
        padding-right: 10px;
        padding-left: 10px; }
        .body-page.page-v3 .content .wrap #timer .padding:last-child {
          padding: none;
          padding-right: 10px !important; }
      .body-page.page-v3 .content .wrap #timer #days {
        font-size: 25px; }
      .body-page.page-v3 .content .wrap #timer #hours {
        font-size: 25px; }
        .body-page.page-v3 .content .wrap #timer #hours:before, .body-page.page-v3 .content .wrap #timer #hours:after {
          left: -8%;
          height: 3px;
          width: 3px; }
      .body-page.page-v3 .content .wrap #timer #minutes {
        font-size: 25px; }
        .body-page.page-v3 .content .wrap #timer #minutes:before, .body-page.page-v3 .content .wrap #timer #minutes:after {
          left: -4%;
          height: 3px;
          width: 3px; }
      .body-page.page-v3 .content .wrap #timer #seconds {
        font-size: 25px; }
        .body-page.page-v3 .content .wrap #timer #seconds:before, .body-page.page-v3 .content .wrap #timer #seconds:after {
          left: -15%;
          height: 3px;
          width: 3px;
          top: 30%; }
        .body-page.page-v3 .content .wrap #timer #seconds:after {
          top: 50%; }
      .body-page.page-v3 .content .wrap #timer #hours:after,
      .body-page.page-v3 .content .wrap #timer #minutes:after {
        top: 50%; }
      .body-page.page-v3 .content .wrap #timer #hours:before,
      .body-page.page-v3 .content .wrap #timer #minutes:before {
        top: 40%; }
      .body-page.page-v3 .content .wrap #timer span {
        font-size: 11px; }
  .body-page .content .page404 {
    font-size: 153px; }
  .body-page .content .h3-404 {
    font-size: 23px;
    margin-top: -22px; }
  .body-page .content .p-404 {
    font-size: 12px; }
  .body-page.page-login {
    height: 70rem; }
    .body-page.page-login .content {
      padding-top: 200px; }
      .body-page.page-login .content .form-control {
        width: 100%; }

  .banner-tems {
    padding-top: 125px; }
    .banner-tems .banner-content h2 {
      font-size: 20px;
      margin-top: 10px;
      margin-bottom: 5px; }
    .banner-tems .banner-content p {
      font-size: 14px; }

  .check-out h2 {
    font-size: 16px; }
  .check-out .check-right .checkout_cart {
    padding: 0px 10px 15px 10px; }
  .check-out .check-right .checkout-option .card .img-responsive {
    width: 40px; }

  .body-restaurant .restaurant_item .text {
    margin-top: 20px;
    margin-bottom: 20px; }

  .body-restaurant-1 .restaurant_item .img,
  .body-restaurant-2 .restaurant_item .img {
    float: none !important;
    width: 100% !important; }
  .body-restaurant-1 .restaurant_item .text,
  .body-restaurant-2 .restaurant_item .text {
    margin-top: 15px !important; }

  .body-restaurant-3 .reservation {
    padding: 25px 15px; }

  .wrap-gallery-restaurant .gallery-restaurant .h2-rooms {
    font-size: 25px;
    margin-bottom: -10px; }

  .section-blog .blog .sidebar {
    margin-right: 0px; }
  .section-blog .blog .post .entry-media .wrap .timer {
    padding: 0 5px !important; }
    .section-blog .blog .post .entry-media .wrap .timer .padding {
      padding: 5px;
      font-size: 14px !important; }
      .section-blog .blog .post .entry-media .wrap .timer .padding span {
        font-size: 8px; }
  .section-blog .blog .post .entry-media .posted-on {
    display: none; }
  .section-blog .blog .post .entry-header,
  .section-blog .blog .post .entry-content,
  .section-blog .blog .post .entry-footer {
    padding-left: 0; }
  .section-blog .blog .page-navigation {
    text-align: center;
    margin-top: 40px; }
    .section-blog .blog .page-navigation .first {
      margin-right: 20px; }
    .section-blog .blog .page-navigation .last {
      margin-left: 5px;
      margin-right: 0; }
    .section-blog .blog .page-navigation li {
      margin-right: 15px; }

  .section-about .wrap-about .about-item .heading {
    font-size: 20px; }

  .section-statistics {
    padding-top: 10px;
    padding-bottom: 30px; }
    .section-statistics .statistics .heading {
      font-size: 20px; }
    .section-statistics .statistics .statistics_content {
      margin-top: -20px; }
      .section-statistics .statistics .statistics_content .item {
        margin-top: 25px; }
        .section-statistics .statistics .statistics_content .item .count {
          font-size: 20px;
          margin-bottom: -10px; }
        .section-statistics .statistics .statistics_content .item span {
          font-size: 7px; }

  .section-team .sub-heading {
    font-size: 15px; } }
/* END /480 */
@media (min-width: 580px) and (max-width: 768px) {
  .rooms-v4 .wrap-rooms .wrap-box-v4 .rooms-content .sky-h4 {
    font-size: 20px;
    margin-bottom: 3px; }
  .rooms-v4 .wrap-rooms .wrap-box-v4 .rooms-content .price {
    font-size: 12px !important;
    margin-bottom: 5px; }
  .rooms-v4 .wrap-rooms .wrap-box-v4 .content {
    padding: 15px 10px 26px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .wrap-content {
      position: absolute;
      top: 50%;
      right: 0;
      left: 0;
      transform: translate(0%, -50%);
      padding: 25px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .btn {
      font-size: 13px !important;
      height: 35px !important;
      width: 125px !important;
      line-height: 21px !important;
      margin-top: 5px !important; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .rooms-content1 .sky-h4 {
      font-size: 20px;
      margin-bottom: 5px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .rooms-content1 .price {
      font-size: 15px !important;
      margin-bottom: 5px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content p {
      font-size: 11px !important; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room {
      padding: 25px 5px !important; }
      .rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li {
        padding: 3px 0px 3px 10px !important; }
        .rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li img {
          vertical-align: middle;
          width: 18px !important; } }
@media (min-width: 480px) and (max-width: 580px) {
  .rooms-v4 .wrap-rooms .wrap-box-v4 .rooms-content {
    padding-top: 0px;
    padding-bottom: 0px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .rooms-content .sky-h4 {
      font-size: 16px;
      margin-bottom: 3px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .rooms-content .price {
      font-size: 10px !important;
      margin-bottom: 5px; }
  .rooms-v4 .wrap-rooms .wrap-box-v4 .content {
    padding: 15px 10px 26px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .wrap-content {
      position: absolute;
      top: 50%;
      right: 0;
      left: 0;
      transform: translate(0%, -50%);
      padding: 5px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .btn {
      font-size: 9px !important;
      height: 22px !important;
      width: 85px !important;
      line-height: 10px !important;
      margin-top: -15px !important; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .rooms-content1 .sky-h4 {
      font-size: 15px;
      margin-bottom: 5px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .rooms-content1 .price {
      font-size: 12px !important;
      margin-bottom: 0px; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content p {
      font-size: 11px !important; }
    .rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room {
      padding: 10px !important; }
      .rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li {
        padding: 2px 0px 3px 5px !important;
        font-size: 9px !important; }
        .rooms-v4 .wrap-rooms .wrap-box-v4 .content .bottom-room ul li img {
          vertical-align: middle;
          width: 18px !important; } }
@media (min-width: 768px) {
  .navbar-nav .dropdown-menu .caret {
    transform: rotate(-90deg); }

  .submenu-hover1 {
    position: relative; }
    .submenu-hover1 .dropdown-margin {
      position: absolute;
      left: 100% !important;
      top: 0;
      margin: 0;
      border: none;
      padding: 0;
      box-shadow: 1px 1px 4px; }

  .section-blog,
  .blog-detail {
    padding-bottom: 55px !important; } }

/*# sourceMappingURL=styles.css.map */