<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />
  <link rel="stylesheet" href="/admin/css/admin.css" />
  <link rel="stylesheet" href="/admin/css/admin.dark.css" />
  <link rel="stylesheet" href="/admin/css/variables.css" />
  <link rel="stylesheet" href="/admin/css/reset.css" />
  <style>
   .GPageSpan{ 
    /* background-color: #009688!important; */
    /* color: #009688!important; */
  }

  .GPageLink
  {
    /* background-color: #009688!important;; */
    color: #009688!important;
  }
.layui-laypage-default  li
{
float:left
}
.layui-form-pane .layui-form-label {
    width: 90px;
  
}

  </style>
</head>

<body class="pear-container pear-admin">
<div style="padding: 16px;" >

  <div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form"   method="get" action="#"  id="newslist">
            <div class="layui-form-pane">

                <div class="layui-form-item layui-inline">
                    <label class="layui-form-label">标题</label>
                    <div class="layui-input-inline">
                        <input type="text" name="title" placeholder="" class="layui-input" value="{{.search.title}}">
                    </div>
                </div>

         

                

                <div class="layui-form-item layui-inline">             
                  <div class="layui-input-inline" style="width: 100px;">
                    <select name="ctype" >
                      <option value="">属性 {{.search.ctype}}</option>
                      <option value="IsTop" {{if eq $.search.ctype "IsTop"}}selected{{end}} >推荐</option>
                      <option value="IsLock" {{if eq $.search.ctype "IsLock"}}selected{{end}}>锁定</option>
                      <option value="IsRed" {{if eq $.search.ctype "IsRed"}}selected{{end}}>图片</option>
                      <option value="IsHistory" {{if eq $.search.ctype "IsHistory"}}selected{{end}}>已删除</option>
                      
                  </select>  

                  </div>
              </div>
                <div class="layui-form-item layui-inline">
                    <label class="layui-form-label">发布日期</label>
                    <div class="layui-input-inline">
                        <input type="text" name="time"  autocomplete="off" id="ID-laydate" placeholder=" - " class="layui-input"  value="{{.search.time}}" >
                    </div>
                </div>
                <div class="layui-form-item layui-inline">
                    <button class="layui-btn layui-btn-sm " lay-submit lay-filter="user-query">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <!-- <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary"  style="color: var(--global-primary-color)">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button> -->
                </div>
            </div>

              <div class="layui-table-tool-self ">
                <button class="layui-btn layui-btn-sm ayui-btn-warm" lay-event="getCheckData" onclick="add()" load style="background-color: #36b368;"><i class="pear-icon pear-icon-add"></i>添加新闻</button>
              </div>
       
      

        </form>
    </div>
</div>

  <div class="layui-card">
    <div class="layui-card-body">

      <table class="layui-table"  id="role-table" >
        <thead>
          <tr>
            <th>ID</th>
            <th>名字</th>
            <th>电话</th>
            <th>微信绑定电话</th>

         

            <th>照片</th>
            <th>身份证照片</th>
            <th>学校专业</th>
            <th>微信注册时间</th>
            <th>申请时间</th>
            
            <th>住址</th>
            <th>修改</th>
          </tr>
        </thead>
        <tbody>
          {{range $key, $value := .newss}}
          <tr>
            <td>{{$value.ID}}</td>
            <td>{{$value.Name}}</td>
            <td>{{$value.Tel}}/{{$value.Tel2}}</td>
      
            <td>{{$value.UTel}}</td>


            <td><img src="{{$value.Photo}}" height="80px"></td> 
            <td><img src="{{$value.IDpics}}" height="80px"></td>
     

            <td>{{$value.School}}-{{$value.class}}</td>
            <td>{{$value.RegTime}}</td>
            <td>{{$value.OCreatTime}}</td>
            <td>{{$value.ADDtitle}}</td>

            <td class="layui-form">


              <input type="checkbox" name="CCC" title="锁定|正常" lay-skin="switch" lay-filter="aaa" value="OState" data-id="{{$value.ID}}" {{if eq $value.OState 1}} checked{{end}} alt="{{$value.OState}}">
              <input type="checkbox" name="CCC" title="推荐|非荐" lay-skin="switch" lay-filter="aaa" value="OType" data-id="{{$value.ID}}" {{if eq $value.OType 1}} checked{{end}} alt="{{$value.OType}}">

            </td>

            <td><a class="layui-btn layui-btn-xs" onclick="edit({{$value.ID}})" style="background-color: #36b368;"><i class="pear-icon pear-icon-edit"></i>编辑</a></td>
           

          </tr>
          {{end}}

        </tbody>
      </table>
      <div>{{.page}}</div> 
   
      
    </div>
  </div>

  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>

  <script>

// layui.use('form', function(){
//     var form = layui.form
//     form.render();

//     form.on('switch(aaa)', function(chkbox){

//   }
// });


    layui.use(["button","form","jquery","laydate"], function () {
      var admin = layui.admin;
      var button = layui.button;
      let form = layui.form;
      let $ = layui.jquery;
      var laydate = layui.laydate;

 // 日期范围
 laydate.render({
    elem: "#ID-laydate",
    range: true,
    shortcuts: [
      {
        text: "上个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month - 1, 1),
            new Date(year, month, 0)
          ];
        }
      },
      {
        text: "这个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month, 1),
            new Date(year, month + 1, 0)
          ];
        }
      },
      {
        text: "下个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month + 1, 1),
            new Date(year, month + 2, 0)
          ];
        }
      }
    ]
  });

      button.load({
        elem: '[load]',
        time: 600,
        done: function () {
        //  popup.success("加载完成");
        }
      })
    })

    layui.use(['toast', 'jquery', 'layer',"form","popup"], function () {
      // var toast = layui.toast;
      let $ = layui.jquery;
      var form = layui.form
      var popup=layui.popup
    form.render();
      // toast.error({ title: "2危险消息", message: "消息描述" })
      // toast.warning({ title: "警告消息", message: "消息描述" })
      // toast.info({ title: "通知消息", message: "消息描述" })
      window.edit = function(obj) {
      layer.open({ 
        title: '编辑 - id:' + obj,
        type: 2,
        area: ['90%', '95%'],
        content: '/system/task/edit?TID=' + obj,
        end: function(){
          location.reload();
        }
      })
    }

    form.on('switch(aaa)', function(chkbox){
      layer.load(2, { shade: [0.35, '#ccc'] });
           console.log(chkbox.elem); //开关是否开启，true或者false
           console.log(chkbox.elem.checked); //开关是否开启，true或者false
           console.log(chkbox.value)
           console.log(">>>",chkbox.elem.dataset['id']); //开关是否开启，true或者false
           $.ajax({
                    url:  '/system/task/t-charge?ID='+chkbox.elem.dataset['id'],
                    data:{type:chkbox.value},
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							          popup.success("成功", function () {
                           // location.reload();
					            	});
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
             return false;

    });
        window.remove = function (obj) {
		    layer.confirm('确定删除吗?',{
					icon: 3,
					title: '提示'
                }, function (index) {
                  $.ajax({
                    url: '/system/task/t-charge?ID=' + obj,
                    data:{type:"del"},
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							             popup.success("删除成功", function () {
                          location.reload();
						           });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
          });
			}

    })
  

    window.add = function(obj) {
      layer.open({
        title: '添加',
        type: 2,
        area: ['90%', '95%'],
        content: '/system/info/edit?ID=0',
        end: function(){
          location.reload();
        }
      })
    }




    // layui.use(['notice', 'jquery', 'layer', 'code'], function () {
    //   var notice = layui.notice;

    //   notice.success("成功消息")
    //   notice.error("危险消息")
    //   notice.warning("警告消息")
    //   notice.info("通用消息")
    
    // })

  </script>
</body>

</html>