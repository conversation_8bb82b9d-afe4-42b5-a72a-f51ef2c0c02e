<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
    <link rel="stylesheet" href="/admin/css/admin.css" />
    <link rel="stylesheet" href="/admin/css/admin.dark.css" />
    <link rel="stylesheet" href="/admin/css/variables.css" />
    <link rel="stylesheet" href="/admin/css/reset.css" />
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>
</head>

<body class="pear-admin">
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">
            <div class="main-container">
                <input name="ID" id="ID" type="hidden" value="{{.data.ID}}">


                <div class="layui-form-item">
                    <label class="layui-form-label">标题:</label>
                    <div class="layui-input-block">
                        <input type="text" name="Title" lay-verify="required" value="{{.data.Title}}" autocomplete="off"
                            class="layui-input" style="max-width: 600px;">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">分类：</label>
                    <div class="layui-input-block" style="max-width:300px;min-width:200px">
                        <ul id="demoTree" class="dtree" data-id="0"></ul>
                    </div>
                </div>

                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label">作者</label>
                        <div class="layui-input-inline">
                            <input type="text" name="Uname" id="Uname" class="layui-input" value="{{.data.Uname}}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">来源</label>
                        <div class="layui-input-inline">
                            <input type="text" name="date" id="date" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>


                <div class="layui-form-item">
                    <label class="layui-form-label">跳转URL</label>
                    <div class="layui-input-block">
                        <input name="url" value="{{.data.Url}}" autocomplete="off" placeholder="请输入地址"
                            class="layui-input" type="text" style="max-width: 600px;">
                    </div>
                </div>


                <div class="layui-form-item">
                    <label class="layui-form-label">多图：</label>

                    <!-- 多图 -->
                    <div class="layui-input-block">
                        <style type="text/css">
                            .layui-upload-drag {
                                position: relative;
                                padding: 10px;
                                border: 1px dashed #e2e2e2;
                                background-color: #fff;
                                text-align: center;
                                cursor: pointer;
                                color: #999;
                                margin-right: 10px;
                                margin-bottom: 10px;
                            }

                            .del_img {
                                position: absolute;
                                z-index: 99;
                                right: 0;
                                top: 0;
                                width: 25px;
                                height: 25px;
                                display: block;
                            }

                            .del_img img {
                                position: absolute;
                                z-index: 9;
                                right: 0px;
                                top: 0px;
                                width: 25px;
                                height: 25px;
                                display: inline-block;
                            }
                        </style>

                        {{range $key, $value := .pics}}
                        <div class="layui-upload-drag">
                            <div class="del_img" onclick="remove_image_site_pic__upimgs(this);">
                                <img src="/assets/delete.png">
                            </div>
                            <a href="{{$value}}" target="_blank">
                                <img name="img_src_site_pic__upimgs" src="{{$value}}" alt="建议上传尺寸450x450(点击放大预览)"
                                    title="建议上传尺寸450x450(点击放大预览)" width="90" height="90">
                            </a>
                        </div>

                        {{end}}


                        <div class="layui-upload-drag img_upload_site_pic__upimgs">
                            <img id="upload_album_site_pic__upimgs" src="/assets/default_upload.png"
                                alt="上传建议上传尺寸450x450" title="上传建议上传尺寸450x450" width="90" height="90"><input
                                class="layui-upload-file" type="file" accept="image/*" name="file" multiple="">
                            <input type="hidden" id="site_pic__upimgs" name="site_pic__upimgs"
                                value="{{.newss.piclist}}">
                        </div>
                        <script type="text/javascript">

                            layui.use(['upload', 'croppers'], function () {

                                //声明变量
                                var layer = layui.layer
                                    , upload = layui.upload
                                    , croppers = layui.croppers
                                    , $ = layui.$;

                                // 初始化图片隐藏域
                                var ids = '';
                                $('img[name="img_src_site_pic__upimgs"]').each(function () {
                                    ids += $(this).attr('src') + ","
                                });
                                ids = ids.substr(0, (ids.length - 1));
                                $("#site_pic__upimgs").val(ids);

                                if (2 == 1) {
                                    // 图片裁剪组件
                                    croppers.render({
                                        elem: '#upload_album_site_pic__upimgs'
                                        , name: "site_pic__upimgs"
                                        , saveW: 300     //保存宽度
                                        , saveH: 300
                                        , mark: 1    //选取比例
                                        , area: ['750px', '500px']  //弹窗宽度
                                        , url: "/system/comme/upload-img"
                                        , done: function (data) {
                                            // 如果上传失败
                                            if (!data.data) {
                                                return layer.msg('上传失败');
                                            }

                                            var hideStr = $("#site_pic__upimgs").attr("value");
                                            var itemArr = hideStr.split(',');
                                            if (itemArr.length >= "20") {
                                                layer.msg("最多上传20张图片", { icon: 5, time: 1000 }, function () {
                                                    //TODO...
                                                });
                                                return false;
                                            }

                                            // 渲染界面
                                            var attStr = '<div class="layui-upload-drag">' +
                                                '<div class="del_img" onclick="remove_image_site_pic__upimgs(this);">' +
                                                '<img src="/assets//delete.png"></img>' +
                                                '</div>' +
                                                '<a href="' + data.data + '" target="_blank">' +
                                                '<img name="img_src_site_pic__upimgs" src="' + data.data + '" alt="建议上传尺寸450x450(点击放大预览)" title="建议上传尺寸450x450(点击放大预览)" width="90" height="90">' +
                                                '</a>' +
                                                '</div>';
                                            $(".img_upload_site_pic__upimgs").before(attStr);

                                            // 获取最新的图集
                                            var ids = '';
                                            $('img[name="img_src_site_pic__upimgs"]').each(function () {
                                                ids += $(this).attr('src') + ","
                                            });
                                            ids = ids.substr(0, (ids.length - 1));
                                            // 给隐藏域赋值
                                            $("#site_pic__upimgs").val(ids);

                                            return false;
                                        }
                                    });

                                } else {
                                    /**
                                     * 普通图片上传
                                     */
                                    var uploadInst = upload.render({
                                        elem: '#upload_album_site_pic__upimgs'
                                        , url: "/system/comme/upload-img"
                                        , accept: 'images'
                                        , acceptMime: 'image/*'
                                        , exts: "jpg|png|gif|bmp|jpeg"
                                        , field: 'file'//文件域字段名
                                        , size: 10240 //最大允许上传的文件大小
                                        , multiple: true
                                        , number: 20 //最大上传张数
                                        , before: function (obj) {
                                            //预读本地文件
                                        }
                                        , done: function (res) {
                                            //上传完毕回调

                                            var hideStr = $("#site_pic__upimgs").attr("value");
                                            var itemArr = hideStr.split(',');
                                            if (itemArr.length >= "20") {
                                                layer.msg("最多上传20张图片", { icon: 5, time: 1000 }, function () {
                                                    //TODO...
                                                });
                                                return false;
                                            }

                                            //如果上传失败
                                            if (res.status <= 0) {
                                                return layer.msg('上传失败');
                                            }

                                            //渲染界面
                                            var attStr = '<div class="layui-upload-drag">' +
                                                '<div class="del_img" onclick="remove_image_site_pic__upimgs(this);">' +
                                                '<img src="/assets/delete.png"></img>' +
                                                '</div>' +
                                                '<a href="' + res.data + '" target="_blank">' +
                                                '<img name="img_src_site_pic__upimgs" src="' + res.data + '" alt="建议上传尺寸450x450(点击放大预览)" title="建议上传尺寸450x450(点击放大预览)" width="90" height="90">' +
                                                '</a>' +
                                                '</div>';
                                            $(".img_upload_site_pic__upimgs").before(attStr);

                                            //获取最新的图集
                                            var ids = '';
                                            $('img[name="img_src_site_pic__upimgs"]').each(function () {
                                                ids += $(this).attr('src') + ","
                                            });
                                            ids = ids.substr(0, (ids.length - 1));
                                            //给隐藏域赋值
                                            $("#site_pic__upimgs").val(ids);

                                            return false;
                                        }
                                        , error: function () {
                                            //请求异常回调
                                            return layer.msg('数据请求异常');
                                        }
                                    });
                                }

                            });

                            // 删除图片
                            function remove_image_site_pic__upimgs(obj) {
                                //obj.remove();
                                layui.$(obj).parent().remove();

                                //获取最新的图集
                                var ids = '';
                                layui.$('img[name="img_src_site_pic__upimgs"]').each(function () {
                                    ids += layui.$(this).attr('src') + ","
                                });
                                ids = ids.substr(0, (ids.length - 1));
                                //给隐藏域赋值
                                layui.$("#site_pic__upimgs").val(ids);
                            }

                        </script>
                    </div>

                </div>



                <div class="layui-form-item cover">
                    <label class="layui-form-label">属性：</label>
                    <div class="layui-form">
                        <input type="checkbox" name="AAA" title="推荐">
                        <input type="checkbox" name="BBB" lay-text="图片" checked>
                        <input type="checkbox" name="CCC" lay-text="置顶" checked>
                        <input type="checkbox" name="CCC" lay-text="锁定" checked>
                    </div>
                </div>



                <div class="layui-form-item" style="max-width:1200px;">
                    <label class="layui-form-label">内容：</label>
                    <div class="layui-input-block">
                        <div id="editor—wrapper">
                            <div id="toolbar-container"><!-- 工具栏 --></div>
                            <div id="editor-container"><!-- 编辑器 --></div>

                            <textarea name="Content" id="Content" style="display: none;"></textarea>

                        </div>
                    </div>
                </div>


                <div class="layui-form-item layui-form-text" style="width:665px;">
                    <label class="layui-form-label">描述：</label>
                    <div class="layui-input-block">
                        <textarea name="description" placeholder="请输入描述"
                            class="layui-textarea">{{.data.Description}}</textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布时间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="Time" id="Time" lay-verify="datetime" placeholder="yyyy-MM-dd"
                            autocomplete="off" value="{{.data.Time}}" class="layui-input">
                    </div>
                </div>
            </div>
        </div>



        <div class="bottom">
            <div class="button-container">
                <div class="layui-btn-container layui-col-xs12">
                    <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">清空</button>
                </div>

            </div>
        </div>
    </form>


    <link href="/component/wangeditoredit/style.css" rel="stylesheet">



    <style>
        #editor—wrapper {
            border: 1px solid #ccc;
            z-index: 100;
            /* 按需定义 */
        }

        #toolbar-container {
            border-bottom: 1px solid #ccc;
        }

        #editor-container {
            height: 500px;
        }
    </style>


    <script src="/component/wangeditoredit/index.js"></script>
    <script>
        layui.use(['jquery'], function () {
            var $ = layui.$ //重点处
            const { createEditor, createToolbar } = window.wangEditor
            const editorConfig = {
                placeholder: '输入内容',
                MENU_CONF: {},
                onChange(editor) {
                    const html = editor.getHtml()
                    console.log('editor content', html)
                    // 也可以同步到 <textarea>
                    $("#Content").val(html)
                }
            }

            editorConfig.MENU_CONF['uploadImage'] = {
                server: '/system/comme/upload-img',
                // 自定义插入图片
                // 单个文件的最大体积限制，默认为 2M
                maxFileSize: 1 * 1024 * 1024, // 1M

                // 最多可上传几个文件，默认为 100
                maxNumberOfFiles: 10,

                // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
                allowedFileTypes: ['image/*'],
                onBeforeUpload(files) {
                    console.log('onBeforeUpload', files)

                    return files // 返回哪些文件可以上传
                    // return false 会阻止上传
                },
                onProgress(progress) {
                    console.log('onProgress', progress)
                },
                onSuccess(file, res) {
                    console.log('onSuccess', file, res)
                },
                onFailed(file, res) {
                    alert(res.message)
                    console.log('onFailed', file, res)
                },
                onError(file, err, res) {
                    alert(err.message)
                    console.error('onError', file, err, res)
                },


                customInsert(res, insertFn) {
                    console.error('res', res)             // JS 语法
                    // res 即服务端的返回结果
                    layer.closeAll('loading');
                    if (res.code == 200) {
                        //insertFn(res.data, alt, href)
                        insertFn(res.data, "", "")
                    } 
                    else {
                        layer.msg(res.msg+"登录超时", { icon: 2, time: 1000 });
                        window.location.href="/login.html"
                    }
                    // 从 res 中找到 url alt href ，然后插入图片
                },
            }

            // var myhtml=$("#Content").html()
            const editor = createEditor({
                selector: '#editor-container',
                // html: '',
                config: editorConfig,
                mode: 'default', // or 'simple'
            })

            const toolbarConfig = {}

            const toolbar = createToolbar({
                editor,
                selector: '#toolbar-container',
                config: toolbarConfig,
                mode: 'default', // or 'simple'
            })
            editor.setHtml("{{.Content}}")

        })

    </script>


    <script>
        layui.use(['form', 'jquery', 'laydate', 'dtree'], function () {


            let form = layui.form;
            let $ = layui.jquery;
            var laydate = layui.laydate;


            var mdata = {{.jsonData }};
            var dtree = layui.dtree;
            
        var DemoTree = dtree.render({
            elem: "#demoTree",
            data: mdata, // 使用data加载
            width: "50%",
            line: true,
            selectTips: "请选择分类",

            selectInitVal: "{{.data.ClassId}}", // 你可以在这里指定默认值
            select: true,
            scroll: "#toolbarDiv", // 绑定div元素
            icon: ["0", "-1"]  // 显示非最后一级节点图标，隐藏最后一级节点图标
            //   ficon: ["0","7"],  // 设定一级图标样式。0表示方形加减图标，7表示文件图标
            //   skin: "layui"  // layui主题风格

        });



        laydate.render({
            elem: '#Time'
            , type: 'datetime'
        });

        form.verify({

            ip: [
                /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
                , 'IP地址不符合规则'
            ]
        });

        form.on('radio(type)', function (data) {
            if (data.value == 1) {
                $(".conn-pwd").show();
                $(".conn-key").hide();
            } else {
                $(".conn-key").show();
                $(".conn-pwd").hide();
            }
        });

        form.on('submit(user-save)', function (data) {
            layer.load(2, { shade: [0.35, '#ccc'] });
            $.ajax({
                url: '/system/info/edit',
                data: $('#news_edit').serialize(),
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.closeAll('loading');
                    if (result.code == 200) {
                        layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            // parent.layui.table.reload("role-table");
                        });
                    } else {
                        layer.msg(result.msg, { icon: 2, time: 1000 });
                    }
                }
            })
            return false;
        });


        })
    </script>
</body>

</html>