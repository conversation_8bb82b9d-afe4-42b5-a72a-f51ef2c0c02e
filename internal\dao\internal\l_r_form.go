// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LRFormDao is the data access object for the table L_RForm.
type LRFormDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LRFormColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LRFormColumns defines and stores column names for the table L_RForm.
type LRFormColumns struct {
	FrID     string //
	FrName   string //
	Fr1      string //
	Fr2      string //
	FrTime   string //
	FrRepaly string //
	FrReOk   string //
	FrReTime string //
	Fr1C     string //
	Fr2C     string //
	Fid      string //
	Fstat    string //
}

// lRFormColumns holds the columns for the table L_RForm.
var lRFormColumns = LRFormColumns{
	FrID:     "FrID",
	FrName:   "FrName",
	Fr1:      "Fr1",
	Fr2:      "Fr2",
	FrTime:   "FrTime",
	FrRepaly: "FrRepaly",
	FrReOk:   "FrReOk",
	FrReTime: "FrReTime",
	Fr1C:     "Fr1c",
	Fr2C:     "Fr2c",
	Fid:      "FID",
	Fstat:    "Fstat",
}

// NewLRFormDao creates and returns a new DAO object for table data access.
func NewLRFormDao(handlers ...gdb.ModelHandler) *LRFormDao {
	return &LRFormDao{
		group:    "default",
		table:    "L_RForm",
		columns:  lRFormColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LRFormDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LRFormDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LRFormDao) Columns() LRFormColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LRFormDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LRFormDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LRFormDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
