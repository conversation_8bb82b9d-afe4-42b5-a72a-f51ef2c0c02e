// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalUserYgpdDao is internal type for wrapping internal DAO implements.
type internalUserYgpdDao = *internal.UserYgpdDao

// userYgpdDao is the data access object for table User_ygpd.
// You can define custom methods on it to extend its functionality as you wish.
type userYgpdDao struct {
	internalUserYgpdDao
}

var (
	// UserYgpd is globally public accessible object for table User_ygpd operations.
	UserYgpd = userYgpdDao{
		internal.NewUserYgpdDao(),
	}
)

// Fill with you ideas below.
