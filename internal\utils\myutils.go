package utils

import (
	"context"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/encoding/gcharset"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// 附件目录
func UploadPath() string {
	// 附件存储路径
	upload_dir := "easygoadmin.upload_dir"
	if upload_dir != "" {
		return upload_dir
	} else {
		// 获取项目根目录
		curDir, _ := os.Getwd()
		return curDir + "/public/uploads"
	}
}

// 临时目录
func TempPath() string {
	return UploadPath() + "/temp"
}

// 图片存放目录
func ImagePath() string {
	return UploadPath() + "/images"
}

// 文件目录(非图片目录)
func FilePath() string {
	return UploadPath() + "/file"
}

// 创建文件夹并设置权限
func CreateDir(path string) bool {
	// 判断文件夹是否存在
	if IsExist(path) {
		return true
	}
	// 创建文件夹
	err2 := os.MkdirAll(path, os.ModePerm)
	if err2 != nil {
		log.Println(err2)
		return false
	}
	return true
}

// 判断文件/文件夹是否存在(返回true是存在)
func IsExist(path string) bool {
	// 读取文件信息，判断文件是否存在
	_, err := os.Stat(path)
	if err != nil {
		log.Println(err)
		if os.IsExist(err) {
			// 根据错误类型进行判断
			return true
		}
		return false
	}
	return true
}

// 判断文件是否存在
func IsDir(fileAddr string) bool {
	s, err := os.Stat(fileAddr)
	if err != nil {
		log.Println(err)
		return false
	}
	return s.IsDir()
}

// 处理富文本
func SaveImageContent(content string, title string, dirname string) string {
	str := `<img src="(?s:(.*?))"`
	//解析、编译正则
	ret := regexp.MustCompile(str)
	// 提取图片信息
	alls := ret.FindAllStringSubmatch(content, -1)
	// 遍历图片数据
	for _, v := range alls {
		// 获取图片地址
		item := v[1]
		if item == "" {
			continue
		}
		// 保存图片至正式目录
		image, _ := SaveImage(item, dirname)
		if image != "" {
			content = strings.ReplaceAll(content, item, "[IMG_URL]"+image)
		}
	}
	// 设置ALT标题
	if strings.Contains(content, "alt=\"\"") && title != "" {
		content = strings.ReplaceAll(content, "alt=\"\"", "alt=\""+title+"\"")
	}
	return content
}

func Md5(password string) (string, error) {
	// 第一次MD5加密
	password, err := gmd5.Encrypt(password)
	if err != nil {
		return "", err
	}
	// 第二次MD5加密
	password2, err := gmd5.Encrypt(password)
	if err != nil {
		return "", err
	}
	return password2, nil
}

// 判断元素是否在数组中
func InArray(value string, array []interface{}) bool {
	for _, v := range array {
		if gconv.String(v) == value {
			return true
		}
	}
	return false
}

// 登录用户ID
func Uid(session *ghttp.Session) string {

	// 返回相应的 session key 的 username key 的用户名
	re_val, _ := session.Get("manager")
	return gconv.String(re_val.Map()["userId"])

	//return session.GetInt("userId")
}

// 判断用户登录状态
func IsLogin(session *ghttp.Session) bool {
	// 获取用户ID
	re_val, err := session.Get("manager")
	if err != nil {
		panic(err)
	}
	uid := gconv.String(re_val.Map()["userId"])
	num, err := strconv.Atoi(uid)
	if err != nil {
		// 如果转换出错，num的值将为0
		return false
		fmt.Println(err)
	}
	//fmt.Println(num) // 输出：123

	return num > 0
}

// 取session 的用户名
func GetSessionUsername(r *ghttp.Request, sessionKey string, usernameKey string) string {
	// 返回相应的 session key 的 username key 的用户名
	re_val, _ := r.Session.Get(sessionKey)
	return gconv.String(re_val.Map()[usernameKey])
}

// 取session 的用户名
func GetSessionUsernameN(r *ghttp.Request) string {
	// 返回相应的 session key 的 username key 的用户名
	re_val, _ := r.Session.Get("manager")
	return gconv.String(re_val.Map()["userId"])
}

// 判断用户登录状态
func GetSessionUserLogin(r *ghttp.Request) bool {
	// 返回相应的 session key 的 username key 的用户名
	re_val, err := r.Session.Get("manager")
	if err != nil {
		fmt.Println(err)
		return false
		// 如果转换出错，num的值将为0
	}
	fmt.Println(re_val)
	uid := gconv.String(re_val.Map()["userId"])
	fmt.Println(uid)
	num, err := strconv.Atoi(uid)
	if err != nil {
		fmt.Println(err)
		return false
		// 如果转换出错，num的值将为0

	}
	//fmt.Println(num) // 输出：123
	return num > 0
}

// 获取数据库表
func GetDatabase() (string, error) {
	// 获取数据库连接
	link := "database.link"
	if link == "" {
		return "", gerror.New("数据库配置读取错误")
	}
	// 分裂字符串
	linkArr := strings.Split(link, "/")
	return linkArr[1], nil
}

// 调试模式
func AppDebug() bool {
	return "easygoadmin.app_debug" == "true"
}

// 系统版本号
func GetVersion() string {
	return "easygoadmin.version"
}

// 数组反转
func Reverse(arr *[]string) {
	length := len(*arr)
	var temp string
	for i := 0; i < length/2; i++ {
		temp = (*arr)[i]
		(*arr)[i] = (*arr)[length-1-i]
		(*arr)[length-1-i] = temp
	}
}

// md5加密
func md5Str(origin string) string {
	m := md5.New()
	m.Write([]byte(origin))
	return hex.EncodeToString(m.Sum(nil))
}

/**
 * 对一个字符串进行MD5加密,不可解密
 */
func GetMd5String(s string) string {
	h := md5.New()
	h.Write([]byte(s)) //使用zhifeiya名字做散列值，设定后不要变
	return hex.EncodeToString(h.Sum(nil))
}

/*获取 SHA1 字符串*/
func GetSHA1String(s string) string {
	t := sha1.New()
	t.Write([]byte(s))
	return hex.EncodeToString(t.Sum(nil))
}

/**
 * 获取一个Guid值
 */
func GetGuid() string {
	b := make([]byte, 48)
	if _, err := io.ReadFull(rand.Reader, b); err != nil {
		return ""
	}
	return GetMd5String(base64.URLEncoding.EncodeToString(b))
}

// 判断路径是否存在
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// 判断元素是否存在数组中
func IsContain(items []interface{}, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

// 判断元素是否存在数组中-字符串类型
func IsContainStrin(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

// 多维数组合并
func ArrayMerge(data []interface{}) []interface{} {
	var rule_ids_arr []interface{}
	for _, mainv := range data {
		ids_arr := strings.Split(mainv.(string), `,`)
		for _, intv := range ids_arr {
			rule_ids_arr = append(rule_ids_arr, intv)
		}
	}
	return rule_ids_arr
}

// 多维数组合并
func ArraymoreMerge(data []interface{}) []interface{} {
	var rule_ids_arr []interface{}
	for _, mainv := range data {
		ids_arr := strings.Split(mainv.(string), `,`)
		for _, intv := range ids_arr {
			rule_ids_arr = append(rule_ids_arr, intv)
		}
	}
	return rule_ids_arr
}

// 合并数组-两个数组合并为一个数组
func MergeArr(a, b []interface{}) []interface{} {
	var arr []interface{}
	for _, i := range a {
		arr = append(arr, i)
	}
	for _, j := range b {
		arr = append(arr, j)
	}
	return arr
}

// 字符串转JSON编码
func StringToJSON(val interface{}) interface{} {
	str := val.(string)
	if strings.HasPrefix(str, "{") && strings.HasSuffix(str, "}") {
		var parameter interface{}
		_ = json.Unmarshal([]byte(str), &parameter)
		return parameter
	} else {
		var parameter []interface{}
		_ = json.Unmarshal([]byte(str), &parameter)
		return parameter
	}
}

// // 获取子菜单包含的父级ID-返回全部ID
// func GetRulesID(tablename string, field string, menus interface{}) interface{} {
// 	menus_rang := menus.([]interface{})
// 	var fnemuid []interface{}
// 	for _, v := range menus_rang {
// 		fid := getParentID(tablename, field, v)
// 		if fid != nil {
// 			fnemuid = MergeArr(fnemuid, fid)
// 		}
// 	}
// 	r_nemu := MergeArr(menus_rang, fnemuid)
// 	uni_fnemuid := UniqueArr(r_nemu) //去重
// 	return uni_fnemuid
// }

// 获取所有父级ID
// func getParentID(tablename string, field string, id interface{}) []interface{} {
// 	var pids []interface{}
// 	pid, _ := g.DB().Table(tablename).Where("id", id).Value(field)
// 	if pid != nil {
// 		a_pid := pid.(int64)
// 		var zr_pid int64 = 0
// 		if a_pid != zr_pid {
// 			pids = append(pids, a_pid)
// 			getParentID(tablename, field, pid)
// 		}
// 	}
// 	return pids
// }

// 去重
func UniqueArr(m []interface{}) []interface{} {
	d := make([]interface{}, 0)
	tempMap := make(map[int]bool, len(m))
	for _, v := range m { // 以值作为键名
		keyv := InterfaceToInt(v)
		if tempMap[keyv] == false {
			tempMap[keyv] = true
			d = append(d, v)
		}
	}
	return d
}

// interface{}转int
func InterfaceToInt(data interface{}) int {
	var t2 int
	switch data.(type) {
	case uint:
		t2 = int(data.(uint))
		break
	case int8:
		t2 = int(data.(int8))
		break
	case uint8:
		t2 = int(data.(uint8))
		break
	case int16:
		t2 = int(data.(int16))
		break
	case uint16:
		t2 = int(data.(uint16))
		break
	case int32:
		t2 = int(data.(int32))
		break
	case uint32:
		t2 = int(data.(uint32))
		break
	case int64:
		t2 = int(data.(int64))
		break
	case uint64:
		t2 = int(data.(uint64))
		break
	case float32:
		t2 = int(data.(float32))
		break
	case float64:
		t2 = int(data.(float64))
		break
	case string:
		t2, _ = strconv.Atoi(data.(string))
		break
	default:
		t2 = data.(int)
		break
	}
	return t2
}

// interface{}转int64
func InterfaceToInt64(data interface{}) int64 {
	var t2 int64
	switch data.(type) {
	case uint:
		t2 = int64(data.(uint))
		break
	case int8:
		t2 = int64(data.(int8))
		break
	case uint8:
		t2 = int64(data.(uint8))
		break
	case int16:
		t2 = int64(data.(int16))
		break
	case uint16:
		t2 = int64(data.(uint16))
		break
	case int32:
		t2 = int64(data.(int32))
		break
	case uint32:
		t2 = int64(data.(uint32))
		break
	case int:
		t2 = int64(data.(int))
		break
	case uint64:
		t2 = int64(data.(uint64))
		break
	case float32:
		t2 = int64(data.(float32))
		break
	case float64:
		t2 = int64(data.(float64))
		break
	case string:
		t2, _ = strconv.ParseInt(data.(string), 10, 64)
		break
	default:
		t2 = data.(int64)
		break
	}
	return t2
}

// interface{}float64
func InterfaceFloat64(data interface{}) float64 {
	var f2 float64
	switch data.(type) {
	case string:
		f2, _ = strconv.ParseFloat(data.(string), 64)
		break
	case int:
		f2 = float64(data.(int))
		break
	case float64:
		f2 = data.(float64)
		break
	}
	return f2
}

// interface{}转string
func InterfaceTostring(i interface{}) string {
	if i == nil {
		return ""
	} else {
		return fmt.Sprintf("%v", i)
	}
}

// 字符串首字母大写
func FirstUpper(s string) string {
	if s == "" {
		return ""
	}
	return strings.ToUpper(s[:1]) + s[1:]
}

// 字符串首字母小写
func FirstLower(s string) string {
	if s == "" {
		return ""
	}
	return strings.ToLower(s[:1]) + s[1:]
}

// md5加密
func Md5Str(origin string) string {
	m := md5.New()
	m.Write([]byte(origin))
	return hex.EncodeToString(m.Sum(nil))
}

// 删除本地附件
func Del_file(file_list []interface{}) {
	for _, val := range file_list {
		dir := fmt.Sprintf("./%v", val)
		os.Remove(dir)
	}
}

// 截取指定字符串中间字符串的方法
func GetBetweenStr(str, start, end string) string {
	n := strings.Index(str, start)
	if n == -1 {
		n = 0
	} else {
		n = n + 1
	}
	str = string([]byte(str)[n:])
	m := strings.Index(str, end)
	if m == -1 {
		m = len(str)
	}
	str = string([]byte(str)[:m])
	return str
}

// // 时间string转 yyyy-MM-dd HH:mm:ss
// func TimeToDateTime(timeStamp gtime.Time) string {
// 	return timeStamp.Format("Y-m-d H:i:s")
// }

// // 时间戳转 yyyy-MM-dd HH:mm:ss
// func TimeStampToDateTime(timeStamp int64) string {
// 	tm := gtime.NewFromTimeStamp(timeStamp)
// 	return tm.Format("Y-m-d H:i:s")
// }

// 时间戳转 yyyy-MM-dd
func TimeStampToDate(timeStamp int64) string {
	tm := gtime.NewFromTimeStamp(timeStamp)
	return tm.Format("Y-m-d")
}

// 日期时间转时间戳
// timetype时间格式类型  datetime=日期时间 datesecond=日期时间秒date=日期
func StringTimestamp(timeLayout string, timetype string) int64 {
	timetpl := "2006-01-02 15:04:05"
	if timetype == "date" {
		timetpl = "2006-01-02"
	} else if timetype == "datetime" {
		timetpl = "2006-01-02 15:04"
	}
	times, _ := time.ParseInLocation(timetpl, timeLayout, time.Local)
	timeUnix := times.Unix()
	return timeUnix
}

// 时间戳格式化为日期字符串
// timetype时间格式类型 date=日期 datetime=日期时间 datesecond=日期时间秒
func TimestampString(timedata interface{}, timetype string) string {
	timetpl := "2006-01-02 15:04:05"
	if timetype == "date" {
		timetpl = "2006-01-02"
	} else if timetype == "datetime" {
		timetpl = "2006-01-02 15:04"
	}
	return time.Unix(timedata.(int64), 0).Format(timetpl)
}

// 获取当前时间戳
func NowTimestamp() int64 {
	return time.Now().Unix()
}

// 是否时间

func IsTime(obj interface{}) bool {
	_, ok := obj.(time.Time)
	return ok
}

// 定义一个接口
type int64er interface {
	int64() int64
}

// 定义一个函数，判断变量是否为int64类型
func isInt64(v interface{}) bool {
	_, ok := v.(int64er)
	return ok
}

// EncryptPassword 密码加密
func EncryptPassword(password, salt string) string {
	return gmd5.MustEncryptString(gmd5.MustEncryptString(password) + gmd5.MustEncryptString(salt))
}

// GetDomain 获取当前请求接口域名
func GetDomain(ctx context.Context) string {
	r := g.RequestFromCtx(ctx)
	pathInfo, err := gurl.ParseURL(r.GetUrl(), -1)
	if err != nil {
		g.Log().Error(ctx, err)
		return ""
	}
	return fmt.Sprintf("%s://%s:%s/", pathInfo["scheme"], pathInfo["host"], pathInfo["port"])
}

// GetClientIp 获取客户端IP
func GetClientIp(ctx context.Context) string {
	return g.RequestFromCtx(ctx).GetClientIp()
}

// GetUserAgent 获取user-agent
func GetUserAgent(ctx context.Context) string {
	fmt.Println("dsdasd===》", g.RequestFromCtx(ctx))

	return g.RequestFromCtx(ctx).Header.Get("User-Agent")
}

// GetLocalIP 服务端ip
func GetLocalIP() (ip string, err error) {
	var addrs []net.Addr
	addrs, err = net.InterfaceAddrs()
	if err != nil {
		return
	}
	for _, addr := range addrs {
		ipAddr, ok := addr.(*net.IPNet)
		if !ok {
			continue
		}
		if ipAddr.IP.IsLoopback() {
			continue
		}
		if !ipAddr.IP.IsGlobalUnicast() {
			continue
		}
		return ipAddr.IP.String(), nil
	}
	return
}

// GetCityByIp 获取ip所属城市
func GetCityByIp(ip string) string {
	if ip == "" {
		return ""
	}
	if ip == "::1" || ip == "127.0.0.1" {
		return "内网IP"
	}
	url := "http://whois.pconline.com.cn/ipJson.jsp?json=true&ip=" + ip
	bytes := g.Client().GetBytes(context.TODO(), url)
	src := string(bytes)
	srcCharset := "GBK"
	tmp, _ := gcharset.ToUTF8(srcCharset, src)
	json, err := gjson.DecodeToJson(tmp)
	if err != nil {
		return ""
	}
	if json.Get("code").Int() == 0 {
		city := fmt.Sprintf("%s %s", json.Get("pro").String(), json.Get("city").String())
		return city
	} else {
		return ""
	}
}
