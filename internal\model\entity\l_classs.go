// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// LClasss is the golang structure for table L_Classs.
type LClasss struct {
	Cid         int    `json:"cid"         orm:"cid"         ` //
	Cnmae       string `json:"cnmae"       orm:"cnmae"       ` //
	Cpareid     int    `json:"cpareid"     orm:"cpareid"     ` //
	Ckeyword    string `json:"ckeyword"    orm:"ckeyword"    ` //
	Curl        string `json:"curl"        orm:"curl"        ` //
	Ctag        string `json:"ctag"        orm:"ctag"        ` //
	Ctag2       string `json:"ctag2"       orm:"ctag2"       ` //
	Ctag3       string `json:"ctag3"       orm:"ctag3"       ` //
	Ctype       int    `json:"ctype"       orm:"ctype"       ` //
	Csubskin    string `json:"csubskin"    orm:"csubskin"    ` //
	Cskin       string `json:"cskin"       orm:"cskin"       ` //
	Csort       int    `json:"csort"       orm:"csort"       ` //
	CsIndexshow int    `json:"csIndexshow" orm:"csIndexshow" ` //
	Czhaiyao    string `json:"czhaiyao"    orm:"czhaiyao"    ` //
	Cisfw       int    `json:"cisfw"       orm:"cisfw"       ` //
}
