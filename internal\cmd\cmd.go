package cmd

import (
	"50go/internal/controller"
	"50go/internal/dao"
	"context"
	"fmt"
	"time"

	"50go/api/ab"
	"50go/api/cms"
	"50go/api/hello"
	"50go/api/info"
	"50go/api/school"
	"50go/api/shop"
	"50go/api/task"
	"50go/api/wx"

	checklogin "50go/internal/logic"
	lnkj "50go/internal/utils"

	captcha "50go/internal/utils"

	"github.com/gogf/gf/v2/frame/g"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gview"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "开始 http 50server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server()

			s.SetSessionMaxAge(time.Minute * 300)
			s.Use(ghttp.MiddlewareHandlerResponse)

			var gctx = gctx.New()
			adapter, err := gcfg.NewAdapterFile("system")
			if err != nil {
				panic(err)
			}
			config := gcfg.NewWithAdapter(adapter)
			fmt.Println(config.MustData(gctx))

			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(ghttp.MiddlewareHandlerResponse)

				group.ALL("/web", func(r *ghttp.Request) {
					r.Response.WriteTpl("web/index.html", g.Map{"site": config.MustData(gctx), "r": r})
				})
				group.ALL("/class/{class}", func(r *ghttp.Request) {
					r.Response.WriteTpl("web/list.html", g.Map{
						"cid": r.Get("class").String(),
						"r":   r,
					})
				})

				group.ALL("/", func(r *ghttp.Request) {
					r.Response.WriteTpl("gov/index.html", g.Map{"site": config.MustData(gctx), "r": r})
				})

				group.ALL("/app/{class}", func(r *ghttp.Request) {
					r.Response.WriteTpl("uav/app.html", g.Map{"site": config.MustData(gctx), "r": r, "cid": r.Get("class").String()})
				})

				group.ALL("/stu/{class}", func(r *ghttp.Request) {
					r.Response.WriteTpl("stu/{class}", g.Map{"site": config.MustData(gctx), "r": r, "cid": r.Get("class").String()})
				})

				group.ALL("/list/{class}", func(r *ghttp.Request) {
					r.Response.WriteTpl("gov/list.html", g.Map{
						"cid": r.Get("class").String(),
						"r":   r,
					})
				})
				group.ALL("/read/{class}/{nid}", func(r *ghttp.Request) {
					cid := r.Get("class").String()
					nid := r.Get("nid").String()

					newsdb := dao.LNewss.Ctx(r.Context())
					news, _ := newsdb.Where("nid", nid).One()

					classdb := dao.LClasss.Ctx(r.Context())
					nclass, _ := classdb.Where("CID", cid).One()

					r.Response.WriteTpl("gov/read.html", g.Map{
						"cid":    r.Get("class").String(),
						"nid":    r.Get("nid").String(),
						"news":   news,
						"nclass": nclass,
					})
				})

			})

			s.Group("/api", func(group *ghttp.RouterGroup) {
				group.Middleware(MiddlewareCORS) //允许跨越的midd

				group.ALLMap(g.Map{
					"/news":   (new(cms.Controller)),    // 用户
					"/wx":     (new(wx.WxController)),   // 交互
					"/hello":  (new(hello.WelcomeInfo)), // 文件
					"/shop":   (new(shop.ShopController)),
					"/school": (new(school.SchoolController)),
					"/task":   (new(task.TaskController)),
					"/ab":     (new(ab.ABController)),
					"/info":   (new(info.InfoController)),
				})

			})

			s.Group("/mysession", func(group *ghttp.RouterGroup) {
				group.Bind(
					controller.Session, // 首页
				)
			})

			//后台  登录中间件
			s.Group("/system", func(group *ghttp.RouterGroup) {

				group.ALL("/login", func(r *ghttp.Request) {
					var (
						idKeyC, base64stringC string
					)
					idKeyC, base64stringC, _ = captcha.GetVerifyImgString(ctx)

					r.Response.WriteTpl("/login.htm", g.Map{"site": config.MustData(gctx), "r": r, "codeimg": base64stringC, "codeid": idKeyC})
				})

				// 这里下面开始需要  验证权限
				group.Middleware(checklogin.MiddlewareAuth)
				group.ALL("/index", func(r *ghttp.Request) {
					r.Response.WriteTpl("index.htm", g.Map{"site": config.MustData(gctx)})

				})

				group.Group("/", func(ngroup *ghttp.RouterGroup) {
					ngroup.ALLMap(g.Map{
						"/news":  controller.Newss, // 用户
						"/info":  controller.Info,  // 交互
						"/ad":    controller.Ad,    // 文件
						"/comme": controller.Comme,
						"/admin": controller.Admin,
						"/goods": controller.Goods,
						"/task":  controller.Task,
						"/ab":    controller.AB,
					})
				})

			})

			//标签LN替换
			gview.Instance().BindFuncMap(gview.FuncMap{
				"ossUrl":    lnkj.GetOssUrl,
				"LnTag":     lnkj.LnTag,
				"LnNews":    lnkj.LnNews,
				"LnNL":      lnkj.LnNL,
				"LnNLP":     lnkj.LnNLP,
				"LnC":       lnkj.LnC,
				"LnAD":      lnkj.LnAD,
				"LnSQL":     lnkj.LnSQL,
				"ImgsSplit": lnkj.ImgsSplit,
			})

			s.Run()
			return nil
		},
	}
)

func MiddlewareCORS(r *ghttp.Request) {
	r.Response.CORSDefault()
	r.Middleware.Next()
}

// func AsyncHandler(r *ghttp.Request) {
// 	// 异步处理
// 	// r.GoCtx(func() {
// 	// 	// 模拟长时间运行的任务
// 	// 	g.Sleep(3 * gtime.Second)

// 	// 	// 渲染模板到响应
// 	// 	if err := r.Response.WriteTplContent("async_template.html"); err != nil {
// 	// 		//g.Log().Error(err)
// 	// 		r.Response.WriteStatus(500, "Server Error")
// 	// 	}
// 	// })

// 	// 创建一个异步HTTP客户端
// 	client := ghttp.Client()
// 	// 异步发送GET请求
// 	response, err := client.Get("http://httpbin.org/get").Async()
// 	if err != nil {
// 		panic(err)
// 	}

// 	// 等待异步请求完成
// 	select {
// 	case r := <-response.Read():
// 		if r.Error != nil {
// 			panic(r.Error)
// 		}
// 		// 输出响应内容
// 		fmt.Println(string(r.Response.ReadAll()))
// 	case <-gtime.SetTimeout(3 * gtime.Second):
// 		fmt.Println("请求超时")
// 	}

// }
