package wx

import (
	"50go/internal/dao"
	sns "50go/internal/utils"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"time"

	"github.com/shopspring/decimal"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

var (
	mchID                      string = "1684020681"                               // 商户号
	mchCertificateSerialNumber string = "43ABC28921CC44F174B767398C8B962B5537F836" // 商户证书序列号
	mchAPIv3Key                string = "ZiheMqM7sxAT9wT46s0vteICw8o3ZbpE"         // 商户APIv3密钥
	Appid                      string = "wx7e0a1d567a276e09"                       //
	MchPKFileName              string = "./manifest/apiclient_key.pem"
)

//	func (c *WxController) getPayResult(param map[string]interface{}, w http.ResponseWriter, r *http.Request) (interface{}, int32) {
//		fmt.Println("微信支付回调")
//
//		handler := notify.NewNotifyHandler(
//			pay.MchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(core.NewCertificateMapWithList(nil)),
//		)
//
//		content := make(map[string]interface{})
//		request, err := handler.ParseNotifyRequest(context.Background(), r, content)
//		utils.ThrowError(err)
//		fmt.Println(request)
//		fmt.Println(content)
//
// //		return "success", 0
// //	}
// func (c *WxController) Notify22(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
// 	r := g.RequestFromCtx(ctx)

// 	mform := r.GetFormMap()

// 	cccc, num := sonotify(mform)
// 	log.Println("微信支付回调：", mform)

// 	r.Response.WriteJson(g.Map{
// 		"code": 200,
// 		"msg":  num,
// 		"data": cccc,
// 	})
// 	return
// }

func (c *WxController) Notify(ctx context.Context, req *PostReq) (res *GetListRes, err error) {

	//func (c *WxController) Notify(param map[string]interface{}) (interface{}, int32) {
	defer func() {
		//错误处理
		if e := recover(); e != nil {
			log.Println("微信支付回调异常：", e)
		}
	}()

	r := g.RequestFromCtx(ctx)
	//mform := r.GetFormMap()
	listItem := r.GetFormMap()
	// log.Println("微信支付回调：", mform)

	// request := notify.Request{}
	// //Map2Struct(mform, &request)

	// mapstructure.Decode(mform, &request)

	log.Println("微信支付回调requestrequestrequest：", listItem)

	if listItem["event_type"] == "TRANSACTION.SUCCESS" {

		//var resource map[string]interface{}
		//json.Unmarshal([]byte(listItem["resource"].(string)), &resource)
		resource := listItem["resource"].(map[string]interface{})
		log.Println("微信支付回调resource：", resource)

		plaintext, _ := utils.DecryptAES256GCM(
			mchAPIv3Key, resource["associated_data"].(string), resource["nonce"].(string), resource["ciphertext"].(string),
		)
		fmt.Println("微信支付失败", err)
		//utils.ThrowError(err)
		transaction := payments.Transaction{}
		//tools.JsonStrToStruct(plaintext, &transaction)

		json.Unmarshal([]byte(plaintext), &transaction)
		fmt.Println("回调回调回调回调回调回调回调", transaction, "plaintextplaintextplaintextplaintextplaintext", plaintext)
		log.Println("微信支付成功：", transaction)

		/*
		   数据处理开始
		*/

		// 将JSON数据解析到map中
		var result map[string]interface{}
		json.Unmarshal([]byte(plaintext), &result)
		// 获取特定的值

		// "out_trade_no": "20249913514",
		// "transaction_id": "4200002326202409091080553719",
		out_trade_no, _ := result["out_trade_no"].(string)
		transaction_id, _ := result["transaction_id"].(string)

		fmt.Println(out_trade_no, transaction_id)

		md := dao.SOrder.Ctx(r.Context())

		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := gtime.Now().Time.In(loc)

		mform := map[string]interface{}{"OState": 1, "OPayState": 1, "OpayType": "微信", "OpayTime": mytime, "transaction_id": out_trade_no}

		md.Where("out_trade_no", out_trade_no).Update(mform)

		// var mydb = db.S_Order.Where(x => x.OParentId == orderid || x.OrderID == orderid);//全部
		// foreach (var yorder in mydb)
		// {
		// 	yorder.OState = 1;
		// 	yorder.OPayState = 1;
		// 	yorder.OpayType = "微信";
		// 	yorder.OpayTime = DateTime.Now;
		// 	yorder.transaction_id = transaction_id;
		// 	yorder.out_trade_no = out_trade_no;
		// 	db.SaveChanges();

		// }

		/*
		   数据处理结束
		*/

		//微信支付回调返回特殊处理，Flag = 40
		tmp := make(map[string]interface{})
		tmp["code"] = "SUCCESS"
		tmp["message"] = "成功"

		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  tmp,
			"data": 40,
		})
		return

	} else {
		fmt.Println("微信支付失败")
		log.Println("微信支付失败", listItem["summary"])
		tmp := make(map[string]interface{})
		tmp["code"] = "500"
		tmp["message"] = "失败"
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  tmp,
			"data": 40,
		})
		return
	}

}

func Map2Struct(m map[string]interface{}, u interface{}) {
	valueOf := reflect.ValueOf(u)

	valueOf = valueOf.Elem()
	typeOf := valueOf.Type()

	for i := 0; i < valueOf.NumField(); i++ {
		//获取字段类型/获取字段标签
		var fieldName = typeOf.Field(i).Name
		//var fieldName=valueOf.Type().Field(i).Name
		if val, ok := m[fieldName]; ok {
			if val != nil && reflect.ValueOf(val).Kind() == valueOf.Field(i).Kind() {
				valueOf.Field(i).Set(reflect.ValueOf(val))
			}
		}
	}

}

// func (c *WxController) notify2222(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
// 	//openid := sns.GetOPENIDbyHeader(ctx) //通过header取得uid

// 	//	paramJson: {"money":3200,"remark":"支付订单 ：edf1bf18d61df96ae40f36ee793787db","orderId":"edf1bf18d61df96ae40f36ee793787db","payName":"在线支付","nextAction":{"type":0,"id":"edf1bf18d61df96ae40f36ee793787db"}}
// 	r := g.RequestFromCtx(ctx)
// 	md := dao.SOrder.Ctx(r.Context())

// 	mform := r.GetFormMap(map[string]interface{}{"UGUID": UID, "OOrderNumber": Guid, "GuidNU": Guid, "OCreatTime": mytime, "ONumber": mynumber,
// 		"OStoreId": onegood["StoreID"], "myprice": onegood["GPrice"], "OLastPrice": OLastPrice, "STruePrice": onegood["GPriceYJ"],

// 		"Oweight": onegood["Oweight"], "SPName": onegood["GName"], "SPImgUrl": onegood["GImg"], "SPID": GID,

// 		"OendTime": mytime3day, "OState": 1, "OPayState": 0, "OpostPrice": 0, "OParentId": 0, "out_trade_no": out_trade_no,
// 	})

// 	result, err := md.Where("GuidNU", GuidNU).Update(mform)
// 	fmt.Println(id, err)
// 	if err == nil {
// 		r.Response.WriteJson(g.Map{
// 			"code": 200,
// 			"msg":  "修改签到成功",
// 			"data": result,
// 		})
// 	}

// }

// 规范路由
func (c *WxController) Getjspai(ctx context.Context, req *PostReq) (res *GetListRes, err error) {

	openid := sns.GetOPENIDbyHeader(ctx) //通过header取得uid

	//	paramJson: {"money":3200,"remark":"支付订单 ：edf1bf18d61df96ae40f36ee793787db","orderId":"edf1bf18d61df96ae40f36ee793787db","payName":"在线支付","nextAction":{"type":0,"id":"edf1bf18d61df96ae40f36ee793787db"}}
	r := g.RequestFromCtx(ctx)
	GuidNU := r.Get("GuidNU")

	md := dao.SOrder.Ctx(r.Context())
	order, _ := md.Where("GuidNU", GuidNU).One()
	log.Println(order)
	wxctx, client, _ := getWechatClient()
	svc := jsapi.JsapiApiService{Client: client}

	d1 := decimal.NewFromFloat(order["OLastPrice"].Float64())
	d2 := d1.Mul(decimal.NewFromInt(100))
	Total := d2.IntPart() //1元
	// 得到prepay_id，以及调起支付所需的参数和签名
	resp, result, err := svc.PrepayWithRequestPayment(wxctx,
		jsapi.PrepayRequest{
			Appid:       core.String(Appid),
			Mchid:       core.String(mchID),
			Description: core.String(order["SPName"].String()),
			OutTradeNo:  core.String(order["out_trade_no"].String()),
			Attach:      core.String("自定义UGUID" + order["UGUID"].String()),
			NotifyUrl:   core.String("https://pd.50cms.com/api/wx/notify"),
			Amount: &jsapi.Amount{
				Total: core.Int64(Total), //1元
			},
			Payer: &jsapi.Payer{
				Openid: core.String(openid),
			},
		},
	)

	log.Println("Total==Total=============================TotalTotal", order["OLastPrice"], Total)

	if err == nil {
		//log.Println(resp, result)
		log.Printf("pay====================>", "status=%d resp=%s", order["OLastPrice"], result.Response.StatusCode, resp)
	} else {
		log.Println("pay==err", order["OLastPrice"], err)
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code": result.Response.StatusCode,
			"data": resp,
			"erro": err,
		})
	return //两种返回方式1

}

// 获取加解密处理
func getWechatClient() (context.Context, *core.Client, error) {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(MchPKFileName)
	if err != nil {
		log.Print("load merchant private key error")
		return nil, nil, err
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, mchCertificateSerialNumber, mchPrivateKey, mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Printf("new wechat pay client err:%s", err)
		return nil, nil, err
	}
	return ctx, client, nil
}

// 创建并生成待支付信息
func CreateWechatPrepayWithPayment(outTradeNo, description, attach, spOpenid string, amount int64) (map[string]interface{}, error) {
	notifyUrl := "/api/thirdParty/wechat/pay/getPayResult"
	ctx, client, err := getWechatClient()
	if err != nil {
		return nil, err
	}

	tmp, _ := time.ParseDuration("5m")
	endTime := time.Now().Add(tmp)
	svc := jsapi.JsapiApiService{Client: client}
	resp, result, err := svc.PrepayWithRequestPayment(ctx,
		jsapi.PrepayRequest{
			Appid:         core.String(Appid),
			Mchid:         core.String(mchID),
			Description:   core.String(description),
			OutTradeNo:    core.String(outTradeNo),
			TimeExpire:    core.Time(endTime),
			Attach:        core.String(attach),
			NotifyUrl:     core.String(notifyUrl),
			GoodsTag:      core.String("WXG"),
			LimitPay:      []string{"no_credit"},
			SupportFapiao: core.Bool(false),
			Amount: &jsapi.Amount{
				Currency: core.String("CNY"),
				Total:    core.Int64(amount),
			},
			Payer: &jsapi.Payer{
				Openid: core.String(spOpenid),
			},
			SettleInfo: &jsapi.SettleInfo{
				ProfitSharing: core.Bool(false),
			},
		},
	)

	if err != nil {
		// 处理错误
		return nil, err
	} else {
		// 处理返回结果
		log.Printf("status=%d resp=%s", result.Response.StatusCode, resp)
	}
	lastresult := make(map[string]interface{})
	//	tmpJson := utils.GetJsonStr(resp)
	//	json.Unmarshal([]byte(tmpJson), &result)
	//	json.Unmarshal([]byte(resp), &result)
	return lastresult, nil
	//return result, nil
}

func JsapiPrepay() {

	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath("./manifest/apiclient_key.pem")
	if err != nil {
		log.Print("load merchant private key error")
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, mchCertificateSerialNumber, mchPrivateKey, mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Printf("new wechat pay client err:%s", err)
	}

	svc := jsapi.JsapiApiService{Client: client}
	resp, result, err := svc.Prepay(ctx,
		jsapi.PrepayRequest{
			Appid:         core.String("wxd678efh567hg6787"),
			Mchid:         core.String("1230000109"),
			Description:   core.String("Image形象店-深圳腾大-QQ公仔"),
			OutTradeNo:    core.String("1217752501201407033233368018"),
			TimeExpire:    core.Time(time.Now()),
			Attach:        core.String("自定义数据说明"),
			NotifyUrl:     core.String("https://www.weixin.qq.com/wxpay/pay.php"),
			GoodsTag:      core.String("WXG"),
			LimitPay:      []string{"LimitPay_example"},
			SupportFapiao: core.Bool(false),
			Amount: &jsapi.Amount{
				Currency: core.String("CNY"),
				Total:    core.Int64(100),
			},
			Payer: &jsapi.Payer{
				Openid: core.String("oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"),
			},
			Detail: &jsapi.Detail{
				CostPrice: core.Int64(608800),
				GoodsDetail: []jsapi.GoodsDetail{jsapi.GoodsDetail{
					GoodsName:        core.String("iPhoneX 256G"),
					MerchantGoodsId:  core.String("ABC"),
					Quantity:         core.Int64(1),
					UnitPrice:        core.Int64(828800),
					WechatpayGoodsId: core.String("1001"),
				}},
				InvoiceId: core.String("wx123"),
			},
			SceneInfo: &jsapi.SceneInfo{
				DeviceId:      core.String("013467007045764"),
				PayerClientIp: core.String("*************"),
				StoreInfo: &jsapi.StoreInfo{
					Address:  core.String("广东省深圳市南山区科技中一道10000号"),
					AreaCode: core.String("440305"),
					Id:       core.String("0001"),
					Name:     core.String("腾讯大厦分店"),
				},
			},
			SettleInfo: &jsapi.SettleInfo{
				ProfitSharing: core.Bool(false),
			},
		},
	)

	if err != nil {
		// 处理错误
		log.Printf("call Prepay err:%s", err)
	} else {
		// 处理返回结果
		log.Printf("status=%d resp=%s", result.Response.StatusCode, resp)
	}
}
