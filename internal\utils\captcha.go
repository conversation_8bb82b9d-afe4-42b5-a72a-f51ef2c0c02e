package utils

import (
	"context"

	"github.com/gogf/gf/v2/text/gstr"
	"github.com/mojocn/base64Captcha"
)

type sCaptcha struct {
	driver *base64Captcha.DriverString
	store  base64Captcha.Store
}

var (
	captcha = sCaptcha{
		driver: &base64Captcha.DriverString{
			Height:          80,
			Width:           240,
			NoiseCount:      3,
			ShowLineOptions: 20,
			Length:          4,
			Source:          "abcdefghjkmnpqrstuvwxyz23456789",
			Fonts:           []string{"chromohv.ttf"},
			//	Fonts:           []string{"chromohv.ttf"},
		},
		store: base64Captcha.DefaultMemStore,
	}
)

// GetVerifyImgString 获取字母数字混合验证码
func GetVerifyImgString(ctx context.Context) (idKeyC string, base64stringC string, err error) {

	driver := captcha.driver.ConvertFonts()
	c := base64Captcha.NewCaptcha(driver, captcha.store)
	idKeyC, base64stringC, _, err = c.Generate()
	return
}

// VerifyString 验证输入的验证码是否正确
func VerifyCode(id, answer string) bool {

	c := base64Captcha.NewCaptcha(captcha.driver, captcha.store)
	answer = gstr.ToLower(answer)
	return c.Verify(id, answer, true)
}

// RefreshCaptcha 刷新验证码
func RefreshCaptcha(ctx context.Context) (string, string, error) {
	idKeyC, base64stringC, err := GetVerifyImgString(ctx)
	if err != nil {
		return "", "", err
	}
	return idKeyC, base64stringC, nil
}
