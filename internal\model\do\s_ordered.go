// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SOrdered is the golang structure of table S_Ordered for DAO operations like Where/Data.
type SOrdered struct {
	g.Meta    `orm:"table:S_Ordered, do:true"`
	Oid       interface{} //
	OrderId   interface{} //
	OHxOpenId interface{} //
	OHxTime   *gtime.Time //
}
