// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// LADClass is the golang structure of table L_ADClass for DAO operations like Where/Data.
type LADClass struct {
	g.Meta     `orm:"table:L_ADClass, do:true"`
	Id         interface{} //
	Cguid      interface{} //
	CNmae      interface{} //
	CpareID    interface{} //
	CKeyWord   interface{} //
	CUrl       interface{} //
	Csort      interface{} //
	CisLock    interface{} //
	CcreatTime *gtime.Time //
	DelTime    *gtime.Time //
}
