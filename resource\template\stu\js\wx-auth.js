// 微信授权相关配置
const wxConfig = {
    appId: '你的公众号APPID', // 需要替换为实际的公众号 APPID
    redirectUri: encodeURIComponent(window.location.origin + '/auth.html'), // 授权后重定向页面
    scope: 'snsapi_userinfo' // snsapi_base 或 snsapi_userinfo
};

// 检查登录状态
function checkLoginStatus() {
    const token = localStorage.getItem('wx_user_token');
    if (!token) {
        redirectToWxAuth();
        return false;
    }
    return true;
}

// 重定向到微信授权页面
function redirectToWxAuth() {
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wxConfig.appId}&redirect_uri=${wxConfig.redirectUri}&response_type=code&scope=${wxConfig.scope}&state=STATE#wechat_redirect`;
    window.location.href = authUrl;
}

// 处理微信授权回调
function handleWxCallback(code) {
    // 发送 code 到后端获取用户信息
    fetch('/api/wx/auth', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 保存用户信息和 token
            localStorage.setItem('wx_user_token', data.token);
            localStorage.setItem('wx_user_info', JSON.stringify(data.userInfo));
            // 跳转到首页
            window.location.href = '/index.html';
        } else {
            alert('微信授权失败，请重试');
        }
    })
    .catch(error => {
        console.error('授权错误：', error);
        alert('授权过程出错，请重试');
    });
} 