<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />
</head>

<body>
<div style="padding: 16px;">

  <div class="layui-card">
    <div class="layui-card-body">

      <div class="" style="height: 34px;">
        <div class="layui-table-tool-self ">
          <button class="layui-btn layui-btn-sm" lay-event="getCheckData" onclick="add()" load><i class="pear-icon pear-icon-add"></i>添加</button>
         <div class="layui-inline" title="提示" lay-event="LAYTABLE_TIPS"><i class="layui-icon layui-icon-tips"></i>
          </div>
        </div>
      </div>


      <table class="layui-table">
        <thead>
          <tr>
            <th>用户名</th>
            <th>登录名</th>
  
            <th>权限</th>
            <th>修改</th>
          </tr>
        </thead>
        <tbody>
          {{range $key, $value := .info}}
          <tr>
            <td>{{$value.uname}}</td>
            <td>{{$value.utel}}</td>
     
            <td>{{$value.upower}}</td>
            <td><a class="layui-btn layui-btn-xs" onclick="edit('{{$value.uguid}}')">编辑</a></td>
          </tr>
          {{end}}
        </tbody>
      </table>

    </div>
  </div>

  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>

  <script>

    layui.use(['jquery', 'layer'], function () {
      var table = layui.table;
 
 //转换静态表格
 table.init('demo', {
   height: 315 //设置高度
   ,limit: 10 //注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
   //支持所有基础参数

 });

      window.edit = function(obj) {
      layer.open({ 
        title: '编辑 - id:' + obj,
        type: 2,
        area: ['80%', '90%'],
        content: '/system/admin/edit?myguid=' + obj,
        end: function(){
          location.reload();
        }
      })
    }

    window.add = function(obj) {
      layer.open({
        title: '添加',
        type: 2,
        area: ['80%', '90%'],
        content: '/system/admin/edit?myguid=0',
        end: function(){
          location.reload();
        }
      })
    }

    })

    // layui.use(['notice', 'jquery', 'layer', 'code'], function () {
    //   var notice = layui.notice;

    //   notice.success("成功消息")
    //   notice.error("危险消息")
    //   notice.warning("警告消息")
    //   notice.info("通用消息")
    
    // })

  </script>
</body>

</html>