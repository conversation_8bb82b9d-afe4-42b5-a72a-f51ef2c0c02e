// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SClasss is the golang structure of table S_Classs for DAO operations like Where/Data.
type SClasss struct {
	g.Meta    `orm:"table:S_Classs, do:true"`
	Scid      interface{} //
	SCNmae    interface{} //
	SCpareID  interface{} //
	SCKeyWord interface{} //
	SCUrl     interface{} //
	SCTag     interface{} //
	SCTag2    interface{} //
	SCTag3    interface{} //
	SCType    interface{} //
	Scimg     interface{} //
	Scsimg    interface{} //
}
