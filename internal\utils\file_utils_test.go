package utils

import (
	"testing"
)

func TestIsImageFile(t *testing.T) {
	tests := []struct {
		filename string
		expected bool
	}{
		{"test.jpg", true},
		{"test.jpeg", true},
		{"test.png", true},
		{"test.gif", true},
		{"test.bmp", true},
		{"test.webp", true},
		{"test.svg", true},
		{"test.ico", true},
		{"test.JPG", true},
		{"test.PNG", true},
		{"test.mp4", false},
		{"test.avi", false},
		{"test.txt", false},
		{"test", false},
		{"", false},
	}

	for _, test := range tests {
		result := IsImageFile(test.filename)
		if result != test.expected {
			t.<PERSON>rrorf("IsImageFile(%s) = %v, expected %v", test.filename, result, test.expected)
		}
	}
}

func TestIsVideoFile(t *testing.T) {
	tests := []struct {
		filename string
		expected bool
	}{
		{"test.mp4", true},
		{"test.avi", true},
		{"test.mov", true},
		{"test.wmv", true},
		{"test.flv", true},
		{"test.webm", true},
		{"test.mkv", true},
		{"test.3gp", true},
		{"test.m4v", true},
		{"test.MP4", true},
		{"test.AVI", true},
		{"test.jpg", false},
		{"test.png", false},
		{"test.txt", false},
		{"test", false},
		{"", false},
	}

	for _, test := range tests {
		result := IsVideoFile(test.filename)
		if result != test.expected {
			t.Errorf("IsVideoFile(%s) = %v, expected %v", test.filename, result, test.expected)
		}
	}
}

func TestGetFileType(t *testing.T) {
	tests := []struct {
		filename string
		expected string
	}{
		{"test.jpg", "image"},
		{"test.png", "image"},
		{"test.mp4", "video"},
		{"test.avi", "video"},
		{"test.txt", "unknown"},
		{"test", "unknown"},
		{"", "unknown"},
	}

	for _, test := range tests {
		result := GetFileType(test.filename)
		if result != test.expected {
			t.Errorf("GetFileType(%s) = %s, expected %s", test.filename, result, test.expected)
		}
	}
}
