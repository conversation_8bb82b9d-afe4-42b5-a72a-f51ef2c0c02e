﻿
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width = device-width, initial-scale = 1.0, maximum-scale = 1.0, user-scalable = 0">
	
<title>登录</title>

<link href="Theme/css/base.css" rel="stylesheet">
<link href="Theme/css/login.css" rel="stylesheet">

 <script src="js/layer-v3.1.1/layer/mobile/layer.js"></script>
    <style>


.loginbut{
    width: 100%;
    height: 1.6rem;
    font-size: .64rem;
    color: #fff;
    text-align: center;
    background: #ffba00;
    border-radius: .8rem;
}
.cologreen
{
    background: #32bb1a
}
    </style>


</head>

<body>
<div class="login">

	<div class="login-form">
		<form runat="server">
			<h1 class="login-logo">
				<img src="<%=mimg %>">
			</h1>
			
			<div class="login-form-group">
				<label class="icon-phone">手机号</label>

		      <div class="input-group">
                   <asp:TextBox ID="txtUserName" runat="server" CssClass="input uname" placeholder="请输入手机号"></asp:TextBox>
		            <i class="clear-keyword"></i>
				</div>
			</div>

			
			
			<div class="login-form-group">
				<label class="icon-pw">密 码</label>

				<div class="input-group">
		
                     <asp:TextBox ID="txtUserPwd" runat="server" CssClass="input passw" placeholder="请输入登录密码" TextMode="Password" ></asp:TextBox>
					<i class="clear-keyword"></i>
				</div>
			</div>


				<div class="login-form-group">
				<label class="icon-pw">推荐码</label>

				<div class="input-group">
		
                     <asp:TextBox ID="txtUserParent" runat="server" CssClass="input" placeholder="请输入推荐码" ></asp:TextBox>
					<i class="clear-keyword"></i>
				</div>
			</div>
			
			<div class="login-form-group">
				<label class="icon-code">验证码</label>

				<div class="input-group form-code">
					<input placeholder="请输入">
					<i class="clear-keyword"></i>
					
					<img src="../../Theme/img/ValidateImage.jpg">
				</div>
			</div>
			
			<div class="login-submit">
				  <asp:Button ID="LoginBUT" runat="server" Text="登   录"   OnClientClick ="charpw()" CssClass="loginbut" OnClick="LoginBUT_Click" />
      	
			</div>
			
		<div class="login-links">
            <asp:Button ID="Button1" runat="server" Text="微  信  登   录"  CssClass="loginbut cologreen" OnClick="wxlogin_Click" />
			</div>
		</form>
	</div>
</div>


<script src="Theme/js/jquery-1.11.2.min.js"></script>
     <script src="/js/base64.js" language="javascript"></script>
     <script type="text/javascript">
         function charpw() {
             var password = $("#txtUserPwd").val();

             var base = new Base64();
             var encypass = base.encode(password);//base64位加密
             $('#txtUserPwd').val(encypass)

         }
    </script>
</body>
</html>
