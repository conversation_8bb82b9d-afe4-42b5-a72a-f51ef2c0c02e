// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLAdDao is internal type for wrapping internal DAO implements.
type internalLAdDao = *internal.LAdDao

// lAdDao is the data access object for table L_AD.
// You can define custom methods on it to extend its functionality as you wish.
type lAdDao struct {
	internalLAdDao
}

var (
	// LAd is globally public accessible object for table L_AD operations.
	LAd = lAdDao{
		internal.NewLAdDao(),
	}
)

// Fill with you ideas below.
