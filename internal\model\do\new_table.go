// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewTable is the golang structure of table new_table for DAO operations like Where/Data.
type NewTable struct {
	g.Meta `orm:"table:new_table, do:true"`
	Myid   interface{} //
	Name   interface{} //
}
