// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskCT is the golang structure for table Task_CT.
type TaskCT struct {
	Id            int         `json:"ID"            orm:"ID"            ` //
	StudentID     int         `json:"StudentID"     orm:"StudentID"     ` //
	TeacherID     int         `json:"TeacherID"     orm:"TeacherID"     ` //
	Guid          string      `json:"Guid"          orm:"Guid"          ` //
	CtClass       string      `json:"CtClass"       orm:"CtClass"       ` //
	CtAnswer      string      `json:"CtAnswer"      orm:"CtAnswer"      ` //
	CTTime        string      `json:"CTTime"        orm:"CTTime"        ` //
	Ctcontent     string      `json:"Ctcontent"     orm:"Ctcontent"     ` //
	CtUID         string      `json:"CtUID"         orm:"CtUID"         ` //
	CTstat        int         `json:"CTstat"        orm:"CTstat"        ` //
	Feedback      string      `json:"Feedback"      orm:"Feedback"      ` //
	JZView        int         `json:"JZView"        orm:"JZView"        ` //
	JZFirstTime   *gtime.Time `json:"JZFirstTime"   orm:"JZFirstTime"   ` //
	JZContent     string      `json:"JZContent"     orm:"JZContent"     ` //
	ManageContent string      `json:"ManageContent" orm:"ManageContent" ` //
	ManageTime    *gtime.Time `json:"ManageTime"    orm:"ManageTime"    ` //
	ManageUID     string      `json:"ManageUID"     orm:"ManageUID"     ` //
	TaskGuid      string      `json:"TaskGuid"      orm:"TaskGuid"      ` //
	CTnum         string      `json:"CTnum"         orm:"CTnum"         ` //
	CTimg         string      `json:"CTimg"         orm:"CTimg"         ` //
	Ctai          string      `json:"CTAI"          orm:"CTAI"          ` //
}
