<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Frameset//EN" "http://www.w3.org/TR/html4/frameset.dtd">
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <link rel="stylesheet" href="/css/normalize.css">
  <link rel="stylesheet" href="/css/main.css">
</head>

<body>

  <!-- page-bg -->
  <image class="page-bg" src="/images/page_bg.jpeg" />
  
  <!-- header --->
  <div class="page-1200 page-header">
    <image class="logo" src="/images/page_header_logo.png" />
    <div class="web-name">昆明海关</div>
    <div class="today">今天是2024年06月04日</div>
  </div>

  <!-- 页面标题 -->
  <div class="page-1200 page-title">
    <image class="title-img" src="/images/page_title.png" />
  </div>

  <!-- content -->
  <table class="page-1200 list-content">
    <tr>
      <td class="left-td">
        <!-- side panel -->
        <div class="side-panel">
          <!-- worker -->
          <div class="col-worker">
            <div class="user-plate">
              <div class="user">
                <image class="icon" src="/images/icon_avatar.png" />
                <div class="name"><span>叶锐</span>，你好！</div>
              </div>
              <div class="links">
                <div class="link">
                  <image class="icon" src="/images/icon_user.png" />
                  <span>个人信息</span>
                </div>
                <div class="link">
                  <image class="icon" src="/images/icon_fav.png" />
                  <span>我的收藏</span>
                </div>
              </div>
            </div>
            <div class="worker-plate red">
              <div class="title">
                <image class="icon" src="/images/icon_worker_red.png" />
                <span>HB2012</span>
              </div>
              <div class="link">待办事项<span>11</span></div>
              <div class="link">待办事项<span>11</span></div>
            </div>
            <div class="worker-plate orange">
              <div class="title">
                <image class="icon" src="/images/icon_worker_orange.png" />
                <span>HB2011</span>
              </div>
              <div class="link">待办事项<span>11</span></div>
              <div class="link">待办事项<span>11</span></div>
            </div>
            <div class="buttons">
              <div class="btn">总署通讯录</div>
              <div class="btn">昆关通讯录</div>
            </div>
          </div>
          <!-- menu -->
          <div class="menu">
            <ul class="menu-list">
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right_red.png" /></li>
              <ul class="item-list">
                <li class="list-item"><a href="#">总署快报</a></li>
                <li class="list-item"><a class="current" href="#">总署快报</a></li>
                <li class="list-item"><a href="#">总署快报</a></li>
                <li class="list-item"><a href="#">总署快报</a></li>
                <li class="list-item"><a href="#">总署快报</a></li>
                <li class="list-item"><a href="#">总署快报</a></li>
                <li class="list-item"><a href="#">总署快报</a></li>
                <li class="list-item"><a href="#">总署快报</a></li>
                <li class="list-item"><a href="#">总署快报</a></li>
                <li class="list-item"><a href="#">总署快报</a></li>
              </ul>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right.png" /></li>
              <li class="menu-item"><a href="#">总署快报</a><image class="icon" src="/images/icon_arrow_right_red.png" /></li>
            </ul>
          </div>
        </div>
      </td>
      <td>
        <!-- list panel -->
        <div class="list-panel">
          <!-- breadcrumb -->
          <div class="breadcrumb">
            <image class="icon" src="/images/icon_position.png" />
            <a href="#">昆明海关</a>
            <image class="icon" src="/images/icon_arrow_line.png" />
            <a href="#">法规处</a>
            <image class="icon" src="/images/icon_arrow_line.png" />
            <span>文章列表</span>
          </div>
          <!-- title -->
          <div class="title">综合业务处</div>

          <div id='calendar'></div>

          <div class="line"></div>
    
    



          <ul class="list">
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li class="space"></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li class="space"></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li class="space"></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li><span>•</span><a href="#">关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a><span class="date">2024-06-04</span></li>
            <li class="space"></li>
          </ul>
          <div class="pagination">
            <a href="#"><image class="icon" src="/images/icon_arrow_line_left.png"></a>
            <span>1</span>
            <a href="#">2</a>
            <a href="#">3</a>
            <a href="#">4</a>
            <a href="#">5</a>
            <a href="#">...</a>
            <a href="#"><image class="icon" src="/images/icon_arrow_line_right.png"></a>
            <p>每页显示25条 共32页</p>
          </div>
        </div>
      </td>
    </tr>
  </table>
  

  <!-- copy -->
  <div class="copy">
    <image class="copy-top-line" src="/images/copy_line.png"></image>
    <image class="copy-bg" src="/images/copy_bg.jpeg"></image>
    <div class="page-1200 copy-panel">
      <div class="select-plate">
        <div class="selector">部门及隶属海关网站</div>
        <div class="selector">友情链接</div>
      </div>
      <div class="info-plate">
        <image class="logo" src="/images/copy_logo.png" />
        <div class="txt">
          <p>Copgright© 2019中华人民共和国昆明海关 版权所有</p>
          <p>电话:0871-63016999 邮编:650051 地址:云南省昆明市北京路618号</p>
        </div>
      </div>
    </div>
  </div>



  
</body>
<link rel="stylesheet" type="text/css" href="/stylesheets/simple-calendar.css?v=1.01">
<link rel="stylesheet" type="text/css" href="/layui/css/layui.css">
<script src="https://www.jq22.com/jquery/jquery-1.10.2.js"></script>
<script type="text/javascript" src="/layui/layui.js"></script>
<script type="text/javascript" src="/javascripts/simple-calendar.js"></script>


<script type="text/javascript">

  /**
 * TODO 本功能需要layer和jquery插件的支持； 本功能为二次开发。
 * @see 源文件地址：http://sc.chinaz.com/jiaobendemo.aspx?downloadid=0201785541739
 */
var layer;
layui.use('layer', function () {
    layer = layui.layer;
});



function main(caldata) {
    if (typeof (layer) != "object" || !layer) {
        setTimeout("main()", 400);
        return;
    }
    var myCalendar = new SimpleCalendar('#calendar', {
        width: '100%',
        height: '500px',
        language: 'CH', //语言
        showLunarCalendar: true, //阴历
        showHoliday: false, //休假-暂时禁用
        showFestival: true, //节日
        showLunarFestival: true, //农历节日
        showSolarTerm: true, //节气
        showMark: true, //标记
        realTime: true, //具体时间
        timeRange: {
            startYear: 2002,
            endYear: 2049
        },
        mark: {},
        markColor: ['#058DC7', '#50B432', '#ED561B', '#DDDF00', '#24CBE5', '#64E572', '#FF9655', '#FFF263', '#6AF9C4'],//记事各个颜色
        main: function (year, month) {
            // alert("[获取数据]" + year + "--->" + month);
            var index = -1;
            if (layer) index = layer.msg('正在查询数据......', {icon: 16, shade: 0.6});
            //@-这里获取数据：
            console.log(year + "--->" + month);

            //模拟获取数据start
            var resultObj = {}, status = ['待揽收', '已发货', '配送中', '已签收'];
            // for (var i = 1; i <= 3; i++) {
            //     var array = [];
            //     var date = year + "-" + month + "-" + (i < 10 ? '0' + i : i);
            //     for (var num = 0; num <= i % 4; num++)
            //         array.push({
            //             title: '第' + (num + 1) + '个货区某个快递在该天需要处理的事情呀呀呀',
            //             name: '某区',
            //             ratio: (num + 1) * (num + 1) + '%',
            //             status: num,
            //             statusText: status[num]
            //         });
            //     resultObj[date] = i == 27 ? [] : array;
            // }


            resultObj=caldata;
//   resultObj= {
//     "2024-8-01": [
//         {
//             "title": "第1个货区某个快递在该天需要处理的事情呀呀呀",
//             "name": "某区",
//             "ratio": "1%",
//             "status": 0,
//             "statusText": "待揽收"
//         },
//         {
//             "title": "第2个货区某个快递在该天需要处理的事情呀呀呀",
//             "name": "某区",
//             "ratio": "4%",
//             "status": 1,
//             "statusText": "已发货"
//         }
//     ],
//     "2024-8-02": [
//         {
//             "title": "第1个货区某个快递在该天需要处理的事情呀呀呀",
//             "name": "某区",
//             "ratio": "1%",
//             "status": 0,
//             "statusText": "待揽收"
//         },
//         {
//             "title": "第2个货区某个快递在该天需要处理的事情呀呀呀",
//             "name": "某区",
//             "ratio": "4%",
//             "status": 1,
//             "statusText": "已发货"
//         },
//         {
//             "title": "第3个货区某个快递在该天需要处理的事情呀呀呀",
//             "name": "某区",
//             "ratio": "9%",
//             "status": 2,
//             "statusText": "配送中"
//         }
//     ],
//     "2024-8-03": [
//         {
//             "title": "第1个货区某个快递在该天需要处理的事情呀呀呀",
//             "name": "某区",
//             "ratio": "1%",
//             "status": 0,
//             "statusText": "待揽收"
//         }
//     ]
// }
            
            console.log(resultObj);
            //模拟获取数据end

            if (layer) layer.close(index);
            return resultObj;
        },
        timeupdate: false,//显示当前的时间HH:mm
        theme: {
            changeAble: false,
            weeks: {
                backgroundColor: '#FBEC9C',
                fontColor: '#4A4A4A',
                fontSize: '20px',
            },
            days: {
                backgroundColor: '#ffffff',
                fontColor: '#565555',
                fontSize: '24px'
            },
            todaycolor: 'orange',
            activeSelectColor: 'orange',
        }
    });
}



$.ajax({url:"/api/news/get-cale",success:function(result){
        main(result);
    }});


  window.ondragstart = function(){
    return false;
  }
</script>
</html>