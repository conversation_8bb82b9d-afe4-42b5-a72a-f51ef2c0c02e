package shop

import (
	"50go/internal/dao"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type ShopController struct{}

type PostReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.<PERSON> `method:"post"  summary:"Post获得新闻信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}

type GetReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.<PERSON>a `method:"get"  summary:"Get获得新闻信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}
type GetListRes struct {
	Name string
	Id   int
}

func GetPageData(r *ghttp.Request) (int, int) {
	page := r.GetQuery("page", 1).Int()
	psize := r.GetQuery("psize", 20).Int()
	return page, psize
}

// 规范路由
func (c *ShopController) NearStore(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)
	md := dao.SStore.Ctx(r.Context())
	ncount, err := md.Count()
	stores, _ := md.FieldsEx("Content").Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"page":   page,
			"psize":  psize,
			"data":   stores,
			"code":   1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *ShopController) Goodlist(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)
	md := dao.SGoods.Ctx(r.Context()).WhereNot("GIState", 1)
	ncount, err := md.Count()
	goods, _ := md.FieldsEx("Content").Page(page, psize).All()

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"page":   page,
			"psize":  psize,
			"data":   goods,
			"code":   1,
		})
	return
}

func (c *ShopController) MenuGoodlist(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	name := r.GetQuery("name", "没参数的默认返回值!") //获取name

	listgood := g.Map{
		"GPrice":      3213,
		"GPrice2":     12,
		"G_Introduce": "颜色分类：透明-抽象猫咪生产企业：深圳款式：保护壳",
		"Name":        "抽象猫咪手机壳",
		"gid":         1,
		"numb":        0,
		"pic":         "/Up/2",
	}

	listjson := g.Map{
		"typeName":    "a4",
		"GetImgs":     "img",
		"menuContent": listgood,
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"businessend":   "21:00",
			"logourl":       "/images/logo.png",
			"name":          "小熊工厂洗--演示系统 请勿下单",
			"minamount":     "99",
			"businessstart": "11:03",
			"address":       "2东路269号",
			"notice":        "洗衣洗鞋新时尚",
			"telephone":     "***********",
			"tel":           "0871-66227317",
			"expressfee":    "1",
			"ad":            name,
			"seller":        listjson,
		})
	return
}
