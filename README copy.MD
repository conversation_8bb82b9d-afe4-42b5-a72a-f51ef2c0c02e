gdb模块支持对数据记录的写入、更新、删除时间自动填充，提高开发维护效率。为了便于时间字段名称、类型的统一维护，如果使用该特性，我们约定：

字段应当设置允许值为null。
字段的类型必须为时间类型，如:date,  datetime,  timestamp。不支持数字类型字段，如int。
字段的名称支持自定义设置，默认名称约定为：
created_at用于记录创建时更新，仅会写入一次。
updated_at用于记录修改时更新，每次记录变更时更新。
deleted_at用于记录的软删除特性，只有当记录删除时会写入一次。
字段名称其实不区分大小写，也会忽略特殊字符，例如CreatedAt,  UpdatedAt,  DeletedAt也是支持的。此外，时间字段名称可以通过配置文件



	ad := []map[string]interface{}{
		{"pic": "upload/ad1.jpg", "title": ""},
		{"pic": "upload/ad2.jpg", "title": ""},
		{"pic": "upload/ad3.jpg", "title": ""},
		{"pic": "upload/ad4.jpg", "title": ""},
		{"pic": "upload/ad5.jpg", "title": ""},
		{"pic": "upload/ad6.jpg", "title": ""},
	}




json转 map


m := make(map[string]string)
	err := json.Unmarshal([]byte(jsonStr), &m)



func Unmarshal(data []byte, v interface{})
如果被解析的对象是以[开头，那么表示这是个数组对象会进入到 scanBeginArray 分支；如果是以{开头，表明被解析的对象是一个结构体或 map，那么进入到 scanBeginObject 分支 等等。




	var datamap []map[string]interface{}

		for _, v := range mydata {
			if v["jjimgs"] != nil && v["jjimgs"] != "" {
				v["arrjjimgs"] = strings.Split(v["jjimgs"].(string), ",")[0]
			}
			datamap = append(datamap, v)

		}