// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TaskCTDao is the data access object for the table Task_CT.
type TaskCTDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TaskCTColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TaskCTColumns defines and stores column names for the table Task_CT.
type TaskCTColumns struct {
	Id            string //
	StudentID     string //
	TeacherID     string //
	Guid          string //
	CtClass       string //
	CtAnswer      string //
	CTTime        string //
	Ctcontent     string //
	CtUID         string //
	CTstat        string //
	Feedback      string //
	JZView        string //
	JZFirstTime   string //
	JZContent     string //
	ManageContent string //
	ManageTime    string //
	ManageUID     string //
	TaskGuid      string //
	CTnum         string //
	CTimg         string //
	Ctai          string //
}

// taskCTColumns holds the columns for the table Task_CT.
var taskCTColumns = TaskCTColumns{
	Id:            "ID",
	StudentID:     "StudentID",
	TeacherID:     "TeacherID",
	Guid:          "Guid",
	CtClass:       "CtClass",
	CtAnswer:      "CtAnswer",
	CTTime:        "CTTime",
	Ctcontent:     "Ctcontent",
	CtUID:         "CtUID",
	CTstat:        "CTstat",
	Feedback:      "Feedback",
	JZView:        "JZView",
	JZFirstTime:   "JZFirstTime",
	JZContent:     "JZContent",
	ManageContent: "ManageContent",
	ManageTime:    "ManageTime",
	ManageUID:     "ManageUID",
	TaskGuid:      "TaskGuid",
	CTnum:         "CTnum",
	CTimg:         "CTimg",
	Ctai:          "CTAI",
}

// NewTaskCTDao creates and returns a new DAO object for table data access.
func NewTaskCTDao(handlers ...gdb.ModelHandler) *TaskCTDao {
	return &TaskCTDao{
		group:    "default",
		table:    "Task_CT",
		columns:  taskCTColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TaskCTDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TaskCTDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TaskCTDao) Columns() TaskCTColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TaskCTDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TaskCTDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TaskCTDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
