package task

import (
	"50go/internal/dao"
	"50go/internal/utils"
	"context"
	"fmt"
	"strings"
	"time"

	sns "50go/internal/utils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
)

type TaskController struct{}

type PostReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.Meta `method:"post"  summary:"获得系统信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}

type GetReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.Meta `method:"get"  summary:"获得系统信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}
type GetListRes struct {
	Name string
	Id   int
}

func GetPageData(r *ghttp.Request) (int, int) {
	page := r.GetQuery("page", 1).Int()
	psize := r.GetQuery("psize", 20).Int()
	return page, psize
}

func (c *TaskController) Tasklist(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)

	pjstate := r.Get("pjstate").String()
	page, psize := GetPageData(r)

	Type := r.Get("Type").String() //SID 学生id， TID 老师
	md := dao.Task.Ctx(r.Context()).WhereNot("Tstate", -1)
	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid

	if Type == "TID" { //老师

		mdteacher := dao.TaskTeacher.Ctx(r.Context()).Where("UID", UID)
		teacherdata, _ := mdteacher.One()
		Studentmd := dao.TaskStudent.Ctx(r.Context()).Fields("ID").Where("TID", teacherdata["ID"]).WhereOr("ID", 1) //第一个用户为测试学生

		md = md.WhereIn("StudentID", Studentmd)

	} else { //家长
		Studentmd := dao.TaskStudent.Ctx(r.Context()).Fields("ID").Where("FID", UID)
		md = md.WhereIn("StudentID", Studentmd)
	}

	if pjstate != "" {
		md = md.Where("PJstate", pjstate)
	}
	ncount, err := md.Count()

	data, _ := md.LeftJoinOnFields("Task_Student", "StudentID", "=", "ID").LeftJoinOnFields("User", "TeacherID", "=", "UID").Fields("Task.*,Task_Student.SGuid,Task_Student.School,Task_Student.Class,Task_Student.Name,User.UID,User.UNickName,User.UTrueName").OrderDesc("Task.ID").Page(page, psize).All()

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"page":   page,
			"psize":  psize,
			"data":   data,
			"code":   1,
		})
	return
}

// 规范路由
func (c *TaskController) GetTask(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	id := r.Get("ID").String()

	md := dao.Task.Ctx(r.Context())
	data, err := md.Where("ID", id).One()

	QDimg := strings.Split(data["QDimg"].String(), ",")
	JHimg := strings.Split(data["JHimg"].String(), ",")
	GOimg := strings.Split(data["GOimg"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"data":   data,
			"code":   1,
			"QDimgs": QDimg,
			"JHimg":  JHimg,
			"GOimg":  GOimg,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 规范路由
func (c *TaskController) GetTaskBysid(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	StudentID := r.Get("SID").String()

	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)
	gt := gtime.New(mytime)
	todaybegan := gt.StartOfDay().String()
	todayend := gt.EndOfDay().String()

	md := dao.Task.Ctx(r.Context())

	data, err := md.Where("StudentID", StudentID).Where("QDTime > ?", todaybegan).Where("QDTime < ?", todayend).All()

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"data": data,
			"code": 1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 签到 签到
func (c *TaskController) TaskEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	id := r.Get("ID").String()
	md := dao.Task.Ctx(r.Context())

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	if id == "0" || id == "" {
		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := gtime.Now().Time.In(loc)
		Guid := utils.GetGuid()

		mform := r.GetFormMap(map[string]interface{}{"Guid": Guid, "TeacherID": UID, "StudentID": "", "QDUID": UID, "QDTime": mytime, "QDimg": "", "Content": "", "Tstate": 0, "PJstate": "未完成"})
		result, err := md.Data(mform).Insert()
		fmt.Println("tt", err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "签到成功",
				"data": result,
			})
		}

	} else {
		mform := r.GetFormMap(map[string]interface{}{"QDUID": UID, "QDTime": "", "QDimg": "", "Content": "", "Tstate": 0, "PJstate": "未完成"})
		result, err := md.Where("ID", id).Update(mform)
		fmt.Println(id, err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "修改签到成功，未完成状态",
				"data": result,
			})
		}
	}

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *TaskController) JhEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	guid := r.GetForm("Guid").String()
	md := dao.Task.Ctx(r.Context())
	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)
	mform := r.GetFormMap(map[string]interface{}{"JHUID": UID, "JHTime": mytime, "JHimg": "", "JHContent": ""})
	result, err := md.Where("Guid", guid).Update(mform)
	if err == nil {
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "修改作业计划成功",
			"data": result,
		})
	}
	return
}

func (c *TaskController) PjEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	guid := r.GetForm("Guid").String()
	md := dao.Task.Ctx(r.Context())
	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)
	mform := r.GetFormMap(map[string]interface{}{"PJUID": UID, "PJTime": mytime, "PJimg": "", "PJContent": "", "PJLeve": "", "PJstate": "未完成"})
	result, err := md.Where("Guid", guid).Update(mform)
	if err == nil {
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "评价成功",
			"data": result,
		})
	}
	return
}

// 规范路由学生列表 老师签约学生列

func (c *TaskController) StudentList(ctx context.Context, req *PostReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)

	Type := r.Get("Type").String() //FID family'家长， TID 老师

	if Type == "" {
		g.RequestFromCtx(ctx).Response.WriteJson(
			g.Map{
				"msg":  "Type参数错误",
				"code": -1,
			})
		return
	}

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid

	mdteacher := dao.TaskTeacher.Ctx(r.Context()).Where("UID", UID)
	teacherdata, err := mdteacher.One()
	md := dao.TaskStudent.Ctx(r.Context())
	if Type == "TID" {

		md = md.Where(Type, teacherdata["ID"]).WhereOr("ID", 1) //第一个用户为测试学生
	} else {
		md = md.Where(Type, UID)
	}

	ncount, err := md.Count()
	data, _ := md.FieldsEx("Content").Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"page":   page,
			"psize":  psize,
			"data":   data,
			"code":   1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 规范路由

// 规范路由
func (c *TaskController) GetStudent(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	id := r.Get("ID").String()

	md := dao.TaskStudent.Ctx(r.Context())
	data, err := md.Where("ID", id).One()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)
	Com := strings.Split(data["Com"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"data": data,
			"code": 1,
			"Com":  Com,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *TaskController) StudentEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	id := r.Get("ID").String()
	md := dao.TaskStudent.Ctx(r.Context())

	// Name: 李锋
	// Tel: 13378719293
	// addre: 详细
	//title: 昆明火车站[地铁站]
	//address: 地铁1号线,地铁2号线二期
	//location: [object Object]
	// addname: undefined
	// id: undefined
	// School: 丰源小学
	// Class: 31

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid

	if id == "0" || id == "" {
		SGuid := utils.GetGuid()
		mform := r.GetFormMap(map[string]interface{}{"SGuid": SGuid, "FID": UID, "Name": "", "Tel": "", "School": "", "Class": "", "Content": "", "ADDress": "", "Addlocation": "", "ADDname": "", "ADDtitle": "", "Adddetail": "", "Addlat": "", "Addlon": "", "BertheDay": "", "ORemarks": "", "Com": ""})
		result, err := md.Data(mform).Insert()
		fmt.Println("tt", err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 1,
				"msg":  "添加成功",
				"data": result,
			})
		}

	} else {
		mform := r.GetFormMap(map[string]interface{}{"Name": "", "Tel": "", "School": "", "Class": "", "Content": "", "ADDress": "", "Addlocation": "", "ADDname": "", "ADDtitle": "", "Adddetail": "", "Addlat": "", "Addlon": "", "BertheDay": "", "ORemarks": "", "Com": ""})
		result, err := md.Where("ID", id).Update(mform)
		fmt.Println(id, err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 1,
				"msg":  "修改成功",
				"data": result,
			})
		}
	}

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *TaskController) GetTeacherByGuid(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	Guid := r.Get("Guid").String()
	md := dao.TaskTeacher.Ctx(r.Context())
	data, err := md.Where("Guid", Guid).One()

	IDpics := strings.Split(data["IDpics"].String(), ",")
	Photo := strings.Split(data["Photo"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"data":   data,
			"code":   1,
			"IDpics": IDpics,
			"Photo":  Photo,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 本人获得老师信息
func (c *TaskController) GetTeacherByHead(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid

	md := dao.TaskTeacher.Ctx(r.Context()).Where("UID", UID)
	data, err := md.One()
	fmt.Println("data", data)

	IDpics := strings.Split(data["IDpics"].String(), ",")
	Photo := strings.Split(data["Photo"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"data":   data,
			"code":   1,
			"IDpics": IDpics,
			"Photo":  Photo,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *TaskController) TeacherEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid

	md := dao.TaskTeacher.Ctx(r.Context()).Where("UID", UID)

	usermd := dao.User.Ctx(r.Context())

	data, err := md.One()
	fmt.Println("data", data)

	if data == nil {

		Guid := utils.GetGuid()
		mform := r.GetFormMap(map[string]interface{}{"UID": UID, "Guid": Guid, "Name": "", "Tel": "", "Tel2": "", "School": "", "ZhuanYe": "", "JiGuan": "", "Content": "", "ADDress": "", "Addlocation": "", "ADDname": "", "ADDtitle": "", "Adddetail": "", "Addlat": "", "Addlon": "", "BertheDay": "", "IDpics": "", "Photo": ""})
		result, err := md.Data(mform).Insert()

		upUser := map[string]interface{}{"Utype": "教师"}
		usermd.Where("UID", UID).Update(upUser)

		fmt.Println("tt", err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 1,
				"msg":  "成功",
				"data": result,
			})
		}

	} else {
		mform := r.GetFormMap(map[string]interface{}{"Name": "", "Tel": "", "Tel2": "", "School": "", "ZhuanYe": "", "JiGuan": "", "Content": "", "ADDress": "", "Addlocation": "", "ADDname": "", "ADDtitle": "", "Adddetail": "", "Addlat": "", "Addlon": "", "BertheDay": "", "IDpics": "", "Photo": ""})
		result, err := md.Where("UID", UID).Update(mform)
		//fmt.Println(id, err)
		upUser := map[string]interface{}{"Utype": "教师"}
		usermd.Where("UID", UID).Update(upUser)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 1,
				"msg":  "成功",
				"data": result,
			})
		}
	}

	return

}

// 获得老师列表推荐
func (c *TaskController) GetTeacherList(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)

	md := dao.TaskTeacher.Ctx(r.Context()).Where("OType", 1).Where("OState", 0)

	data, _ := md.LeftJoinOnFields("User", "UID", "=", "UID").Fields("Task_Teacher.Name,Task_Teacher.School,Task_Teacher.ZhuanYe,User.UID,User.UTrueName,User.UHeadImg").Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"page":  page,
			"psize": psize,
			"data":  data,
			"code":  1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 获得学生列表推荐
func (c *TaskController) GetStudentList(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)

	md := dao.TaskStudent.Ctx(r.Context()).Where("OType", 1).Where("OState", 0)

	data, _ := md.LeftJoinOnFields("User", "FID", "=", "UID").Fields("Task_Student.Name,Task_Student.Com,Task_Student.School,Task_Student.Class,User.UID,User.UTrueName,User.UHeadImg").Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"page":  page,
			"psize": psize,
			"data":  data,
			"code":  1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *TaskController) GetCtById(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	id := r.Get("ID").String()
	md := dao.TaskCT.Ctx(r.Context()).Where("ID", id)

	data, _ := md.One()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)
	CTimg := strings.Split(data["CTimg"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{

			"CTimg": CTimg,
			// "page":  page,
			// "psize": psize,
			"data": data,
			"code": 1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 获得错题列表
func (c *TaskController) GetCtList(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	Tguid := r.Get("Tguid").String()
	md := dao.TaskCT.Ctx(r.Context()).Where("TaskGuid", Tguid)

	data, _ := md.All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			// "page":  page,
			// "psize": psize,
			"data": data,
			"code": 1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *TaskController) CtEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	ctid := r.Get("ctid").String()
	md := dao.TaskCT.Ctx(r.Context())

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	if ctid == "0" || ctid == "" {
		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := gtime.Now().Time.In(loc)
		Guid := utils.GetGuid()
		mform := r.GetFormMap(map[string]interface{}{"TaskGuid": "", "Guid": Guid, "CTTime": mytime, "TeacherID": UID, "StudentID": "", "TID": UID, "CTimg": "", "CTContent": "", "CTnum": "", "CtClass": ""})
		result, err := md.Data(mform).Insert()
		fmt.Println("tt", err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "添加错题成功",
				"data": result,
			})
		}

	} else {
		mform := r.GetFormMap(map[string]interface{}{"TeacherID": UID, "StudentID": "", "TID": UID, "CTimg": "", "CTContent": "", "CTnum": "", "CtClass": ""})
		result, err := md.Where("ID", ctid).Update(mform)
		fmt.Println(ctid, err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "修改错题成功",
				"data": result,
			})
		}
	}

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}
