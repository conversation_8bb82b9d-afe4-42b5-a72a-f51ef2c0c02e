// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ABSmsLogDao is the data access object for the table AB_sms_log.
type ABSmsLogDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ABSmsLogColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ABSmsLogColumns defines and stores column names for the table AB_sms_log.
type ABSmsLogColumns struct {
	Id         string // 主键ID
	Guid       string // 订单GUID
	JyId       string // 订单编号
	SmsType    string // 短信类型：1-支付成功通知客户，2-新订单通知审核员，3-修改订单通知审核员，4-审核结果通知客户，5-审核结果通知司机
	Phone      string // 接收手机号
	Content    string // 短信内容
	Status     string // 发送状态：0-失败，1-成功
	ErrorMsg   string // 失败原因
	SendTime   string // 发送时间
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
	Operator   string // 操作人
	Remark     string // 备注
	SenderUid  string // 发送人
	SenderType string //
}

// aBSmsLogColumns holds the columns for the table AB_sms_log.
var aBSmsLogColumns = ABSmsLogColumns{
	Id:         "id",
	Guid:       "guid",
	JyId:       "jy_id",
	SmsType:    "sms_type",
	Phone:      "phone",
	Content:    "content",
	Status:     "status",
	ErrorMsg:   "error_msg",
	SendTime:   "send_time",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	Operator:   "operator",
	Remark:     "remark",
	SenderUid:  "sender_uid",
	SenderType: "sender_type",
}

// NewABSmsLogDao creates and returns a new DAO object for table data access.
func NewABSmsLogDao(handlers ...gdb.ModelHandler) *ABSmsLogDao {
	return &ABSmsLogDao{
		group:    "default",
		table:    "AB_sms_log",
		columns:  aBSmsLogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ABSmsLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ABSmsLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ABSmsLogDao) Columns() ABSmsLogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ABSmsLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ABSmsLogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ABSmsLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
