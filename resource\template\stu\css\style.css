* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.5;
    color: #333;
    background-color: #f7f8fa;
}

.page {
    max-width: 750px;
    margin: 0 auto;
    padding-bottom: 50px;
}

/* 顶部导航 */
.nav-bar {
    background-color: #fff;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.nav-bar h1 {
    font-size: 16px;
    font-weight: 500;
}

/* 通知栏 */
.notice-bar {
    background-color: #fff7cc;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    margin: 8px 0;
}

.notice-bar .iconfont {
    margin-right: 8px;
}

/* 菜单网格 */
.grid-menu {
    background-color: #fff;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    padding: 16px;
    gap: 16px;
}

.grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #333;
    padding: 16px;
    border-radius: 8px;
    background-color: #f7f8fa;
}

.grid-item .iconfont {
    font-size: 24px;
    margin-bottom: 8px;
}

/* 最新资讯 */
.news-list {
    background-color: #fff;
    margin-top: 16px;
    padding-bottom: 70px;
}

.section-title {
    font-size: 14px;
    color: #666;
    padding: 16px;
    margin: 0;
    border-bottom: 1px solid #eee;
}

.news-item {
    border-bottom: 1px solid #eee;
}

.news-item a {
    display: block;
    padding: 16px;
    text-decoration: none;
    color: #333;
}

.news-item h3 {
    font-size: 14px;
    margin: 0;
    font-weight: normal;
}

.news-item .date {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
}

/* 用户信息样式 */
.user-info {
    background-color: #fff;
    padding: 20px;
    text-align: center;
    margin-bottom: 16px;
}

.user-info .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 10px;
}

.user-info .nickname {
    font-size: 18px;
    color: #333;
}

/* 菜单列表样式 */
.menu-list {
    background-color: #fff;
}

.menu-item {
    display: block;
    padding: 16px;
    text-decoration: none;
    color: #333;
    border-bottom: 1px solid #eee;
}

.menu-item:active {
    background-color: #f7f8fa;
}

/* 加载提示样式 */
.loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

/* 新闻列表样式优化 */
.news-item-content {
    display: flex;
    padding: 12px;
    gap: 12px;
}

.news-image {
    width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
}

.news-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news-abstract {
    font-size: 12px;
    color: #666;
    margin: 4px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.date {
    font-size: 12px;
    color: #999;
    margin-top: auto;
}

/* 分类导航样式 */
.category-nav {
    background: #fff;
    margin: 8px 0;
    overflow: hidden;
    z-index: 1000;
}

.category-scroll {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.category-list {
    display: flex;
    padding: 12px 16px;
    white-space: nowrap;
}

.category-item {
    display: inline-block;
    padding: 6px 16px;
    margin-right: 12px;
    border-radius: 16px;
    font-size: 14px;
    color: #666;
    text-decoration: none;
    background: #f5f5f5;
}

.category-item.active {
    color: #fff;
    background: #1989fa;
}

/* 加载提示样式 */
.loading-tip {
    text-align: center;
    padding: 16px;
    color: #999;
    background: #fff;
    bottom: 70px;
}

.loading-text {
    display: inline-block;
    vertical-align: middle;
}

.loading-text::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    vertical-align: middle;
    border: 2px solid #999;
    border-top-color: transparent;
    border-radius: 50%;
    animation: loading 0.8s linear infinite;
}

@keyframes loading {
    to {
        transform: rotate(360deg);
    }
}

/* 下拉刷新样式 */
.pull-down-refresh {
    height: 0;
    overflow: hidden;
    text-align: center;
    color: #999;
    position: relative;
    background: #f7f8fa;
}

.pull-down-refresh.loading .refresh-text::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    vertical-align: middle;
    border: 2px solid #999;
    border-top-color: transparent;
    border-radius: 50%;
    animation: loading 0.8s linear infinite;
}

.refresh-text {
    line-height: 50px;
    font-size: 14px;
}

/* 加载更多提示样式 */
.loading-tip {
    text-align: center;
    padding: 16px;
    color: #999;
    background: #fff;
    bottom: 70px;
}

/* 无更多数据提示 */
.no-more {
    text-align: center;
    padding: 16px;
    color: #999;
    font-size: 12px;
}

/* 列表加载动画 */
.news-item.skeleton {
    animation: skeleton-loading 1s infinite alternate;
}

@keyframes skeleton-loading {
    from {
        opacity: 0.6;
    }
    to {
        opacity: 1;
    }
}

/* 骨架屏样式 */
.skeleton {
    background: #f5f5f5;
    position: relative;
    overflow: hidden;
    margin-bottom: 70px;
}

.skeleton::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0) 0%, 
        rgba(255, 255, 255, 0.6) 50%, 
        rgba(255, 255, 255, 0) 100%);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.skeleton-item {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

.skeleton-content {
    display: flex;
    gap: 12px;
}

.skeleton-image {
    width: 120px;
    height: 80px;
    background: #eee;
    border-radius: 4px;
}

.skeleton-info {
    flex: 1;
}

.skeleton-title {
    height: 20px;
    background: #eee;
    margin-bottom: 8px;
    width: 80%;
    border-radius: 4px;
}

.skeleton-text {
    height: 16px;
    background: #eee;
    margin-bottom: 8px;
    width: 60%;
    border-radius: 4px;
}

/* 错误提示样式 */
.error-tip {
    text-align: center;
    padding: 20px;
    color: #ff4d4f;
    background: #fff;
    display: none;
    bottom: 70px;
}

.retry-button {
    display: inline-block;
    margin-top: 8px;
    padding: 6px 16px;
    background: #1989fa;
    color: #fff;
    border-radius: 4px;
    border: none;
    cursor: pointer;
}

.retry-button:active {
    opacity: 0.8;
}

/* 文章详情页样式 */
.nav-bar {
    position: relative;
}

.nav-back {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.icon-back {
    font-size: 20px;
    color: #333;
}

.article-container {
    background: #fff;
    padding: 16px;
    margin-top: 8px;
}

.article-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
    line-height: 1.4;
}

.article-info {
    display: flex;
    gap: 16px;
    color: #999;
    font-size: 14px;
    margin-bottom: 20px;
}

.article-content {
    line-height: 1.8;
    color: #333;
    font-size: 16px;
}

.article-content img {
    max-width: 100%;
    height: auto;
    margin: 16px 0;
}

/* 文章骨架屏 */
.article-skeleton {
    padding: 16px;
}

.skeleton-title {
    height: 24px;
    margin-bottom: 16px;
    width: 100%;
}

.skeleton-info {
    height: 16px;
    width: 60%;
    margin-bottom: 24px;
}

.skeleton-line {
    height: 16px;
    margin-bottom: 12px;
    width: 100%;
}

.skeleton-line:nth-child(2) {
    width: 94%;
}

.skeleton-line:nth-child(3) {
    width: 88%;
}

/* 文章操作栏 */
.article-actions {
    display: flex;
    justify-content: space-around;
    padding: 16px 0;
    border-top: 1px solid #eee;
    margin-top: 20px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 16px;
    border: none;
    background: none;
    color: #666;
    cursor: pointer;
}

.like-btn.liked {
    color: #ff4d4f;
}

/* 图片预览 */
.image-preview {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.preview-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.preview-content img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
}

.close-preview {
    position: absolute;
    top: -40px;
    right: 0;
    color: #fff;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
}

/* 评论区样式 */
.comments-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.comments-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
}

.comment-form {
    margin-bottom: 20px;
}

.comment-form textarea {
    width: 100%;
    height: 80px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: none;
    margin-bottom: 8px;
}

.comment-form button {
    padding: 8px 16px;
    background: #1989fa;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.comment-item {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.comment-user {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.user-name {
    font-size: 14px;
    color: #333;
}

.comment-time {
    font-size: 12px;
    color: #999;
    margin-left: auto;
}

.comment-content {
    font-size: 14px;
    color: #333;
    line-height: 1.6;
} 