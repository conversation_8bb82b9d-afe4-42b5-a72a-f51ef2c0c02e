package utils

import (
	"50go/internal/dao"
	"context"

	"github.com/gogf/gf/v2/os/gtime"
)

// 发送人类型常量
const (
	SenderTypeWxUser = 1 // 微信用户
	SenderTypeAdmin  = 2 // 后台管理员
)

// SmsLogService 短信记录服务
type SmsLogService struct{}

// SmsLogInput 短信记录输入参数
type SmsLogInput struct {
	Guid       string // 订单GUID
	JyId       string // 订单编号
	SmsType    int    // 短信类型：1-支付成功通知客户，2-新订单通知审核员，3-修改订单通知审核员，4-审核结果通知客户，5-审核结果通知司机
	Phone      string // 接收手机号
	Content    string // 短信内容
	Status     int    // 发送状态：0-失败，1-成功
	ErrorMsg   string // 失败原因
	Operator   string // 操作人
	Remark     string // 备注
	SenderUid  string // 发送人UID
	SenderType int    // 发送人类型：1-微信用户，2-后台管理员
}

// LogSms 记录短信发送日志
func (s *SmsLogService) LogSms(ctx context.Context, input SmsLogInput) error {
	now := gtime.Now()

	// 构建短信记录数据
	smsLog := map[string]interface{}{
		"guid":        input.Guid,
		"jy_id":       input.JyId,
		"sms_type":    input.SmsType,
		"phone":       input.Phone,
		"content":     input.Content,
		"status":      input.Status,
		"error_msg":   input.ErrorMsg,
		"send_time":   now,
		"create_time": now,
		"update_time": now,
		"operator":    input.Operator,
		"remark":      input.Remark,
		"sender_uid":  input.SenderUid,
		"sender_type": input.SenderType,
	}

	// 插入到数据库
	_, err := dao.ABSmsLog.Ctx(ctx).Data(smsLog).Insert()
	return err
}

// LogSmsSuccess 记录短信发送成功日志
func (s *SmsLogService) LogSmsSuccess(ctx context.Context, input SmsLogInput) error {
	input.Status = 1 // 成功
	return s.LogSms(ctx, input)
}

// LogSmsFailure 记录短信发送失败日志
func (s *SmsLogService) LogSmsFailure(ctx context.Context, input SmsLogInput, errorMsg string) error {
	input.Status = 0 // 失败
	input.ErrorMsg = errorMsg
	return s.LogSms(ctx, input)
}

// GetSmsLogsByOrder 根据订单获取短信记录
func (s *SmsLogService) GetSmsLogsByOrder(ctx context.Context, guid string) ([]map[string]interface{}, error) {
	data, err := dao.ABSmsLog.Ctx(ctx).
		Where("guid", guid).
		OrderDesc("create_time").
		All()

	if err != nil {
		return nil, err
	}

	return data.List(), nil
}

// GetSmsLogsByType 根据短信类型获取短信记录
func (s *SmsLogService) GetSmsLogsByType(ctx context.Context, smsType int, page, pageSize int) ([]map[string]interface{}, int, error) {
	md := dao.ABSmsLog.Ctx(ctx).Where("sms_type", smsType)

	total, err := md.Count()
	if err != nil {
		return nil, 0, err
	}

	data, err := md.OrderDesc("create_time").Page(page, pageSize).All()
	if err != nil {
		return nil, 0, err
	}

	return data.List(), total, nil
}

// GetSmsLogsBySender 根据发送人获取短信记录
func (s *SmsLogService) GetSmsLogsBySender(ctx context.Context, senderUid string, senderType int, page, pageSize int) ([]map[string]interface{}, int, error) {
	md := dao.ABSmsLog.Ctx(ctx).
		Where("sender_uid", senderUid).
		Where("sender_type", senderType)

	total, err := md.Count()
	if err != nil {
		return nil, 0, err
	}

	data, err := md.OrderDesc("create_time").Page(page, pageSize).All()
	if err != nil {
		return nil, 0, err
	}

	return data.List(), total, nil
}

// GetSmsStats 获取短信发送统计
func (s *SmsLogService) GetSmsStats(ctx context.Context) (map[string]interface{}, error) {
	// 总发送数量
	totalCount, err := dao.ABSmsLog.Ctx(ctx).Count()
	if err != nil {
		return nil, err
	}

	// 成功数量
	successCount, err := dao.ABSmsLog.Ctx(ctx).Where("status", 1).Count()
	if err != nil {
		return nil, err
	}

	// 失败数量
	failureCount, err := dao.ABSmsLog.Ctx(ctx).Where("status", 0).Count()
	if err != nil {
		return nil, err
	}

	// 按类型统计
	typeStats := make(map[int]int)
	for i := 1; i <= 5; i++ {
		count, err := dao.ABSmsLog.Ctx(ctx).Where("sms_type", i).Count()
		if err != nil {
			continue
		}
		typeStats[i] = count
	}

	// 按发送人类型统计
	senderTypeStats := make(map[int]int)
	for i := 1; i <= 2; i++ {
		count, err := dao.ABSmsLog.Ctx(ctx).Where("sender_type", i).Count()
		if err != nil {
			continue
		}
		senderTypeStats[i] = count
	}

	return map[string]interface{}{
		"total_count":       totalCount,
		"success_count":     successCount,
		"failure_count":     failureCount,
		"type_stats":        typeStats,
		"sender_type_stats": senderTypeStats,
	}, nil
}
