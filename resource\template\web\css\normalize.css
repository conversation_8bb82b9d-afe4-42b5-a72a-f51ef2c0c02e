/*!
 * Author: ch233
 * Created: 2020-05-03
 * Last update: 2024-05-13
 * Version: 1.1
 * Normalize CSS default properties
 */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a:link,
a:visited,
a:hover {
  text-decoration: none;
}

html {
  margin: 0;
  padding: 0;
  font-size: 12px;
}

body {
  margin: 0;
  padding: 0;
  font-family: Helvetica, Arial, sans-serif;
  color: #161823;
  line-height: 1;
}

header,
footer,
main,
section,
div,
p,
span,
i,
a,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
}

ul,
ol,
li,
dl,
dd {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

img {
  margin: 0;
  padding: 0;
  border: 0;
}

span,
i,
b {
  font-style: normal;
}

input,
textarea {
  margin: 0;
  padding: 0;
  outline: 0;
}

textarea {
  resize: none;
}