// 定义草稿保存的键名
const DRAFT_KEY = 'info_draft';
let autoSaveTimer = null;
const MAX_IMAGES = 9;
let uploadedImages = [];
let categories = [];

// 显示提示信息
function showTip(message) {
    const tipDiv = document.createElement('div');
    tipDiv.className = 'draft-tip';
    tipDiv.textContent = message;
    document.body.appendChild(tipDiv);
    tipDiv.style.display = 'block';
    
    setTimeout(() => {
        tipDiv.style.display = 'none';
        tipDiv.remove();
    }, 2000);
}

// 保存草稿
function saveDraft() {
    const draftData = {
        title: document.getElementById('title').value,
        content: document.getElementById('content').value,
        category: document.getElementById('category').value,
        timestamp: new Date().getTime()
    };
    
    localStorage.setItem(DRAFT_KEY, JSON.stringify(draftData));
    return draftData;
}

// 加载草稿
function loadDraft() {
    const draftStr = localStorage.getItem(DRAFT_KEY);
    if (!draftStr) {
        showTip('没有找到草稿');
        return null;
    }
    
    const draft = JSON.parse(draftStr);
    document.getElementById('title').value = draft.title || '';
    document.getElementById('content').value = draft.content || '';
    
    // 等待分类加载完成后再设置选中值
    if (categories.length > 0) {
        document.getElementById('category').value = draft.category || '';
    } else {
        // 如果分类还未加载完成，等待加载后再设置
        const checkInterval = setInterval(() => {
            if (categories.length > 0) {
                document.getElementById('category').value = draft.category || '';
                clearInterval(checkInterval);
            }
        }, 100);
        
        // 设置超时，避免无限等待
        setTimeout(() => clearInterval(checkInterval), 5000);
    }
    
    return draft;
}

// 自动保存功能
function startAutoSave() {
    if (autoSaveTimer) {
        clearInterval(autoSaveTimer);
    }
    
    autoSaveTimer = setInterval(() => {
        const draft = saveDraft();
        if (draft.title || draft.content) {
            showTip('草稿已自动保存');
        }
    }, 30000); // 每30秒自动保存一次
}

// 清除草稿
function clearDraft() {
    localStorage.removeItem(DRAFT_KEY);
}

// 添加图片上传相关函数
function initImageUpload() {
    const uploadButton = document.querySelector('.upload-button');
    const imageInput = document.getElementById('images');
    const previewList = document.getElementById('imagePreviewList');

    uploadButton.addEventListener('click', () => {
        if (uploadedImages.length >= MAX_IMAGES) {
            showTip('最多只能上传9张图片');
            return;
        }
        imageInput.click();
    });

    imageInput.addEventListener('change', handleImageSelect);
}

function handleImageSelect(e) {
    const files = Array.from(e.target.files);
    const remainingSlots = MAX_IMAGES - uploadedImages.length;
    
    if (files.length > remainingSlots) {
        showTip(`最多还能上传${remainingSlots}张图片`);
        files.splice(remainingSlots);
    }

    files.forEach(file => {
        if (file.type.startsWith('image/')) {
            addImagePreview(file);
        }
    });

    // 清空 input 值，允许重复选择相同文件
    e.target.value = '';
}

function addImagePreview(file) {
    const reader = new FileReader();
    const previewList = document.getElementById('imagePreviewList');
    const uploadButton = previewList.querySelector('.upload-button');
    
    reader.onload = (e) => {
        const previewItem = document.createElement('div');
        previewItem.className = 'image-upload-item preview-item';
        previewItem.style.backgroundImage = `url(${e.target.result})`;
        
        // 添加删除按钮
        const deleteButton = document.createElement('div');
        deleteButton.className = 'delete-button';
        deleteButton.innerHTML = '×';
        deleteButton.onclick = () => {
            uploadedImages = uploadedImages.filter(img => img.file !== file);
            previewItem.remove();
            updateUploadButton();
        };
        
        previewItem.appendChild(deleteButton);
        
        // 添加进度条（实际上传时使用）
        const progressBar = document.createElement('div');
        progressBar.className = 'upload-progress';
        progressBar.innerHTML = '<div class="progress-bar"></div>';
        previewItem.appendChild(progressBar);
        
        // 将新预览插入到上传按钮之前
        previewList.insertBefore(previewItem, uploadButton);
        
        // 保存文件信息
        uploadedImages.push({
            file,
            preview: previewItem
        });
        
        updateUploadButton();
    };
    
    reader.readAsDataURL(file);
}

function updateUploadButton() {
    const uploadButton = document.querySelector('.upload-button');
    uploadButton.style.display = uploadedImages.length >= MAX_IMAGES ? 'none' : 'flex';
}

// 加载分类列表
async function loadCategories() {
    try {
        const categorySelect = document.getElementById('category');
        categorySelect.innerHTML = '<option value="">加载中...</option>';
        
        categories = await fetchCategories();
        
        // 更新选择器选项
        categorySelect.innerHTML = `
            <option value="">请选择分类</option>
            ${categories.map(category => `
                <option value="${category.id}">${category.name}</option>
            `).join('')}
        `;
    } catch (error) {
        console.error('加载分类失败：', error);
        document.getElementById('category').innerHTML = `
            <option value="">加载失败，请刷新重试</option>
        `;
    }
}

// 页面加载时检查是否有草稿
document.addEventListener('DOMContentLoaded', async () => {
    await loadCategories(); // 先加载分类
    
    // 然后检查草稿
    const draft = loadDraft();
    if (draft) {
        if (confirm('检测到上次未发布的草稿，是否加载？')) {
            loadDraft();
        } else {
            clearDraft();
        }
    }
    
    startAutoSave();
    initImageUpload();
});

// 手动保存草稿
document.getElementById('saveDraft').addEventListener('click', () => {
    saveDraft();
    showTip('草稿已保存');
});

// 手动加载草稿
document.getElementById('loadDraft').addEventListener('click', () => {
    loadDraft();
    showTip('草稿已加载');
});

// 发布成功后清除草稿
document.getElementById('publishForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const categoryId = document.getElementById('category').value;
    if (!categoryId) {
        alert('请选择分类');
        return;
    }
    
    const formData = new FormData();
    formData.append('title', document.getElementById('title').value);
    formData.append('content', document.getElementById('content').value);
    formData.append('classId', categoryId); // 使用 classId 作为参数名
    
    // 添加所有图片文件
    uploadedImages.forEach((img, index) => {
        formData.append(`images[${index}]`, img.file);
    });
    
    try {
        const response = await apiService.publishInfo(formData);
        if (response.success) {
            clearDraft();
            alert('发布成功！');
            window.location.href = '/index.html';
        } else {
            alert('发布失败：' + response.message);
        }
    } catch (error) {
        console.error('发布错误：', error);
        alert('发布失败，请重试');
    }
});

// 页面关闭前提示
window.addEventListener('beforeunload', (e) => {
    const draft = JSON.parse(localStorage.getItem(DRAFT_KEY) || '{}');
    if (draft.title || draft.content) {
        e.preventDefault();
        e.returnValue = '有未保存的草稿，确定要离开吗？';
        return e.returnValue;
    }
}); 