// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalABSmsLogDao is internal type for wrapping internal DAO implements.
type internalABSmsLogDao = *internal.ABSmsLogDao

// aBSmsLogDao is the data access object for table AB_sms_log.
// You can define custom methods on it to extend its functionality as you wish.
type aBSmsLogDao struct {
	internalABSmsLogDao
}

var (
	// ABSmsLog is globally public accessible object for table AB_sms_log operations.
	ABSmsLog = aBSmsLogDao{
		internal.NewABSmsLogDao(),
	}
)

// Fill with you ideas below.
