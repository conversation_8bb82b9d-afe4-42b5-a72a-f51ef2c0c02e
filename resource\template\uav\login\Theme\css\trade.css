@charset "utf-8";
/* CSS Document */


/**********交易模块样式**********/
/**************************/
/******子栏目列表******/
.column{ width: 100%; background: #fff; overflow: hidden; padding: .2rem;}
.column li{ width: 33.3333333%; overflow: hidden; float: left; padding: .15rem .2rem;}
.column li a{ width: 100%; height: 1.3rem; line-height: 1.3rem; text-align: center; color: #999; background: #f1f1f1; border-radius: .15rem; display: block;}

.column li a.active{ color: #46a9fd;}


/******买入/卖出******/
/*------银行卡信息------*/
.bankcard-info{ width: 100%; background: #fff; border-bottom: #f1f1f1 1px solid; overflow: hidden;}
.bankcard-info .bankcard-info-title{ color: #999; border-bottom: #f1f1f1 1px solid; overflow: hidden; padding: .3rem .5rem;}

.bankcard-info .bankcard-info-content{ width: 100%; overflow: hidden; padding: .5rem;}
.bankcard-info .bankcard-info-content h3{ line-height: .8rem; font-size: .6rem; padding: 0 0 .2rem 1.3rem; position: relative;}
.bankcard-info .bankcard-info-content h3:after{ width: .8rem; height: .8rem; content: ''; background: url(../images/setting/user_icon_3.svg) no-repeat center; background-size: .8rem auto; position: absolute; left: 0; top: 0;}
.bankcard-info .bankcard-info-content p{ color:#777; text-indent: 1.3rem;}

/*------选择购买数量------*/
.choose-number{ width: 100%; background: #fff; border-bottom: #f1f1f1 1px solid; overflow: hidden;}
.choose-number .choose-number-title{ color: #999; border-bottom: #f1f1f1 1px solid; overflow: hidden; padding: .3rem .5rem;}
.choose-number .choose-number-title h3{ font-size: .52rem; float: left;}
.choose-number .choose-number-title a{ font-size: .48rem; color: #fff; background: #18c481; border-radius: .1rem; padding: .02rem .22rem; display: block; float: right;}

.choose-number .choose-number-form{ overflow: hidden; padding: .3rem .2rem;}
.choose-number .choose-number-form li{ width: 33.333333%; overflow: hidden; float: left; padding: .2rem .75rem; position: relative;}
.choose-number .choose-number-form input, .choose-number .choose-number-form label{ width: 100%; height: 1.1rem; line-height: 1.1rem;}
.choose-number .choose-number-form input{ opacity: 0; position: absolute; left: 0; top: .2rem; z-index: 2;}
.choose-number .choose-number-form label{ text-align: center; color: #46a9fd; border: #2ea1fa 1px solid; border-radius: .15rem; display: block;}

.choose-number .choose-number-form input:checked+label{ color: #fff; background: #46a9fd;}


/******买入/卖出订单******/
/*------订单列表------*/
.buy-order .trade-order-info a{ min-height: 2.4rem; padding: .3rem .5rem .3rem 3.4rem;}
.buy-order .trade-order-info .trade-order-people{ left: .5rem; top: .3rem;}
.buy-order .trade-order-info .trade-order-content h3{ padding: 0;}
.buy-order .trade-order-info .trade-order-number{ top: .5rem;}


/******买入/卖出中心******/
/*------页面主体------*/
.sell-center{ padding: 7.2rem 0 0 0;}
.sell-center .choose-number{ position: fixed; left: 0; top: 1.76rem; z-index: 999;}

/*------列表------*/
.sell-list{ width: 100%; min-height: 3.5rem; background: #fff; border-bottom: #eee 1px solid; overflow: hidden; padding: .5rem .5rem .4rem 2.8rem; position: relative;}

/*------会员信息------*/
.sell-list .sell-list-people{ width:1.8rem; height: 1.8rem; border-radius: 50%; overflow: hidden; position: absolute; left: .5rem; top: .6rem;}
.sell-list .sell-list-people img{ width: 100%; height: 100%;}

.sell-list .sell-list-info{ width:100%; overflow: hidden;}
.sell-list .sell-list-info h3{ font-size: .56rem; color: #333;}
.sell-list .sell-list-info p{ font-size: .45rem; color: #999;}

/*------信用等级------*/
.sell-list .user-level{ width: 100%; overflow: hidden; padding: .1rem 0 .22rem 0;}
.sell-list .user-level span, .sell-list .user-level i{ height: .6rem; display: block; float: left;}
.sell-list .user-level span{ line-height: .6rem; font-size: .45rem; color: #999; margin-right: .1rem;}
.sell-list .user-level i{ width:.55rem; background: url(../images/icon_heart.svg) no-repeat center; background-size: .46rem auto;}

/*------金额------*/
.sell-list .sell-list-price{ text-align: right; position: absolute; top: .5rem; right: .5rem;}
.sell-list .sell-list-price h3{ font-size: .52rem; color: #333;}
.sell-list .sell-list-price p{ font-size: .45rem; color: #999;}

.sell-list .sell-list-price span{ width: 2rem; height: .85rem; line-height: .85rem; font-size: .45rem; text-align: center; color: #fff; background: #ffb400; border-radius: .1rem; display: block; float: right; margin-top: .2rem;}



/**********转入/转出**********/
/**************************/
/******交易表单******/
/*------主体------*/
.trade-form{ padding: .5rem 0;}
.trade-form .form-group:last-child{ border-bottom:none;}

/*------记录列表数字------*/
.transfer-number{ width: 100%; color: #777; background: #fff3dc; overflow: hidden; padding: .3rem;}
.transfer-number h3{ color: #333; display: inline;}


/******交易余额******/
/*------列表------*/
.balance{ width: 100%; background: linear-gradient(90deg,#27a6fa,#8e71f5); background: -webkit-linear-gradient(left,#27a6fa,#8e71f5); overflow: hidden; padding: 1rem 0 .6rem 0;}
.balance li{ width: 50%; color: #fff; text-align: center; float: left; overflow: hidden; position: relative;}
.balance li:first-child:after{ width:1px; height: 1rem; content: ''; background: rgba(255, 255, 255, .5); position: absolute; right: 0; top: .5rem;}

.balance li span{ font-size: .56rem; display: block;}
.balance li h3{ font-size: .88rem;}

/*------数字资产------*/
.balance-logo{ width: 2.2rem; overflow: hidden; margin: 0 auto;}
.balance-logo img{ width: 100%;}

.balance-current{ width: 100%; font-size: .52rem; color: #fff; text-align: center; margin-top: .5rem;}
.balance-current h3{ display: inline;}


/******交易伙伴******/
/*------主体------*/
.trade-people{ width: 100%; background: url(../images/user_bg.png) no-repeat center top; background-size: cover; overflow: hidden; padding: 1.3rem 0 .9rem 0;}

/*------头像和名字------*/
.trade-people .trade-people-photo{ width: 2.8rem; height: 2.8rem; border: #fff .1rem solid; border-radius: 50%; overflow: hidden; margin: 0 auto;}
.trade-people .trade-people-photo img{ width: 100%;}

.trade-people .trade-people-name{ font-size: .56rem; color: #fff; text-align: center; overflow: hidden; padding: .5rem;}


/******转入******/
/*------主体------*/
.transfer{ min-height: 100%; background: url(../images/login_bg.jpg) no-repeat center; background-size: cover;}
.transfer-header{ background: none;}

.transfer-container{ height: 100%; padding: 1.8rem 0 4rem 0; position: relative;}

/*------二维码------*/
.transfer-box{ width: 80%; border-radius: .5rem; overflow: hidden; margin: 0 auto; margin-top: 1rem;}
.transfer-box .transfer-code{ width: 100%; background: #fff; overflow: hidden; padding: .6rem;}
.transfer-box .transfer-code img{ width: 100%; height: auto;}

.transfer-box-bottom{ width: 100%;}
.transfer-box-bottom img{ width: 100%;}
.transfer-box-bottom p{ width: 100%; font-size: .62rem; color: #333; text-align: center; background: #fff; display: block; padding: .2rem .5rem .8rem .5rem;}

/*------转入记录链接------*/
.transfer-save{ width: 100%; overflow: hidden; position: absolute; left: 0; bottom: .5rem;}
.transfer-save button{ width: 80%; line-height: 1.4rem; font-size: .56rem; color: #fff; text-align: center; background: rgba(255,255,255,.4); border-radius: .7rem; display: block; margin: 0 auto;}



/**********数字资产**********/
/**************************/
/******数字资产导航******/
/*------主体------*/
.digital-balance{ width: 100%; background: linear-gradient(90deg,#27a6fa,#8e71f5); background: -webkit-linear-gradient(left,#27a6fa,#8e71f5); overflow: hidden; padding: .5rem 0 0 0;}

.digital-balance-current{ width: 100%; text-align: center; color: #fff; overflow: hidden; padding: .5rem 0 1rem 0;}
.digital-balance-current span{ font-size: .56rem; display: block;}
.digital-balance-current h3{ font-size: 1.2rem;}

/*------导航菜单------*/
.digital-toolbar{ background: rgba(255,255,255,.15); overflow: hidden; padding: .1rem .5rem .3rem .5rem;}

.digital-toolbar li{ width:25%; overflow: hidden; float: left;}
.digital-toolbar li a, .digital-toolbar li i, .digital-toolbar span{ display: block;}
.digital-toolbar li i{ width: 1.8rem; height: 1.8rem; background-repeat: no-repeat; background-position: center; background-size: 1.3rem auto; margin: 0 auto;}
.digital-toolbar li span{ font-size: .56rem; color: #fff; text-align: center;}

.digital-toolbar li .digital-toolbar-1 i{ background-image: url(../images/assets/assets_icon_1.svg);}
.digital-toolbar li .digital-toolbar-2 i{ background-image: url(../images/assets/assets_icon_2.svg);}
.digital-toolbar li .digital-toolbar-3 i{ background-image: url(../images/assets/assets_icon_3.svg);}
.digital-toolbar li .digital-toolbar-4 i{ background-image: url(../images/assets/assets_icon_4.svg);}

/*------充值按钮------*/
.digital-balance-charge{ width: 100%; padding: 0 0 .8rem 0;}
.digital-balance-charge a{ width: 7rem; line-height: 1.2rem; font-size: .56rem; color: #fff; text-align: center; border: rgba(255,255,255,.3) 1px solid; border-radius: .6rem; display: block; margin: 0 auto;}
.digital-balance-charge span, .digital-balance-charge i{ height: 1.2rem; display: inline-block; vertical-align: top;}
.digital-balance-charge i{ width: .8rem;}


/******资产地址******/
/*------资产地址------*/
.digital-address{ width:auto; background: #fff; border-radius: .15rem; overflow: hidden; padding: .4rem .5rem; margin: .4rem;}
.digital-address p{ font-size: .52rem; color: #999; padding: 0 0 .4rem 0;}
.digital-address b{ font-size: .6rem; color: #333; margin-left: .2rem;}

.digital-address-input{ width: 100%; border: #46a9fd 1px solid; border-radius: .15rem; overflow: hidden; position: relative; padding: 0 3.6rem 0 0;}
.digital-address-input:after{ width: 1px; height: .6rem; content: ''; background: #46a9fd; position: absolute; right: 3.6rem; top: .4rem;}

.digital-address-input input{ width: 100%; height: 1.3rem; float: left; padding: .3rem;}
.digital-address-input button{ width: 3.6rem; height: 1.3rem; color: #46a9fd; position: absolute; right: 0; top: 0;}


.digital-ads{ border-radius: .15rem; overflow: hidden; margin: 0 .4rem;}
.digital-ads a{ display: block;}
.digital-ads img{ width: 100%;}


/******数字资产列表******/
/*------主体------*/
.digital-assets{ width: 100%; overflow: hidden; padding: 0 .4rem;}

.digital-assets-list{ width:100%; background: #fff; border-radius: .2rem; overflow: hidden; padding: .5rem; margin-top: .4rem;}
.digital-assets-list h3{ line-height: 1rem; font-size: .6rem; color: #111; position: relative; padding: 0 0 0 1.2rem;}
.digital-assets-list h3:after{ width: 1rem; height: 1rem; content: ''; background-repeat: no-repeat; background-position: left center; background-size: auto 1rem; position: absolute; left: 0; top: 0;}

/*------图标------*/
.digital-coin-1:after{ background-image: url(../images/coin_logo/201803080005042_BYRb3.png);}
.digital-coin-2:after{ background-image: url(../images/coin_logo/201803161740053_skRTw.png);}
.digital-coin-3:after{ background-image: url(../images/coin_logo/201709130938034_gP76Q.png);}
.digital-coin-4:after{ background-image: url(../images/coin_logo/lite.png);}
.digital-coin-5:after{ background-image: url(../images/coin_logo/201711151113009_9GlN4.png);}
.digital-coin-6:after{ background-image: url(../images/coin_logo/b8c67160a4af01a9f861e206f82f17c1.png);}

/*------内容------*/
.digital-assets-details{ width: 100%; overflow: hidden; margin-top: .3rem;}
.digital-assets-details span{ font-size: .46rem; color: #999;}
.digital-assets-details h5{ font-size: .56rem; color: #333;}

.digital-assets-details .digital-assets-account{ width: 4rem; float: left; padding-left: .5rem;}
.digital-assets-details .digital-assets-price{ float: left;}

/*------按钮------*/
.digital-assets-details .digital-assets-btn{ float: right;}
.digital-assets-details .digital-assets-btn a{ font-size: .48rem; color: #fff; border-radius: .1rem; padding: .15rem .2rem; display: block; float: left;}
.digital-assets-details .digital-assets-btn a:first-child{ background: #46a9fd;}
.digital-assets-details .digital-assets-btn a:last-child{ background: #ffcb18; margin-left: .2rem;}



/**********现金/余额交易**********/
/**************************/
/******页头弹框******/
.header-open-arrow:after{ width: 0; height: 0; content: ''; border-left:.2rem solid transparent; border-right:.2rem solid transparent; border-top:.2rem solid #fff; display: block; position: absolute; left:48.5%; bottom: .2rem; }


/******当前价格******/
/*------主体------*/
.price-update{ width: 100%; background: #fff; overflow: hidden; padding: .4rem 0;}
.price-update li{ width: 33.3333333%; text-align: center; float: left;}

.price-update span, .price-update b, .price-update i{ height: .6rem; line-height: .6rem; font-size: .45rem; display: inline-block; vertical-align: middle;}
.price-update span{ color: #aaa;}
.price-update b{ font-weight: normal; color: #333;}

/*------小箭头------*/
.price-update i.price-up{ width:.5rem; background: url(../images/trade/arrow_up.svg) no-repeat center; background-size: .4rem auto;}
.price-update i.price-down{ width:.5rem; background: url(../images/trade/arrow_down.svg) no-repeat center; background-size: .4rem auto;}


/******交易订单******/
/*------主体------*/
.trade-order{ width: 100%; background: #fff; border-top: #eee 1px solid; overflow: hidden;}
.trade-order li{ width: 25%; float: left;}
.trade-order li a{ width: 100%; text-align: center; overflow: hidden; display: block; padding: .5rem 0;}

.trade-order li span, .trade-order li i{ display: block;}
.trade-order li span{ font-size: .52rem; color: #111; margin-top: .15rem;}
.trade-order li i{ width: 1.4rem; height: 1.4rem; background-repeat: no-repeat; background-position: center; background-size: 1.1rem auto; margin: 0 auto;}

/*------图标------*/
.trade-order li .trade-order-1 i{ background-image: url(../images/trade/trade_icon_1.svg);}
.trade-order li .trade-order-2 i{ background-image: url(../images/trade/trade_icon_2.svg);}
.trade-order li .trade-order-3 i{ background-image: url(../images/trade/trade_icon_3.svg);}
.trade-order li .trade-order-4 i{ background-image: url(../images/trade/trade_icon_4.svg); background-size: 1.2rem auto;}


/******折线图******/
/*------主体------*/
.chart { width: 100%; background: #fff; overflow: hidden;}

/*------切换标题------*/
.chart-title{ width: 100%; overflow: hidden;}
.chart-title span{ width: 33.333333%; line-height: 1.5rem; font-size: .56rem; text-align: center; color: #999; display: block; float: left; position: relative;}
.chart-title span.active{ color: #fff; color: #46a9fd;}
.chart-title span.active:after{ width: 60%; height: .1rem; content: ''; background: #46a9fd; border-radius: .05rem; position: absolute; left: 20%; bottom: 0;}

/*------切换内容------*/
.chart-content{ width: 100%; height: 11rem; overflow: hidden; padding: .5rem;}
.chart-content .line-chart{ width: 100%; height: 100%; display: none;}
.chart-content .line-chart>div{ width:100%; height: 10rem;}


/******交易列表******/
/*------主体------*/
.trade-list{ width: 100%; overflow: hidden;}

/*------标题------*/
.trade-list-title{ width: 100%; overflow: hidden; position:relative;}
.trade-list-title ul{ width: auto; height: 1.1rem; border: #ff5a5a 1px solid; border-radius: .55rem; overflow: hidden; position: relative; margin: .2rem 2rem .4rem 2rem;}
.trade-list-title li{ width: 50%; height: 1.1rem; line-height: 1.1rem; font-size: .5rem; color: #ff5a5a; text-align: center; float: left; z-index:2; position:relative;}
.trade-list-title li.active{ color: #fff; background: #ff5a5a;}

/*------列表------*/
.trade-tab{ width: 100%; overflow: hidden; position: relative;}
.trade-tab>div{ display: none; position: relative; padding-bottom: 1.6rem;}
.trade-tab>div:first-child{ display: block;}

.trade-list-content{ width:100%; min-height: 2.6rem; background: #fff; border-bottom: #f1f1f1 1px solid; overflow: hidden; padding: .5rem .5rem .5rem 2.6rem; position: relative;}
.trade-list-content img{ width: 1.6rem; height: 1.6rem; border-radius: 50%; position: absolute; top: .5rem; left: .5rem;}

.trade-list-item{ width: 100%;}
.trade-list-item h3{ font-size: .56rem; color: #333;}
.trade-list-item p{ font-size: .5rem; color: #999;}
.trade-list-item span{ margin: 0 .3rem;}

.trade-list-btn{ position: absolute; right: .5rem; top: .3rem;}
.trade-list-btn h3{ font-size: .6rem; color: #18c481;}
.trade-list-btn span{ width: 1.7rem; line-height: .7rem; font-size: .48rem; color: #ffcb18; text-align: center; border-radius: .4rem; border: #ffcb18 1px solid; display: block; float: right; margin-top: .1rem;}


/******发布出售/购买******/
.release-form{ width: auto; background: #fff; border-radius: .3rem; box-shadow: 0 0 .5rem #ddd; overflow: hidden; padding: .3rem; margin: .5rem;}


/******交易订单******/
/*------主体------*/
.trade-order-list{ width: 100%; background: #fff; border-bottom: #f1f1f1 1px solid; overflow: hidden;}

/*------标题：时间/状态------*/
.trade-order-title{ width: 100%; border-bottom: #eee 1px solid; overflow: hidden; padding: .3rem .5rem;}
.trade-order-title span{ font-size: .52rem; color: #999; display: block;}

.trade-order-title span.trade-order-status{ color: #46a9fd;}

/*------订单内容------*/
.trade-order-info{ width: 100%;}
.trade-order-info a{ width: 100%; min-height: 3.6rem; overflow: hidden; display: block; padding: .6rem .5rem .6rem 4rem; position: relative;}

.trade-order-info .trade-order-people{ width: 2.4rem; position: absolute; left: .5rem; top: .6rem;}
.trade-order-info .trade-order-people img{ width: 1.8rem; height: 1.8rem; border-radius: 50%; display: block; margin: 0 auto;}
.trade-order-info .trade-order-people span{ width: 100%; height: .6rem; font-size: .45rem; color: #46a9fd; text-align: center; text-overflow: ellipsis; overflow: hidden; display: block; margin-top: .2rem;}

.trade-order-info .trade-order-content{ width: 6rem;}
.trade-order-info .trade-order-content h3{ font-size: .56rem; color: #333; padding-bottom: .3rem;}
.trade-order-info .trade-order-content p{ font-size: .5rem; color: #999;}

/*------订单总价------*/
.trade-order-info .trade-order-number{ text-align: right; position: absolute; right: .5rem; top: 1rem;}
.trade-order-info .trade-order-number h1{ font-size: .68rem; color: #111;}
.trade-order-info .trade-order-number span{ font-size: .45rem; color: #aaa; display: block;}

/*------订单操作------*/
.trade-order-operate{ width: 100%; border-top: #eee 1px solid; overflow: hidden; padding: .3rem .5rem;}
.trade-order-operate li{ float: right;}
.trade-order-operate li a{ height: 1.1rem; line-height: 1.1rem; font-size: .52rem; color: #999; border: #ddd 1px solid; border-radius: .55rem; display: block; float: left; margin-left: .3rem; padding: 0 .6rem;}
.trade-order-operate li a.active{ color:#333;}


/******交易订单详情******/
/*------主体------*/
.trade-order-details{ width: 100%; background: #fff; overflow: hidden;}
.trade-order-details h3{ font-size: .6rem; color: #46a9fd; padding: .4rem .5rem;}

/*------详情------*/
.trade-order-details li{ width: 100%; font-size:.56rem; border-bottom: #f1f1f1 1px solid; overflow: hidden; padding: .4rem .5rem; display: table;}
.trade-order-details label{ width: 4rem; color: #aaa; display: table-cell;}
.trade-order-details span{ color: #333; display: table-cell;}
.trade-order-details span.order-details-status{ color: #46a9fd;}
.trade-order-details span.order-details-price{ font-size: .7rem; color: #f80;}

/*------操作栏------*/
.trade-order-toolbar{ width: 100%; background: #fff; overflow: hidden;}
.trade-order-toolbar ul{ width: 100%; table-layout: fixed; display: table;}
.trade-order-toolbar li{ display: table-cell; padding: .5rem;}
.trade-order-toolbar li span{ width: 100%; line-height: 1.2rem; text-align: center; color: #777; border: #e4e4e4 1px solid; border-radius: .2rem; display: block;}
.trade-order-toolbar li span.active{ color: #46a9fd; border: #46a9fd 1px solid;}


/******发布出售/购买******/
/*------价格滑动条------*/
.sell-price{ width: 100%; overflow:hidden; border-bottom: #f1f1f1 1px solid; padding: 0 0 .3rem 0;}
.sell-price-bar{ width: 100%; height:.8rem; padding: 0 0 0 11.5rem; position:relative;}

.sell-price .rangeslider, .sell-price .rangeslider__fill{ width: 11rem; height:.5rem; background: -webkit-linear-gradient(left,#42e3fb,#46a9fd); box-shadow: none; -webkit-box-shadow: none; border-radius: .25rem; position:absolute; left:.3rem; top:.15rem;}
.sell-price .rangeslider__fill{ background: none; left:0; top:0;}
.sell-price .rangeslider__handle{ width:.8rem; height:.8rem; box-shadow: none; -webkit-box-shadow: none; top:-.15rem;}

.sell-price .sell-price-text, .sell-price span{ line-height:.8rem; font-size:.56rem; color: #666; display: block; float:left;}
