// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalNewTableDao is internal type for wrapping internal DAO implements.
type internalNewTableDao = *internal.NewTableDao

// newTableDao is the data access object for table new_table.
// You can define custom methods on it to extend its functionality as you wish.
type newTableDao struct {
	internalNewTableDao
}

var (
	// NewTable is globally public accessible object for table new_table operations.
	NewTable = newTableDao{
		internal.NewNewTableDao(),
	}
)

// Fill with you ideas below.
