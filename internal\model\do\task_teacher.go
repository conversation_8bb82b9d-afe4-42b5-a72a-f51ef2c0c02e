// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskTeacher is the golang structure of table Task_Teacher for DAO operations like Where/Data.
type TaskTeacher struct {
	g.Meta      `orm:"table:Task_Teacher, do:true"`
	Id          interface{} //
	Uid         interface{} //
	Guid        interface{} //
	OParentId   interface{} //
	ISvipleve   interface{} //
	OState      interface{} //
	OType       interface{} //
	OCreatTime  *gtime.Time //
	Name        interface{} //
	Tel         interface{} //
	Tel2        interface{} //
	Provance    interface{} //
	City        interface{} //
	District    interface{} //
	Street      interface{} //
	ADDress     interface{} //
	BertheDay   *gtime.Time //
	Content     interface{} //
	Photo       interface{} //
	IDpics      interface{} //
	School      interface{} //
	ZhuanYe     interface{} //
	ADDlocation interface{} //
	ADDname     interface{} //
	ADDtitle    interface{} //
	ADDdetail   interface{} //
	ADDlat      interface{} //
	ADDlon      interface{} //
	JiGuan      interface{} //
}
