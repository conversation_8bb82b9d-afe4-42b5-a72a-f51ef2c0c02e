// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// LRForm is the golang structure for table L_RForm.
type LRForm struct {
	FrID     int         `json:"FrID"     orm:"FrID"     ` //
	FrName   string      `json:"FrName"   orm:"FrName"   ` //
	Fr1      string      `json:"Fr1"      orm:"Fr1"      ` //
	Fr2      string      `json:"Fr2"      orm:"Fr2"      ` //
	FrTime   *gtime.Time `json:"FrTime"   orm:"FrTime"   ` //
	FrRepaly string      `json:"FrRepaly" orm:"FrRepaly" ` //
	FrReOk   int         `json:"FrReOk"   orm:"FrReOk"   ` //
	FrReTime *gtime.Time `json:"FrReTime" orm:"FrReTime" ` //
	Fr1C     string      `json:"Fr1c"     orm:"Fr1c"     ` //
	Fr2C     string      `json:"Fr2c"     orm:"Fr2c"     ` //
	Fid      int         `json:"FID"      orm:"FID"      ` //
	Fstat    int         `json:"Fstat"    orm:"Fstat"    ` //
}
