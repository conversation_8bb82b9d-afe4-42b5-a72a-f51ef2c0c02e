package controller

import (
	"50go/internal/dao"
	mypage "50go/internal/utils"
	tree "50go/internal/utils"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

var Task = cTask{}

type cTask struct{}

func (c *cTask) TaskList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}
	md := dao.Task.Ctx(r.Context()).OrderDesc("ID")
	// title := r.Get("title").String()
	// time := r.Get("time").String()
	// ctype := r.Get("ctype").String()
	// ClassId := r.Get("ClassId").String()

	// if ctype != "IsHistory" { //正常的
	// 	md = md.WhereNot("IsHistory", "1").WhereOrNull("IsHistory")
	// }

	// if title != "" {
	// 	md = md.Where("Title like ?", "%"+title+"%")
	// }
	// if ctype != "" {
	// 	md = md.Where(ctype, "1")
	// }
	// if time != "" {
	// 	_time := strings.Split(time, " - ")
	// 	md = md.Where("Time > ?", _time[0])
	// 	md = md.Where("Time < ?", _time[1])
	// }
	// if ClassId != "" {
	// 	md = md.Where("ClassId", ClassId)

	// }

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页
		///	pagenews, _ := md.InnerJoinOnFields("User", "UID", "=", "UID").Fields("Task_Teacher.*,User.*").Page(temppage, pageSize).All()
		pageuser, _ := md.Page(temppage, pageSize).All()
		r.Response.WriteTpl("task/task.html", g.Map{
			"ncount": newsscount,
			//"search":    g.Map{"title": title, "time": time, "ClassId": ClassId, "ctype": ctype},
			"data":  pageuser,
			"page":  mypage.WrapContent(page, 3),
			"page2": mypage.PageContent(page),
		})
	}
}

// //混编一体list  原生表格渲染
func (c *cTask) UserList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}
	md := dao.User.Ctx(r.Context()).OrderDesc("UID")

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页
		///	pagenews, _ := md.InnerJoinOnFields("User", "UID", "=", "UID").Fields("Task_Teacher.*,User.*").Page(temppage, pageSize).All()
		pageuser, _ := md.Page(temppage, pageSize).All()
		r.Response.WriteTpl("task/wxuser.html", g.Map{
			"ncount": newsscount,
			//"search":    g.Map{"title": title, "time": time, "ClassId": ClassId, "ctype": ctype},
			"data":  pageuser,
			"page":  mypage.WrapContent(page, 3),
			"page2": mypage.PageContent(page),
		})
	}
}

// //混编一体list  原生表格渲染
func (c *cTask) TeacherList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}
	md := dao.TaskTeacher.Ctx(r.Context())

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页
		pagenews, _ := md.LeftJoinOnFields("User", "UID", "=", "UID").Fields("Task_Teacher.*,User.*").Page(temppage, pageSize).All()

		r.Response.WriteTpl("task/teacher.html", g.Map{
			"ncount": newsscount,
			//"search":    g.Map{"title": title, "time": time, "ClassId": ClassId, "ctype": ctype},
			"newss": pagenews,
			"page":  mypage.WrapContent(page, 3),
			"page2": mypage.PageContent(page),
		})
	}
}

// //混编一体list  原生表格渲染
func (c *cTask) StudentList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}
	md := dao.TaskStudent.Ctx(r.Context())

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页
		pagenews, _ := md.LeftJoinOnFields("Task_Teacher", "TID", "=", "ID").Fields("Task_Student.*,Task_Teacher.Name as tname,Task_Teacher.Tel as tTel,Task_Teacher.Address as tAddress,Task_Teacher.ID as TeacID").Page(temppage, pageSize).All()

		r.Response.WriteTpl("task/student.html", g.Map{
			"ncount": newsscount,
			//"search":    g.Map{"title": title, "time": time, "ClassId": ClassId, "ctype": ctype},
			"newss": pagenews,
			"page":  mypage.WrapContent(page, 3),
			"page2": mypage.PageContent(page),
		})
	}
}

// 混编一体Classlist
func (c *cTask) StdentEdit(r *ghttp.Request) {
	md := dao.TaskStudent.Ctx(r.Context())
	mdteacher := dao.TaskTeacher.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		tid := r.Get("TID").String()
		getsid := r.Get("SID").String()

		nclass, err := mdteacher.Fields("ID,Name,Tel,ADDress").All()
		var a int32 = 0

		zhdatra := nclass.List()
		var chridnum []map[string]interface{}
		if zhdatra != nil {
			for _, v := range zhdatra {
				v["id"] = int32(v["ID"].(int32)) //int 要转成 int64
				v["title"] = strconv.Itoa(int(v["id"].(int32))) + v["Name"].(string) + v["Tel"].(string) + v["ADDress"].(string)
				num, _ := strconv.ParseInt(tid, 10, 64)
				if v["ID"] == num { //当前id 不可选
					v["disabled"] = true
				}

				v["pid"] = a

				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "请选择"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		fmt.Println("jsonData==>", string(jsonData))

		if getsid == "0" || getsid == "" { //新增
			if err == nil {
				r.Response.WriteTpl("task/studentedit.html", g.Map{
					"header":   "This is header",
					"data":     g.Map{"SID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			data, err := md.Where("ID", getsid).One()
			if err == nil {
				r.Response.WriteTpl("task/studentedit.html", g.Map{
					"header":   "This is header",
					"data":     data,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		sid := r.GetForm("SID").String()
		pid := r.GetForm("demoTree_select_nodeId")
		if sid == "0" || sid == "" {

			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "失败",

				"data": "学生ID 为空",
			})
		} else {
			mform := r.GetFormMap(map[string]interface{}{"TID": pid})
			result, err := md.Where("ID", sid).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// //  列表页面修改
func (c *cTask) TaskCharge(r *ghttp.Request) {
	ID := r.Get("ID").Int()
	if ID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.Task.Ctx(r.Context()).Where("ID", ID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.Task.Ctx(r.Context()).Data(gdb.Map{"Tstate": "-1"}).Where("ID", ID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.Task.Ctx(r.Context()).Where("ID", ID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// //  列表页面修改
func (c *cTask) UCharge(r *ghttp.Request) {
	ID := r.Get("ID").Int()
	if ID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.User.Ctx(r.Context()).Where("UID", ID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.User.Ctx(r.Context()).Where("UID", ID).Delete()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.User.Ctx(r.Context()).Where("ID", ID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// //  列表页面修改
func (c *cTask) TCharge(r *ghttp.Request) {
	ID := r.Get("ID").Int()
	if ID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.TaskTeacher.Ctx(r.Context()).Where("ID", ID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.TaskTeacher.Ctx(r.Context()).Data(gdb.Map{"IsHistory": "1"}).Where("ID", ID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.TaskTeacher.Ctx(r.Context()).Where("ID", ID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// //  列表页面修改
func (c *cTask) SCharge(r *ghttp.Request) {
	ID := r.Get("ID").Int()
	if ID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.TaskStudent.Ctx(r.Context()).Where("ID", ID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.TaskStudent.Ctx(r.Context()).Data(gdb.Map{"IsHistory": "1"}).Where("ID", ID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.TaskStudent.Ctx(r.Context()).Where("ID", ID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// 混编一体Classlist
func (c *cTask) Class_list(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	md := dao.InfoClass.Ctx(r.Context())
	classs, err := md.All()
	var a int64 = 0
	var chridnum []map[string]interface{}
	zhdatra := classs.List()
	if zhdatra != nil {
		for _, v := range zhdatra {
			v["id"] = v["CID"].(int64)
			v["title"] = v["CNmae"]
			v["name"] = v["CNmae"]
			if v["CpareID"] == nil {
				v["pid"] = a
			} else {
				v["pid"] = int64(v["CpareID"].(int64))
			}
			chridnum = append(chridnum, v)
		}
	}
	GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
	treeData, err := json.Marshal(GetRuleTreeArrayLayui)

	if err == nil {
		r.Response.WriteTpl("info/class_list.html", g.Map{
			"header":   "This is header",
			"info":     classs,
			"treeData": string(treeData),
		})
	}

}

//混编一体编辑Class修改

// 混编一体Classlist
func (c *cTask) Class_edit(r *ghttp.Request) {

	md := dao.InfoClass.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("ID").String()

		nclass, err := md.Fields("CNmae,CpareID,CID").All()
		var a int64 = 0

		zhdatra := nclass.List()
		var chridnum []map[string]interface{}
		if zhdatra != nil {
			for _, v := range zhdatra {
				v["id"] = v["CID"].(int64) //int 要转成 int64
				v["title"] = v["CNmae"]
				num, _ := strconv.ParseInt(id, 10, 64)
				if v["CID"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["CpareID"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int64(v["CpareID"].(int64))
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		fmt.Println("jsonData==>", string(jsonData))

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("info/class_edit.html", g.Map{
					"header":   "This is header",
					"data":     g.Map{"CID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			data, err := md.Where("CID", id).One()
			if err == nil {
				r.Response.WriteTpl("info/class_edit.html", g.Map{
					"header":   "This is header",
					"data":     data,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("ID").String()
		pid := r.GetForm("demoTree_select_nodeId")
		if id == "0" || id == "" {
			mform := r.GetFormMap(map[string]interface{}{"CNmae": "667", "CpareID": pid})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"CNmae": "", "CpareID": pid})
			result, err := md.Where("CID", id).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// 混编一体列表
func (c *cTask) List(r *ghttp.Request) {
	goods, _ := dao.Info.Ctx(r.Context()).All()

	r.Response.WriteTpl("info/list.html", g.Map{
		"header": "This is header",
		"data":   goods,
	})

}

// 混编一体商品修改
func (c *cTask) Edit(r *ghttp.Request) {
	md := dao.Info.Ctx(r.Context())
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("ID").String()

		myclass, err := dao.InfoClass.Ctx(r.Context()).All()
		classlist := myclass.List()
		var a int32 = 0
		var chridnum []map[string]interface{}
		if classlist != nil {
			for _, v := range classlist {
				v["id"] = v["CID"].(int32) //int 要转成 int64
				v["title"] = v["CNmae"]

				if v["id"] == id { //当前id 不可选
					v["disabled"] = true
				}
				if v["CpareID"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = v["CpareID"].(int)
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("info/edit.html", g.Map{
					"header":   "This is header",
					"data":     g.Map{"GID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			data, err := md.Where("ID", id).One()

			pics := strings.Split(data["piclist"].String(), ",")
			Content := strings.ReplaceAll(data["Content"].String(), "\"", "\\\"")

			Content = strings.ReplaceAll(Content, "\n", "")
			Content = strings.ReplaceAll(Content, "\r", "")

			if err == nil {
				r.Response.WriteTpl("info/edit.html", g.Map{
					"header":   "This is header",
					"data":     data,
					"jsonData": string(jsonData),
					"Content":  Content,
					"pics":     pics,
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("ID").String()
		pid := r.Get("demoTree_select_nodeId")

		fpiclist := r.GetForm("site_pic__upimgs")
		fIsLock := r.GetForm("BBB")
		fIsTop := r.GetForm("CCC")

		mform := r.GetFormMap(map[string]interface{}{"Title": "为空时候默认值", "Uname": "", "Content": "", "Time": "", "ClassId": pid, "piclist": fpiclist, "IsLock": fIsLock, "IsTop": fIsTop})

		if id == "0" || id == "" {

			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {

			result, err := md.Where("ID", id).Update(mform)

			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}
