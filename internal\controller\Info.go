package controller

import (
	"50go/internal/dao"
	tree "50go/internal/utils"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

var Info = cInfo{}

type cInfo struct{}

// 混编一体Classlist
func (c *cInfo) Class_list(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	md := dao.InfoClass.Ctx(r.Context())
	classs, err := md.All()
	var a int32 = 0
	var chridnum []map[string]interface{}
	zhdatra := classs.List()
	if zhdatra != nil {
		for _, v := range zhdatra {
			v["id"] = v["CID"].(int32)
			v["title"] = v["CNmae"]
			v["name"] = v["CNmae"]
			if v["CpareID"] == nil {
				v["pid"] = a
			} else {
				v["pid"] = int32(v["CpareID"].(int))
			}
			chridnum = append(chridnum, v)
		}
	}
	GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
	treeData, err := json.Marshal(GetRuleTreeArrayLayui)

	if err == nil {
		r.Response.WriteTpl("info/class_list.html", g.Map{
			"header":   "This is header",
			"info":     classs,
			"treeData": string(treeData),
		})
	}

}

//混编一体编辑Class修改

// 混编一体Classlist
func (c *cInfo) Class_edit(r *ghttp.Request) {

	md := dao.InfoClass.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("ID").String()

		nclass, err := md.Fields("CNmae,CpareID,CID").All()
		var a int32 = 0

		zhdatra := nclass.List()
		var chridnum []map[string]interface{}
		if zhdatra != nil {
			for _, v := range zhdatra {
				v["id"] = v["CID"].(int32) //int 要转成 int64
				v["title"] = v["CNmae"]
				num, _ := strconv.ParseInt(id, 10, 64)
				if v["CID"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["CpareID"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int32(v["CpareID"].(int))
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		fmt.Println("jsonData==>", string(jsonData))

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("info/class_edit.html", g.Map{
					"header":   "This is header",
					"data":     g.Map{"CID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			data, err := md.Where("CID", id).One()
			if err == nil {
				r.Response.WriteTpl("info/class_edit.html", g.Map{
					"header":   "This is header",
					"data":     data,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("ID").String()
		pid := r.GetForm("demoTree_select_nodeId")
		if id == "0" || id == "" {
			mform := r.GetFormMap(map[string]interface{}{"CNmae": "667", "CpareID": pid})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"CNmae": "", "CpareID": pid})
			result, err := md.Where("CID", id).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// 混编一体列表
func (c *cInfo) List(r *ghttp.Request) {
	goods, _ := dao.Info.Ctx(r.Context()).All()

	r.Response.WriteTpl("info/list.html", g.Map{
		"header": "This is header",
		"data":   goods,
	})

}

// 混编一体商品修改
func (c *cInfo) Edit(r *ghttp.Request) {
	md := dao.Info.Ctx(r.Context())
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("ID").String()

		myclass, err := dao.InfoClass.Ctx(r.Context()).All()
		classlist := myclass.List()
		var a int64 = 0
		var chridnum []map[string]interface{}
		if classlist != nil {
			for _, v := range classlist {
				v["id"] = v["CID"].(int64) //int 要转成 int64
				v["title"] = v["CNmae"]
				num, _ := strconv.ParseInt(id, 10, 64)
				if v["id"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["CpareID"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = v["CpareID"].(int64)
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("info/edit.html", g.Map{
					"header":   "This is header",
					"data":     g.Map{"GID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			data, err := md.Where("ID", id).One()

			pics := strings.Split(data["piclist"].String(), ",")
			Content := strings.ReplaceAll(data["Content"].String(), "\"", "\\\"")

			Content = strings.ReplaceAll(Content, "\n", "")
			Content = strings.ReplaceAll(Content, "\r", "")

			if err == nil {
				r.Response.WriteTpl("info/edit.html", g.Map{
					"header":   "This is header",
					"data":     data,
					"jsonData": string(jsonData),
					"Content":  Content,
					"pics":     pics,
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("ID").String()
		pid := r.Get("demoTree_select_nodeId")

		fpiclist := r.GetForm("site_pic__upimgs")
		fIsLock := r.GetForm("BBB")
		fIsTop := r.GetForm("CCC")

		mform := r.GetFormMap(map[string]interface{}{"Title": "为空时候默认值", "Uname": "", "Content": "", "Time": "", "ClassId": pid, "piclist": fpiclist, "IsLock": fIsLock, "IsTop": fIsTop})

		if id == "0" || id == "" {

			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {

			result, err := md.Where("ID", id).Update(mform)

			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}
