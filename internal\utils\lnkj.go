package utils

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
)

type NewslistType struct {
	TitleNum int    `default:"25",json:"titlenum"`
	IsType   string `json:"istype"`
	Ord      string `default:"Time" ,json:"ord"`
	OrdDes   string `default:"Time" ,json:"orddes"`

	Num  int `default:"10" ,json:"num"`
	Skip int `default:"0" ,json:"skip"`

	PageSize int `default:"10" ,json:"pagesize"`
}

func GetOssUrl() string {
	v := ""
	if v == "null" {
		return ""
	}
	return v
}

func ImgsSplit(imgs string) string {
	if imgs == "" {
		return ""
	} else {
		split := strings.Split(imgs, ",")
		return split[0]
	}
}

func LnTag(name string) string {
	v := ""
	info, err := g.Model("L_Tags").Where("TName", name).One()
	if err == nil {
		v = info["TContent"].String()
	}
	return v
}

// ln 广告news list 通用
func LnAD(id string, html string, num int) string {
	_id, _ := strconv.Atoi(id)
	//_num, _ := strconv.Atoi(id)

	info, _ := g.Model("L_AD").Where("ClassId", _id).OrderDesc("Sort").Limit(0, num).All()
	// md := dao.LNewss.Ctx(r.Context())
	// classs, _ := md.All()
	zhdatra := info.List()
	myhtml := ""
	if zhdatra != nil {
		for _, v := range zhdatra {
			//	myhtml += html.ReplaceAll() + v["Title"].(string)
			temp := html
			temp = strings.ReplaceAll(temp, "#title#", v["Title"].(string))
			temp = strings.ReplaceAll(temp, "#fulltitle#", v["Title"].(string))
			temp = strings.ReplaceAll(temp, "#url#", v["Url"].(string))

			if v["ZhaiYao"] == nil {
				v["ZhaiYao"] = ""
			}
			temp = strings.ReplaceAll(temp, "#info#", v["ZhaiYao"].(string))

			if v["Img"] == nil {
				v["Img"] = ""
			}
			temp = strings.ReplaceAll(temp, "#img#", v["Img"].(string))

			myhtml += temp

		}
	}
	return myhtml

}

// ln sqlserver 通用
func LnSQL(id string, html string, num int) string {
	data, _ := g.DB("h4a").Model("a3").Where("classID", id).Limit(0, num).All()

	zhdatra := data.List()
	myhtml := ""
	if zhdatra != nil {
		for _, v := range zhdatra {
			//	myhtml += html.ReplaceAll() + v["Title"].(string)
			temp := html
			temp = strings.ReplaceAll(temp, "#title#", v["classcname"].(string))
			myhtml += temp

		}
	}
	return myhtml

}

// ln news list 通用
func LnNews(id string, html string, obj string, r *ghttp.Request) string {

	var newsType NewslistType
	_ = json.Unmarshal([]byte(obj), &newsType)
	var gmod = g.Model("L_Newss").FieldsEx("Content").WhereNot("IsHistory", "1").WhereOrNull("IsHistory")

	newsscount := 0
	if id == "list" { //分页 用于list
		cid := r.Get("class").String()
		temppage := r.Get("page").Int()
		if temppage == 0 {
			temppage = 1
		}

		newsscount, _ = gmod.Where(newsType.IsType, "1").Where("Classid", cid).Count()

		gmod = gmod.Where(newsType.IsType, "1").Where("Classid", cid).OrderDesc(newsType.Ord).Page(temppage, newsType.PageSize)

	} else { //首页不分页

		ids := strings.Split(id, "|")

		gmod = gmod.Where(newsType.IsType, "1").WhereIn("Classid", ids).OrderDesc(newsType.Ord).Limit(newsType.Skip, newsType.Num)
	}

	info, _ := gmod.All()

	zhdatra := info.List()
	myhtml := ""
	if zhdatra != nil {
		for _, v := range zhdatra {
			//	myhtml += html.ReplaceAll() + v["Title"].(string)
			temp := html

			temp = strings.ReplaceAll(temp, "#fulltitle#", v["Title"].(string))

			v["Title"] = string([]rune(v["Title"].(string))[:newsType.TitleNum])
			temp = strings.ReplaceAll(temp, "#title#", v["Title"].(string))
			temp = strings.ReplaceAll(temp, "#link#", "/read/"+InterfaceTostring(v["ClassId"].(int64))+"/"+InterfaceTostring(v["NID"].(int64)))

			if IsTime(v["Time"]) || v["Time"] == nil {
				temp = strings.ReplaceAll(temp, "#time#", "")
				temp = strings.ReplaceAll(temp, "#yy#", "")
				temp = strings.ReplaceAll(temp, "#mm#", "")
				temp = strings.ReplaceAll(temp, "#dd#", "")
			} else {

				temp = strings.ReplaceAll(temp, "#time#", (v["Time"].(*gtime.Time)).Format("Y-m-d"))
				temp = strings.ReplaceAll(temp, "#yy#", (v["Time"].(*gtime.Time)).Format("Y"))
				temp = strings.ReplaceAll(temp, "#mm#", (v["Time"].(*gtime.Time)).Format("m"))
				temp = strings.ReplaceAll(temp, "#dd#", (v["Time"].(*gtime.Time)).Format("d"))

			}

			if v["ZhaiYao"] == nil {
				v["ZhaiYao"] = ""
			}
			temp = strings.ReplaceAll(temp, "#info#", v["ZhaiYao"].(string))

			if v["Img"] == nil {
				v["Img"] = ""
			}
			temp = strings.ReplaceAll(temp, "#img#", v["Img"].(string))

			myhtml += temp

		}
		if id == "list" { //分页 用于list
			page := r.GetPage(newsscount, newsType.PageSize) //总100条，每10条一页
			myhtml += WrapContentList(page, 3)
		}

	}
	return myhtml

}

// ln news list 通用
func LnNL(id string, html string, num int) string {
	//_id, _ := strconv.Atoi(id)
	//_num, _ := strconv.Atoi(id)

	var sql = "select * from user limit 2 where classid=" + id

	info, _ := g.DB().GetAll(ctx, sql)

	//	var gmod = g.Model("L_Newss").FieldsEx("Content").WhereNot("IsHistory", "1").WhereOrNull("IsHistory")
	//info, _ := gmod.Where("Classid", _id).OrderDesc("Time").Limit(0, num).All()

	//info, _ := g.Model("L_Newss").Where("Classid", _id).WhereNot("IsHistory", "1").OrderDesc("Time").Limit(0, num).All()
	// md := dao.LNewss.Ctx(r.Context())
	// classs, _ := md.All()
	zhdatra := info.List()
	myhtml := ""
	if zhdatra != nil {
		for _, v := range zhdatra {
			//	myhtml += html.ReplaceAll() + v["Title"].(string)
			temp := html
			temp = strings.ReplaceAll(temp, "#title#", v["Title"].(string))
			temp = strings.ReplaceAll(temp, "#fulltitle#", v["Title"].(string))
			temp = strings.ReplaceAll(temp, "#link#", "/read/"+InterfaceTostring(v["ClassId"].(int64))+"/"+InterfaceTostring(v["NID"].(int64)))

			if IsTime(v["Time"]) || v["Time"] == nil {
				temp = strings.ReplaceAll(temp, "#time#", "")
				temp = strings.ReplaceAll(temp, "#yy#", "")
				temp = strings.ReplaceAll(temp, "#mm#", "")
				temp = strings.ReplaceAll(temp, "#dd#", "")
			} else {

				temp = strings.ReplaceAll(temp, "#time#", (v["Time"].(*gtime.Time)).Format("Y-m-d"))
				temp = strings.ReplaceAll(temp, "#yy#", (v["Time"].(*gtime.Time)).Format("Y"))
				temp = strings.ReplaceAll(temp, "#mm#", (v["Time"].(*gtime.Time)).Format("m"))
				temp = strings.ReplaceAll(temp, "#dd#", (v["Time"].(*gtime.Time)).Format("d"))

			}

			if v["ZhaiYao"] == nil {
				v["ZhaiYao"] = ""
			}
			temp = strings.ReplaceAll(temp, "#info#", v["ZhaiYao"].(string))

			if v["Img"] == nil {
				v["Img"] = ""
			}
			temp = strings.ReplaceAll(temp, "#img#", v["Img"].(string))

			myhtml += temp

		}
	}
	return myhtml

}

// ln news list 带分页
func LnNLP(id string, html string, pageSize int, r *ghttp.Request) string {

	_id, _ := strconv.Atoi(id)
	//_num, _ := strconv.Atoi(id)
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	fmt.Println(temppage, pageSize)

	newsscount, _ := g.Model("L_Newss").FieldsEx("Content").Where("Classid", _id).Count()
	page := r.GetPage(newsscount, pageSize) //总100条，每10条一页

	var gmod = g.Model("L_Newss").FieldsEx("Content").WhereNot("IsHistory", "1").WhereOrNull("IsHistory")
	info, _ := gmod.Where("Classid", _id).OrderDesc("Time").Page(temppage, pageSize).All()

	// md := dao.LNewss.Ctx(r.Context())
	// classs, _ := md.All()
	zhdatra := info.List()
	myhtml := ""
	if zhdatra != nil {
		for _, v := range zhdatra {
			//	myhtml += html.ReplaceAll() + v["Title"].(string)
			temp := html
			temp = strings.ReplaceAll(temp, "#title#", v["Title"].(string))
			temp = strings.ReplaceAll(temp, "#fulltitle#", v["Title"].(string))
			temp = strings.ReplaceAll(temp, "#link#", "/read/"+InterfaceTostring(v["ClassId"].(int64))+"/"+InterfaceTostring(v["NID"].(int64)))

			//fmt.Println(IsTime(v["Time"]), v["Time"])

			if IsTime(v["Time"]) || v["Time"] == nil {
				temp = strings.ReplaceAll(temp, "#time#", "")
				temp = strings.ReplaceAll(temp, "#yy#", "")
				temp = strings.ReplaceAll(temp, "#mm#", "")
				temp = strings.ReplaceAll(temp, "#dd#", "")
			} else {

				temp = strings.ReplaceAll(temp, "#time#", (v["Time"].(*gtime.Time)).Format("Y-m-d"))
				temp = strings.ReplaceAll(temp, "#yy#", (v["Time"].(*gtime.Time)).Format("Y"))
				temp = strings.ReplaceAll(temp, "#mm#", (v["Time"].(*gtime.Time)).Format("m"))
				temp = strings.ReplaceAll(temp, "#dd#", (v["Time"].(*gtime.Time)).Format("d"))

			}

			if v["ZhaiYao"] == nil {
				v["ZhaiYao"] = ""
			}
			temp = strings.ReplaceAll(temp, "#info#", v["ZhaiYao"].(string))

			if v["Img"] == nil {
				v["Img"] = ""
			}
			temp = strings.ReplaceAll(temp, "#img#", v["Img"].(string))

			myhtml += temp

		}

		myhtml += WrapContentList(page, 3)
	}
	return myhtml

}

func LnC(name string, cid string) string { //ln classs 通用
	//_num := (*ghttp.Request).Get("class")
	// request := g.RequestFromCtx(ctx)
	//_num := request.GetRouter("class")
	//_id, _ := strconv.Atoi(cid)
	v := ""
	info, err := g.Model("L_Classs").Where("CID", cid).One()
	if err == nil {
		v = info["CNmae"].String()
	}
	return v

}

// 用于测试的带参数的内置函数
func funcHello(name string) string {
	return "发布系统"
}
