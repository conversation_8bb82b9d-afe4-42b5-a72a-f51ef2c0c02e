// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LADClassDao is the data access object for the table L_ADClass.
type LADClassDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LADClassColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LADClassColumns defines and stores column names for the table L_ADClass.
type LADClassColumns struct {
	Id         string //
	Cguid      string //
	CNmae      string //
	CpareID    string //
	CKeyWord   string //
	CUrl       string //
	Csort      string //
	CisLock    string //
	CcreatTime string //
	DelTime    string //
}

// lADClassColumns holds the columns for the table L_ADClass.
var lADClassColumns = LADClassColumns{
	Id:         "ID",
	Cguid:      "CGUID",
	CNmae:      "CNmae",
	CpareID:    "CpareID",
	CKeyWord:   "CKeyWord",
	CUrl:       "CUrl",
	Csort:      "Csort",
	CisLock:    "CisLock",
	CcreatTime: "CcreatTime",
	DelTime:    "delTime",
}

// NewLADClassDao creates and returns a new DAO object for table data access.
func NewLADClassDao(handlers ...gdb.ModelHandler) *LADClassDao {
	return &LADClassDao{
		group:    "default",
		table:    "L_ADClass",
		columns:  lADClassColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LADClassDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LADClassDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LADClassDao) Columns() LADClassColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LADClassDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LADClassDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LADClassDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
