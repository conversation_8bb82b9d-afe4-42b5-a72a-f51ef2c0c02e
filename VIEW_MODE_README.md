# 订单查看模式功能

## 功能概述

新增了订单查看模式，允许用户以只读方式查看订单详情，而无需进入审核模式。这样可以复用审核页面的布局和功能，提高代码复用性。

## 主要特性

### 1. 查看按钮
- 在订单列表页面添加了"查看"按钮
- 按钮样式为绿色，与审核按钮区分
- 点击后以只读模式打开订单详情

### 2. 模式控制
- 通过URL参数 `mode=view` 控制页面模式
- 编辑模式：`/system/ab/jy-edit?guid=xxx`
- 查看模式：`/system/ab/jy-edit?guid=xxx&mode=view`

### 3. 界面适配
- **查看模式**：隐藏审核表单，显示审核结果信息
- **编辑模式**：显示完整的审核表单
- 所有表单元素在查看模式下自动禁用

## 技术实现

### 后端控制器修改
```go
// 获取模式参数
mode := r.Get("mode").String() // edit 或 view

// 传递到模板
r.Response.WriteTpl("ab/jyedit.html", g.Map{
    "data": datamap,
    "mode": mode,
})
```

### 前端模板控制
```html
<!-- 根据模式显示不同内容 -->
{{if ne .mode "view"}}
    <!-- 编辑模式：显示审核表单 -->
{{else}}
    <!-- 查看模式：显示审核信息 -->
{{end}}
```

### JavaScript控制
```javascript
// 检查是否为查看模式
var isViewMode = '{{.mode}}' === 'view';

if (isViewMode) {
    // 禁用所有表单元素
    $('input, textarea, select').prop('disabled', true);
    $('button[lay-submit]').hide();
} else {
    // 启用表单提交功能
}
```

## 界面差异

### 编辑模式
- 显示审核结果选择框
- 显示审核备注输入框
- 显示提交和重置按钮
- 所有表单元素可编辑

### 查看模式
- 显示审核结果（只读）
- 显示审核备注（只读）
- 显示审核人和审核时间
- 隐藏所有操作按钮
- 所有表单元素禁用

## 样式特点

### 查看模式样式
- 背景色略微变灰，区分编辑模式
- 禁用元素有明显的视觉反馈
- 信息标签加粗显示
- 整体界面更加友好

## 使用方法

1. **查看订单**：点击列表中的"查看"按钮
2. **审核订单**：点击列表中的"审核"按钮
3. **关闭页面**：点击右上角关闭按钮

## 优势

1. **代码复用**：复用审核页面的布局和功能
2. **用户体验**：提供清晰的查看和编辑区分
3. **权限控制**：通过参数控制，避免误操作
4. **界面一致**：保持统一的视觉风格

## 注意事项

1. 查看模式下所有表单元素都会被禁用
2. 查看模式不会触发任何数据提交
3. 两种模式使用相同的页面模板，通过参数控制
4. 建议在权限控制中区分查看和编辑权限 