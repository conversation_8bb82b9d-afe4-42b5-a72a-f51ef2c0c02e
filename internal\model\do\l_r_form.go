// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// LRForm is the golang structure of table L_RForm for DAO operations like Where/Data.
type LRForm struct {
	g.Meta   `orm:"table:L_RForm, do:true"`
	FrID     interface{} //
	FrName   interface{} //
	Fr1      interface{} //
	Fr2      interface{} //
	FrTime   *gtime.Time //
	FrRepaly interface{} //
	FrReOk   interface{} //
	FrReTime *gtime.Time //
	Fr1C     interface{} //
	Fr2C     interface{} //
	Fid      interface{} //
	Fstat    interface{} //
}
