<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
    <link rel="stylesheet" href="/admin/css/admin.css" />
    <link rel="stylesheet" href="/admin/css/admin.dark.css" />
    <link rel="stylesheet" href="/admin/css/variables.css" />
    <link rel="stylesheet" href="/admin/css/reset.css" />
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>
</head>

<body class="pear-admin">
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">
            <div class="main-container">
                <input name="nid" id="nid" type="hidden" value="{{.newss.nid}}">


                <div class="layui-form-item">
                    <label class="layui-form-label">标题:</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" lay-verify="required" value="{{.newss.title}}"
                            autocomplete="off" class="layui-input" style="max-width: 600px;">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">分类：</label>
                    <div class="layui-input-inline">
                        <select name="classid" lay-verify="required">

                            <option value="">请选择分类</option>
                            {{range $key, $value := .nclass}}
                            <option value="{{$value.cid}}" {{if eq $.newss.classid $value.cid }}selected{{end}}>
                                {{$value.cnmae}}</option>
                            {{end}}


                        </select>
                    </div>
                </div>

                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label">作者</label>
                        <div class="layui-input-inline">
                            <input type="text" name="author" id="author" class="layui-input" value="{{.newss.author}}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">来源</label>
                        <div class="layui-input-inline">
                            <input type="text" name="from" id="From" autocomplete="off" class="layui-input" value="{{.newss.from}}"  >
                        </div>
                    </div>
                </div>


                <div class="layui-form-item">
                    <div class="layui-inline">
                    <label class="layui-form-label">查看链接</label>
                    <div class="layui-input-block">
                        <input name="url" value="{{.newss.url}}" autocomplete="off" placeholder="跳转查看链接"
                            class="layui-input" type="text" style="max-width: 450px;">
                    </div>
                </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">编辑链接</label>
                        <div class="layui-input-inline">
                            <input type="text" name="nsubnid" id="nsubnid" autocomplete="off" class="layui-input" value="{{.newss.nsubnid}}"  style="max-width: 450px;"  placeholder="跳转编辑链接">
                        </div>
                    </div>
                </div>


                <div class="layui-form-item">
                    <label class="layui-form-label">封面图片：</label>

                    <!-- 单图 -->
                    <style type="text/css">
                        .layui-upload-drag {
                            position: relative;
                            padding: 10px;
                            border: 1px dashed #e2e2e2;
                            background-color: #fff;
                            text-align: center;
                            cursor: pointer;
                            color: #999;
                        }
                    </style>
                    <div class="layui-input-block">
                        <div class="layui-upload-drag"><img id="site_logo__upimage_show_id" src="{{.newss.img}}"
                                onerror="javascript:this.src='/assets/default_upload.png';" alt="上传封面" width="90"
                                height="90"><input type="hidden" id="site_logo__upimage" name="site_logo__upimage"
                                value="{{.newss.img}}">
                        </div>
                        <div style="margin-top:10px;">
                            <button type="button" class="layui-btn" id="upload_site_logo__upimage"><i
                                    class="layui-icon"></i>上传封面</button><input class="layui-upload-file" type="file"
                                accept="image/*" name="file">
                        </div>
                        <!-- <div class="layui-form-mid layui-word-aux">建议尺寸：建议上传尺寸500x450</div> -->
                    </div>
                    <script type="text/javascript">
                        layui.use(['upload'], function () {
                            //声明变量
                            var layer = layui.layer
                                , upload = layui.upload
                                // , croppers = layui.croppers
                                , $ = layui.$;



                            function darkTheme() {
                                if (localStorage.getItem('dark') === 'true') {
                                    //此处需要根据你的iframe页面结构调整
                                    $(".pear-admin").addClass("pear-admin-dark");
                                } else {
                                    //此处需要根据你的iframe页面结构调整
                                    $(".pear-admin").removeClass("pear-admin-dark");
                                }
                            }

                            // if (2 == 1) {

                            //     //图片裁剪组件
                            //     croppers.render({
                            //         elem: '#upload_site_logo__upimage'
                            //         , name: "site_logo__upimage"
                            //         , saveW: 300     //保存宽度
                            //         , saveH: 300
                            //         , mark: 1    //选取比例
                            //         , area: ['750px', '500px']  //弹窗宽度
                            //         , url: "/system/comme/upload-img"
                            //         , done: function (data) {
                            //             //上传完毕回调
                            //             $('#site_logo__upimage').val(data.data);
                            //             $('#site_logo__upimage_show_id').attr('src', data.data);
                            //         }
                            //     });

                            // } else {

                                /**
                                 * 普通图片上传
                                 */
                                var uploadInst = upload.render({
                                    elem: '#upload_site_logo__upimage'
                                    , url: "/system/comme/upload-img"
                                    , accept: 'images'
                                    , acceptMime: 'image/*'
                                    , exts: "jpg|png|gif|bmp|jpeg"
                                    , field: 'file'//文件域字段名
                                    , size: 10240 //最大允许上传的文件大小
                                    , before: function (obj) {
                                        //预读本地文件
                                    }
                                    , done: function (res) {
                                        //上传完毕回调

                                        if (res.code <= 0) {
                                            layer.msg(res.msg, { icon: 5 });
                                            return false;
                                        }

                                        //上传成功
                                        $('#site_logo__upimage_show_id').attr('src', res.data);
                                        $('#site_logo__upimage').val(res.data);
                                    }
                                    , error: function () {
                                        //请求异常回调
                                        return layer.msg('数据请求异常');
                                    }
                                });

                          
                        });

                    </script>

                </div>





                <div class="layui-form-item cover">
                    <label class="layui-form-label">属性：</label>
                    <div class="layui-form">
                        <input type="checkbox" name="istop" value="1" title="推荐" {{if eq .newss.istop 1}}
                            checked{{end}}>
                        <input type="checkbox" name="isred" value="1" lay-text="图片" {{if eq .newss.isred 1}}
                            checked{{end}}>
                        <input type="checkbox" name="islock" value="1" lay-text="锁定" checked>
                    </div>
                </div>



                <div class="layui-form-item" style="max-width:1200px;">
                    <label class="layui-form-label">内容：</label>
                    <div class="layui-input-block">
                      <textarea id="lncontent" name="lncontent" style="width:900px;height:300px;">{{.newss.content}}</textarea>
             
                      <input type="hidden" id="content" name="content" >
                    </div>     
                </div>


                <div class="layui-form-item layui-form-text" style="width:665px;">
                    <label class="layui-form-label">描述：</label>
                    <div class="layui-input-block">
                        <textarea name="zhaiyao" placeholder="请输入描述"
                            class="layui-textarea">{{.newss.zhaiyao}}</textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布时间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="time" lay-verify="required" id="time" lay-verify="datetime"
                            placeholder="yyyy-MM-dd" autocomplete="off" value="{{.newss.time}}" class="layui-input">
                    </div>
                </div>
            </div>
        </div>



        <div class="bottom">
            <div class="button-container">
                <div class="layui-btn-container layui-col-xs12">
                    <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">清空</button>
                </div>

            </div>
        </div>
    </form>



<script src="/goEdit/kindeditor.js"></script>
   
   
<script>  
            var editor;
            KindEditor.ready(function (K) {
            editor = K.create('#lncontent', {
                resizeType: 2,
                uploadJson: '/system/comme/upload-file', // 相对于当前页面的路径
                //fileManagerJson: '../yaEditor/Tools/file_manager_json.ashx',
                //allowFileManager: true,
                fillDescAfterUploadImage: true,

            });
        });

        
          //  【编辑器获取焦点】 

</script>


    <script>
        layui.use(['form', 'jquery', 'laydate'], function () {
            let form = layui.form;
            let $ = layui.jquery;
            var laydate = layui.laydate;


            laydate.render({
                elem: '#time'
                , type: 'datetime',
                fullPanel: true // 2.8+
            });

            form.verify({

                ip: [
                    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
                    , 'IP地址不符合规则'
                ]
            });

            form.on('radio(type)', function (data) {
                if (data.value == 1) {
                    $(".conn-pwd").show();
                    $(".conn-key").hide();
                } else {
                    $(".conn-key").show();
                    $(".conn-pwd").hide();
                }
            });

            form.on('submit(user-save)', function (data) {

                 //【取得编辑器HTML内容】 
     
                $("#content").val(editor.html());
                
                layer.load(2, { shade: [0.35, '#ccc'] });
                $.ajax({
                    url: '/system/news/editnews',
                    data:$("#news_edit").serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");
                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });


        })
    </script>
</body>

</html>