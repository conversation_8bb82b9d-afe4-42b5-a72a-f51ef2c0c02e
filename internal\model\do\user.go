// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// User is the golang structure of table User for DAO operations like Where/Data.
type User struct {
	g.Meta            `orm:"table:User, do:true"`
	Uid               interface{} //
	UOpenID           interface{} //
	UGuid             interface{} //
	UparentId         interface{} //
	UIndirectParentId interface{} //
	Uwx               interface{} //
	Uzfb              interface{} //
	UTel              interface{} //
	Umima             interface{} //
	Utouxiang         interface{} //
	Uname             interface{} //
	UHeadImg          interface{} //
	UNickName         interface{} //
	UTrueName         interface{} //
	IsMember          interface{} //
	Ustate            interface{} //
	Utype             interface{} //
	UstateEndTime     interface{} //
	LoginTime         *gtime.Time //
	RegTime           *gtime.Time //
	Ujifen            interface{} //
	Upower            interface{} //
	USex              interface{} //
	UProvince         interface{} //
	USession          interface{} //
	SessionKey        interface{} //
	Uwxapp            interface{} //
	Issj              interface{} //
	Ismanage          interface{} //
	Ishistory         interface{} //
	Isshr             interface{} //
}
