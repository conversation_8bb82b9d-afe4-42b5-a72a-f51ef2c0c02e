// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SJf is the golang structure for table S_JF.
type SJf struct {
	Jid        int         `json:"JID"        orm:"JID"        ` //
	JorderID   int         `json:"JorderID"   orm:"JorderID"   ` //
	JFDorderID int         `json:"JFDorderID" orm:"JFDorderID" ` //
	UGuid      string      `json:"UGuid"      orm:"UGuid"      ` //
	UfdGuid    string      `json:"UfdGuid"    orm:"UfdGuid"    ` //
	JAddTime   *gtime.Time `json:"JAddTime"   orm:"JAddTime"   ` //
	JFDTime    *gtime.Time `json:"JFDTime"    orm:"JFDTime"    ` //
	JFen       string      `json:"JFen"       orm:"JFen"       ` //
	JoldFen    string      `json:"JoldFen"    orm:"JoldFen"    ` //
	JGetWay    string      `json:"JGetWay"    orm:"JGetWay"    ` //
	JGetWayID  int         `json:"JGetWayID"  orm:"JGetWayID"  ` //
	IsTest     uint        `json:"IsTest"     orm:"IsTest"     ` //
	JState     int         `json:"JState"     orm:"JState"     ` //
	IsDel      uint        `json:"IsDel"      orm:"IsDel"      ` //
}
