package controller

import (
	"50go/internal/dao"
	"50go/internal/model/do"
	"50go/internal/model/entity"
	"50go/internal/utils"
	"encoding/json"
	"fmt"
	"time"

	captcha "50go/internal/utils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
)

var ctx = gctx.New()
var usession = g.Cfg().MustGet(ctx, "50cms.usession").String()
var Session = cSession{}

type cSession struct {
}

// 混编一体Classlist
func (c *cSession) Set(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	r.Session.Set("time", gtime.Timestamp())
	r.Response.Write("ok")

}

func (c *cSession) Get(r *ghttp.Request) {
	re_val, _ := r.Session.Get(usession)
	//return gconv.String(re_val.Map()[usernameKey])
	r.Response.Write(r.Session.Data())
	fmt.Println(re_val) //false <nil>

}

func (c *cSession) Del(r *ghttp.Request) {
	r.Session.RemoveAll()
	r.Response.WriteJson(g.Map{
		"code": 200,
		"msg":  "成功",
	})
}

// SignIn creates session for given user account.
func (c *cSession) SignIn(r *ghttp.Request) {

	codeid := r.Get("codeid").String()
	answer := r.Get("answer").String()

	coderight := captcha.VerifyCode(codeid, answer)
	if !coderight {

		r.Response.WriteJson(g.Map{
			"code": -1,
			"user": nil,
			"msg":  "验证码错误",
		})
		return
	}

	var user *entity.Admin
	MName := r.Get("uname")
	Password := r.Get("confirmPassword").String()
	hashedPassword := utils.GetMd5String(Password)
	err := dao.Admin.Ctx(r.Context()).Where(do.Admin{
		Utel:  MName,
		Umima: hashedPassword,
	}).Scan(&user)

	//url := r.Request.URL //请求地址

	userAgent := utils.GetUserAgent(r.GetCtx())

	if err == nil {

		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := gtime.New(gtime.Now().Time.In(loc))
		if user == nil {

			data := &do.LLog{
				LTiTLE: "登录失败",
				LUid:   MName,
				LAct:   userAgent,
				LUname: MName,
				LTime:  mytime,
				LIp:    utils.GetClientIp(r.GetCtx()),
			}

			md := dao.LLog.Ctx(r.Context())
			md.Data(data).Insert()
			r.Response.WriteJson(g.Map{
				"code": -1,
				"user": nil,
				"msg":  "登录失败,noUser",
			})

		} else {

			data := &do.LLog{
				LTiTLE: "登录成功",
				LUid:   MName,
				LAct:   userAgent,
				LUname: user.Uname,
				LTime:  mytime,
				LIp:    utils.GetClientIp(r.GetCtx()),
			}

			md := dao.LLog.Ctx(r.Context())
			md.Data(data).Insert()

			r.Response.WriteJson(g.Map{
				"code": 200,
				"user": user,
			})

			r.Session.Set(usession, user.Uguid)
			r.Session.Set("Passport", user.Umima)
			r.Session.Set("Nickname", user.Uname)
			r.Session.Set("power", user.Upower)
			r.Session.Set("rolesid", user.Utype)
			r.Session.Set("UCDpower", user.Ucdpower)
			fmt.Println("user.Uguid, user.Ucdpower=------------->", user.Uguid, user.Ucdpower) //false <nil>
		}
	}
}

// 混编一体部门（角色）列表
func (c *cSession) Log_list(r *ghttp.Request) {
	md := dao.LLog.Ctx(r.Context()).OrderDesc("lid")
	logdata, err := md.All()
	jsonData, err := json.Marshal(logdata)
	if err == nil {
		r.Response.WriteTpl("manage/log_list.html", g.Map{
			"jsonData": string(jsonData),
		})
	}
}

// RefreshCaptcha handles captcha refresh requests
func (c *cSession) RefreshCaptcha(r *ghttp.Request) {
	id, img, err := captcha.RefreshCaptcha(r.GetCtx())
	if err != nil {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "验证码刷新失败",
		})
		return
	}

	r.Response.WriteJson(g.Map{
		"code": 200,
		"data": g.Map{
			"id":  id,
			"img": img,
		},
	})
}
