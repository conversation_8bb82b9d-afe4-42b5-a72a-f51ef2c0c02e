// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SGoodsDao is the data access object for the table S_Goods.
type SGoodsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SGoodsColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SGoodsColumns defines and stores column names for the table S_Goods.
type SGoodsColumns struct {
	Gid         string //
	StoreID     string //
	AreaID      string //
	Scclassid   string //
	GName       string //
	Gday        string //
	GPriceYJ    string //
	GPrice      string //
	GPrice2     string //
	GPrice3     string //
	FXPrice2    string //
	FXPrice     string //
	FXPrice3    string //
	GCounts     string //
	GSoldCount  string //
	GImg        string //
	GSImg       string //
	GContent    string //
	GPost       string //
	GPostCost   string //
	GTime       string //
	GIState     string //
	GTop        string //
	GIntroduce  string //
	CodeClassID string //
	GSort       string //
	GvipLeve    string //
	GArea       string //
	GSales      string //
	GWeight     string //
	Gdw         string //
}

// sGoodsColumns holds the columns for the table S_Goods.
var sGoodsColumns = SGoodsColumns{
	Gid:         "GID",
	StoreID:     "StoreID",
	AreaID:      "AreaID",
	Scclassid:   "SCCLASSID",
	GName:       "GName",
	Gday:        "GDAY",
	GPriceYJ:    "GPriceYJ",
	GPrice:      "GPrice",
	GPrice2:     "GPrice2",
	GPrice3:     "GPrice3",
	FXPrice2:    "FXPrice2",
	FXPrice:     "FXPrice",
	FXPrice3:    "FXPrice3",
	GCounts:     "GCounts",
	GSoldCount:  "GSoldCount",
	GImg:        "GImg",
	GSImg:       "GSImg",
	GContent:    "GContent",
	GPost:       "GPost",
	GPostCost:   "GPostCost",
	GTime:       "GTime",
	GIState:     "GIState",
	GTop:        "GTop",
	GIntroduce:  "G_Introduce",
	CodeClassID: "CodeClassID",
	GSort:       "GSort",
	GvipLeve:    "GvipLeve",
	GArea:       "GArea",
	GSales:      "GSales",
	GWeight:     "GWeight",
	Gdw:         "Gdw",
}

// NewSGoodsDao creates and returns a new DAO object for table data access.
func NewSGoodsDao(handlers ...gdb.ModelHandler) *SGoodsDao {
	return &SGoodsDao{
		group:    "default",
		table:    "S_Goods",
		columns:  sGoodsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SGoodsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SGoodsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SGoodsDao) Columns() SGoodsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SGoodsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SGoodsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SGoodsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
