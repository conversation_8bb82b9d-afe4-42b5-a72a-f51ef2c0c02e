// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// LClasss is the golang structure of table L_Classs for DAO operations like Where/Data.
type LClasss struct {
	g.Meta      `orm:"table:L_Classs, do:true"`
	Cid         interface{} //
	Cnmae       interface{} //
	Cpareid     interface{} //
	Ckeyword    interface{} //
	Curl        interface{} //
	Ctag        interface{} //
	Ctag2       interface{} //
	Ctag3       interface{} //
	Ctype       interface{} //
	<PERSON><PERSON><PERSON>kin    interface{} //
	Cskin       interface{} //
	Csort       interface{} //
	CsIndexshow interface{} //
	Czhaiyao    interface{} //
	Cisfw       interface{} //
}
