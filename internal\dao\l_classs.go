// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLClasssDao is internal type for wrapping internal DAO implements.
type internalLClasssDao = *internal.LClasssDao

// lClasssDao is the data access object for table L_Classs.
// You can define custom methods on it to extend its functionality as you wish.
type lClasssDao struct {
	internalLClasssDao
}

var (
	// LClasss is globally public accessible object for table L_Classs operations.
	LClasss = lClasssDao{
		internal.NewLClasssDao(),
	}
)

// Fill with you ideas below.
