.plugin {
  margin-right: 12px;
}
.plugin-a {
  right: 2px;
}
body.slide-blur {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  overflow: hidden;
  background-color: #FF3CAC;
  background-image: linear-gradient(225deg, #FF3CAC 0%, #784BA0 50%, #2B86C5 100%);
}

.gallery3 {
  width: 100%;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}
.gallery__fake {
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.gallery__fullscreen__wrap {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.98);
  opacity: 0;
  text-align: center;
  transition: All 0.5s ease;
  -webkit-transition: All 0.5s ease;
  -moz-transition: All 0.5s ease;
  -o-transition: All 0.5s ease;
}
.gallery__fullscreen__wrap .gallery__controls {
  position: absolute !important;
}
.gallery__fullscreen__wrap.open {
  display: block;
  opacity: 1;
}
.gallery__fullscreen__bt {
  position: absolute;
  right: 15px;
  bottom: 8px;
  width: 30px;
  height: 30px;
  background-image: url('../images/Product/fullscreen_black.png');
  background-repeat: no-repeat;
  background-size: 100%;
  z-index: 99;
  cursor: pointer;
  display: none;
}
.gallery__fullscreen__exit {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-image: url('../images/Product/close_black_2x.png');
  background-repeat: no-repeat;
  background-size: 100%;
  cursor: pointer;
}
.gallery__fullscreen__img {
  max-width: 70%;
  display: inline-block;
  vertical-align: middle;
}
.gallery__fullscreen__controls {
  position: absolute;
  width: 100%;
  top: 50%;
  margin-top: -30px;
}
.gallery__fullscreen__controls .prev,
.gallery__fullscreen__controls .next {
  position: absolute;
  display: inline-block;
  width: 60px;
  height: 60px;
  cursor: pointer;
  vertical-align: middle;
  transition: All 0.3s ease;
  -webkit-transition: All 0.3s ease;
  -moz-transition: All 0.3s ease;
  -o-transition: All 0.3s ease;
  background-color: rgba(255, 255, 255, 0.8);
  background-repeat: no-repeat;
  border-radius: 0 20px 20px 0;
}
.gallery__fullscreen__controls .prev {
  left: 0;
  background-image: url('../images/Product/left_black_2x.png');
  background-size: 100%;
  border-radius: 0 20px 20px 0;
  background-position: right;
}
.gallery__fullscreen__controls .next {
  right: 0;
  background-image: url('../images/Product/right_black_2x.png');
  background-size: 100%;
  border-radius: 20px 0 0 20px;
  background-position: left;
}
.gallery__inner {
  font-size: 0;
  position: relative;
  left: 0;
  height: 100%;
  text-align: left;
}
.gallery__inner.slide-blur {
  will-change: transform;
}
.gallery__block {
  position: relative;
  overflow: hidden;
}
.gallery__block.slide-blur {
  overflow: visible;
  will-change: transform;
}
.gallery__block:hover .gallery__controls-buttons .prev {
  left: 5px;
  background-position: center;

  background-image: url('../images/Product/left_black-1.png');

}
.gallery__block:hover .gallery__controls-buttons .next {
  right: 5px;
  background-position: center;
  background-image: url('../images/Product/right_black-1.png');
}
.gallery__img-block {
  display: none;
  position: relative;
  text-align: center;
  height: 100%;
  vertical-align: middle;
}
.gallery__img-block.slide {
  display: none;
  position: relative;
  text-align: center;
  height: 100%;
  vertical-align: middle;
}
.gallery__img-block.crossfade {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
}
.gallery__img-block__img {
  max-width: 100%;
  width: 100%;
}
.gallery__img-block.load {
  background-size: 40px;
}
.gallery__description-block {
  padding: 12px 0;
  width: 100%;
  margin: 0 auto;
  position: absolute;
  bottom: 0;
  background: rgba(63, 60, 60, 0.5);
  text-align: left;
}
.gallery__description-block__description {
  font-size: 20px;
  color: #fff;
  display: none;
  margin-left: 30px;
}
.gallery__description-block__description.current {
  display: inline-block;
  font-size: 14px;
}
.gallery__img-block:first-child {
  display: inline-block;
}
.gallery__controls {
  z-index: 2;
  bottom: 0;
  width: 100%;
  text-align: center;
}
.gallery__controls.fullscreen {
  position: absolute;
  bottom: 0;
}
.gallery__controls__ul {
  display: inline-block;
  margin: 0;
  padding: 0;
  vertical-align: middle;
}
.gallery__controls__thumbnails-ul {
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  text-align: left;
  overflow: hidden;
  padding-top: 15px;
}
.gallery__controls__inner {
  position: relative;
}
.gallery__controls__inner.go-back {
  transition: All 0.3s ease;
  -webkit-transition: All 0.3s ease;
  -moz-transition: All 0.3s ease;
  -o-transition: All 0.3s ease;
}
.gallery__controls__item {
  display: inline-block;
  width: 10px;
  height: 10px;
  background: grey;
  border: 2px solid white;
  margin: 0 10px;
  border-radius: 50%;
  cursor: pointer;
}
.gallery__controls__item.current {
  background: red;
}
.gallery__controls__item:hover {
  color: #fff;
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
}
.gallery__controls-buttons {
  position: absolute;
  top: 50%;
  width: 100%;
  z-index: 999;
  
}
.gallery__controls-buttons .hide {
  opacity: 0.3;
}
.gallery__controls-buttons .prev,
.gallery__controls-buttons .next {
  position: absolute;
  display: inline-block;
  width: 60px;
  height: 40px;
  cursor: pointer;
  vertical-align: middle;
  transition: All 0.3s ease;
  -webkit-transition: All 0.3s ease;
  -moz-transition: All 0.3s ease;
  -o-transition: All 0.3s ease;
  background-repeat: no-repeat;
  border-radius: 0 20px 20px 0;
}
.gallery__controls-buttons .prev {
  left: -25px;
  background-image: url('../images/Product/left_black.png');
  background-size: 30px;
  border-radius: 0 20px 20px 0;
  background-position: right;
}
.gallery__controls-buttons .next {
  right: -25px;
  background-image: url('../images/Product/right_black.png');
  background-size: 30px;
  border-radius: 20px 0 0 20px;
  background-position: left;
}
.gallery__thumbnail {
  display: inline-block;
  vertical-align: top;
  position: relative;
  height: auto !important;
}
.gallery__thumbnail img {
  opacity: 0.5;
  height: 75px;
  width: 110px !important;
}
.gallery__thumbnail i {
  content: "";
  display: block;
  position: absolute;
  bottom: -7px;
  left: 0;
  right: 0;
  width: 0;
  height: 3px;
  background-color:  #8E7037;
}
.gallery__thumbnail.current img {
  opacity: 1;
}
.gallery__thumbnail.current i {
  background-color: #8E7037;
  width: 100%;
}
.gallery .slick-track {
  will-change: transform;
}
.gallery .slick-list {
  overflow: visible;
  will-change: transform;
}
.gallery .slick-slide {
  padding: 0 100px;
  width: 100vw;
  filter: drop-shadow(0px 10px 40px rgba(0, 0, 0, 0.55));
}
.gallery .slick-slide img {
  max-width: 100%;
  margin: 0 auto;
}
@keyframes motion-blur {
  0% {
    filter: url(#blur0);
    transform: scale(1, 1);
  }
  15% {
    filter: url(#blur1);
    transform: scale(1, 0.98);
  }
  30% {
    filter: url(#blur2);
    transform: scale(1, 0.93);
  }
  45% {
    filter: url(#blur3);
    transform: scale(1.1, 0.9);
  }
  60% {
    filter: url(#blur4);
    transform: scale(1.2, 0.88);
  }
  75%,
  100% {
    filter: url(#blur5);
    transform: scale(1.35, 0.85);
  }
}
.do-transition {
  animation: motion-blur 0.1s linear forwards, motion-blur 0.4s linear reverse forwards 0.1s;
}
