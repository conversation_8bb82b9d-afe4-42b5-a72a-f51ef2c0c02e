// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalTaskTeacherDao is internal type for wrapping internal DAO implements.
type internalTaskTeacherDao = *internal.TaskTeacherDao

// taskTeacherDao is the data access object for table Task_Teacher.
// You can define custom methods on it to extend its functionality as you wish.
type taskTeacherDao struct {
	internalTaskTeacherDao
}

var (
	// TaskTeacher is globally public accessible object for table Task_Teacher operations.
	TaskTeacher = taskTeacherDao{
		internal.NewTaskTeacherDao(),
	}
)

// Fill with you ideas below.
