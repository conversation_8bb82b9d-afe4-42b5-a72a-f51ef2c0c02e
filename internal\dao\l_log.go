// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLLogDao is internal type for wrapping internal DAO implements.
type internalLLogDao = *internal.LLogDao

// lLogDao is the data access object for table L_Log.
// You can define custom methods on it to extend its functionality as you wish.
type lLogDao struct {
	internalLLogDao
}

var (
	// LLog is globally public accessible object for table L_Log operations.
	LLog = lLogDao{
		internal.NewLLogDao(),
	}
)

// Fill with you ideas below.
