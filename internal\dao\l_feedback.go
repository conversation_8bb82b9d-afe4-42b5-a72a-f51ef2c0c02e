// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLFeedbackDao is internal type for wrapping internal DAO implements.
type internalLFeedbackDao = *internal.LFeedbackDao

// lFeedbackDao is the data access object for table L_Feedback.
// You can define custom methods on it to extend its functionality as you wish.
type lFeedbackDao struct {
	internalLFeedbackDao
}

var (
	// LFeedback is globally public accessible object for table L_Feedback operations.
	LFeedback = lFeedbackDao{
		internal.NewLFeedbackDao(),
	}
)

// Fill with you ideas below.
