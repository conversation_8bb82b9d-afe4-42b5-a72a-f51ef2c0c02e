// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// LAd is the golang structure for table L_AD.
type LAd struct {
	Id        int         `json:"ID"        orm:"ID"        ` //
	ClassId   int         `json:"ClassId"   orm:"ClassId"   ` //
	Guid      string      `json:"Guid"      orm:"Guid"      ` //
	Title     string      `json:"Title"     orm:"Title"     ` //
	From      string      `json:"From"      orm:"From"      ` //
	Url       string      `json:"Url"       orm:"Url"       ` //
	Tag       string      `json:"Tag"       orm:"Tag"       ` //
	Img       string      `json:"Img"       orm:"Img"       ` //
	Content   string      `json:"Content"   orm:"Content"   ` //
	Click     string      `json:"Click"     orm:"Click"     ` //
	Time      *gtime.Time `json:"Time"      orm:"Time"      ` //
	CreatTime *gtime.Time `json:"CreatTime" orm:"CreatTime" ` //
	Sort      int         `json:"Sort"      orm:"Sort"      ` //
	IsHot     bool        `json:"IsHot"     orm:"IsHot"     ` //
	IsSlide   bool        `json:"IsSlide"   orm:"IsSlide"   ` //
	IsLock    bool        `json:"IsLock"    orm:"IsLock"    ` //
	IsRed     bool        `json:"IsRed"     orm:"IsRed"     ` //
	DelTime   *gtime.Time `json:"delTime"   orm:"delTime"   ` //
}
