<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
    <link rel="stylesheet" href="/admin/css/admin.css" />
    <link rel="stylesheet" href="/admin/css/admin.dark.css" />
    <link rel="stylesheet" href="/admin/css/variables.css" />
    <link rel="stylesheet" href="/admin/css/reset.css" />
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>
</head>

<body class="pear-admin">
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">
            <div class="main-container">
                <input name="SID" id="SID" type="hidden" value="{{.newss.SID}}">


                <div class="layui-form-item">
                    <label class="layui-form-label">名称:</label>
                    <div class="layui-input-block">
                        <input type="text" name="Sname" lay-verify="required" value="{{.newss.Sname}}"
                            autocomplete="off" class="layui-input" style="max-width: 600px;">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">分类：</label>
                    <div class="layui-input-block" style="max-width: 300px;">

                        <select name="areaID" lay-verify="required">
                            <option value="0">请选择地区</option>

                            <option value="1" {{if eq $.newss.areaID 1 }}selected{{end}}>昆明</option>

                        </select>
                    </div>


                </div>

                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label">联系电话</label>
                        <div class="layui-input-inline">
                            <input type="text" name="Stel" class="layui-input" value="{{.newss.Stel}}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">有效时间</label>
                        <div class="layui-input-inline">

                            <input type="text" name="Stime" id="Stime" lay-verify="datetime" placeholder="yyyy-MM-dd"
                                autocomplete="off" value="{{.newss.Stime}}" class="layui-input">
                        </div>
                    </div>
                </div>



                <div class="layui-form-item">
                    <label class="layui-form-label">多图：</label>

                    <!-- 多图 -->
                    <div class="layui-input-block">
                        <style type="text/css">
                            .layui-upload-drag {
                                position: relative;
                                padding: 10px;
                                border: 1px dashed #e2e2e2;
                                background-color: #fff;
                                text-align: center;
                                cursor: pointer;
                                color: #999;
                                margin-right: 10px;
                                margin-bottom: 10px;
                            }

                            .del_img {
                                position: absolute;
                                z-index: 99;
                                right: 0;
                                top: 0;
                                width: 25px;
                                height: 25px;
                                display: block;
                            }

                            .del_img img {
                                position: absolute;
                                z-index: 9;
                                right: 0px;
                                top: 0px;
                                width: 25px;
                                height: 25px;
                                display: inline-block;
                            }
                        </style>

                        {{range $key, $value := .pics}}
                        <div class="layui-upload-drag">
                            <div class="del_img" onclick="remove_image_site_pic__upimgs(this);">
                                <img src="/assets/delete.png">
                            </div>
                            <a href="{{$value}}" target="_blank">
                                <img name="img_src_site_pic__upimgs" src="{{$value}}" alt="建议上传尺寸450x450(点击放大预览)"
                                    title="建议上传尺寸450x450(点击放大预览)" width="90" height="90">
                            </a>
                        </div>
                        {{end}}

                        <div class="layui-upload-drag img_upload_site_pic__upimgs">
                            <img id="upload_album_site_pic__upimgs" src="/assets/default_upload.png"
                                alt="上传建议上传尺寸450x450" title="上传建议上传尺寸450x450" width="90" height="90"><input
                                class="layui-upload-file" type="file" accept="image/*" name="file" multiple="">
                            <input type="hidden" id="site_pic__upimgs" name="site_pic__upimgs"
                                value="{{.newss.Simg}}">
                        </div>
                        <script type="text/javascript">

                            layui.use(['upload', 'croppers'], function () {

                                //声明变量
                                var layer = layui.layer
                                    , upload = layui.upload
                                    , croppers = layui.croppers
                                    , $ = layui.$;

                                // 初始化图片隐藏域
                                var ids = '';
                                $('img[name="img_src_site_pic__upimgs"]').each(function () {
                                    ids += $(this).attr('src') + ","
                                });
                                ids = ids.substr(0, (ids.length - 1));
                                $("#site_pic__upimgs").val(ids);

                                if (2 == 1) {
                                    // 图片裁剪组件
                                    croppers.render({
                                        elem: '#upload_album_site_pic__upimgs'
                                        , name: "site_pic__upimgs"
                                        , saveW: 300     //保存宽度
                                        , saveH: 300
                                        , mark: 1    //选取比例
                                        , area: ['750px', '500px']  //弹窗宽度
                                        , url: "/system/comme/upload-img"
                                        , done: function (data) {
                                            // 如果上传失败
                                            if (!data.data) {
                                                return layer.msg('上传失败');
                                            }

                                            var hideStr = $("#site_pic__upimgs").attr("value");
                                            var itemArr = hideStr.split(',');
                                            if (itemArr.length >= "20") {
                                                layer.msg("最多上传20张图片", { icon: 5, time: 1000 }, function () {
                                                    //TODO...
                                                });
                                                return false;
                                            }

                                            // 渲染界面
                                            var attStr = '<div class="layui-upload-drag">' +
                                                '<div class="del_img" onclick="remove_image_site_pic__upimgs(this);">' +
                                                '<img src="/assets//delete.png"></img>' +
                                                '</div>' +
                                                '<a href="' + data.data + '" target="_blank">' +
                                                '<img name="img_src_site_pic__upimgs" src="' + data.data + '" alt="建议上传尺寸450x450(点击放大预览)" title="建议上传尺寸450x450(点击放大预览)" width="90" height="90">' +
                                                '</a>' +
                                                '</div>';
                                            $(".img_upload_site_pic__upimgs").before(attStr);

                                            // 获取最新的图集
                                            var ids = '';
                                            $('img[name="img_src_site_pic__upimgs"]').each(function () {
                                                ids += $(this).attr('src') + ","
                                            });
                                            ids = ids.substr(0, (ids.length - 1));
                                            // 给隐藏域赋值
                                            $("#site_pic__upimgs").val(ids);

                                            return false;
                                        }
                                    });

                                } else {
                                    /**
                                     * 普通图片上传
                                     */
                                    var uploadInst = upload.render({
                                        elem: '#upload_album_site_pic__upimgs'
                                        , url: "/system/comme/upload-img"
                                        , accept: 'images'
                                        , acceptMime: 'image/*'
                                        , exts: "jpg|png|gif|bmp|jpeg"
                                        , field: 'file'//文件域字段名
                                        , size: 10240 //最大允许上传的文件大小
                                        , multiple: true
                                        , number: 20 //最大上传张数
                                        , before: function (obj) {
                                            //预读本地文件
                                        }
                                        , done: function (res) {
                                            //上传完毕回调

                                            var hideStr = $("#site_pic__upimgs").attr("value");
                                            var itemArr = hideStr.split(',');
                                            if (itemArr.length >= "20") {
                                                layer.msg("最多上传20张图片", { icon: 5, time: 1000 }, function () {
                                                    //TODO...
                                                });
                                                return false;
                                            }

                                            //如果上传失败
                                            if (res.status <= 0) {
                                                return layer.msg('上传失败');
                                            }

                                            //渲染界面
                                            var attStr = '<div class="layui-upload-drag">' +
                                                '<div class="del_img" onclick="remove_image_site_pic__upimgs(this);">' +
                                                '<img src="/assets/delete.png"></img>' +
                                                '</div>' +
                                                '<a href="' + res.data + '" target="_blank">' +
                                                '<img name="img_src_site_pic__upimgs" src="' + res.data + '" alt="建议上传尺寸450x450(点击放大预览)" title="建议上传尺寸450x450(点击放大预览)" width="90" height="90">' +
                                                '</a>' +
                                                '</div>';
                                            $(".img_upload_site_pic__upimgs").before(attStr);

                                            //获取最新的图集
                                            var ids = '';
                                            $('img[name="img_src_site_pic__upimgs"]').each(function () {
                                                ids += $(this).attr('src') + ","
                                            });
                                            ids = ids.substr(0, (ids.length - 1));
                                            //给隐藏域赋值
                                            $("#site_pic__upimgs").val(ids);

                                            return false;
                                        }
                                        , error: function () {
                                            //请求异常回调
                                            return layer.msg('数据请求异常');
                                        }
                                    });
                                }

                            });

                            // 删除图片
                            function remove_image_site_pic__upimgs(obj) {
                                //obj.remove();
                                layui.$(obj).parent().remove();

                                //获取最新的图集
                                var ids = '';
                                layui.$('img[name="img_src_site_pic__upimgs"]').each(function () {
                                    ids += layui.$(this).attr('src') + ","
                                });
                                ids = ids.substr(0, (ids.length - 1));
                                //给隐藏域赋值
                                layui.$("#site_pic__upimgs").val(ids);
                            }

                        </script>
                    </div>
                </div>



                

                <div class="layui-form-item" style="max-width:1200px;">
                    <label class="layui-form-label">内容：</label>
                    <div class="layui-input-block">
                      <textarea id="lnContent" name="lnContent" style="width:900px;height:300px;">{{.newss.Sintroduce}}</textarea>
             
                      <input type="hidden" id="Content" name="Sintroduce" >
                    </div>     
                </div>


                <div class="layui-form-item">

                    <div class="pear-col pear-col-md14">
                        <label class="layui-form-label">地图定位</label>
                        <div class="layui-input-form">
                            <div class="layui-input-inline">
                                <input id="where" name="where" type="text" class="layui-input " placeholder="请输入地址" />
                            </div>

                            <input id="button" type="button" value="地图查找"
                                onclick="searchAddress();" class="layui-btn" />
                            <div class="layui-input-block">

                                <h3 style="color: red;line-height: 50px;">介绍：输入地点然后点击"地图查找"搜索， 再点击地图地点获取相应经纬度</h3>

                                <div style="margin: auto; width: 100%; height: 400px; border: 2px solid gray; margin-bottom: 50px; float: left;"
                                    id="container">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pear-col pear-col-md7 pdt-20">
                     
                        <div class="layui-input-block">

                            <label class="layui-form-label">地址：</label>
                            <input name="Address" ID="Address" class="layui-input" value="{{.newss.Saddress}}" style="width: 100%;">

                     
                                <label class="layui-form-label">经度：</label>
                                <input name="Slongitude" id="lng" class="layui-input"  value="{{.newss.Slongitude}}"/>
                                <label class="layui-form-label">纬度：</label>
                                <input name="Slatitude" id="lat" class="layui-input"  value="{{.newss.Slatitude}}"/>
    
        
                        </div>

                        
                  
                    </div>
                </div>

            </div>
        </div>



        <div class="bottom">
            <div class="button-container">
                <div class="layui-btn-container layui-col-xs12">
                    <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">清空</button>
                </div>

            </div>
        </div>
    </form>

    <script src="/goEdit/kindeditor.js"></script>
   
   
    <script>  
                var editor;
                KindEditor.ready(function (K) {
                editor = K.create('#lnContent', {
                    resizeType: 2,
                    uploadJson: '/system/comme/upload-file', // 相对于当前页面的路径
                    //fileManagerJson: '../yaEditor/Tools/file_manager_json.ashx',
                    //allowFileManager: true,
                    fillDescAfterUploadImage: true,
    
                });
            });
    
            
              //  【编辑器获取焦点】 
    
    </script>

   <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: "d03cffb1fe3e0adc2124a52bf61feff7",
    };
  </script>

    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=0683ac7da924cfcf9f41f2c6220298bb&plugin=AMap.Geocoder,AMap.Scale,AMap.PlaceSearch,AMap.ToolBar,AMap.MapType"></script>

    <script>
        var map = new AMap.Map('container', {
            zoom: 13,
            center: [102.720652, 25.044309],
    
        });

        // 添加控件
        map.addControl(new AMap.Scale());
        map.addControl(new AMap.ToolBar());
        map.addControl(new AMap.MapType());

        var markers = []; // 存储所有标记点
        var geocoder = new AMap.Geocoder({
            city: "昆明", // 限制在昆明市范围内
            radius: 50000 ,// 设置搜索半径为50公里
            extensions: "all"
        });

        // 点击地图事件
        map.on('click', function(e) {
            var lnglat = e.lnglat;
            
            // 更新经纬度输入框
            document.getElementById("lng").value = lnglat.getLng();
            document.getElementById("lat").value = lnglat.getLat();

            // 清除所有已有标记
            markers.forEach(function(marker) {
                marker.setMap(null);
            });
            markers = [];

            // 获取地址信息
            geocoder.getAddress(lnglat, function(status, result) {
                if (status === 'complete' && result.info === 'OK') {
                    var address = result.regeocode.formattedAddress;
                    document.getElementById("Address").value = address;
                    
                    // 添加新标记和信息窗体
                    var marker = new AMap.Marker({
                        position: lnglat,
                        title: address
                    });
                    
                    var infoWindow = new AMap.InfoWindow({
                        content: address,
                        offset: new AMap.Pixel(0, -30)
                    });
                    
                    // 直接打开信息窗体,不需要点击
                    infoWindow.open(map, marker.getPosition());

                    marker.setMap(map);
                    markers.push(marker);
                }
            });
        });

        // 搜索地址
        var placeSearch = new AMap.PlaceSearch({
            city: '昆明',
            citylimit: true,
            pageSize: 10
        });

        function searchAddress() {
            var address = document.getElementById('where').value;
            
            placeSearch.search(address, function(status, result) {
                if (status === 'complete' && result.info === 'OK') {
                    // 清除所有标记
                    markers.forEach(function(marker) {
                        marker.setMap(null);
                    });
                    markers = [];

                    // 遍历所有搜索结果
                    result.poiList.pois.forEach(function(poi) {
                        var location = poi.location;
                        var address = poi.address;
                        var name = poi.name;

                        // 添加新标记
                        var marker = new AMap.Marker({
                            position: [location.lng, location.lat],
                            title: name
                        });

                        // 添加信息窗体
                        var infoWindow = new AMap.InfoWindow({
                            content: '<div>' + name + '</div><div>' + address + '</div>',
                            offset: new AMap.Pixel(0, -30)
                        });



                                      // 直接打开信息窗体,不需要点击
                        infoWindow.open(map, marker.getPosition());
                        // 鼠标移入显示信息窗体
                        marker.on('mouseover', function() {
                            infoWindow.open(map, marker.getPosition());
                        });

                        // 鼠标移出关闭信息窗体
                        marker.on('mouseout', function() {
                            infoWindow.close();
                        });

                        // 点击标记时更新经纬度和地址
                        marker.on('click', function() {
                            document.getElementById("lng").value = location.lng;
                            document.getElementById("lat").value = location.lat;
                            document.getElementById("Address").value = address;
                        });

                        marker.setMap(map);
                        markers.push(marker);
                    });

                    // 调整地图视野以包含所有标记
                    map.setFitView();
                }
            });
        }
    </script>

    <script>
        layui.use(['form', 'jquery', 'laydate'], function () {
            let form = layui.form;
            let $ = layui.jquery;
            var laydate = layui.laydate;
            laydate.render({
                elem: '#STime'
                , type: 'datetime'
            });
            form.verify({
                ip: [
                    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
                    , 'IP地址不符合规则'
                ]
            });

            form.on('radio(type)', function (data) {
                if (data.value == 1) {
                    $(".conn-pwd").show();
                    $(".conn-key").hide();
                } else {
                    $(".conn-key").show();
                    $(".conn-pwd").hide();
                }
            });

            form.on('submit(user-save)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                $.ajax({
                    url: '/system/ab/store_edit',
                    data: $('#news_edit').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");
                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });
        })
    </script>
</body>

</html>