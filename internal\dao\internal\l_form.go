// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LFormDao is the data access object for the table L_Form.
type LFormDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LFormColumns       // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LFormColumns defines and stores column names for the table L_Form.
type LFormColumns struct {
	Fid    string //
	FNmae  string //
	FStat  string //
	Fother string //
	F1     string //
	FMust  string //
	FTime  string //
	F1C    string //
}

// lFormColumns holds the columns for the table L_Form.
var lFormColumns = LFormColumns{
	Fid:    "FID",
	FNmae:  "FNmae",
	FStat:  "FStat",
	Fother: "Fother",
	F1:     "F1",
	FMust:  "FMust",
	FTime:  "FTime",
	F1C:    "F1C",
}

// NewLFormDao creates and returns a new DAO object for table data access.
func NewLFormDao(handlers ...gdb.ModelHandler) *LFormDao {
	return &LFormDao{
		group:    "default",
		table:    "L_Form",
		columns:  lFormColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LFormDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LFormDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LFormDao) Columns() LFormColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LFormDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LFormDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LFormDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
