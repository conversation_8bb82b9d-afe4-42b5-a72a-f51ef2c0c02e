<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />
</head>

<body>
<div style="padding: 16px;">

  <div class="layui-card">
    <div class="layui-card-body">

      <div class="" style="height: 34px;">
        <div class="layui-table-tool-self ">
          <button class="layui-btn layui-btn-sm" lay-event="getCheckData" onclick="add()" load><i class="pear-icon pear-icon-add"></i>添加</button>
         <div class="layui-inline" title="提示" lay-event="LAYTABLE_TIPS"><i class="layui-icon layui-icon-tips"></i>
          </div>
        </div>
      </div>


 
      <table class="layui-hide" id="menu" lay-filter="menu"></table>


    </div>
  </div>
  <script type="text/html" id="TPL-treeTable-demo-tools">
    <div class="layui-btn-container">
      <a class="layui-btn layui-btn layui-btn-xs" lay-event="edit">修改</a>
      <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="lock">新增</a>
       <!--<a class="layui-btn layui-btn-xs" lay-event="more">更多 <i class="layui-icon layui-icon-down"></i></a> -->
    </div>
  </script>
  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>

  <script>

 layui.use(["button","treeTable"], function () {
      var button = layui.button;
      var treeTable =layui.treetable
      button.load({
        elem: '[load]',
        time: 600,
        done: function () {
        //  popup.success("加载完成");
        }
      }) 

  var treeTable = layui.treeTable;
  var layer = layui.layer;

  // 渲染
  var inst = treeTable.render({
    elem: '#menu',
    id: 'mytreetable', // 自定义 id 索引
    //url: '/static/json/2/treeTable/demo-1.json', // 此处为静态模拟数据，实际使用时需换成真实接口
    data: {{.treeData}},  // 数据
    tree: {
      view:{
        iconLeaf:'<i class="layui-icon layui-icon-note"></i>'
      }
    },
        cols:[[{type: 'checkbox', fixed: 'left'},
        {field: 'id', title: 'ID', width: 160},
        {field: 'name', title: '名称', width: 500},
        {field: 'time_flage', title: '创建时间'},
        {fixed: "right", title: "操作", align: "center", toolbar: "#TPL-treeTable-demo-tools"} 
    ]],
  });

  treeTable.expandAll('mytreetable', true); // 展开全部节点

  treeTable.on('tool(menu)', function (obj) {   //tool里面的不是table的ID,而是table中设置的属性：lay-filter的值，如果没有此属性需要增加
    var layEvent = obj.event; // 获得 lay-event 对应的值
    var trElem = obj.tr;
    var trData = obj.data;
    var tableId = obj.config.id;

    if (layEvent === "edit") {
      layer.msg("查看操作：" + trData.id);      
      layer.open({
        title: '编辑 - id:' + trData.id,
        type: 2,
        area: ['80%', '90%'],
        content: '/system/ad/class_edit?ID=' + trData.id,
        end: function(){
          location.reload();
        }
      }) 
    } else if (layEvent === "lock") {
      var data = { id: Date.now(), name: "新节点" };
      var newNode2 = treeTable.addNodes(tableId, {
        parentIndex: trData["LAY_DATA_INDEX"], 
        index: -1, 
        data: data
      });
    }
  });

  })


    layui.use(['toast', 'jquery', 'layer'], function () {

    window.add = function(obj) {
      layer.open({
        title: '添加',
        type: 2,
        area: ['80%', '90%'],
        content: '/system/ad/class_edit?ID=0',
        end: function(){
          location.reload();
        }
      })
    }

    })

    // layui.use(['notice', 'jquery', 'layer', 'code'], function () {
    //   var notice = layui.notice;

    //   notice.success("成功消息")
    //   notice.error("危险消息")
    //   notice.warning("警告消息")
    //   notice.info("通用消息")
    
    // })

  </script>
</body>

</html>