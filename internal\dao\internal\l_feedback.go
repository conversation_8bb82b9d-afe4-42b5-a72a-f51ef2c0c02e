// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LFeedbackDao is the data access object for the table L_Feedback.
type LFeedbackDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LFeedbackColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LFeedbackColumns defines and stores column names for the table L_Feedback.
type LFeedbackColumns struct {
	Fid        string //
	Nid        string //
	FName      string //
	FTel       string //
	Fqq        string //
	FMail      string //
	FTitle     string //
	FContent   string //
	FTime      string //
	FReContent string //
	FReTime    string //
	FLock      string //
}

// lFeedbackColumns holds the columns for the table L_Feedback.
var lFeedbackColumns = LFeedbackColumns{
	Fid:        "FID",
	Nid:        "NID",
	FName:      "FName",
	FTel:       "FTel",
	Fqq:        "FQQ",
	FMail:      "FMail",
	FTitle:     "FTitle",
	FContent:   "FContent",
	FTime:      "FTime",
	FReContent: "FReContent",
	FReTime:    "FReTime",
	FLock:      "FLock",
}

// NewLFeedbackDao creates and returns a new DAO object for table data access.
func NewLFeedbackDao(handlers ...gdb.ModelHandler) *LFeedbackDao {
	return &LFeedbackDao{
		group:    "default",
		table:    "L_Feedback",
		columns:  lFeedbackColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LFeedbackDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LFeedbackDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LFeedbackDao) Columns() LFeedbackColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LFeedbackDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LFeedbackDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LFeedbackDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
