.publish-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.btn-publish {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-publish:hover {
    background-color: #0056b3;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.btn-draft {
    background-color: #6c757d;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-draft:hover {
    background-color: #5a6268;
}

.draft-tip {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    display: none;
}

/* 浮动按钮样式 */
.float-button {
    position: fixed;
    right: 20px;
    bottom: 20px;
    width: 60px;
    height: 60px;
    background-color: #007bff;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s, background-color 0.2s;
    z-index: 1000;
}

.float-button:hover {
    background-color: #0056b3;
    transform: scale(1.1);
}

/* 加号图标样式 */
.plus-icon {
    position: relative;
    width: 20px;
    height: 20px;
}

.plus-icon::before,
.plus-icon::after {
    content: '';
    position: absolute;
    background-color: white;
}

.plus-icon::before {
    width: 20px;
    height: 2px;
    top: 9px;
    left: 0;
}

.plus-icon::after {
    width: 2px;
    height: 20px;
    left: 9px;
    top: 0;
}

/* 添加移动端适配 */
@media (max-width: 768px) {
    .float-button {
        width: 50px;
        height: 50px;
        right: 15px;
        bottom: 15px;
    }

    .plus-icon {
        width: 16px;
        height: 16px;
    }

    .plus-icon::before {
        width: 16px;
        top: 7px;
    }

    .plus-icon::after {
        height: 16px;
        left: 7px;
    }
}

/* 底部导航栏样式 */
.nav-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 56px;
    background: white;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 999;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #666;
    text-decoration: none;
    padding: 8px 0;
}

.nav-item.active {
    color: #007bff;
}

.nav-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 4px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.nav-text {
    font-size: 12px;
}

/* 调整主内容区域，防止被底部导航遮挡 */
.page {
    padding-bottom: 66px;
}

/* 调整浮动按钮位置，避免与底部导航重叠 */
.float-button {
    bottom: 76px;
}

/* 图标样式 */
.icon-home {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>');
}

.icon-publish {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>');
}

.icon-profile {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
}

/* 适配 iPhone X 等带有底部安全区域的设备 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .nav-bottom {
        padding-bottom: env(safe-area-inset-bottom);
        height: calc(56px + env(safe-area-inset-bottom));
    }
    
    .page {
        padding-bottom: calc(66px + env(safe-area-inset-bottom));
    }
}

/* 个人中心页面样式 */
.profile-container {
    padding: 20px;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
}

.avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 10px;
}

.username {
    font-size: 18px;
    color: #333;
    margin: 0;
}

.function-list {
    margin-top: 20px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.function-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
    color: #333;
    text-decoration: none;
}

.function-item:last-child {
    border-bottom: none;
}

.arrow-right {
    width: 8px;
    height: 8px;
    border-top: 2px solid #999;
    border-right: 2px solid #999;
    transform: rotate(45deg);
}

/* 图片上传区域样式 */
.image-upload-container {
    margin-top: 10px;
}

.image-upload-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.image-upload-item {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.upload-button {
    border: 1px dashed #ddd;
    background: #fafafa;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.upload-button:hover {
    border-color: #007bff;
}

.upload-icon {
    font-size: 24px;
    color: #999;
    margin-bottom: 4px;
}

.upload-text {
    font-size: 12px;
    color: #999;
}

.preview-item {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.delete-button {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    font-size: 14px;
}

.delete-button:hover {
    background: rgba(0, 0, 0, 0.7);
}

.upload-tip {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
}

/* 添加图片上传进度条样式 */
.upload-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    background: #007bff;
    width: 0;
    transition: width 0.3s;
} 