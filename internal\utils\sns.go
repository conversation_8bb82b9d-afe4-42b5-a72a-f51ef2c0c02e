package utils

import (
	"50go/internal/consts"
	"50go/internal/dao"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

const (
	code2SessionURL        = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code"
	accessTokenURL         = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s"
	sendTemplateMessageURL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s"
)

type CommonError struct {
	ErrCode int64  `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

// ResCode2Session 登录凭证校验的返回结果
type ResCode2Session struct {
	CommonError

	OpenID     string `json:"openid"`      // 用户唯一标识
	SessionKey string `json:"session_key"` // 会话密钥
	UnionID    string `json:"unionid"`     // 用户在开放平台的唯一标识符，在满足UnionID下发条件的情况下会返回
}

// ResAccessToken 获取access_token返回结果
type ResAccessToken struct {
	CommonError

	AccessToken string `json:"access_token"` // 获取到的凭证
	ExpiresIn   int64  `json:"expires_in"`   // 凭证有效时间，单位：秒
}

// TemplateMessage 发送模板消息请求参数
type TemplateMessage struct {
	ToUser     string                       `json:"touser"`         // 接收者（用户）的 openid
	TemplateID string                       `json:"template_id"`    // 所需下发的订阅模板id
	Page       string                       `json:"page,omitempty"` // 点击模板卡片后的跳转页面，仅限本小程序内的页面
	Data       map[string]map[string]string `json:"data"`           // 模板内容
}

// OrderTemplateData 订单待处理通知模板数据
type OrderTemplateData struct {
	OrderNo      string // 订单编号
	OrderStatus  string // 业务状态
	CustomerName string // 客户名称
	NotifyTime   string // 通知时间
}

// Code2Session 登录凭证校验
func Code2Session(jsCode string, AppID string, AppSecret string) (result ResCode2Session, err error) {

	urlStr := fmt.Sprintf(code2SessionURL, AppID, AppSecret, jsCode)
	var response []byte
	response, err = HTTPGetSNS(urlStr)
	if err != nil {
		return
	}
	err = json.Unmarshal(response, &result)
	if err != nil {
		return
	}
	if result.ErrCode != 0 {
		err = fmt.Errorf("Code2Session error : errcode=%v , errmsg=%v", result.ErrCode, result.ErrMsg)
		return
	}
	return
}

// GetAccessToken 获取小程序全局唯一后台接口调用凭据
func GetAccessToken(AppID string, AppSecret string) (result ResAccessToken, err error) {
	urlStr := fmt.Sprintf(accessTokenURL, AppID, AppSecret)
	var response []byte
	response, err = HTTPGetSNS(urlStr)
	if err != nil {
		return
	}
	err = json.Unmarshal(response, &result)
	if err != nil {
		return
	}
	if result.ErrCode != 0 {
		err = fmt.Errorf("GetAccessToken error : errcode=%v , errmsg=%v", result.ErrCode, result.ErrMsg)
		return
	}
	return
}

// SendTemplateMessage 发送订阅消息
func SendTemplateMessage(openID, templateID, page string, data map[string]map[string]string, accessToken string) error {
	message := TemplateMessage{
		ToUser:     openID,
		TemplateID: templateID,
		Page:       page,
		Data:       data,
	}

	urlStr := fmt.Sprintf(sendTemplateMessageURL, accessToken)
	response, err := PostJSON(urlStr, message)
	if err != nil {
		return err
	}

	var result CommonError
	err = json.Unmarshal(response, &result)
	if err != nil {
		return err
	}

	if result.ErrCode != 0 {
		return fmt.Errorf("发送模板消息失败: errcode=%v, errmsg=%v", result.ErrCode, result.ErrMsg)
	}

	return nil
}

// SendOrderNotify 发送订单待处理通知
// userID: 用户ID
// templateID: 模板ID，如果为空则使用默认值
// orderData: 订单数据
// page: 跳转页面路径，如 "pages/order/detail?id=123"
// ctx: 上下文
func SendOrderNotify(userID interface{}, templateID string, orderData OrderTemplateData, page string, ctx context.Context) (err error) {
	// 1. 获取用户openid
	var openID string
	userData, err := dao.User.Ctx(ctx).Where("UID", userID).One()
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	openID = userData["UOpenID"].String()
	if openID == "" {
		return fmt.Errorf("用户未绑定微信，无法发送消息")
	}

	// 3. 获取access_token
	appID := consts.AppIDab
	appSecret := consts.AppSecretab

	accessTokenResult, err := GetAccessToken(appID, appSecret)
	if err != nil {
		return fmt.Errorf("获取access_token失败: %v", err)
	}

	// 4. 使用默认模板ID（如果未提供）
	if templateID == "" {
		templateID = "JrqIiTeNCd2ih4wuuzU3e0RB4uONwcStjxIqh1XupTY"
	}

	// 5. 构建模板数据
	data := make(map[string]map[string]string)
	data["character_string1"] = map[string]string{
		"value": orderData.OrderNo,
	}
	data["phrase2"] = map[string]string{
		"value": orderData.OrderStatus,
	}
	data["thing3"] = map[string]string{
		"value": orderData.CustomerName,
	}
	data["time4"] = map[string]string{
		"value": orderData.NotifyTime,
	}

	// 6. 发送模板消息
	return SendTemplateMessage(openID, templateID, page, data, accessTokenResult.AccessToken)
}

// SendTemplateMessageWithData 直接使用自定义数据发送模板消息
// userID: 用户ID
// templateID: 模板ID，必须提供
// page: 跳转页面路径
// data: 自定义模板数据
// ctx: 上下文
func SendTemplateMessageWithData(userID interface{}, templateID string, page string, data map[string]map[string]string, ctx context.Context) (err error) {
	// 1. 检查参数
	if templateID == "" {
		return fmt.Errorf("模板ID不能为空")
	}

	if data == nil {
		return fmt.Errorf("模板数据不能为空")
	}

	// 2. 获取用户openid
	var openID string
	userData, err := dao.User.Ctx(ctx).Where("UID", userID).One()
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	openID = userData["UOpenID"].String()
	if openID == "" {
		return fmt.Errorf("用户未绑定微信，无法发送消息")
	}

	// 3. 获取微信配置
	appID := consts.AppIDab
	appSecret := consts.AppSecretab

	accessTokenResult, err := GetAccessToken(appID, appSecret)
	if err != nil {
		return fmt.Errorf("获取access_token失败: %v", err)
	}

	// 5. 发送模板消息
	return SendTemplateMessage(openID, templateID, page, data, accessTokenResult.AccessToken)
}

// SendWxTemplateMsg 封装好的发送模板消息函数，可直接从其他控制器调用
// userID: 用户ID
// templateID: 模板ID
// page: 跳转页面路径，如 "pages/index/index"
// customData: 自定义模板数据，如果为nil则使用默认模板数据
// ctx: 上下文
func SendWxTemplateMsg(userID interface{}, templateID, page string, customData map[string]map[string]string, ctx context.Context) (err error) {
	// 1. 获取用户openid
	var openID string
	userData, err := dao.User.Ctx(ctx).Where("UID", userID).One()
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	openID = userData["UOpenID"].String()
	if openID == "" {
		return fmt.Errorf("用户未绑定微信，无法发送消息")
	}

	// 2. 获取微信配置
	appID := consts.AppIDab
	appSecret := consts.AppSecretab

	accessTokenResult, err := GetAccessToken(appID, appSecret)
	if err != nil {
		return fmt.Errorf("获取access_token失败: %v", err)
	}

	// 4. 使用默认模板ID（如果未提供）
	if templateID == "" {
		// 默认使用订单待处理通知模板ID
		templateID = "JrqIiTeNCd2ih4wuuzU3e0RB4uONwcStjxIqh1XupTY"
	}

	// 5. 使用默认页面（如果未提供）
	if page == "" {
		page = "pages/index/index"
	}

	// 6. 构建模板数据（如果未提供）
	data := customData
	if data == nil {
		// 默认使用系统通知模板
		data = make(map[string]map[string]string)
		data["character_string1"] = map[string]string{
			"value": "ORDER" + time.Now().Format("20060102150405"),
		}
		data["phrase2"] = map[string]string{
			"value": "待处理",
		}
		data["thing3"] = map[string]string{
			"value": "默认客户",
		}
		data["time4"] = map[string]string{
			"value": time.Now().Format("2006-01-02 15:04"),
		}
	}

	// 7. 发送模板消息
	return SendTemplateMessage(openID, templateID, page, data, accessTokenResult.AccessToken)
}

// SendWxTemplateMsgByOpenID 通过OpenID直接发送模板消息
// openID: 用户OpenID
// templateID: 模板ID
// page: 跳转页面路径，如 "pages/index/index"
// customData: 自定义模板数据，如果为nil则使用默认模板数据
// ctx: 上下文
func SendWxTemplateMsgByOpenID(openID, templateID, page string, customData map[string]map[string]string, ctx context.Context) (err error) {
	// 1. 获取微信配置
	appID := consts.AppIDab
	appSecret := consts.AppSecretab

	accessTokenResult, err := GetAccessToken(appID, appSecret)
	if err != nil {
		return fmt.Errorf("获取access_token失败: %v", err)
	}

	// 3. 使用默认模板ID（如果未提供）
	if templateID == "" {
		// 使用默认模板ID，实际使用时应替换为真实的模板ID
		templateID = "YqRglgDE9clqXiMX_Q8MvRgn21W2z66WqiQq4nw5jXE"
	}

	// 4. 使用默认页面（如果未提供）
	if page == "" {
		page = "pages/index/index"
	}

	// 5. 构建模板数据（如果未提供）
	data := customData
	if data == nil {
		data = make(map[string]map[string]string)
		data["thing1"] = map[string]string{
			"value": "系统通知",
		}
		data["time2"] = map[string]string{
			"value": time.Now().Format("2006-01-02 15:04"),
		}
		data["thing3"] = map[string]string{
			"value": "您有一条新的系统通知，请查看",
		}
	}

	// 6. 发送模板消息
	return SendTemplateMessage(openID, templateID, page, data, accessTokenResult.AccessToken)
}

func GetUIDbyHeader(ctx context.Context) string {
	r := g.RequestFromCtx(ctx)
	USession := r.GetHeader("access_token")
	user := dao.User.Ctx(r.Context())
	userdata, _ := user.Fields("UID").Where("USession", USession).One()
	UID := userdata["UID"]
	return UID.String()
}

func GetOPENIDbyHeader(ctx context.Context) string {
	r := g.RequestFromCtx(ctx)
	USession := r.GetHeader("access_token")
	user := dao.User.Ctx(r.Context())
	userdata, _ := user.Fields("UOpenID").Where("USession", USession).One()
	UOpenID := userdata["UOpenID"]
	return UOpenID.String()
}
