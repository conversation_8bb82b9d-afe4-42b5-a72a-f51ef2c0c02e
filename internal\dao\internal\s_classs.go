// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SClasssDao is the data access object for the table S_Classs.
type SClasssDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SClasssColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SClasssColumns defines and stores column names for the table S_Classs.
type SClasssColumns struct {
	Scid      string //
	SCNmae    string //
	SCpareID  string //
	SCKeyWord string //
	SCUrl     string //
	SCTag     string //
	SCTag2    string //
	SCTag3    string //
	SCType    string //
	Scimg     string //
	Scsimg    string //
}

// sClasssColumns holds the columns for the table S_Classs.
var sClasssColumns = SClasssColumns{
	Scid:      "SCID",
	SCNmae:    "SCNmae",
	SCpareID:  "SCpareID",
	SCKeyWord: "SCKeyWord",
	SCUrl:     "SCUrl",
	SCTag:     "SCTag",
	SCTag2:    "SCTag2",
	SCTag3:    "SCTag3",
	SCType:    "SCType",
	Scimg:     "SCIMG",
	Scsimg:    "SCSIMG",
}

// NewSClasssDao creates and returns a new DAO object for table data access.
func NewSClasssDao(handlers ...gdb.ModelHandler) *SClasssDao {
	return &SClasssDao{
		group:    "default",
		table:    "S_Classs",
		columns:  sClasssColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SClasssDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SClasssDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SClasssDao) Columns() SClasssColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SClasssDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SClasssDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SClasssDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
