// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLADClassDao is internal type for wrapping internal DAO implements.
type internalLADClassDao = *internal.LADClassDao

// lADClassDao is the data access object for table L_ADClass.
// You can define custom methods on it to extend its functionality as you wish.
type lADClassDao struct {
	internalLADClassDao
}

var (
	// LADClass is globally public accessible object for table L_ADClass operations.
	LADClass = lADClassDao{
		internal.NewLADClassDao(),
	}
)

// Fill with you ideas below.
