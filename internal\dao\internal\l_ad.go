// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LAdDao is the data access object for the table L_AD.
type LAdDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LAdColumns         // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LAdColumns defines and stores column names for the table L_AD.
type LAdColumns struct {
	Id        string //
	ClassId   string //
	Guid      string //
	Title     string //
	From      string //
	Url       string //
	Tag       string //
	Img       string //
	Content   string //
	Click     string //
	Time      string //
	CreatTime string //
	Sort      string //
	IsHot     string //
	IsSlide   string //
	IsLock    string //
	IsRed     string //
	DelTime   string //
}

// lAdColumns holds the columns for the table L_AD.
var lAdColumns = LAdColumns{
	Id:        "ID",
	ClassId:   "ClassId",
	Guid:      "Guid",
	Title:     "Title",
	From:      "From",
	Url:       "Url",
	Tag:       "Tag",
	Img:       "Img",
	Content:   "Content",
	Click:     "Click",
	Time:      "Time",
	CreatTime: "CreatTime",
	Sort:      "Sort",
	IsHot:     "IsHot",
	IsSlide:   "IsSlide",
	IsLock:    "IsLock",
	IsRed:     "IsRed",
	DelTime:   "delTime",
}

// NewLAdDao creates and returns a new DAO object for table data access.
func NewLAdDao(handlers ...gdb.ModelHandler) *LAdDao {
	return &LAdDao{
		group:    "default",
		table:    "L_AD",
		columns:  lAdColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LAdDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LAdDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LAdDao) Columns() LAdColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LAdDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LAdDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LAdDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
