<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>
</head>

<body>
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">
            <div class="main-container">
                <input name="myguid"  type="hidden" value="{{.newss.uguid}}">

                <div class="layui-form-item">
                    <div class="layui-inline">
                    <label class="layui-form-label">用户名:</label>
                    <div class="layui-input-block">
                        <input type="text" name="uname" lay-verify="required" value="{{.newss.uname}}"
                            autocomplete="off" class="layui-input" style="max-width: 600px;">
                    </div>
                </div>
                <div class="layui-inline">
                <label class="layui-form-label">电话(登录用):</label>
                <div class="layui-input-block">
                    <input type="text" name="utel" lay-verify="required" value="{{.newss.utel}}"
                        autocomplete="off" class="layui-input" style="max-width: 600px;">
                        
                </div>
            </div>
                </div>

                <div class="layui-form-item">
           
                    <div class="layui-form-item" id="toolbarDiv">
                        <label class="layui-form-label">部门分类：</label>
                        <div class="layui-input-inline " style="width: 50%;">                      
                            <ul id="demotree" class="dtree layui-input" data-id="0" style="width: 50%;"></ul>                      
                        </div>
                    </div>

                </div>

                <div class="layui-form-item">



                    <div class="layui-inline">
                        <label class="layui-form-label">真实姓名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="utruename" id="Author" class="layui-input" value="{{.newss.utruename}}">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">密码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="myumima"  autocomplete="off" class="layui-input" value=""> 
                        </div>
                    </div>
                </div>


                

                <div class="layui-form-item cover">
                    <label class="layui-form-label">权限：</label>
                    <div class="layui-form">
                        <input type="checkbox" name="issh" value="1" title="审核员" {{if eq .newss.issh 1}}
                            checked{{end}}>
                        <input type="checkbox" name="ispc" value="1" lay-text="派车员" {{if eq .newss.ispc 1}}
                            checked{{end}}>
                      
                    </div>
                </div>



                <div class="layui-form-item">
                    <div class="layui-col-xs6">
                    <label class="layui-form-label">菜单权限：</label>
                    <div class="layui-form">
                        <div style="overflow: auto;">
                            <ul id="powertree" class="dtree"></ul>
                            <input name="upower" id="upower"  type="hidden" value="{{.newss.upower}}">
                        </div>
                    </div>
                </div>

                    <div class="layui-col-xs6">
                    <label class="layui-form-label">新闻权限：</label>
                    <div class="layui-form">
                        <div style="overflow: auto;">
                            <ul id="cdpowertree" class="dtree"></ul>
                            <input name="ucdpower" id="ucdpower"  type="hidden" value="{{.newss.ucdpower}}">
                        </div>
                    </div>
                </div>
                </div>

                <div class="layui-form-item">
              
                </div>



            </div>
        </div>
        <div class="bottom">
            <div class="button-container">
                <div class="layui-btn-container layui-col-xs12">
                    <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">清空</button>
                </div>

            </div>
        </div>
    </form>


    <link href="/component/wangeditoredit/style.css" rel="stylesheet">



    <style>
        #editor—wrapper {
            border: 1px solid #ccc;
            z-index: 100;
            /* 按需定义 */
        }

        #toolbar-container {
            border-bottom: 1px solid #ccc;
        }

        #editor-container {
            height: 500px;
        }
    </style>






    <script>



layui.use(['jquery', 'tree','dtree'], function () {
            var tree = layui.tree;
            var mdata = {{.jsonData }};
           // var treeSelect= layui.treeSelect;
            var dtree = layui.dtree,
            $ = layui.jquery;
            
    var DemoTree = dtree.render({   //部门
      elem: "#demotree",
      data: mdata, // 使用data加载
      width:"50%",
      line:true,
      selectTips: "顶级部门",
      selectInitVal: "{{.newss.utype}}", // 你可以在这里指定默认值
      select:true,
      scroll:"#toolbarDiv", // 绑定div元素
      icon: ["0","-1"]  // 显示非最后一级节点图标，隐藏最后一级节点图标
    //   ficon: ["0","7"],  // 设定一级图标样式。0表示方形加减图标，7表示文件图标
    //   skin: "layui"  // layui主题风格
     
    });
    
    // 绑定节点点击
    dtree.on("node('demotree')" ,function(obj){
      layer.msg(JSON.stringify(obj.param));
    });

});




        layui.use(['form', 'jquery', 'laydate','tree'], function () {
            let form = layui.form;
            let $ = layui.jquery;
      //新闻分类绑定 
            var classtree = layui.tree;
            var classmdata = {{.classMenu }};    //菜单
        classtree.render({
            elem: '#cdpowertree',
            data: classmdata,
            spread: true, // 默认展开所有节点
            id: 'classtreeid', // 自定义 id 索引
            showCheckbox: true,     //是否显示复选框

            oncheck: function (obj) {
                    var childs = $(obj.elem).find('.' + "layui-tree-pack").find('input[same="layuiTreeCheck"]');
                    childs.each(function () {
                      this.checked = false;
                    });
                    form.render('checkbox');
                  },

            checkChirld:false,  //是否关联子集菜单
            // onlyIconControl: true
            // showLine: false,  // 是否开启连接线
            customName: { // 自定义 data 字段名 --- 2.8.14+
    id: 'id',
    title: 'title',
    children: 'children',
    spread:true
  },
            click: function (obj) {
                var data = obj.data;  //获取当前点击的节点数据
                layer.msg('状态：' + obj.state + '<br>节点数据：' + JSON.stringify(data));
            },
          
        });

        const classarr = "{{.cdpowers}}".split(',');
        console.log(classarr)
        classtree.setChecked('classtreeid',classarr); // 勾选对应 id 值的节点

   //新闻分类绑定end
  //菜单绑定end
            var tree = layui.tree;
            var mdata = {{.payload }};    //菜单
        tree.render({
            elem: '#powertree',
            data: mdata,
            spread: true, // 默认展开所有节点
            id: 'treeId', // 自定义 id 索引
            showCheckbox: true,     //是否显示复选框

            oncheck: function (obj) {
                    var childs = $(obj.elem).find('.' + "layui-tree-pack").find('input[same="layuiTreeCheck"]');
                    childs.each(function () {
                      this.checked = false;
                    });
                    form.render('checkbox');
                  },

            checkChirld:false,  //是否关联子集菜单
            // onlyIconControl: true
            // showLine: false,  // 是否开启连接线
            customName: { // 自定义 data 字段名 --- 2.8.14+
    id: 'id',
    title: 'title',
    children: 'children',
    spread:true
  },
            click: function (obj) {


 
                var data = obj.data;  //获取当前点击的节点数据
                layer.msg('状态：' + obj.state + '<br>节点数据：' + JSON.stringify(data));
            },
          
        });

        const arr = "{{.powers}}".split(',');
        console.log(arr)
       
        tree.setChecked('treeId',arr); // 勾选对应 id 值的节点
        //     laydate.render({
        //         elem: '#Time'
        //         , type: 'datetime'
        //     });

            form.verify({

                ip: [
                    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
                    , 'IP地址不符合规则'
                ]
            });

            form.on('radio(type)', function (data) {
                if (data.value == 1) {
                    $(".conn-pwd").show();
                    $(".conn-key").hide();
                } else {
                    $(".conn-key").show();
                    $(".conn-pwd").hide();
                }
            });

            form.on('submit(user-save)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                var checkData = tree.getChecked('treeId');
                var list = new Array();
                list = getChecked_list(checkData);
                $("#upower").val(list);


                var classcheckData = classtree.getChecked('classtreeid');
                var classlist = new Array();
                classlist = getChecked_list(classcheckData);
                $("#ucdpower").val(classlist);
                


                $.ajax({
                    url: '/system/admin/edit',
                    data: $('#news_edit').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");
                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });



                 // 获取选中节点的id
          function getChecked_list(data) {
                var id = "";
                $.each(data, function (index, item) {
                    if (id != "") {
                        id = id + "," + item.id;
                    }
                    else {
                        id = item.id;
                    }
                    var i = getChecked_list(item.children);
                    if (i != "") {
                        id = id + "," + i;
                    }
                });
                return id;
            }


        })

     
    </script>
</body>

</html>