// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Admin is the golang structure for table Admin.
type Admin struct {
	Uid               int         `json:"uid"               orm:"uid"               ` //
	Uopenid           string      `json:"uopenid"           orm:"uopenid"           ` //
	Uwxopenid         string      `json:"uwxopenid"         orm:"uwxopenid"         ` //
	Uwxtel            string      `json:"uwxtel"            orm:"uwxtel"            ` //
	Uwxhead           string      `json:"uwxhead"           orm:"uwxhead"           ` //
	Uuniid            string      `json:"uuniid"            orm:"uuniid"            ` //
	Uguid             string      `json:"uguid"             orm:"uguid"             ` //
	Uparentid         string      `json:"uparentid"         orm:"uparentid"         ` //
	Uindirectparentid string      `json:"uindirectparentid" orm:"uindirectparentid" ` //
	Uwx               string      `json:"uwx"               orm:"uwx"               ` //
	Uzfb              string      `json:"uzfb"              orm:"uzfb"              ` //
	Utel              string      `json:"utel"              orm:"utel"              ` //
	Umima             string      `json:"umima"             orm:"umima"             ` //
	Utouxiang         string      `json:"utouxiang"         orm:"utouxiang"         ` //
	Uname             string      `json:"uname"             orm:"uname"             ` //
	Uheadimg          string      `json:"uheadimg"          orm:"uheadimg"          ` //
	Unickname         string      `json:"unickname"         orm:"unickname"         ` //
	Utruename         string      `json:"utruename"         orm:"utruename"         ` //
	Ustate            int         `json:"ustate"            orm:"ustate"            ` //
	Ismember          int         `json:"ismember"          orm:"ismember"          ` //
	Ustateendtime     *gtime.Time `json:"ustateendtime"     orm:"ustateendtime"     ` //
	Logintime         *gtime.Time `json:"logintime"         orm:"logintime"         ` //
	Regtime           *gtime.Time `json:"regtime"           orm:"regtime"           ` //
	Ujifen            int         `json:"ujifen"            orm:"ujifen"            ` //
	Utype             string      `json:"utype"             orm:"utype"             ` //
	Upower            string      `json:"upower"            orm:"upower"            ` //
	Ucdpower          string      `json:"ucdpower"          orm:"ucdpower"          ` //
	Usex              string      `json:"usex"              orm:"usex"              ` //
	Uprovince         string      `json:"uprovince"         orm:"uprovince"         ` //
	Issh              int         `json:"issh"              orm:"issh"              ` //
	Ispc              int         `json:"ispc"              orm:"ispc"              ` //
}
