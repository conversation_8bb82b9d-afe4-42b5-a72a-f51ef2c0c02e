// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WeChatConfigDao is the data access object for the table WeChatConfig.
type WeChatConfigDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  WeChatConfigColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// WeChatConfigColumns defines and stores column names for the table WeChatConfig.
type WeChatConfigColumns struct {
	Id         string //
	WeChatName string //
	Appid      string //
	Mchid      string //
	Key        string //
	Appsecret  string //
	PayUrl     string //
	IsUser     string //
	PayAPI     string //
}

// weChatConfigColumns holds the columns for the table WeChatConfig.
var weChatConfigColumns = WeChatConfigColumns{
	Id:         "Id",
	WeChatName: "WeChatName",
	Appid:      "APPID",
	Mchid:      "MCHID",
	Key:        "KEY",
	Appsecret:  "APPSECRET",
	PayUrl:     "PayUrl",
	IsUser:     "IsUser",
	PayAPI:     "PayAPI",
}

// NewWeChatConfigDao creates and returns a new DAO object for table data access.
func NewWeChatConfigDao(handlers ...gdb.ModelHandler) *WeChatConfigDao {
	return &WeChatConfigDao{
		group:    "default",
		table:    "WeChatConfig",
		columns:  weChatConfigColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WeChatConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WeChatConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WeChatConfigDao) Columns() WeChatConfigColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WeChatConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WeChatConfigDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WeChatConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
