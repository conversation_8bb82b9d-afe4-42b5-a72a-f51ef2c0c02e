package utils

import (
	"path/filepath"
	"strings"
)

// IsImageFile 检查文件是否为图片
func IsImageFile(filename string) bool {
	if filename == "" {
		return false
	}

	ext := strings.ToLower(filepath.Ext(filename))
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".ico"}

	for _, imgExt := range imageExts {
		if ext == imgExt {
			return true
		}
	}
	return false
}

// IsVideoFile 检查文件是否为视频
func IsVideoFile(filename string) bool {
	if filename == "" {
		return false
	}

	ext := strings.ToLower(filepath.Ext(filename))
	videoExts := []string{".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv", ".3gp", ".m4v"}

	for _, vidExt := range videoExts {
		if ext == vidExt {
			return true
		}
	}
	return false
}

// GetFileType 获取文件类型
func GetFileType(filename string) string {
	if IsImageFile(filename) {
		return "image"
	} else if IsVideoFile(filename) {
		return "video"
	}
	return "unknown"
}
