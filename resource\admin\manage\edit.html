<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>
</head>

<body>
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">
            <div class="main-container">
                <input name="MID"  type="hidden" value="{{.newss.MID}}">

                       {{.newss}}
                <div class="layui-form-item">
                    <label class="layui-form-label">登录名:</label>
                    <div class="layui-input-block">
                        <input type="text" name="MName" lay-verify="required" value="{{.newss.MName}}"
                            autocomplete="off" class="layui-input" style="max-width: 600px;">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">部门分类：</label>
                    <div class="layui-input-inline">
                        <select name="CpareID" lay-verify="required">
                            <option value="0">部门</option>
                            {{range $key, $value := .list}}
                            <option value="{{$value.CID}}" {{if eq $.newss.CpareID $value.CID }}selected{{end}} {{if eq
                                $.newss.CID $value.CID }}disabled{{end}}>{{$value.spacer}} {{$value.CNmae}}</option>
                            {{end}}

                        </select>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">真实姓名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="TrueName" id="Author" class="layui-input" value="{{.newss.Author}}">
                        </div>
                    </div>

                </div>

                <div class="layui-form-item">


                    <div class="layui-inline">
                        <label class="layui-form-label">密码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="Pass" id="date" autocomplete="off" class="layui-input" value="{{.newss.MPWD}}"> 
                        </div>
                    </div>
                </div>



                <div class="layui-form-item cover">
                    <label class="layui-form-label">权限：</label>
                    <div class="layui-form">
                        <input type="checkbox" name="AAA" title="推荐">
                        <input type="checkbox" name="BBB" lay-text="启用" checked>
                        <input type="checkbox" name="CCC" lay-text="图片" checked>

                    </div>
                </div>



                <div class="layui-form-item">
                    <label class="layui-form-label">权限：</label>
                    <div class="layui-form">
                        <div style="overflow: auto;">
                            <ul id="PowerTree" class="dtree"></ul>
                            <input name="Power" id="Power"  type="hidden" value="{{.newss.MPower}}">
                        </div>
                    </div>
                </div>


            </div>
        </div>
        <div class="bottom">
            <div class="button-container">
                <div class="layui-btn-container layui-col-xs12">
                    <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">清空</button>
                </div>

            </div>
        </div>
    </form>


    <link href="/component/wangeditoredit/style.css" rel="stylesheet">



    <style>
        #editor—wrapper {
            border: 1px solid #ccc;
            z-index: 100;
            /* 按需定义 */
        }

        #toolbar-container {
            border-bottom: 1px solid #ccc;
        }

        #editor-container {
            height: 500px;
        }
    </style>






    <script>


        layui.use(['form', 'jquery', 'laydate','tree'], function () {
            let form = layui.form;
            let $ = layui.jquery;
            var laydate = layui.laydate;

            var tree = layui.tree;
            var mdata = {{.payload }};
        tree.render({
            elem: '#PowerTree',
            data: mdata,
            spread: true, // 默认展开所有节点
            id: 'treeId', // 自定义 id 索引
            showCheckbox: true,     //是否显示复选框

            oncheck: function (obj) {
                    var childs = $(obj.elem).find('.' + "layui-tree-pack").find('input[same="layuiTreeCheck"]');
                    childs.each(function () {
                      this.checked = false;
                    });
                    form.render('checkbox');
                  },

            checkChirld:false,  //是否关联子集菜单
            // onlyIconControl: true
            // showLine: false,  // 是否开启连接线
            customName: { // 自定义 data 字段名 --- 2.8.14+
    id: 'id',
    title: 'title',
    children: 'children',
    spread:true
  },
            click: function (obj) {
                var data = obj.data;  //获取当前点击的节点数据
                layer.msg('状态：' + obj.state + '<br>节点数据：' + JSON.stringify(data));
            },
          
        });

        const arr = "{{.powers}}".split(',');
        console.log(arr)
        tree.setChecked('treeId',arr); // 勾选对应 id 值的节点
        	//选中节点id，包涵了父id
	        //  var checkedData = tree.getChecked('treeId'); //获取选中节点的数据，demoId1在tree定义一个id
            //   console.log("111", checkedData)
            //   var arrId = []
            //   getCheckedId(checkedData);
            //   // 测试
            //   console.log(arrId);
            //   // 获取选中节点的id,遍历树形列表去获取每一级的id
            //   function getCheckedId(jsonObj) {
            //     jsonObj.forEach((item, index) => {
            //       arrId.push(item.id)
            //       if (item.children != []) {
            //         getCheckedId(item.children);//递归实现遍历每一层级数据
            //       }
            //     });
            //     return arrId;
            //   }
              


            laydate.render({
                elem: '#Time'
                , type: 'datetime'
            });

            form.verify({

                ip: [
                    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
                    , 'IP地址不符合规则'
                ]
            });

            form.on('radio(type)', function (data) {
                if (data.value == 1) {
                    $(".conn-pwd").show();
                    $(".conn-key").hide();
                } else {
                    $(".conn-key").show();
                    $(".conn-pwd").hide();
                }
            });

            form.on('submit(user-save)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                var checkData = tree.getChecked('treeId');
                var list = new Array();
                list = getChecked_list(checkData);


                $("#Power").val(list);
                
                $.ajax({
                    url: '/system/manage/edit',
                    data: $('#news_edit').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");
                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });



                 // 获取选中节点的id
          function getChecked_list(data) {
                var id = "";
                $.each(data, function (index, item) {
                    if (id != "") {
                        id = id + "," + item.id;
                    }
                    else {
                        id = item.id;
                    }
                    var i = getChecked_list(item.children);
                    if (i != "") {
                        id = id + "," + i;
                    }
                });
                return id;
            }


        })

     
    </script>
</body>

</html>