// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// LAd is the golang structure of table L_AD for DAO operations like Where/Data.
type LAd struct {
	g.Meta    `orm:"table:L_AD, do:true"`
	Id        interface{} //
	ClassId   interface{} //
	Guid      interface{} //
	Title     interface{} //
	From      interface{} //
	Url       interface{} //
	Tag       interface{} //
	Img       interface{} //
	Content   interface{} //
	Click     interface{} //
	Time      *gtime.Time //
	CreatTime *gtime.Time //
	Sort      interface{} //
	IsHot     interface{} //
	IsSlide   interface{} //
	IsLock    interface{} //
	IsRed     interface{} //
	DelTime   *gtime.Time //
}
