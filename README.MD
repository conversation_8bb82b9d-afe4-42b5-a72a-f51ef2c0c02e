编译 

gf build


gf init gf_demo01 #在当前目录里创建项目名为gf01的项目

下载依赖

go mod tidy
运行程序

gf run main.go

框架升级-up
升级前 如果 文件夹是微力同步过来的需要吧只读模式关掉
gf up -a   all参数同时更新cli工具版本，并且自动修复本地代码在升级中的不兼容变更


Windows下的Golang版本升级
3.1 检查当前版本
打开命令提示符，输入：
go version
3.2 下载新版本
访问Go官方网站，下载Windows版本直接安装


3.5 验证安装
再次打开命令提示符，输入go version，确认版本已更新。


# GoFrame Template For SingleRepo

Quick Start: 
- https://goframe.org/pages/viewpage.action?pageId=1114399



	ad := []map[string]interface{}{
		{"pic": "upload/ad1.jpg", "title": ""},
		{"pic": "upload/ad2.jpg", "title": ""},
		{"pic": "upload/ad3.jpg", "title": ""},
		{"pic": "upload/ad4.jpg", "title": ""},
		{"pic": "upload/ad5.jpg", "title": ""},
		{"pic": "upload/ad6.jpg", "title": ""},
	}


# GoFrame Template For SingleRepo

Quick Start: 
- https://goframe.org/pages/viewpage.action?pageId=1114399



大部分场景下，进入项目根目录执行 gf gen dao 即可。以下为命令行帮助信息。

在 main.go 中加入一行 _ "github.com/gogf/gf/contrib/drivers/mysql/v2" （如果你使用的是 mysql；只需写一次）



数据库字段约定
id  pid  都用 int
name





# 依赖关系处理 ,根据go.mod文件
go mod tidy

# 验证依赖是否正确
go mod verify



上传项目包至生产环境服务器并解压至 /www/webroot/
打包部署
# 整理依赖包
# go mod tidy

# 打包构建
# go build main.go
# 重命名
# mv main tjjweb

打包生成后会在服务器上生成一个 tjjweb 的可执行文件；

# 以后台守护进程启动
# nohup ./tjjweb &



生产环境配置文件位置:hack/config.yaml
发布后的配置文件路径是:manifest/config/config.yaml
需要在main.go 声明和导入使用的数据库
mysql运行go get -u github.com/gogf/gf/contrib/drivers/mysql/v2
使用了internal目录，进行了约束。除了对外暴露接口的方法放在api目录，其他不需要对外的逻辑都要放在internal中



静态资源
静态资源不属于模板引擎的内容，但在模板文件中也有需要用到静态资源的地方，因此进行一下补充。

静态资源一般指的是js/css/image文件或者静态HTML文件，在GoFrame的项目目录中，这些文件放在resource/public下，之后还需要开启静态资源服务才能在模板文件中对这些资源进行引用。开启方式有两种

配置文件
manifest/config/config.yaml

server:

  serverRoot:  "resource/public"
  indexFolder: true # 这个可以不用配置，放在这里了解一下
用代码开启
internal/cmd/cmd.go

s := g.Server()
s.SetServerRoot("resource/public")
s.SetIndexFolder(true)


# GoFrame CLI tool configuration.
gfcli:

  build:
    name: "gf11" #编译后的文件名称
    arch: "386,amd64,arm"
    system: "linux,windows,darwin"
    mod: "none"
    pack: ""
    version: "v1.0.1"  # 编译后的文件会生成到/bin/v1.0.0目录里
    #path: "./bin"  # 输出的可执行文件路径，
    output: "./bin"  # 输出的可执行文件路径，会输出到temp/v1.0.0   这里写bin没有用
    extra: ""
    varMap:   # 自定义编译时内置变量
      k1: v1
      k2: v2
      

打包命令：执行 gf build 即可，具体配置请参考：build 交叉编译

打包结果：默认 linux/macos/windows 三个版本。

将config.toml和focus放到同一级目录，启动focus即可。





  $.ajax({url:"demo_test.txt",success:function(result){
        $("#div1").html(result);
    }});



go get -u github.com/wechatpay-apiv3/wechatpay-go




var ctx = gctx.New()


大部分场景下，进入项目根目录执行 gf gen dao 即可。以下为命令行帮助信息。




三种方式来发送微信小程序模板消息：
使用 SendOrderNotify 函数，传入模板ID和订单数据：
 utils.SendOrderNotify(userID, "", orderData, page, ctx)
   
   // 使用自定义模板ID
   utils.SendOrderNotify(userID, "your-template-id", orderData, page, ctx)emplateMessageWithData(userID, "your-template-id", page, customData, ctx)


   2使用 SendTemplateMessageWithData 函数，完全自定义模板ID和数据

      // 自定义数据
   customData := make(map[string]map[string]string)
   customData["character_string1"] = map[string]string{"value": "订单编号"}
   // ...更多字段
   
   utils.SendTemplateMessageWithData(userID, "your-template-id", page, customData, ctx)