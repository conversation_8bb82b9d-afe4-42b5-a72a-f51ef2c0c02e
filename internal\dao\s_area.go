// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalSAreaDao is internal type for wrapping internal DAO implements.
type internalSAreaDao = *internal.SAreaDao

// sAreaDao is the data access object for table S_Area.
// You can define custom methods on it to extend its functionality as you wish.
type sAreaDao struct {
	internalSAreaDao
}

var (
	// SArea is globally public accessible object for table S_Area operations.
	SArea = sAreaDao{
		internal.NewSAreaDao(),
	}
)

// Fill with you ideas below.
