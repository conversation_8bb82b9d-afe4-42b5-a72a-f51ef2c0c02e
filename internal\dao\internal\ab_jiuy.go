// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ABJiuyDao is the data access object for the table AB_jiuy.
type ABJiuyDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ABJiuyColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ABJiuyColumns defines and stores column names for the table AB_jiuy.
type ABJiuyColumns struct {
	JyId          string //
	Guid          string //
	OpenID        string //
	Uid           string //
	Fcren         string //
	Fctel         string //
	Fctitle       string //
	Fclat         string //
	Fclong        string //
	Jcren         string //
	Jctel         string //
	Jctitle       string //
	Jcaddress     string //
	Jclat         string //
	Jclong        string //
	Distance      string //
	Carjz         string //
	Carzt         string //
	Cartype       string //
	Content       string //
	AddTime       string //
	EditTime      string //
	State         string //
	DelTime       string //
	Paystat       string //
	Payway        string //
	Paytime       string //
	OutTradeNo    string //
	OOrderNumber  string //
	Paynotice     string //
	Imgs          string //
	Jjimgs        string //
	Idimgs        string //
	Didimgs       string //
	Caridimgs     string //
	Price         string //
	Price2        string //
	Type          string //
	TransactionId string //
	Uwxapp        string //
	UsjID         string //
	BegainUsjID   string //
	BegainTime    string //
	BegainImg     string //
	BegainContent string //
	EndUsjID      string //
	EndTime       string //
	EndImg        string //
	EndContent    string //
	Yytime        string //
	Yydate        string //
	Isdel         string //
	Endstate      string //
	Sh1           string //
	Shtime        string //
	Shmark        string //
	Shren         string //
}

// aBJiuyColumns holds the columns for the table AB_jiuy.
var aBJiuyColumns = ABJiuyColumns{
	JyId:          "JY_ID",
	Guid:          "Guid",
	OpenID:        "OpenID",
	Uid:           "UID",
	Fcren:         "fcren",
	Fctel:         "fctel",
	Fctitle:       "fctitle",
	Fclat:         "fclat",
	Fclong:        "fclong",
	Jcren:         "jcren",
	Jctel:         "jctel",
	Jctitle:       "jctitle",
	Jcaddress:     "jcaddress",
	Jclat:         "jclat",
	Jclong:        "jclong",
	Distance:      "distance",
	Carjz:         "carjz",
	Carzt:         "carzt",
	Cartype:       "cartype",
	Content:       "Content",
	AddTime:       "addTime",
	EditTime:      "editTime",
	State:         "state",
	DelTime:       "delTime",
	Paystat:       "paystat",
	Payway:        "payway",
	Paytime:       "paytime",
	OutTradeNo:    "out_trade_no",
	OOrderNumber:  "OOrderNumber",
	Paynotice:     "paynotice",
	Imgs:          "imgs",
	Jjimgs:        "jjimgs",
	Idimgs:        "idimgs",
	Didimgs:       "didimgs",
	Caridimgs:     "caridimgs",
	Price:         "price",
	Price2:        "price2",
	Type:          "type",
	TransactionId: "transaction_id",
	Uwxapp:        "uwxapp",
	UsjID:         "UsjID",
	BegainUsjID:   "BegainUsjID",
	BegainTime:    "BegainTime",
	BegainImg:     "BegainImg",
	BegainContent: "BegainContent",
	EndUsjID:      "EndUsjID",
	EndTime:       "EndTime",
	EndImg:        "EndImg",
	EndContent:    "EndContent",
	Yytime:        "yytime",
	Yydate:        "yydate",
	Isdel:         "isdel",
	Endstate:      "Endstate",
	Sh1:           "sh1",
	Shtime:        "shtime",
	Shmark:        "shmark",
	Shren:         "shren",
}

// NewABJiuyDao creates and returns a new DAO object for table data access.
func NewABJiuyDao(handlers ...gdb.ModelHandler) *ABJiuyDao {
	return &ABJiuyDao{
		group:    "default",
		table:    "AB_jiuy",
		columns:  aBJiuyColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ABJiuyDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ABJiuyDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ABJiuyDao) Columns() ABJiuyColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ABJiuyDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ABJiuyDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ABJiuyDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
