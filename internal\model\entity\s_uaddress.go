// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SUaddress is the golang structure for table S_UADDRESS.
type SUaddress struct {
	Uaid           int         `json:"UAID"           orm:"UAID"           ` //
	Uid            int         `json:"UID"            orm:"UID"            ` //
	Uopenid        string      `json:"Uopenid"        orm:"Uopenid"        ` //
	UPeopleName    string      `json:"UPeopleName"    orm:"UPeopleName"    ` //
	UAPhone        string      `json:"UAPhone"        orm:"UAPhone"        ` //
	UAProvance     string      `json:"UAProvance"     orm:"UAProvance"     ` //
	UACity         string      `json:"UACity"         orm:"UACity"         ` //
	UADistrict     string      `json:"UADistrict"     orm:"UADistrict"     ` //
	UAStreet       string      `json:"UAStreet"       orm:"UAStreet"       ` //
	UAddName       string      `json:"UAddName"       orm:"UAddName"       ` //
	UAddress       string      `json:"UAddress"       orm:"UAddress"       ` //
	UDetailAddress string      `json:"UDetailAddress" orm:"UDetailAddress" ` //
	UAAddTime      *gtime.Time `json:"UAAddTime"      orm:"UAAddTime"      ` //
	UAState        string      `json:"UAState"        orm:"UAState"        ` //
	Ulatng         string      `json:"Ulatng"         orm:"Ulatng"         ` //
}
