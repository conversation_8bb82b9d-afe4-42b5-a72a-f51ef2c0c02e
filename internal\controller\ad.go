package controller

import (
	"50go/internal/dao"
	tree "50go/internal/utils"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

var Ad = cAD{}

type cAD struct{}

// 混编一体Classlist
func (c *cAD) Class_list(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	md := dao.LADClass.Ctx(r.Context())
	classs, err := md.All()
	var a int32 = 0
	var chridnum []map[string]interface{}
	zhdatra := classs.List()
	if zhdatra != nil {
		for _, v := range zhdatra {

			v["id"] = int32(v["ID"].(int32)) //int 要转成 int64
			v["title"] = v["CNmae"]
			v["name"] = v["CNmae"]
			fmt.Println("v[CpareID]:", v["CpareID"])
			if v["CpareID"] == nil || v["CpareID"] == "" {
				v["pid"] = a
			} else {
				v["pid"] = int32(v["CpareID"].(int))
			}
			chridnum = append(chridnum, v)
		}
	}
	GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
	treeData, err := json.Marshal(GetRuleTreeArrayLayui)

	if err == nil {
		r.Response.WriteTpl("ad/class_list.html", g.Map{
			"header":   "This is header",
			"info":     classs,
			"treeData": string(treeData),
		})
	}

}

//混编一体编辑Class修改

func (c *cAD) Class_edit(r *ghttp.Request) {

	md := dao.LADClass.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("ID").String()

		nclass, err := md.Fields("CNmae,CpareID,ID").All()
		var a int32 = 0

		zhdatra := nclass.List()
		var chridnum []map[string]interface{}
		if zhdatra != nil {
			for _, v := range zhdatra {
				v["id"] = int32(v["ID"].(int32)) //int 要转成 int64
				v["title"] = v["CNmae"]
				num, _ := strconv.ParseInt(id, 10, 64)
				if v["ID"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["CpareID"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int32(v["CpareID"].(int))
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		fmt.Println("jsonData==>", string(jsonData))

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("ad/class_edit.html", g.Map{
					"data":     g.Map{"ID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			data, err := md.Where("ID", id).One()
			if err == nil {
				r.Response.WriteTpl("ad/class_edit.html", g.Map{
					"data":     data,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("ID").String()
		pid := r.GetForm("demoTree_select_nodeId")
		if id == "0" || id == "" {
			mform := r.GetFormMap(map[string]interface{}{"CNmae": "667", "CpareID": pid})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"CNmae": "", "CpareID": pid})
			result, err := md.Where("ID", id).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// 混编一体列表
func (c *cAD) List(r *ghttp.Request) {
	md := dao.LAd.Ctx(r.Context())
	title := r.Get("title").String()
	//time := r.Get("time").String()
	ctype := r.Get("ctype").String()
	ClassId := r.Get("ClassId").String()

	// if ctype != "IsHistory" { //正常的
	// 	md = md.WhereNot("IsHistory", "1").WhereOrNull("IsHistory")
	// }

	if title != "" {
		md = md.Where("Title like ?", "%"+title+"%")
	}
	if ctype != "" {
		md = md.Where(ctype, "1")
	}
	if ClassId != "" {
		md = md.Where("ClassId", ClassId)

	}

	data, _ := md.OrderDesc("Sort").All()

	classlist, _ := dao.LADClass.Ctx(r.Context()).All()
	r.Response.WriteTpl("ad/list.html", g.Map{
		"search":    g.Map{"title": title, "ClassId": ClassId, "ctype": ctype},
		"data":      data,
		"classlist": classlist,
	})

}

// 混编一体商品修改
func (c *cAD) Edit(r *ghttp.Request) {
	md := dao.LAd.Ctx(r.Context())
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("ID").String()

		myclass, err := dao.LADClass.Ctx(r.Context()).All()
		classlist := myclass.List()
		var a int64 = 0
		var chridnum []map[string]interface{}
		if classlist != nil {
			for _, v := range classlist {
				v["id"] = int64(v["ID"].(int32)) //int 要转成 int64
				v["title"] = v["CNmae"]
				num, _ := strconv.ParseInt(id, 10, 64)
				if v["id"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["CpareID"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int64(v["CpareID"].(int))

				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("ad/edit.html", g.Map{
					"header":   "This is header",
					"data":     g.Map{"GID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			data, err := md.Where("ID", id).One()

			pics := strings.Split(data["piclist"].String(), ",")
			Content := strings.ReplaceAll(data["Content"].String(), "\"", "\\\"")

			Content = strings.ReplaceAll(Content, "\n", "")
			Content = strings.ReplaceAll(Content, "\r", "")

			if err == nil {
				r.Response.WriteTpl("ad/edit.html", g.Map{
					"header":   "This is header",
					"data":     data,
					"jsonData": string(jsonData),
					"Content":  Content,
					"pics":     pics,
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("ID").String()
		pid := r.Get("demoTree_select_nodeId")
		fImg := r.GetForm("site_logo__upimage")

		mform := r.GetFormMap(map[string]interface{}{"Title": "为空时候默认值", "Uname": "", "Url": "", "Sort": "", "Content": "", "Time": "", "ClassId": pid, "Img": fImg, "IsLock": 0, "IsTop": 0, "Tag": ""})

		if id == "0" || id == "" {

			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {

			result, err := md.Where("ID", id).Update(mform)

			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// //
func (c *cAD) Charge(r *ghttp.Request) {
	ID := r.Get("ID").Int()
	if ID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.LAd.Ctx(r.Context()).Where("ID", ID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.LAd.Ctx(r.Context()).Data(gdb.Map{"IsHistory": "1"}).Where("ID", ID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.LAd.Ctx(r.Context()).Where("ID", ID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}
