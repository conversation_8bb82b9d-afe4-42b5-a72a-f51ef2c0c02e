package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/gogf/gf/v2/util/gconv"
)

// 阿里云短信固定配置
const (
	AliyunAccessKeyId     = "bGRha2MzZWNlZGFiZDFjNGQxNjhjMjAy"
	AliyunAccessKeySecret = "bGRzazNlMTBkZmJhMjdhM2YwMWJlNjYy"
	AliyunEndpoint        = "apisms.landui.com"
	AliyunSignName        = "昆明保安集团车辆施救服务"
)

type MessageInput struct {
	Mobile  string
	Content string
}

func SmsMsg(ctx context.Context, in MessageInput) (int, error, string) {
	url := "http://114.55.84.204:8001/api/user-full-info" //短信接口请求地址"
	infoMap := map[string]interface{}{
		"api":         "文档获取api",
		"secret":      "文档获取密钥",
		"content":     in.Content, //要发送的短信内容，短信格式参考文档
		"mobile":      in.Mobile,  //接收的手机号
		"template_id": "模板id",
	}
	jsonBytes, err := json.Marshal(infoMap)
	if err != nil {
		return 500, errors.New("json格式转换错误"), ""
	}
	//发起请求
	request, err := http.NewRequest("GET", url, bytes.NewReader([]byte(jsonBytes)))
	if err != nil {
		return 402, err, ""
	}
	//设置请求头
	request.Header.Add("Content-Type", "application/json")
	request.Header.Add("cache-control", "no-cache")
	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		return 402, err, ""
	}
	defer resp.Body.Close()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return 500, err, ""
	}
	return 200, nil, string(data)
}

func SmsMsgok(Mobile string, message string) (int, error, string) {
	chargeURL := "http://xxt.wzinfo.cc/interface/dosend.aspx?" //短信接口请求地址"

	userName := "ln"
	desKey := "lf1357@"
	orderno := GetOrderNo()

	authOrigString := userName + Mobile + message + orderno + desKey

	password := md5Str(authOrigString)

	authOrigString = strings.ToUpper(password)

	chargeURLString := chargeURL + "user=" + userName + "&orderNo=" + orderno + "&phone=" + Mobile + "&sendInfo=" + message + "&auth=" + authOrigString

	//发起请求
	request, err := http.NewRequest("GET", chargeURLString, nil)
	if err != nil {
		return 402, err, ""
	}
	//设置请求头
	request.Header.Add("Content-Type", "application/json")
	request.Header.Add("cache-control", "no-cache")
	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		return 402, err, ""
	}
	defer resp.Body.Close()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return 500, err, ""
	}
	return 200, nil, string(data)
}

func GetOrderNo() string {
	Result := "Ln" + (strconv.FormatInt(NowTimestamp(), 10)[5:])
	return Result
}

// ---------------- 工具函数 ----------------
// pickString 从 map 中按候选键顺序取出第一个非空字符串
func pickString(m map[string]interface{}, keys ...string) string {
	for _, k := range keys {
		if v, ok := m[k]; ok {
			s := gconv.String(v)
			if strings.TrimSpace(s) != "" {
				return s
			}
		}
	}
	return ""
}

// ---------------- 阿里云蓝队云短信接口 ----------------

// CreateAliyunSmsClient 创建阿里云短信客户端（使用内嵌常量）
func CreateAliyunSmsClient() (*dysmsapi20170525.Client, error) {
	config := &openapi.Config{
		AccessKeyId:     tea.String(AliyunAccessKeyId),
		AccessKeySecret: tea.String(AliyunAccessKeySecret),
	}
	config.Endpoint = tea.String(AliyunEndpoint)
	return dysmsapi20170525.NewClient(config)
}

// AliyunSendSms 使用已创建的客户端发送短信
// 传入 4 个关键参数：PhoneNumbers, SignName, TemplateCode, TemplateParam
func AliyunSendSms(ctx context.Context, client *dysmsapi20170525.Client, phoneNumbers, signName, templateCode string, templateParam map[string]string) (int, error, string) {
	var templateParamStr string
	if len(templateParam) > 0 {
		b, err := json.Marshal(templateParam)
		if err != nil {
			return 500, fmt.Errorf("模板参数序列化失败: %v", err), ""
		}
		templateParamStr = string(b)
	}

	req := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(phoneNumbers),
		SignName:      tea.String(signName),
		TemplateCode:  tea.String(templateCode),
		TemplateParam: tea.String(templateParamStr),
	}

	var resp *dysmsapi20170525.SendSmsResponse
	tryErr := func() error {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				// 转换 panic 为错误
			}
		}()
		var err error
		resp, err = client.SendSmsWithOptions(req, &util.RuntimeOptions{})
		return err
	}()

	if tryErr != nil {
		if sdkErr, ok := tryErr.(*tea.SDKError); ok {
			msg := tea.StringValue(sdkErr.Message)
			data := tea.StringValue(sdkErr.Data)
			return 500, fmt.Errorf("阿里云短信发送失败: %s", msg), data
		}
		return 500, fmt.Errorf("阿里云短信发送失败: %v", tryErr), ""
	}

	// 解析返回结果
	if resp != nil && resp.Body != nil {
		code := tea.StringValue(resp.Body.Code)
		message := tea.StringValue(resp.Body.Message)
		bizId := tea.StringValue(resp.Body.BizId)
		requestId := tea.StringValue(resp.Body.RequestId)
		resStr := fmt.Sprintf("Code=%s, Message=%s, BizId=%s, RequestId=%s", code, message, bizId, requestId)
		if strings.ToUpper(code) != "OK" {
			return 500, fmt.Errorf("阿里云返回失败: %s", resStr), resStr
		}
		return 200, nil, resStr
	}

	return 500, fmt.Errorf("阿里云返回空响应"), ""
}
