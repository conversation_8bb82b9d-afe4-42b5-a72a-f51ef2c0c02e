// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Admin is the golang structure of table Admin for DAO operations like Where/Data.
type Admin struct {
	g.Meta            `orm:"table:Admin, do:true"`
	Uid               interface{} //
	Uopenid           interface{} //
	Uwxopenid         interface{} //
	Uwxtel            interface{} //
	Uwxhead           interface{} //
	Uuniid            interface{} //
	Uguid             interface{} //
	Uparentid         interface{} //
	Uindirectparentid interface{} //
	Uwx               interface{} //
	Uzfb              interface{} //
	Utel              interface{} //
	Umima             interface{} //
	Utouxiang         interface{} //
	Uname             interface{} //
	Uheadimg          interface{} //
	Unickname         interface{} //
	Utruename         interface{} //
	Ustate            interface{} //
	Ismember          interface{} //
	Ustateendtime     *gtime.Time //
	Logintime         *gtime.Time //
	Regtime           *gtime.Time //
	Ujifen            interface{} //
	Utype             interface{} //
	Upower            interface{} //
	Ucdpower          interface{} //
	Usex              interface{} //
	Uprovince         interface{} //
	Issh              interface{} //
	Ispc              interface{} //
}
