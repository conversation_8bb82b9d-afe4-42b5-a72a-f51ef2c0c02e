﻿/*******************************************************************************
* KindEditor - WYSIWYG HTML Editor for Internet
* Copyright (C) 2006-2011 kindsoft.net
*
* <AUTHOR> <<EMAIL>>
* @site http://www.kindsoft.net/
* @licence http://www.kindsoft.net/license.php
* Arabic Translation By daif alotaibi (http://daif.net/)
*******************************************************************************/

KindEditor.lang({
    source: 'عرض المصدر',
    preview: 'معاينة الصفحة',
    undo: 'تراجع(Ctrl+Z)',
    redo: 'إعادة التراجع(Ctrl+Y)',
    cut: 'قص(Ctrl+X)',
    copy: 'نسخ(Ctrl+C)',
    paste: 'لصق(Ctrl+V)',
    plainpaste: 'لصق كنص عادي',
    wordpaste: 'لصق من مايكروسفت ورد',
    selectall: 'تحديد الكل',
    justifyleft: 'محاذاه لليسار',
    justifycenter: 'محاذاه للوسط',
    justifyright: 'محاذاه لليمين',
    justifyfull: 'محاذاه تلقائية',
    insertorderedlist: 'قائمة مرقمه',
    insertunorderedlist: 'قائمة نقطية',
    indent: 'إزاحه النص',
    outdent: 'إلغاء الازاحة',
    subscript: 'أسفل النص',
    superscript: 'أعلى النص',
    formatblock: 'Paragraph format',
    fontname: 'نوع الخط',
    fontsize: 'حجم الخط',
    forecolor: 'لون النص',
    hilitecolor: 'لون خلفية النص',
    bold: 'عريض(Ctrl+B)',
    italic: 'مائل(Ctrl+I)',
    underline: 'خط تحت النص(Ctrl+U)',
    strikethrough: 'خط على النص',
    removeformat: 'إزالة التنسيق',
    image: 'إدراج صورة',
    multiimage: 'Multi image',
    flash: 'إدراج فلاش',
    media: 'إدراج وسائط متعددة',
    table: 'إدراج جدول',
    tablecell: 'خلية',
    hr: 'إدراج خط أفقي',
    emoticons: 'إدراج وجه ضاحك',
    link: 'رابط',
    unlink: 'إزالة الرابط',
    fullscreen: 'محرر ملئ الشاشة',
    about: 'حول',
    print: 'طباعة',
    filemanager: 'مدير الملفات',
    code: 'إدراج نص برمجي',
    map: 'خرائط قووقل',
    baidumap: 'خرائط قووقل',
    lineheight: 'إرتفاع السطر',
    clearhtml: 'مسح كود HTML',
    pagebreak: 'إدراج فاصل صفحات',
    quickformat: 'تنسيق سريع',
    insertfile: 'إدراج ملف',
    template: 'إدراج قالب',
    anchor: 'رابط',
    yes: 'موافق',
    no: 'إلغاء',
    close: 'إغلاق',
    editImage: 'خصائص الصورة',
    deleteImage: 'حذفالصورة',
    editFlash: 'خصائص الفلاش',
    deleteFlash: 'حذف الفلاش',
    editMedia: 'خصائص الوسائط',
    deleteMedia: 'حذف الوسائط',
    editLink: 'خصائص الرابط',
    deleteLink: 'إزالة الرابط',
    editAnchor: 'Anchor properties',
    deleteAnchor: 'Delete Anchor',
    tableprop: 'خصائص الجدول',
    tablecellprop: 'خصائص الخلية',
    tableinsert: 'إدراج جدول',
    tabledelete: 'حذف جدول',
    tablecolinsertleft: 'إدراج عمود لليسار',
    tablecolinsertright: 'إدراج عمود لليسار',
    tablerowinsertabove: 'إدراج صف للأعلى',
    tablerowinsertbelow: 'إدراج صف للأسفل',
    tablerowmerge: 'دمج للأسفل',
    tablecolmerge: 'دمج لليمين',
    tablerowsplit: 'تقسم الصف',
    tablecolsplit: 'تقسيم العمود',
    tablecoldelete: 'حذف العمود',
    tablerowdelete: 'حذف الصف',
    noColor: 'إفتراضي',
    pleaseSelectFile: 'Please select file.',
    invalidImg: "الرجاء إدخال رابط صحيح.\nالملفات المسموح بها: jpg,gif,bmp,png",
    invalidMedia: "الرجاء إدخال رابط صحيح.\nالملفات المسموح بها: swf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb",
    invalidWidth: "العرض يجب أن يكون رقم.",
    invalidHeight: "الإرتفاع يجب أن يكون رقم.",
    invalidBorder: "عرض الحد يجب أن يكون رقم.",
    invalidUrl: "الرجاء إدخال رابط حيح.",
    invalidRows: 'صفوف غير صحيح.',
    invalidCols: 'أعمدة غير صحيحة.',
    invalidPadding: 'The padding must be number.',
    invalidSpacing: 'The spacing must be number.',
    invalidJson: 'Invalid JSON string.',
    uploadSuccess: 'تم رفع الملف بنجاح.',
    cutError: 'حاليا غير مدعومة من المتصفح, إستخدم إختصار لوحة المفاتيح (Ctrl+X).',
    copyError: 'حاليا غير مدعومة من المتصفح, إستخدم إختصار لوحة المفاتيح (Ctrl+C).',
    pasteError: 'حاليا غير مدعومة من المتصفح, إستخدم إختصار لوحة المفاتيح (Ctrl+V).',
    ajaxLoading: 'Loading ...',
    uploadLoading: 'Uploading ...',
    uploadError: 'Upload Error',
    'plainpaste.comment': 'إستخدم إختصار لوحة المفاتيح (Ctrl+V) للصق داخل النافذة.',
    'wordpaste.comment': 'إستخدم إختصار لوحة المفاتيح (Ctrl+V) للصق داخل النافذة.',
    'code.pleaseInput': 'Please input code.',
    'link.url': 'الرابط',
    'link.linkType': 'الهدف',
    'link.newWindow': 'نافذة جديدة',
    'link.selfWindow': 'نفس النافذة',
    'flash.url': 'الرابط',
    'flash.width': 'العرض',
    'flash.height': 'الإرتفاع',
    'flash.upload': 'رفع',
    'flash.viewServer': 'أستعراض',
    'media.url': 'الرابط',
    'media.width': 'العرض',
    'media.height': 'الإرتفاع',
    'media.autostart': 'تشغيل تلقائي',
    'media.upload': 'رفع',
    'media.viewServer': 'أستعراض',
    'image.remoteImage': 'إدراج الرابط',
    'image.localImage': 'رفع',
    'image.remoteUrl': 'الرابط',
    'image.localUrl': 'الملف',
    'image.size': 'الحجم',
    'image.width': 'العرض',
    'image.height': 'الإرتفاع',
    'image.resetSize': 'إستعادة الأبعاد',
    'image.align': 'محاذاة',
    'image.defaultAlign': 'الإفتراضي',
    'image.leftAlign': 'اليسار',
    'image.rightAlign': 'اليمين',
    'image.imgTitle': 'العنوان',
    'image.upload': 'أستعراض',
    'image.viewServer': 'أستعراض',
    'multiimage.uploadDesc': 'Allows users to upload <%=uploadLimit%> images, single image size not exceeding <%=sizeLimit%>',
    'multiimage.startUpload': 'Start upload',
    'multiimage.clearAll': 'Clear all',
    'multiimage.insertAll': 'Insert all',
    'multiimage.queueLimitExceeded': 'Queue limit exceeded.',
    'multiimage.fileExceedsSizeLimit': 'File exceeds size limit.',
    'multiimage.zeroByteFile': 'Zero byte file.',
    'multiimage.invalidFiletype': 'Invalid file type.',
    'multiimage.unknownError': 'Unknown upload error.',
    'multiimage.pending': 'Pending ...',
    'multiimage.uploadError': 'Upload error',
    'filemanager.emptyFolder': 'فارغ',
    'filemanager.moveup': 'المجلد الأب',
    'filemanager.viewType': 'العرض: ',
    'filemanager.viewImage': 'مصغرات',
    'filemanager.listImage': 'قائمة',
    'filemanager.orderType': 'الترتيب: ',
    'filemanager.fileName': 'بالإسم',
    'filemanager.fileSize': 'بالحجم',
    'filemanager.fileType': 'بالنوع',
    'insertfile.url': 'الرابط',
    'insertfile.title': 'العنوان',
    'insertfile.upload': 'رفع',
    'insertfile.viewServer': 'أستعراض',
    'table.cells': 'خلايا',
    'table.rows': 'صفوف',
    'table.cols': 'أعمدة',
    'table.size': 'الأبعاد',
    'table.width': 'العرض',
    'table.height': 'الإرتفاع',
    'table.percent': '%',
    'table.px': 'px',
    'table.space': 'الخارج',
    'table.padding': 'الداخل',
    'table.spacing': 'الفراغات',
    'table.align': 'محاذاه',
    'table.textAlign': 'افقى',
    'table.verticalAlign': 'رأسي',
    'table.alignDefault': 'إفتراضي',
    'table.alignLeft': 'يسار',
    'table.alignCenter': 'وسط',
    'table.alignRight': 'يمين',
    'table.alignTop': 'أعلى',
    'table.alignMiddle': 'منتصف',
    'table.alignBottom': 'أسفل',
    'table.alignBaseline': 'Baseline',
    'table.border': 'الحدود',
    'table.borderWidth': 'العرض',
    'table.borderColor': 'اللون',
    'table.backgroundColor': 'الخلفية',
    'map.address': 'العنوان: ',
    'map.search': 'بحث',
    'baidumap.address': 'العنوان: ',
    'baidumap.search': 'بحث',
    'baidumap.insertDynamicMap': 'Dynamic Map',
    'anchor.name': 'إسم الرابط',
    'formatblock.formatBlock': {
        h1: 'عنوان 1',
        h2: 'عنوان 2',
        h3: 'عنوان 3',
        h4: 'عنوان 4',
        p: 'عادي'
    },
    'fontname.fontName': {
        'Arial': 'Arial',
        'Arial Black': 'Arial Black',
        'Comic Sans MS': 'Comic Sans MS',
        'Courier New': 'Courier New',
        'Garamond': 'Garamond',
        'Georgia': 'Georgia',
        'Tahoma': 'Tahoma',
        'Times New Roman': 'Times New Roman',
        'Trebuchet MS': 'Trebuchet MS',
        'Verdana': 'Verdana'
    },
    'lineheight.lineHeight': [
		{ '1': 'إرتفاع السطر 1' },
		{ '1.5': 'إرتفاع السطر 1.5' },
		{ '2': 'إرتفاع السطر 2' },
		{ '2.5': 'إرتفاع السطر 2.5' },
		{ '3': 'إرتفاع السطر 3' }
    ],
    'template.selectTemplate': 'قالب',
    'template.replaceContent': 'إستبدال المحتوى الحالي',
    'template.fileList': {
        '1.html': 'صورة ونص',
        '2.html': 'جدول',
        '3.html': 'قائمة'
    }
}, 'ar');

KindEditor.each(KindEditor.options.items, function (i, name) {
    if (name == 'baidumap') {
        KindEditor.options.items[i] = 'map';
    }
});
KindEditor.options.langType = 'ar';