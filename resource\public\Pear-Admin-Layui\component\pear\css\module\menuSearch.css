/* 搜索面板 */
.menu-search-content .layui-input {
	padding-left: 30px;
}

.menu-search-content .layui-input:focus {
	border: 1px solid var(--global-primary-color)!important;
	box-shadow: none;
}

.menu-search-content {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

.menu-search-input-wrapper {
	width: 100%;
	padding: 15px 15px;
}

.menu-search-no-data {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 122px;
	align-items: center;
}

.menu-search-list {
	width: 100%;
	padding: 5px 15px;
}

.menu-search-list li {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: nowrap;
	height: 50px;
	margin-bottom: 8px;
	padding: 0px 10px;
	color: currentColor;
	font-size: 14px;
	border-radius: 4px;
	box-shadow: 0 1px 3px #d4d9e1;
	cursor: pointer;
	background-color: #fff;
}

.menu-search-list li.this,
.menu-search-list li:hover {
	background-color: var(--global-primary-color);
	color: white;
}
.menu-search-tips {
    margin-bottom: 15px;
    padding: 0 15px;
    width: 100%
}

.menu-search-tips>div {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 12px;
}

.menu-search-tips .mr-1 {
    margin-right: 4px;
}

.menu-search-tips .mr-5 {
    margin-right: 20px;
}

.menu-search-tips .w-5 {
    width: 14px;
}

.menu-search-tips kbd {
	line-height: 1.5;
    border: 1px solid #e5e7eb;
    font-size: 10px;
    text-align: center;
    padding: 2px 6px;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    border-radius: 5px;
}