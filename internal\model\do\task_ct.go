// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskCT is the golang structure of table Task_CT for DAO operations like Where/Data.
type TaskCT struct {
	g.Meta        `orm:"table:Task_CT, do:true"`
	Id            interface{} //
	StudentID     interface{} //
	TeacherID     interface{} //
	Guid          interface{} //
	CtClass       interface{} //
	CtAnswer      interface{} //
	CTTime        interface{} //
	Ctcontent     interface{} //
	CtUID         interface{} //
	CTstat        interface{} //
	Feedback      interface{} //
	JZView        interface{} //
	JZFirstTime   *gtime.Time //
	JZContent     interface{} //
	ManageContent interface{} //
	ManageTime    *gtime.Time //
	ManageUID     interface{} //
	TaskGuid      interface{} //
	CTnum         interface{} //
	CTimg         interface{} //
	Ctai          interface{} //
}
