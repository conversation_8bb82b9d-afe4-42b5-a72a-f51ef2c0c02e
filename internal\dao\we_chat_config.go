// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalWeChatConfigDao is internal type for wrapping internal DAO implements.
type internalWeChatConfigDao = *internal.WeChatConfigDao

// weChatConfigDao is the data access object for table WeChatConfig.
// You can define custom methods on it to extend its functionality as you wish.
type weChatConfigDao struct {
	internalWeChatConfigDao
}

var (
	// WeChatConfig is globally public accessible object for table WeChatConfig operations.
	WeChatConfig = weChatConfigDao{
		internal.NewWeChatConfigDao(),
	}
)

// Fill with you ideas below.
