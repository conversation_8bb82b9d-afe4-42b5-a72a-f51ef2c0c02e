// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalSJfDao is internal type for wrapping internal DAO implements.
type internalSJfDao = *internal.SJfDao

// sJfDao is the data access object for table S_JF.
// You can define custom methods on it to extend its functionality as you wish.
type sJfDao struct {
	internalSJfDao
}

var (
	// SJf is globally public accessible object for table S_JF operations.
	SJf = sJfDao{
		internal.NewSJfDao(),
	}
)

// Fill with you ideas below.
