// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserYgpd is the golang structure for table User_ygpd.
type UserYgpd struct {
	Uid               int         `json:"UID"               orm:"UID"               ` //
	UOpenID           string      `json:"UOpenID"           orm:"UOpenID"           ` //
	UGuid             string      `json:"UGuid"             orm:"UGuid"             ` //
	UparentId         string      `json:"UparentId"         orm:"UparentId"         ` //
	UIndirectParentId string      `json:"UIndirectParentId" orm:"UIndirectParentId" ` //
	Uwx               string      `json:"Uwx"               orm:"Uwx"               ` //
	Uzfb              string      `json:"Uzfb"              orm:"Uzfb"              ` //
	UTel              string      `json:"UTel"              orm:"UTel"              ` //
	Umima             string      `json:"Umima"             orm:"Umima"             ` //
	Utouxiang         string      `json:"Utouxiang"         orm:"Utouxiang"         ` //
	Uname             string      `json:"Uname"             orm:"Uname"             ` //
	UHeadImg          string      `json:"UHeadImg"          orm:"UHeadImg"          ` //
	UNickName         string      `json:"UNickName"         orm:"UNickName"         ` //
	UTrueName         string      `json:"UTrueName"         orm:"UTrueName"         ` //
	IsMember          string      `json:"isMember"          orm:"isMember"          ` //
	Ustate            int         `json:"Ustate"            orm:"Ustate"            ` //
	Utype             string      `json:"Utype"             orm:"Utype"             ` //
	UstateEndTime     string      `json:"UstateEndTime"     orm:"UstateEndTime"     ` //
	LoginTime         *gtime.Time `json:"LoginTime"         orm:"LoginTime"         ` //
	RegTime           *gtime.Time `json:"RegTime"           orm:"RegTime"           ` //
	Ujifen            int         `json:"Ujifen"            orm:"Ujifen"            ` //
	Upower            string      `json:"Upower"            orm:"Upower"            ` //
	USex              string      `json:"USex"              orm:"USex"              ` //
	UProvince         string      `json:"UProvince"         orm:"UProvince"         ` //
	USession          string      `json:"USession"          orm:"USession"          ` //
	SessionKey        string      `json:"SessionKey"        orm:"SessionKey"        ` //
	Uwxapp            string      `json:"uwxapp"            orm:"uwxapp"            ` //
}
