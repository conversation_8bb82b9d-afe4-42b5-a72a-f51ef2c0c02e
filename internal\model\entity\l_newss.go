// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// LNewss is the golang structure for table L_Newss.
type LNewss struct {
	Nid         int         `json:"nid"         orm:"nid"         ` //
	ClassId     int         `json:"classId"     orm:"classId"     ` //
	NsubclassId string      `json:"nsubclassId" orm:"nsubclassId" ` //
	Nsubnid     string      `json:"nsubnid"     orm:"nsubnid"     ` //
	Title       string      `json:"title"       orm:"title"       ` //
	Stitle      string      `json:"stitle"      orm:"stitle"      ` //
	Author      string      `json:"author"      orm:"author"      ` //
	Teviewer    string      `json:"teviewer"    orm:"teviewer"    ` //
	From        string      `json:"from"        orm:"from"        ` //
	Tag         string      `json:"tag"         orm:"tag"         ` //
	Zhaiyao     string      `json:"zhaiyao"     orm:"zhaiyao"     ` //
	Img         string      `json:"img"         orm:"img"         ` //
	Content     string      `json:"content"     orm:"content"     ` //
	Click       string      `json:"click"       orm:"click"       ` //
	Url         string      `json:"url"         orm:"url"         ` //
	Time        *gtime.Time `json:"time"        orm:"time"        ` //
	Creattime   *gtime.Time `json:"creattime"   orm:"creattime"   ` //
	Istop       bool        `json:"istop"       orm:"istop"       ` //
	Ishot       bool        `json:"ishot"       orm:"ishot"       ` //
	Isslide     bool        `json:"isslide"     orm:"isslide"     ` //
	Islock      bool        `json:"islock"      orm:"islock"      ` //
	Isred       bool        `json:"isred"       orm:"isred"       ` //
	Img2        string      `json:"img2"        orm:"img2"        ` //
	Piclist     string      `json:"piclist"     orm:"piclist"     ` //
	Piclisttag  string      `json:"piclisttag"  orm:"piclisttag"  ` //
	Fujian      string      `json:"fujian"      orm:"fujian"      ` //
	Isok        int         `json:"isok"        orm:"isok"        ` //
	Txtaddress  string      `json:"txtaddress"  orm:"txtaddress"  ` //
	Ishistory   bool        `json:"Ishistory"   orm:"Ishistory"   ` //
	Fujianpdf   string      `json:"fujianpdf"   orm:"fujianpdf"   ` //
	Fwzh        string      `json:"fwzh"        orm:"fwzh"        ` //
	Fwsybh      string      `json:"fwsybh"      orm:"fwsybh"      ` //
	Fwcwdate    *gtime.Time `json:"fwcwdate"    orm:"fwcwdate"    ` //
	Fwjg        string      `json:"fwjg"        orm:"fwjg"        ` //
	Fwsy        string      `json:"fwsy"        orm:"fwsy"        ` //
	Uid         string      `json:"uid"         orm:"uid"         ` //
	Toleid      string      `json:"toleid"      orm:"toleid"      ` //
	Nickname    string      `json:"nickname"    orm:"nickname"    ` //
	Uidedit     string      `json:"uidedit"     orm:"uidedit"     ` //
	Nguid       string      `json:"nguid"       orm:"nguid"       ` //
	Edittime    *gtime.Time `json:"edittime"    orm:"edittime"    ` //
	HistoryTime *gtime.Time `json:"historyTime" orm:"historyTime" ` //
}
