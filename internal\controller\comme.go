package controller

import (
	"50go/internal/utils"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"runtime"
	"strconv"
	"strings"
	"time"

	tree "50go/internal/utils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/load"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/tiger1103/gfast/v3/api/v1/system"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

var Comme cComme

func init() {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.New(gtime.Now().Time.In(loc))
	Comme = cComme{
		startTime: mytime,
	}
}

type cComme struct {
	startTime *gtime.Time
}

// 获得菜单
func (c *cComme) Getmenu(r *ghttp.Request) {

	content, err := os.ReadFile("./manifest/config/menu.json")
	if err != nil {
		fmt.Println("Error when opening file: ", err)
	}
	powerok := true

	//strings := []string{}
	//items := []string{}
	var payload []map[string]interface{}
	err = json.Unmarshal(content, &payload)
	if powerok {
		fmt.Println("power ok")
		power, _ := r.Session.Get("power")
		items := strings.Split(power.String(), ",")
		GetMenuChild := tree.GetMenulist(payload, items)
		r.Response.WriteJson(GetMenuChild)
	} else {

		if err != nil {
			fmt.Println("Error during Unmarshal(): ", err)
		}
		//	fmt.Println(payload)
		r.Response.WriteJson(payload)
	}

}

// 方法
func (c *cComme) Upload(r *ghttp.Request) {
	file := r.GetUploadFiles("file")
	//utils.SaveImage()
	imgUrl, code := utils.UpImg("/uoload", file)
	r.Response.WriteJson(g.Map{
		"code": code,
		"msg":  "成功",
		"data": imgUrl,
	})

}

func (c *cComme) UploadImg(req *ghttp.Request) {
	//file := r.GetUploadFiles("file")
	//utils.SaveImage()
	//imgUrl, err := utils.UpImg("/uoload", file)
	// if err == nil {
	imgUrl, _ := utils.Upload(req)
	req.Response.WriteJson(g.Map{
		"code": 200,
		"msg":  "",
		"data": imgUrl,
	})
	// }

}

func (c *cComme) UploadFile(r *ghttp.Request) {
	//file := r.GetUploadFiles("file")
	//utils.SaveImage()
	//imgUrl, err := utils.UpImg("/uoload", file)
	// if err == nil {
	imgUrl, code := utils.Upload(r)
	r.Response.WriteJson(g.Map{
		"error": code,
		"msg":   "成功",
		"url":   imgUrl,
	})
}

// //混编一体edit加载
func (c *cComme) SystemEdit(r *ghttp.Request) {

	path := "./manifest/config/system.json"

	// /// ./是你当前的工程目录，并不是该go文件所对应的目录。
	if r.Request.Method == "GET" { //加载使用赋值用
		content, _ := os.ReadFile(path)
		var thesystem map[string]interface{}
		err := json.Unmarshal(content, &thesystem)
		if err != nil {
			fmt.Println("Error when opening file: ", err)
		}
		if err == nil {
			r.Response.WriteTpl("manage/system.html", g.Map{
				"thesystem": thesystem,
			})
		}
	} else { //提交表单用

		logo := r.GetForm("site_logo__upimage")

		conten := r.GetFormMap(map[string]interface{}{"logo": logo, "name": 0, "type": "", "tel": "", "tag": ""})

		file, err := os.OpenFile(path, os.O_CREATE|os.O_RDWR, 0666)
		if err != nil {
			//log.Fatal(err)
		}

		// 颜氏函数，为了避免文件没有关闭
		defer func(file *os.File) {
			err := file.Close()
			if err != nil {
				log.Fatal(err)
			}
		}(file)

		// 传入的conten是结构体的格式，需要转换为
		// 结构体转为json格式
		data, err := json.Marshal(conten)
		_, err = file.Write(data)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": "",
			})
		}

	}
}

func (c *cComme) SysInfo(req *ghttp.Request) {

	cpuNum := runtime.NumCPU() //核心数
	var cpuUsed float64 = 0    //用户使用率
	var cpuAvg5 float64 = 0    //CPU负载5
	var cpuAvg15 float64 = 0   //当前空闲率

	cpuInfo, err := cpu.Percent(time.Duration(time.Second), false)
	if err == nil {
		cpuUsed, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cpuInfo[0]), 64)
	}

	loadInfo, err := load.Avg()
	if err == nil {
		cpuAvg5, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", loadInfo.Load5), 64)
		cpuAvg15, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", loadInfo.Load5), 64)
	}

	var memTotal uint64 = 0  //总内存
	var memUsed uint64 = 0   //总内存  := 0 //已用内存
	var memFree uint64 = 0   //剩余内存
	var memUsage float64 = 0 //使用率

	v, err := mem.VirtualMemory()
	if err == nil {
		memTotal = v.Total
		memUsed = v.Used
		memFree = memTotal - memUsed
		memUsage, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", v.UsedPercent), 64)
	}

	var goTotal uint64 = 0  //go分配的总内存数
	var goUsed uint64 = 0   //go使用的内存数
	var goFree uint64 = 0   //go剩余的内存数
	var goUsage float64 = 0 //使用率

	var gomem runtime.MemStats
	runtime.ReadMemStats(&gomem)
	goUsed = gomem.Sys
	goUsage = gconv.Float64(fmt.Sprintf("%.2f", gconv.Float64(goUsed)/gconv.Float64(memTotal)*100))
	sysComputerIp := "" //服务器IP

	ip, err := libUtils.GetLocalIP()
	if err == nil {
		sysComputerIp = ip
	}

	sysComputerName := "" //服务器名称
	sysOsName := ""       //操作系统
	sysOsArch := ""       //系统架构

	sysInfo, err := host.Info()

	if err == nil {
		sysComputerName = sysInfo.Hostname
		sysOsName = sysInfo.OS
		sysOsArch = sysInfo.KernelArch
	}

	goName := "GoLang"             //语言环境
	goVersion := runtime.Version() //版本
	gtime.Date()
	goStartTime := c.startTime //启动时间

	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)

	gt := gtime.New(mytime)
	goRunTime := gt.Timestamp() - c.startTime.Timestamp() //运行时长（秒）
	goHome := runtime.GOROOT()                            //安装路径
	goUserDir := ""                                       //项目路径

	curDir, err := os.Getwd()

	if err == nil {
		goUserDir = curDir
	}

	//服务器磁盘信息
	diskList := make([]disk.UsageStat, 0)
	diskInfo, err := disk.Partitions(true) //所有分区
	if err == nil {
		for _, p := range diskInfo {
			diskDetail, err := disk.Usage(p.Mountpoint)
			if err == nil {
				diskDetail.UsedPercent, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", diskDetail.UsedPercent), 64)
				diskList = append(diskList, *diskDetail)
			}
		}
	}
	res := new(system.MonitorSearchRes)
	res = &system.MonitorSearchRes{
		"cpuNum":          cpuNum,
		"cpuUsed":         cpuUsed,
		"cpuAvg5":         gconv.String(cpuAvg5),
		"cpuAvg15":        gconv.String(cpuAvg15),
		"memTotal":        memTotal,
		"goTotal":         goTotal,
		"memUsed":         memUsed,
		"goUsed":          goUsed,
		"memFree":         memFree,
		"goFree":          goFree,
		"memUsage":        memUsage,
		"goUsage":         goUsage,
		"sysComputerName": sysComputerName,
		"sysOsName":       sysOsName,
		"sysComputerIp":   sysComputerIp,
		"sysOsArch":       sysOsArch,
		"goName":          goName,
		"goVersion":       goVersion,
		"goStartTime":     goStartTime,
		"goRunTime":       goRunTime,
		"goHome":          goHome,
		"goUserDir":       goUserDir,
		"diskList":        diskList,
	}
	req.Response.WriteTpl("home.html", g.Map{
		"code":    1,
		"msg":     "",
		"sysInfo": res,
	})

}

func (c *cComme) SysInfoLit(req *ghttp.Request) {

	cpuNum := runtime.NumCPU() //核心数
	var cpuUsed float64 = 0    //用户使用率
	var cpuAvg5 float64 = 0    //CPU负载5
	var cpuAvg15 float64 = 0   //当前空闲率

	cpuInfo, err := cpu.Percent(time.Duration(time.Second), false)
	if err == nil {
		cpuUsed, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cpuInfo[0]), 64)
	}

	loadInfo, err := load.Avg()
	if err == nil {
		cpuAvg5, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", loadInfo.Load5), 64)
		cpuAvg15, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", loadInfo.Load5), 64)
	}

	var memTotal uint64 = 0  //总内存
	var memUsed uint64 = 0   //总内存  := 0 //已用内存
	var memFree uint64 = 0   //剩余内存
	var memUsage float64 = 0 //使用率

	v, err := mem.VirtualMemory()
	if err == nil {
		memTotal = v.Total
		memUsed = v.Used
		memFree = memTotal - memUsed
		memUsage, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", v.UsedPercent), 64)
	}

	var goTotal uint64 = 0  //go分配的总内存数
	var goUsed uint64 = 0   //go使用的内存数
	var goFree uint64 = 0   //go剩余的内存数
	var goUsage float64 = 0 //使用率

	var gomem runtime.MemStats
	runtime.ReadMemStats(&gomem)
	goUsed = gomem.Sys
	goUsage = gconv.Float64(fmt.Sprintf("%.2f", gconv.Float64(goUsed)/gconv.Float64(memTotal)*100))

	res := new(system.MonitorSearchRes)
	res = &system.MonitorSearchRes{
		"cpuNum":   cpuNum,
		"cpuUsed":  cpuUsed,
		"cpuAvg5":  gconv.String(cpuAvg5),
		"cpuAvg15": gconv.String(cpuAvg15),
		"memTotal": memTotal,
		"goTotal":  goTotal,
		"memUsed":  memUsed,
		"goUsed":   goUsed,
		"memFree":  memFree,
		"goFree":   goFree,
		"memUsage": memUsage,
		"goUsage":  goUsage,
	}
	req.Response.WriteJson(g.Map{
		"code":    1,
		"msg":     "",
		"sysInfo": res,
	})

}
