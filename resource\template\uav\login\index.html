﻿
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>手机app通用模板jQuery手机验证码登录表单</title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <link href="css/style.css" rel="stylesheet" type="text/css" />
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
            border: 0;
        }

        html, body {
            margin: 0;
        }

        @-webkit-keyframes STAR-MOVE {
            from {
                background-position: 0% 0%
            }

            to {
                background-position: 200% 0%
            }
        }

        @keyframes STAR-MOVE {
            from {
                background-position: 0% 0%
            }

            to {
                background-position: 200% 0%
            }
        }

        .wall {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        div#background {
            background: black url('images/background.png')repeat-x 5% 0%;
            background-size: cover;
            -webkit-animation: STAR-MOVE 200s linear infinite;
            -moz-animation: STAR-MOVE 200s linear infinite;
            -ms-animation: STAR-MOVE 200s linear infinite;
            animation: STAR-MOVE 200s linear infinite;
        }

        div#midground {
            background: url('images/midground.png')repeat 50% 0%;
            background-size: cover;
            background-repeat: no-repeat;
            z-index: 2;
            -webkit-animation: STAR-MOVE 100s linear infinite;
            -moz-animation: STAR-MOVE 100s linear infinite;
            -ms-animation: STAR-MOVE 100s linear infinite;
            animation: STAR-MOVE 100s linear infinite;
        }

        div#foreground {
            background: url('images/foreground.png')repeat 50% 0%;
            background-size: 100%;
            z-index: 1;
            -webkit-animation: STAR-MOVE 50s linear infinite;
            -moz-animation: STAR-MOVE 50s linear infinite;
            -ms-animation: STAR-MOVE 50s linear infinite;
            animation: STAR-MOVE 50s linear infinite;
        }

        div#dl {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
            z-index: 333;
            width: 80%;
        }

        img#dl {
            background-size: cover;
            background-repeat: no-repeat;
        }
        .aui-logo{
            width:35%;
            margin:10px auto;
        }
    </style>
</head>
<body>

<section class="aui-flexView">
	<header class="aui-navBar aui-navBar-fixed">
		<a href="javascript:;" class="aui-navBar-item">
			<i class="icon icon-return"></i>
		</a>
		<div class="aui-center">
			<span class="aui-center-title"></span>
		</div>
		<a href="javascript:;" class="aui-navBar-item" >
			客服
		</a>
	</header>
    <section class="aui-scrollView">
        
        <div id="background" class="wall aui-ver-head"></div>
        <div id="midground" class="wall"></div>
        <div id="foreground" class="wall"></div>
        <div class="aui-ver-form">
            <div class="aui-logo">
                <img src="images/logo.png" alt="">
            </div>

            <h2>短信快捷登录</h2>
            <div class="aui-flex">
                <div class="aui-flex-box">
                    <i class="icon icon-phone"></i>
                    <input id="phone1" type="text" autocomplete="off" placeholder="手机号码">
                </div>
            </div>
            <div class="aui-flex">
                <div class="aui-flex-box">
                    <i class="icon icon-code"></i>
                    <input id="code1" type="text" autocomplete="off" placeholder="验证码">
                </div>
                <div class="aui-button-code">
                    <input id="btnSendCode1" type="button" class="btn btn-default" value="获取验证码" onClick="sendMessage1()" />
                </div>
            </div>
            <div class="aui-ver-button">
                <button onClick="binding()">立即登录 / 注册</button>
            </div>
            <!--<div class="aui-cell-box">
                <label class="cell-right">
                    <input type="checkbox" value="1" name="checkbox" checked="checked">
                    <i class="cell-checkbox-icon"></i>
                    <em>同意扣扣网注册协议</em>
                </label>
            </div>-->

        </div>
    
    

    </section>
</section>
</body>
<script src="https://www.jq22.com/jquery/jquery-1.10.2.js"></script>
<script type="text/javascript">
	var phoneReg = /(^1[3|4|5|7|8]\d{9}$)|(^09\d{8}$)/;
	var count = 60;
	var InterValObj1;
	var curCount1;
	function sendMessage1() {
		curCount1 = count;
		var phone = $.trim($('#phone1').val());
		if (!phoneReg.test(phone)) {
			alert(" 请输入有效的手机号码");
			return false;
		}

		$("#btnSendCode1").attr("disabled", "true");
		$("#btnSendCode1").val( + curCount1 + "秒再获取");
		InterValObj1 = window.setInterval(SetRemainTime1, 1000);

	}
	function SetRemainTime1() {
		if (curCount1 == 0) {
			window.clearInterval(InterValObj1);
			$("#btnSendCode1").removeAttr("disabled");
			$("#btnSendCode1").val("重新发送");
		}
		else {
			curCount1--;
			$("#btnSendCode1").val( + curCount1 + "秒再获取");
		}
	}

	function binding(){
		alert('请输入手机号码')
	}
</script>

</html>
