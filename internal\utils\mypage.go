package utils

import (
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gpage"
)

// pageContent customizes the page tag name.
func PageContent(page *gpage.Page) string {
	page.NextPageTag = "NextPage"
	page.PrevPageTag = "PrevPage"
	page.FirstPageTag = "HomePage"
	page.LastPageTag = "LastPage"
	pageStr := page.FirstPage()
	pageStr += page.PrevPage()
	pageStr += page.PageBar()
	pageStr += page.NextPage()
	pageStr += page.LastPage()
	return pageStr
}

// wrapContent wraps each of the page tag with html li and ul.
func WrapContent(page *gpage.Page, pagetype int) string {
	content := page.GetContent(pagetype)
	content = gstr.ReplaceByMap(content, map[string]string{
		"<span":  "<li><span",
		"/span>": "/span></li>",
		"<a":     "<li><a", "/a>": "/a></li>",
	})
	return "<ul class='layui-box layui-laypage layui-laypage-default'>" + content + "</ul>"
}

// wrapContent wraps each of the page tag with html li and ul.
func WrapContentList(page *gpage.Page, pagetype int) string {

	page.PageBarNum = 5
	content := page.GetContent(pagetype)
	// content = gstr.ReplaceByMap(content, map[string]string{
	// 	"<span":  "<li><span",
	// 	"/span>": "/span></li>",
	// 	"<a":     "<li><a", "/a>": "/a></li>",
	// })

	return "<ul class='pagination flex-rn-vs'>" + content + "</ul>"
}
