package cms

import (
	"50go/internal/dao"
	"context"
	"strings"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type Controller struct{}
type GetListReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.Meta `method:"get"  summary:"获得新闻"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}
type GetListRes struct {
	Name string
	Id   int
}

func GetPageData(r *ghttp.Request) (int, int) {
	page := r.<PERSON>uery("page", 1).Int()
	psize := r.<PERSON>uer<PERSON>("psize", 5).Int()
	return page, psize
}

// func (*Lnewss) GetOrderingSetFields() []string {
// 	return []string{
// 		df_goods_sku.Columns.CreateTime,
// 		df_goods_sku.Columns.Id,
// 	}
// }

// func (this *Lnewss) GetOrdering(r *ghttp.Request) string {
// 	orderingFields := this.GetOrderingSetFields()

// 	ordering := r.Get("ordering").String()

// 	// 判断正序或者倒序
// 	field, orderSort := GetOrderSort(ordering)

// 	var orderingFieldResult string

// 	// 遍历制定的排序的字符串
// 	// 如果获取到的请求的字符串等于排序的字符串,那么则拼接成orm的规则
// 	for _, orderingField := range orderingFields {

// 		if field == orderingField {
// 			orderingFieldResult = field + " " + orderSort
// 			break
// 		}
// 	}
// 	// 如果是空,则取默认的规则
// 	if orderingFieldResult == "" {
// 		orderingFieldResult = this.GetDefaultOrdering()
// 	}

// 	return orderingFieldResult

// }

// 规范路由
func (c *Controller) GetList(ctx context.Context, req *GetListReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	//name := r.GetQuery("name", "没参数的默认返回值") //获取name
	//	filterMap := GetFilterMap(r)
	//orderingField := GetOrdering(r)
	page, psize := GetPageData(r)
	md := dao.LNewss.Ctx(r.Context()).WhereNot("ishistory", "1").WhereOrNull("ishistory")
	ncount, err := md.Count()
	mnewss, _ := md.FieldsEx("content").Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"page":   page,
			"psize":  psize,
			"newss":  mnewss,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 规范路由
func (c *Controller) GetCale(ctx context.Context, req *GetListReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	//name := r.GetQuery("name", "没参数的默认返回值") //获取name
	//	filterMap := GetFilterMap(r)
	//orderingField := GetOrdering(r)
	page, psize := GetPageData(r)
	md := dao.LNewss.Ctx(r.Context()).WhereNot("ishistory", "1").WhereOrNull("ishistory")

	mnewss, _ := md.FieldsEx("content").Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	mysclasss := mnewss.List()

	var categoryList []map[string]interface{}
	if mysclasss != nil {
		for _, v := range mysclasss {
			v["title"] = v["title"]
			v["name"] = v["zhaiyao"]
			v["status"] = 0
			v["ratio"] = 0
			v["statusText"] = v["stitle"]

			categoryList = append(categoryList, v)
		}
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"2024-8-28": categoryList,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *Controller) GetNews(ctx context.Context, req *GetListReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	NID := r.Get("nid")
	md := dao.LNewss.Ctx(r.Context()).Where("nid", NID)
	mnewss, _ := md.One()
	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	Img := strings.Split(mnewss["img"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code":    1,
			"data":    mnewss,
			"Content": Img,
		})
	return
}

func (c *Controller) GetListv2(ctx context.Context, req *GetListReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	name := r.GetQuery("name", "没参数的默认返回值!") //获取name
	res = &GetListRes{
		Name: name.String(),
		Id:   10,
	}
	return res, gerror.NewCode(gcode.CodeNotImplemented) //两种返回方式
}

// 简化路由
// RESTFul - GET
func (c *Controller) Get(r *ghttp.Request) {
	r.Response.Write("GET")
}

// RESTFul - POST
func (c *Controller) Post(r *ghttp.Request) {
	r.Response.Write("POST")
}

// 规范路由
func (c *Controller) GetAdList(ctx context.Context, req *GetListReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	page, psize := GetPageData(r)
	md := dao.LAd.Ctx(r.Context())
	ncount, err := md.Count()
	mnewss, _ := md.FieldsEx("content").Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"page":   page,
			"psize":  psize,
			"newss":  mnewss,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *Controller) GetAd(ctx context.Context, req *GetListReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	ID := r.Get("ID")
	md := dao.LAd.Ctx(r.Context()).Where("ID", ID)
	mnewss, _ := md.One()

	Img := strings.Split(mnewss["Img"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code": 1,
			"data": mnewss,
			"Img":  Img,
		})
	return
}
