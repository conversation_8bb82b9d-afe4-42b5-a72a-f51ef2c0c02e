﻿
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width = device-width, initial-scale = 1.0, maximum-scale = 1.0, user-scalable = 0">
	
<title>登录</title>

<link href="Theme/css/base.css" rel="stylesheet">
<link href="Theme/css/login.css" rel="stylesheet">

    <style>


.loginbut{
    width: 100%;
    height: 1.6rem;
    font-size: .64rem;
    color: #fff;
    text-align: center;
    background: #ffba00;
    border-radius: .8rem;
}

.login .login-form .login-form-group input {

    font-size: .65rem;

}
.login .login-logo img {
    width: 6rem;
}
.cologreen
{
    background: #32bb1a
}
 .layui-m-layer .layui-m-layer-msg {
   top:-10rem;
   background-color:#09C1FF; 
}
 .no-content p {
 font-size: .56rem;
    color: #ffde00;
}


 /*预定须知、免责声明、服务标准*/
        .black {
            width: 100%;
            height: 100%;
            background-color: black;
            /*opacity: 0.7;*/
            position: fixed;
            left: 0;
            top: 0;
            /*z-index: 1000;*/
        }

        .statement, .standard, .cancel_box {
            width: 80%;
            height: 90%;
            position: fixed;
            left: 10%;
            bottom: 5%;
            background-color: #fff;
            z-index: 1500;
            overflow: scroll;
        }

        #statement {
            z-index: 1500;
        }

        #statement1 {
            z-index: 1499;
        }

        .statement_header, .standard_header {
            /*height: 42px;
            line-height: 42px;*/
            /*position: relative;*/
            padding:6px 0;
            color: #191919;
            font-size: 16px;
            text-align: center
        }

        .standard {
            overflow: auto
        }

        .statement_header {
            position: fixed;
            background: #fff;
            width: 80%;
        }

        .statement_bottom {
            width: 100%;
            height: 1.6rem;
            line-height: 1.6rem;
            font-size: .6rem;
            color: #fff;
            text-align: center;
            background: #81B43D;
            display: block;
        }

        .close {
            position: absolute;
            top: 20%;
            right: 5%;
            width: 1rem;
            height: 1rem;
            /* background:url(/Theme/images/shop/icon_clear.svg) no-repeat center; background-size:1rem auto; 
                width:1rem; height:1rem; position:absolute; top:.3rem; right:.3rem;*/
        }

        .statement_text, .standard_text {
            padding:2.2rem 0.2rem;
            box-sizing: border-box
        }

            .statement_text h1, .standard_text h1 {
                font-size: 14px;
                line-height: 18px;
                color: #01aff0;
            }

            .standard_text h2 {
                font-size: 15px;
                width: 100%;
                height: 26px;
                position: relative
            }

            .statement_text p, .standard_text p {
                font-size: 12px;
                line-height: 15px;
                color: #65646b;
            }

        .border_bottom {
            background-image: -webkit-linear-gradient(bottom,#d3d3d3 33.3%,transparent 66.7%);
            background-size: 100% 1px;
            background-repeat: no-repeat;
            background-position: bottom;
            position: absolute;
            height: 1px;
            width: 100%;
            left: 0;
            bottom: 0;
        }

        .statement span {
            white-space: normal !important;
        }


    </style>


</head>

<body>
<div class="login">

	<div class="login-form">
		<form runat="server">
			<h1 class="login-logo">
				<img src="<%=mimg %>">
			</h1>

            <asp:Panel ID="Panel1" runat="server">
            <div class="no-content">
                <i></i>
                <p>请扫描瑞轲雅专属二维码进入系统</p>
            </div>
        </asp:Panel>
			
              <asp:Panel ID="Panel2" runat="server">
			<div class="login-form-group">
				<label class="icon-phone">手机号</label>

		      <div class="input-group">
                   <asp:TextBox ID="Tel" runat="server" CssClass="input uname" placeholder="请输入手机号"></asp:TextBox>
		            <i class="clear-keyword"></i>
				</div>
			</div>

       
            <div class="login-form-group">
				<label class="icon-code">验证码</label>

				<div class="input-group form-code">
					<input  id="txtCode" placeholder="请输入" runat="server"  style="width:4rem">
					<i class="clear-keyword"></i>
					
					 <input type="button"  class="btn btn-default" value="获取验证码" id="btnSendCode1"  style="width:4rem"  onClick="sendMessage1()" />
				</div>
			</div>


				<div class="login-form-group">
				<label class="icon-pw">推荐码</label>

				<div class="input-group">
		
                     <asp:TextBox ID="txtUdirectParent" runat="server" CssClass="input"  ReadOnly="true"></asp:TextBox>
					<i class="clear-keyword"></i>
				</div>
			</div>
			
			       <div class="register">
                    <span>
                        <input type="checkbox" id="protocol" checked="checked"></span>
                    <span onclick="$('#statement').show();"><u>我已阅读并同意《瑞轲雅买卖协议》</u></span>
                </div>

			
			
			<div class="login-submit">

      	           <input id="LoginBUT" type="button" class="loginbut" value=" 确  定"/>
			</div>
			</asp:Panel>
		</form>
	</div>
</div>






<script src="/Theme/js/jquery-1.11.2.min.js"></script>
<script src="/js/layer-v3.1.1/layer/mobile/layer.js"></script>

    <script>

        //#region 发送短信验证码
        var phoneReg = /(^1[3|4|5|6|7|8|9]\d{9}$)|(^09\d{8}$)/;
        var count = 60; //间隔函数，1秒执行
        var InterValObj1; //timer变量，控制时间
        var curCount1;//当前剩余秒数
        /*第一*/
        function sendMessage1() {
            curCount1 = count;
            var Tel = $.trim($('#Tel').val());
            if (!phoneReg.test(Tel)) {

                layer.open({
                    content: '请输入有效的手机号码'     
                    , time: 3
                    , skin: 'msg' 
                });
          
                return false;
            }          
            
            else {
                ////向后台发送处理数据
                if (Tel == "")
                    return;


                layer.open({
                    type: 2
                    , content: '提交中...'
                });

                //设置button效果，开始计时
                $("#btnSendCode1").attr("disabled", "true");
                $("#btnSendCode1").val(+ curCount1 + "秒再获取");
                InterValObj1 = window.setInterval(SetRemainTime1, 1000); //启动计时器，1秒执行一次
                $.ajax({
                    url: '../AJAX/getSMSCode.ashx?phone=' + Tel,
                    type: 'post',
                    dataType: 'JSON',
                    timeout: 300000,
                    success: function (json) {
                        time = 0;
                        layer.closeAll()
                        if (json.code == 0) {

                            layer.open({
                                content: '发送成功,请耐心等待...'
                                , time: 3
                                , skin: 'msg'
                            });
                            $("#txtCode").focus();

                        }
                        if (json.code == 1 || json.code == 2) {

                            layer.open({
                                content: json.msg
                                , time: 3
                                , skin: 'msg'
                            });

                        }
                       
                    },
                    error: function () {
                        time = 0;
                        layer.open({
                            content: 'error'
                            , time: 3
                            , skin: 'msg'
                        });
                    }
                })
            }
        }
        function SetRemainTime1() {
            if (curCount1 == 0) {
                window.clearInterval(InterValObj1);//停止计时器
                $("#btnSendCode1").removeAttr("disabled");//启用按钮
                $("#btnSendCode1").val("重新发送");
            }
            else {
                curCount1--;
                $("#btnSendCode1").val(+ curCount1 + "秒再获取");
            }
        }
        //#endregion
        //#region 登录验证
        $("#LoginBUT").on("click", function () {
            //电话
            var Tel = $("#Tel").val();
            var myreg = /(^1[3|4|5|6|7|8|9]\d{9}$)|(^09\d{8}$)/;
            if (!myreg.test(Tel)) {
             
                layer.open({
                    content: '请填写正确的手机号'
                    , time: 3
                    , skin: 'msg'
                });


                $("#Tel").focus();
                return;
            }
            
            if (!$('#protocol').is(':checked')) {
                layer.open({
                                content: '请阅读并同意《瑞轲雅服务协议》'
                                , time: 3
                                , skin: 'msg'
                            });
                
                return;
            }

            //验证码
            var Msgcode = $("#txtCode").val();
            if (Msgcode == "") {

                layer.open({
                    content: '请填写验证码'
                    , time: 3
                    , skin: 'msg'
                });
           
                $("#txtCode").focus();
                return;
            }

            var UdirectParent = $("#txtUdirectParent").val();
            if (UdirectParent == "") {

                layer.open({
                    content: '请填推荐码'
                    , time: 3
                    , skin: 'msg'
                });

                $("#txtUdirectParent").focus();
                return;
            }

            else {
                console.log(1);
                var data = new FormData();
                data.append('Tel', Tel);
                data.append('Msgcode', Msgcode);
                data.append('type', "tel");
                data.append('UP', UdirectParent);
                $.ajax({
                    url: "../AJAX/AddUserInfo.ashx",
                    type: 'post',
                    data: data,
                    dataType: 'JSON',
                    processData: false,
                    contentType: false,
                    success: function (json) {
                        if (json.status == 1) {
                       
                            layer.open({
                                content: json.data
                                , time: 3
                                , skin: 'msg'
                            });
                            $("#txtCode").focus();


                        }
                        if (json.status == 2) {
                   

                            layer.open({
                                content: json.data
                                , time: 3
                                , skin: 'msg'
                            });
                            $("#txtCode").focus();

                        }
                        if (json.status == 3) {

                            layer.open({
                                content: json.data
                                , time: 3
                                , skin: 'msg'
                            });

                         <%--   if ("<%=Request.QueryString["reurl"] %>" != "") {
                                window.location.href = "<%=Request.QueryString["reurl"] %>";

                             }
                             else {--%>
                                 window.location.href = "../user.aspx";

                             //}
                        }
                        if (json.status == 4) {
                            layer.open({
                                content: json.data
                                , time: 3
                                , skin: 'msg'
                            });
                            $("#Tel").focus();
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        layer.open({
                            content: '出错了，请稍后再试'
                            , skin: 'msg'
                            , time: 1
                        });
                    }
                });
            }
        })
        //#endregion
    </script>
</body>
</html>
