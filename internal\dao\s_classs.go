// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalSClasssDao is internal type for wrapping internal DAO implements.
type internalSClasssDao = *internal.SClasssDao

// sClasssDao is the data access object for table S_Classs.
// You can define custom methods on it to extend its functionality as you wish.
type sClasssDao struct {
	internalSClasssDao
}

var (
	// SClasss is globally public accessible object for table S_Classs operations.
	SClasss = sClasssDao{
		internal.NewSClasssDao(),
	}
)

// Fill with you ideas below.
