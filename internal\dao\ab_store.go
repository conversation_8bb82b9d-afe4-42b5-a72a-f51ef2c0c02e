// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalABStoreDao is internal type for wrapping internal DAO implements.
type internalABStoreDao = *internal.ABStoreDao

// aBStoreDao is the data access object for table AB_Store.
// You can define custom methods on it to extend its functionality as you wish.
type aBStoreDao struct {
	internalABStoreDao
}

var (
	// ABStore is globally public accessible object for table AB_Store operations.
	ABStore = aBStoreDao{
		internal.NewABStoreDao(),
	}
)

// Fill with you ideas below.
