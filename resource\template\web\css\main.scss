@charset "UTF-8";

body {
  font-family: MicrosoftYaHei;
  background-color: #F5F5F5;
  overflow-x: hidden;
}

.page-bg {
  position: absolute;
  z-index: 1;
  display: block;
  width: 100%;
  height: auto;
}

.page-1200 {
  position: relative;
  z-index: 9;
  width: 1200px;
  margin: 0 auto;
}

.icon {
  margin-right: 5px;
  font-size: 0;
}

a:link,a:visited {
  font-size: inherit;
  color: #141414;
}

a:hover {
  font-size: inherit;
  color: #000;
  text-decoration: underline;
}

.page-header {
  padding: 20px 0;
  text-align: right;
  .logo,
  .web-name,
  .today {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    vertical-align: middle;
  }
  .logo {
    height: 18px;
    width: auto;
    font-size: 0;
  }
  .web-name {
    margin: 0 15px 0 5px;
    font-size: 18px;
    color: #FFF;
  }
  .today {
    font-size: 16px;
    color: #FFF;
  }
}

.page-title {
  margin: 50px auto 80px;
  .title-img {
    width: 100%;
    height: auto;
  }
}

.top-section {
  background: #FFF;
  border-top: 3px solid #F2E5C2;
  border-bottom: 1px solid #F2E5C2;
  .line {
    position: absolute;
    top: -15px;
    right: 0;
    height: 12px;
    width: auto;
  }
  .top-header {
    position: absolute;
    top: 12px;
    left: 30px;
    height: 22px;
    width: auto;
  }
  .swiper {
    position: relative;
    margin: 0 auto;
    width: 1100px;
    height: 40px;
    padding: 30px 40px 30px 0;
    // background: #ccc;
    // opacity: .3;
    .swiper-item {
      position: absolute;
      top: 30px;
      left: 0;
      width: 100%;
      text-align: center;
      .title {
        display: none;
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 28px;
        font-weight: bold;
        color: #000;
      }
    }
    .swiper-dots {
      position: absolute;
      right: 0;
      top: 24px;
      text-align: center;
      width: 35px;
      .prev,
      .next {
        height: 6px;
        width: auto;
      }
      .dot {
        padding: 0;
        height: 40px;
        line-height: 40px;
        font-family: Arial;
      }
    }
  }
}

.main-news-section {
  display: table;
  background: #FFF;
  .col-worker,
  .col-tabpanel,
  .col-swiper {
    display: table-cell;
    white-space: nowrap;
    vertical-align: top;
  }
  .col-tabpanel {
    width: 440px;
    padding: 25px 25px 0;
    .tabs {
      width: 440px;
      display: table;
      .item {
        display: table-cell;
        padding-bottom: 12px;
        border-bottom: 1px solid #D2DFEF;
      }
      .item {
        width: 110px;
        height: 30px;
        line-height: 30px;
        font-size: 16px;
        text-align: center;
        color: #8A8B91;
        span {
          font-size: inherit;
          color: inherit;
        }
      }
      .active {
        border-bottom: 1px solid #BD1A2D;
        font-weight: bold;
        color: #000;
      }
      .icon, span {
        font-size: 16px;
        display: inline-block;
        vertical-align: middle;
      }
      .icon {
        height: 18px;
        width: auto;
      }
    }
    .tab-conts {
      width: 440px;
      padding: 10px 0 0 0;
      ul {
        display: none;
        width: 430px;
        overflow: hidden;
        li {
          height: 40px;
          line-height: 40px;
          white-space: nowrap;
          span {
            display: inline-block;
            padding: 0 8px;;
          }
          a {
            font-size: 14px;
            color: #000;
          }
        }
      }
    }
  }
  .col-swiper {
    position: relative;
    width: 360px;
    padding-top: 25px;
    .swiper-item {
      position: absolute;
      top: 25px;
      left: 0;
      width: 340px;
      height: 270px;
      display: none;
      .img {
        display: block;
        width: 100%;
        height: 100%;
      }
      .title {
        padding-top: 15px;
        display: block;
        font-size: 16px;
        font-weight: normal!important;
        text-align: center;
        white-space: nowrap;
      }
    }
    .dots {
      position: absolute;
      top: 345px;
      width: 340px;
      text-align: center;
      font-size: 0;
      .dot {
        display: inline-block;
        width: 20px;
        height: 2px;
        font-size: 0;
        background: #E6E6E6;
      }
      .current {
        width: 30px;
        background: #075395;
      }
    }
  }
}

.col-worker {
  padding: 30px 20px 20px;
  width: 300px;
  background: #FDFBF6;
  .user-plate {
    position: relative;
    .user,
    .icon,
    .name,
    .links,
    .link,
    span {
      display: inline-block;
      vertical-align: middle;
      height: 30px;
      line-height: 30px;
    }
    .user {
      .icon {
        height: 30px;
        width: auto;
      }
      .name {
        font-size: 15px;
        span {
          font-size: inherit;
          font-weight: bold;
          color: #BD1A2D;
        }
      }
    }
    .links {
      position: absolute;
      right: 0;
      top: 0;
      text-align: right;
      .icon {
        height: 12px;
        width: auto;
      }
      .link {
        margin-left: 15px;
      }
    }
  }
  .worker-plate {
    margin: 20px auto;
    padding: 15px;
    width: 270px;
    height: 65px;
    &.red {
      background: url("../images/worker_plate_red.png") repeat;
    }
    &.orange {
      background: url("../images/worker_plate_orange.png") repeat;
    }
    .icon,
    span,
    .link {
      display: inline-block;
      vertical-align: middle;
    }
    .title {
      .icon {
        height: 30px;
        width: auto;
      }
      span {
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        font-weight: bold;
      }
    }
    .link {
      margin: 12px 25px 0 10px;
      padding-left: 10px;
      height: 20px;
      line-height: 20px;
      background-image: url(../images/icon_arrow_right.png);
      background-repeat: no-repeat;
      background-position: left center;
      font-size: 12px;
      span {
        margin-left: 10px;
        font-family: Arial;
        font-size: inherit;
        color: #BD1A2D;
      }
    }
  }
  .buttons {
    text-align: center;
    width: 300px;
    .btn {
      width: 140px;
      height: 48px;
      margin: 0 4px;
      line-height: 48px;
      display: inline-block;
      text-align: center;
      background-image: url(../images/worker_btn.png);
      background-repeat: no-repeat;
      background-position: center center;
    }
  }
}

.banners-swiper {
  position: relative;
  margin: 40px auto 0;
  .swiper-item {
    width: 1200px;
    height: 130px;
    .img {
      width: 1200px;
      height: 130px;
    }
  }
  .dots {
    position: absolute;
    right: 0;
    bottom: 0;
    .dot {
      display: inline-block;
      background: #161823;
      width: 18px;
      height: 18px;
      line-height: 18px;
      color: #FFF;
      text-align: center;
      font-family: Arial;
      font-size: 12px;
    }
  }
}

.content-panel {
  width: 1200px;
  margin: 0 auto;
  .col {
    width: 33.3333333333333333333%;
    .plate {
      position: relative;
      display: inline-block;
      margin-top: 40px;
      width: 370px;
      height: 280px;
      background: #FFF;
      .title {
        padding: 30px 0 0 30px;
        font-size: 22px;
        font-weight: bold;
        text-align-last: left;
        .icon {
          display: inline-block;
          vertical-align: middle;
          height: 28px;
          width: auto;
        }
        span {
          display: inline-block;
          vertical-align: middle;
          font-size: inherit;
          font-weight: inherit;
          color: inherit;
        }
      }
      .title-line {
        margin: 15px 0 0 30px;
        width: 40px;
        height: 2px;
        background: #BD1A2D;
      }
      .blue {
        margin-left: 28px;
        width: 30px;
        background: #075395;
      }
      .more {
        position: absolute;
        right: 30px;
        top: 30px;
        width: 20px;
        height: 20px;
        .more-img {
          width: 100%;
          height: 100%;
        }
      }
      .list {
        padding: 20px 30px 20px;
        li {
          height: 42px;
          line-height: 42px;
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          span {
            margin: 0 4px 0 0;
            display: inline-block;
          }
        }
      }
      .calendar-list {
        padding: 20px 30px 20px;
        text-align: left;
        li {
          margin-bottom: 20px;
        }
        .day {
          display: inline-block;
          vertical-align: middle;
          width: 38px;
          height: 38px;
          line-height: 38px;
          text-align: center;
          font-size: 20px;
          font-family: Arial;
          background-image: url(../images/list_day.jpeg);
          background-repeat: no-repeat;
          background-position: center center;
        }
        .tt {
          display: inline-block;
          vertical-align: middle;
          margin-left: 5px;
          .date {
            color: #075395;
            font-size: 12px;
          }
          .t {
            margin-top: 5px;
            font-size: 14px;
          }
        }
      }
    }
  }
  .col-mid {
    text-align: center;
  }
  .col-right {
    text-align: right;
  }
}

.unit-panel {
  position: relative;
  margin-top: 60px;
  .title {
    .line-left,
    .line-right {
      position: absolute;
      top: 10px;
      width: 520px;
      height: 1px;
      background: #B3B3B3;
      span {
        position: absolute;
        top: -1px;
        width: 40px;
        height: 3px;
        background: #BD1A2D;
      }
    }
    .line-left {
      left: 0;
      span {
        right: 0;
      }
    }
    .line-right {
      right: 0;
      span {
        left: 0;
      }
    }
    .tt {
      width: 100%;
      text-align: center;
      font-size: 22px;
      font-weight: bold;
    }
  }
  .unit-cont {
    width: 100%;
    padding: 20px 0;
    text-align: center;
    border-bottom: 1px solid #B3B3B3;
    .item {
      padding: 20px 25px;
      display: inline-block;
      vertical-align: middle;
      font-size: 18px;
    }
  }
}

.feature-and-info {
  display: table;
  margin-top: 60px;
  padding-bottom: 35px;
  background: #FFF;
  border-bottom: 1px solid #E5E5E5;
  .headers {
    .title {
      display: inline-block;
      vertical-align: middle;
      font-size: 22px;
      font-weight: bold;
    }
    .more {
      margin-left: 20px;
      display: inline-block;
      vertical-align: middle;
      a {
        display: inline-block;
        vertical-align: middle;
        font-size: 14px;
        color: #7F7F7F;
      }
      .more-img {
        margin-left: 5px;
        display: inline-block;
        vertical-align: middle;
        height: 14px;
      }
    }
  }
  .feature-plate {
    display: table-cell;
    width: 625px;
    padding: 35px 20px 0 35px;
    .f-banners {
      width: 100%;
      margin-top: 20px;
      .banner {
        display: inline-block;
        vertical-align: middle;
        font-size: 0;
        margin-right: 20px;
        width: 290px;
        height: 75px;
      }
    }
  }
  .info-plate {
    display: table-cell;
    .buttons {
      .btn,
      .btn .icon,
      .btn span {
        display: inline-block;
        vertical-align: middle;
      }
      .btn {
        margin-top: 25px;
        width: 170px;
        // margin-right: 10px;
      }
      .icon {
        height: 14px;
      }
      span {
        white-space: nowrap;
        font-size: 15px;
      }
    }
  }
}

.apps {
  position: relative;
  padding-bottom: 35px;
  margin-bottom: 20px;
  background: #FFF;
  .headers {
    padding: 35px 35px 0;
    .title {
      display: inline-block;
      vertical-align: middle;
      font-size: 22px;
      font-weight: bold;
    }
    .count {
      display: inline-block;
      vertical-align: middle;
      padding: 0 35px;
      font-family: Arial;
      font-size: 20px;
      color: #8D8D8D;
    }
  }
  .conts {
    position: relative;
    z-index: 1;
    height: 160px;
    overflow: hidden;
    text-align: center;
    .btn {
      display: inline-block;
      margin: 26px 26px 0;
      padding-left: 50px;
      padding-right: 20px;
      width: 270px;
      height: 50px;
      line-height: 50px;
      white-space: nowrap;
      overflow: hidden;
      background-image: url(../images/app_btn.png);
      background-repeat: no-repeat;
      font-size: 14px;
      text-align: left;
    }
  }
  .conts.show {
    height: auto;
  }
  .mask-button {
    position: absolute;
    z-index: 9;
    bottom: -15px;
    left: 0;
    width: 1200px;
    .to-hide {
      display: none;
    }
  }
  .mask-button.show {
    position: absolute;
    bottom: -18px;
  }
}

.copy {
  position: relative;
  width: 100%;
  height: 372px;
  margin-top: 100px;
  border-top: 12px solid #075395;
  .copy-top-line {
    position: absolute;
    top: -24px;
    right: 0;
    height: 12px;
  }
  .copy-bg {
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 372px;
  }
  .copy-panel {
    position: relative;
    z-index: 9;
    padding-top: 20px;
    .select-plate {
      position: relative;
      .selector {
        position: relative;
        z-index: 1;
        margin-top: 40px;
        padding-left: 80px;
        padding-right: 40px;
        width: 360px;
        height: 70px;
        line-height: 70px;
        font-size: 18px;
        background-image: url(../images/guide_select.png);
        background-repeat: no-repeat;
      }
      .options {
        position: absolute;
        z-index: 9;
        bottom: 80px;
        left: 0;
        width: 400px;
        padding: 30px 40px;
        font-size: 14px;
        background: #3b3b3b;
        a {
          display: block;
          height: 36px;
          line-height: 36px;
          color: #FFF;
        }
      }
    }
    .info-plate {
      position: absolute;
      z-index: 9;
      top: 120px;
      right: 0;
      text-align: right;
      .logo {
        padding-bottom: 20px;
        height: 50px;
      }
      .txt {
        p {
          height: 20px;
          line-height: 20px;
          font-size: 14px;
        }
      }
    }
  }
}

.list-content {
  position: relative;
  td {
    position: relative;
    vertical-align: top;
  }
  .left-td {
    width: 340px;
  }
  .side-panel {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 340px;
    background: #FFF;
    .col-worker {
      border-top: 3px solid #F2E5C2;
    }
    .menu {
      width: 300px;
      padding: 30px 20px;
      .menu-list { // ul
        padding-left: 30px;
        .menu-item { // li
          height: 30px;
          line-height: 30px;
          font-size: 16px;
          a, .icon {
            display: inline-block;
            vertical-align: middle;
          }
          .icon {
            margin-left: 5px;
          }
        }
      }
      .item-list {
        margin-left: 25px;
        .list-item {
          padding-left: 15px;
          height: 30px;
          line-height: 30px;
          border-left: 1px solid #E6E6E6;
          a {
            display: inline-block;
            height: 14px;
            line-height: 14px;
            padding: 5px 8px;
            font-size: 14px;
          }
          a.current {
            background: #BD1A2D;
            color: #FFF;
          }
        }
      }
    }
  }
  .list-panel {
    vertical-align: top;
    width: 820px;
    margin-left: 40px;
    background: #FFF;
    border-top: 3px solid #F2E5C2;
    .title {
      font-size: 28px;
      font-weight: bold;
      text-align: center;
    }
    .line {
      width: 50px;
      height: 3px;
      margin: 30px auto 40px;
      background: #BD1A2D;
    }
    .list {
      padding: 0 35px;
      li {
        position: relative;
        padding: 12px 0;
        height: 20px;
        line-height: 20px;
        font-size: 16px;
        span {
          font-size: inherit;
          margin-right: 8px;
        }
        span.date {
          position: absolute;
          right: 0;
          top: 14px;
          margin-right: 0;
          color: #7F7F7F;
        }
      }
      li.space {
        height: 0;
        margin-top: 12px;
        border-top: 1px dashed #dadada;
      }
    }
    .pagination {
      position: relative;
      padding: 30px 30px 50px;
      .icon {
        display: block;
        margin: 0 auto;
        padding-top: 8px;
        height: 14px;
      }
      a {
        display: inline-block;
        vertical-align: middle;
        width: 30px;
        height: 30px;
        line-height: 30px;
        margin: 0 5px;
        font-size: 16px;
        font-family: Arial;
        text-align: center;
        background: #dadada;
      }
      span {
        display: inline-block;
        vertical-align: middle;
        width: 30px;
        height: 30px;
        line-height: 30px;
        margin: 0 5px;
        font-size: 16px;
        font-family: Arial;
        text-align: center;
        color: #075395;
      }
      p {
        position: absolute;
        right: 30px;
        top: 38px;
        font-size: 16px;
        color: #7F7F7F;
      }
    }
  }
}

.breadcrumb {
  padding: 30px 20px;
  width: 780px;
  font-size: 14px;
  .icon {
    display: inline-block;
    vertical-align: middle;
    height: 12px;
  }
  a {
    margin-right: 5px;
    color: #075395;
  }
  span {
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
  }
}

.read-content {
  background: #FFF;
  .title {
    width: 970px;
    margin: 40px auto;
    text-align: center;
    font-size: 28px;
    font-weight: bold;
  }
  .info {
    width: 970px;
    margin: 40px auto;
    padding: 20px 0;
    text-align: center;
    border-top: 1px dashed #dadada;
    border-bottom: 1px dashed #dadada;
    .item {
      display: inline-block;
      vertical-align: middle;
      min-width: 250px;
      font-size: 14px;
      .icon {
        display: inline-block;
        vertical-align: middle;
        height: 12px;
      }
      span {
        display: inline-block;
        vertical-align: middle;
        font-size: 14px;
      }
    }
    .media {
      span {
        padding: 5px 10px;
        border: 1px solid #F5DDE0;
        background: #FCF3F4;
      }
    }
    .readed {
      min-width: 120px!important;
      padding: 5px 10px;
      border: 1px solid #D9D9D9;
      background: #F2F2F2;
    }
  }
  .article {
    width: 970px;
    margin: 40px auto 0;
    padding-bottom: 80px;
    p {
      padding-bottom: 15px;
      font-size: 18px;
      line-height: 32px;
      text-indent: 36px;
    }
  }
}