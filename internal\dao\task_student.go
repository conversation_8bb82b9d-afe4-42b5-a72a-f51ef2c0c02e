// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalTaskStudentDao is internal type for wrapping internal DAO implements.
type internalTaskStudentDao = *internal.TaskStudentDao

// taskStudentDao is the data access object for table Task_Student.
// You can define custom methods on it to extend its functionality as you wish.
type taskStudentDao struct {
	internalTaskStudentDao
}

var (
	// TaskStudent is globally public accessible object for table Task_Student operations.
	TaskStudent = taskStudentDao{
		internal.NewTaskStudentDao(),
	}
)

// Fill with you ideas below.
