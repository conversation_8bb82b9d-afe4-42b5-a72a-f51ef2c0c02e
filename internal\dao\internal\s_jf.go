// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SJfDao is the data access object for the table S_JF.
type SJfDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SJfColumns         // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SJfColumns defines and stores column names for the table S_JF.
type SJfColumns struct {
	Jid        string //
	JorderID   string //
	JFDorderID string //
	UGuid      string //
	UfdGuid    string //
	JAddTime   string //
	JFDTime    string //
	JFen       string //
	JoldFen    string //
	JGetWay    string //
	JGetWayID  string //
	IsTest     string //
	JState     string //
	IsDel      string //
}

// sJfColumns holds the columns for the table S_JF.
var sJfColumns = SJfColumns{
	Jid:        "JID",
	JorderID:   "JorderID",
	JFDorderID: "JFDorderID",
	UGuid:      "UGuid",
	UfdGuid:    "UfdGuid",
	JAddTime:   "JAddTime",
	JFDTime:    "JFDTime",
	JFen:       "JFen",
	JoldFen:    "JoldFen",
	JGetWay:    "JGetWay",
	JGetWayID:  "JGetWayID",
	IsTest:     "IsTest",
	JState:     "JState",
	IsDel:      "IsDel",
}

// NewSJfDao creates and returns a new DAO object for table data access.
func NewSJfDao(handlers ...gdb.ModelHandler) *SJfDao {
	return &SJfDao{
		group:    "default",
		table:    "S_JF",
		columns:  sJfColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SJfDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SJfDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SJfDao) Columns() SJfColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SJfDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SJfDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SJfDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
