// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SJf is the golang structure of table S_JF for DAO operations like Where/Data.
type SJf struct {
	g.Meta     `orm:"table:S_JF, do:true"`
	Jid        interface{} //
	JorderID   interface{} //
	JFDorderID interface{} //
	UGuid      interface{} //
	UfdGuid    interface{} //
	JAddTime   *gtime.Time //
	JFDTime    *gtime.Time //
	JFen       interface{} //
	JoldFen    interface{} //
	JGetWay    interface{} //
	JGetWayID  interface{} //
	IsTest     interface{} //
	JState     interface{} //
	IsDel      interface{} //
}
