// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// LFeedback is the golang structure for table L_Feedback.
type LFeedback struct {
	Fid        int    `json:"FID"        orm:"FID"        ` //
	Nid        int    `json:"NID"        orm:"NID"        ` //
	FName      string `json:"FName"      orm:"FName"      ` //
	FTel       string `json:"FTel"       orm:"FTel"       ` //
	Fqq        string `json:"FQQ"        orm:"FQQ"        ` //
	FMail      string `json:"FMail"      orm:"FMail"      ` //
	FTitle     string `json:"FTitle"     orm:"FTitle"     ` //
	FContent   string `json:"FContent"   orm:"FContent"   ` //
	FTime      string `json:"FTime"      orm:"FTime"      ` //
	FReContent string `json:"FReContent" orm:"FReContent" ` //
	FReTime    string `json:"FReTime"    orm:"FReTime"    ` //
	FLock      string `json:"FLock"      orm:"FLock"      ` //
}
