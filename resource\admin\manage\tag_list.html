<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />
</head>

<body>
<div style="padding: 16px;">

  <div class="layui-card">
    <div class="layui-card-body">

      <div class="" style="height: 34px;">
        <div class="layui-table-tool-self ">
          <button class="layui-btn layui-btn-sm" lay-event="getCheckData" onclick="add()" load><i class="pear-icon pear-icon-add"></i>添加新闻</button>
         <div class="layui-inline" title="提示" lay-event="LAYTABLE_TIPS"><i class="layui-icon layui-icon-tips"></i>
          </div>
        </div>
      </div>


      <table class="layui-hide" id="menu" lay-filter="tmenu"></table>


    </div>
  </div>


  <script type="text/html" id="TPL-treeTable-demo-tools">
    <div class="layui-btn-container">
      <a class="layui-btn layui-btn layui-btn-xs" lay-event="detail" >修改</a>
       <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="addChild">新增</a>
      <!--<a class="layui-btn layui-btn-xs" lay-event="more">更多 <i class="layui-icon layui-icon-down"></i></a> -->
    </div>
  </script>

  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>

  <script>

    layui.use(["button","treeTable","layer"], function () {
      var button = layui.button;
      var treeTable = layui.treeTable;
      var layer = layui.layer;

      button.load({
        elem: '[load]',
        time: 600,
        done: function () {
        //  popup.success("加载完成");
        }
      })

  // 渲染
   treeTable.render({
    elem: '#menu',
    id: 'mytreetable', // 自定义 id 索引
    //url: '/static/json/2/treeTable/demo-1.json', // 此处为静态模拟数据，实际使用时需换成真实接口
    data: {{.info}},  // 数据
    cols: [[{type: 'checkbox', fixed: 'left'},
        {field: 'TID', title: 'ID', width: 160},
        {field: 'TName', title: '名称'},
        {field: 'TNotice', title: ' 备注'},
        {fixed: "right", title: "操作", align: "center", toolbar: "#TPL-treeTable-demo-tools"} 
       ]],  
    });

    treeTable.expandAll('mytreetable', true); // 展开全部节点
      // 行单击事件
    treeTable.on('tool(tmenu)', function (obj) {   //tool里面的不是table的ID,而是table中设置的属性：lay-filter的值，如果没有此属性需要增加
    console.log("查看操作：" + obj.data); 
    var layEvent = obj.event; // 获得 lay-event 对应的值
    var trElem = obj.tr;
    var trData = obj.data;
    var tableId = obj.config.id;
    
    if (layEvent === "detail") {
      layer.msg("查看操作：" + trData.id);   
      layer.open({
        title: '编辑 - id:' + trData.id,
        type: 2,
        area: ['80%', '90%'],
        content: '/system/admin/tag_edit?TID=' + trData.id,
        end: function(){
          location.reload();
        }
      }) 
    } else if (layEvent === "addChild") {
      var data = { id: Date.now(), name: "新节点" };
      var newNode2 = treeTable.addNodes(tableId, {
        parentIndex: trData["LAY_DATA_INDEX"], 
        index: -1, 
        data: data
      });
    }
    });

  })

    layui.use(['jquery', 'layer'], function () {
      
      window.edit = function(obj) {
      layer.open({
        title: '编辑 - id:' + obj,
        type: 2,
        area: ['80%', '90%'],
        content: '/system/admin/tag_edit?TID=' + obj,
        end: function(){
          location.reload();
        }
      })
    }

    window.add = function(obj) {
      layer.open({
        title: '添加',
        type: 2,
        area: ['80%', '90%'],
        content: '/system/admin/tag_edit?TID=0',
        end: function(){
          location.reload();
        }
      })
    }

    })



  </script>
</body>

</html>