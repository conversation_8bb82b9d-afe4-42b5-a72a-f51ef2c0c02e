// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ABStoreDao is the data access object for the table AB_Store.
type ABStoreDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ABStoreColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ABStoreColumns defines and stores column names for the table AB_Store.
type ABStoreColumns struct {
	Sid        string //
	AreaID     string //
	Sname      string //
	Sstime     string //
	Setime     string //
	Stel       string //
	Simg       string //
	Saddress   string //
	Slatitude  string //
	Slongitude string //
	Sintroduce string //
	Stime      string //
	IsLock     string //
	SType      string //
	SPower     string //
}

// aBStoreColumns holds the columns for the table AB_Store.
var aBStoreColumns = ABStoreColumns{
	Sid:        "SID",
	AreaID:     "areaID",
	Sname:      "Sname",
	Sstime:     "Sstime",
	Setime:     "Setime",
	Stel:       "Stel",
	Simg:       "Simg",
	Saddress:   "Saddress",
	Slatitude:  "Slatitude",
	Slongitude: "Slongitude",
	Sintroduce: "Sintroduce",
	Stime:      "Stime",
	IsLock:     "IsLock",
	SType:      "SType",
	SPower:     "SPower",
}

// NewABStoreDao creates and returns a new DAO object for table data access.
func NewABStoreDao(handlers ...gdb.ModelHandler) *ABStoreDao {
	return &ABStoreDao{
		group:    "default",
		table:    "AB_Store",
		columns:  aBStoreColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ABStoreDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ABStoreDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ABStoreDao) Columns() ABStoreColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ABStoreDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ABStoreDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ABStoreDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
