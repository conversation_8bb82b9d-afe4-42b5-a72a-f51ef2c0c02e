package hello

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type CommonPaginationReq struct {
	g.<PERSON>a `method:"get"  summary:"标准接口测试"`
	Page   int `json:"page" in:"query" d:"1"  v:"min:0#分页号码错误" dc:"分页号码，默认1"`
	Size   int `json:"size" in:"query" d:"10" v:"max:50#分页数量最大50条" dc:"分页数量，最大50"`
}

type CommonPaginationRes struct {
	Total int    `dc:"总数"`
	wdata string `sc:"内容"`
}

type WelcomeInfo struct {
	Message string `json:"message"`
}

func (c *WelcomeInfo) Welcome(ctx context.Context, req *CommonPaginationReq) (res *CommonPaginationRes, err error) {

	//r := g.RequestFromCtx(ctx)
	var welcomeInfo WelcomeInfo
	welcomeInfo.Message = "Welcome to goframe!"
	res = &CommonPaginationRes{
		Total: 18,
		wdata: "其他",
	}

	//r.Response.WriteJson(g.Map{"welcomeInfo": welcomeInfo, "Page": page, size})
	return res, err
}
