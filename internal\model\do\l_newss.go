// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// LNewss is the golang structure of table L_Newss for DAO operations like Where/Data.
type LNewss struct {
	g.Meta      `orm:"table:L_Newss, do:true"`
	Nid         interface{} //
	ClassId     interface{} //
	NsubclassId interface{} //
	Nsubnid     interface{} //
	Title       interface{} //
	Stitle      interface{} //
	Author      interface{} //
	Teviewer    interface{} //
	From        interface{} //
	Tag         interface{} //
	Zhaiyao     interface{} //
	Img         interface{} //
	Content     interface{} //
	Click       interface{} //
	Url         interface{} //
	Time        *gtime.Time //
	Creattime   *gtime.Time //
	Istop       interface{} //
	Ishot       interface{} //
	Isslide     interface{} //
	Islock      interface{} //
	Isred       interface{} //
	Img2        interface{} //
	Piclist     interface{} //
	Piclisttag  interface{} //
	Fujian      interface{} //
	Isok        interface{} //
	Txtaddress  interface{} //
	Ishistory   interface{} //
	Fujianpdf   interface{} //
	Fwzh        interface{} //
	Fwsybh      interface{} //
	Fwcwdate    *gtime.Time //
	Fwjg        interface{} //
	Fwsy        interface{} //
	Uid         interface{} //
	Toleid      interface{} //
	Nickname    interface{} //
	Uidedit     interface{} //
	Nguid       interface{} //
	Edittime    *gtime.Time //
	HistoryTime *gtime.Time //
}
