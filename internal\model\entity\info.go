// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Info is the golang structure for table Info.
type Info struct {
	Id         int         `json:"ID"         orm:"ID"         ` //
	Guid       string      `json:"GUID"       orm:"GUID"       ` //
	ClassId    int         `json:"ClassId"    orm:"ClassId"    ` //
	ClassName  string      `json:"ClassName"  orm:"ClassName"  ` //
	UserID     string      `json:"UserID"     orm:"UserID"     ` //
	Uname      string      `json:"Uname"      orm:"Uname"      ` //
	Title      string      `json:"Title"      orm:"Title"      ` //
	STitle     string      `json:"sTitle"     orm:"sTitle"     ` //
	Reviewer   string      `json:"Reviewer"   orm:"Reviewer"   ` //
	From       string      `json:"From"       orm:"From"       ` //
	Tag        string      `json:"Tag"        orm:"Tag"        ` //
	Viewsin    int         `json:"Viewsin"    orm:"Viewsin"    ` //
	Reply      int         `json:"Reply"      orm:"Reply"      ` //
	Img        string      `json:"Img"        orm:"Img"        ` //
	Content    string      `json:"Content"    orm:"Content"    ` //
	Url        string      `json:"Url"        orm:"Url"        ` //
	Time       *gtime.Time `json:"Time"       orm:"Time"       ` //
	CreatTime  *gtime.Time `json:"CreatTime"  orm:"CreatTime"  ` //
	IsTop      bool        `json:"IsTop"      orm:"IsTop"      ` //
	IsHot      bool        `json:"IsHot"      orm:"IsHot"      ` //
	IsSlide    bool        `json:"IsSlide"    orm:"IsSlide"    ` //
	IsLock     bool        `json:"IsLock"     orm:"IsLock"     ` //
	IsRed      bool        `json:"IsRed"      orm:"IsRed"      ` //
	Img2       string      `json:"Img2"       orm:"Img2"       ` //
	Piclist    string      `json:"piclist"    orm:"piclist"    ` //
	PiclistTag string      `json:"piclistTag" orm:"piclistTag" ` //
	Fujian     string      `json:"fujian"     orm:"fujian"     ` //
	Isok       int         `json:"isok"       orm:"isok"       ` //
	TxtAddress string      `json:"txtAddress" orm:"txtAddress" ` //
	IsHistory  bool        `json:"IsHistory"  orm:"IsHistory"  ` //
	Fujianpdf  string      `json:"fujianpdf"  orm:"fujianpdf"  ` //
	DelTime    *gtime.Time `json:"delTime"    orm:"delTime"    ` //
}
