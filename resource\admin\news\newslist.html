<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<title>新闻管理</title>
	<link rel="stylesheet" href="/component/pear/css/pear.css" />
	<link rel="stylesheet" href="/admin/css/admin.css" />
	<link rel="stylesheet" href="/admin/css/admin.dark.css" />
	<link rel="stylesheet" href="/admin/css/variables.css" />
	<link rel="stylesheet" href="/admin/css/reset.css" />

</head>

<body class="pear-container">
	<div class="layui-card">
		<div class="layui-card-body">


			<form class="layui-form" action="">
				<div class="layui-form-pane">



					<div class="layui-form-item layui-inline">
						<label class="layui-form-label">标题</label>
						<div class="layui-input-inline" style="width: 140px;">

							<input type="text" name="title" placeholder="" class="layui-input"
								value="{{.search.title}}">
						</div>
					</div>
					<div class="layui-form-item layui-inline">
		
						<div class="layui-input-inline">
							<select name="classid">
								<option value="">所有分类</option>
								{{range $key, $value := .classlist}}
								<option value="{{$value.cid}}" {{if eq $.search.classid $value.cid }}selected{{end}}>
									{{$value.cnmae}}({{$value.cid}})</option>
								{{end}}

							</select>
						</div>
					</div>
					<div class="layui-form-item layui-inline">
				
						<div class="layui-input-inline"  style="width: 80px;">
							<input type="text" name="author" placeholder="作者" class="layui-input">
						</div>
					</div>

					<div class="layui-form-item layui-inline">
						<div class="layui-input-inline" style="width: 80px;">
							<select name="ctype">
								<option value="">属性 {{.search.ctype}}</option>
								<option value="istop" {{if eq $.search.ctype "istop" }}selected{{end}}>推荐</option>
								<option value="islock" {{if eq $.search.ctype "islock" }}selected{{end}}>锁定</option>
								<option value="isred" {{if eq $.search.ctype "isred" }}selected{{end}}>图片</option>
								<option value="ishistory" {{if eq $.search.ctype "ishistory" }}selected{{end}}>已删除
								</option>

							</select>

						</div>
					</div>
					<div class="layui-form-item layui-inline">
						<label class="layui-form-label">发布日期</label>
						<div class="layui-input-inline">
							<input type="text" name="time"  autocomplete="off" id="ID-laydate" placeholder=" - " class="layui-input"  value="{{.search.time}}" >
						</div>
					</div>

					<div class="layui-form-item layui-inline">
						<button class="layui-btn layui-btn-sm " lay-submit lay-filter="user-query">
							<i class="layui-icon layui-icon-search"></i>
							查询 
						</button>
						<button type="reset" class="layui-btn layui-btn-sm layui-btn-primary">
							<i class="layui-icon layui-icon-refresh"></i>
							重置
						</button>
					</div>

				</div>


			</form>
		</div>
	</div>
	<div class="layui-row layui-col-space15">
		<div class="layui-col-md2">
			<div class="layui-card">
				<div class="layui-card-body">
					<div id="organizationTreeContent" style="overflow: auto">
						<ul id="organizationTree" class="dtree organizationTree" data-id=""></ul>
					</div>
				</div>
			</div>
		</div>
		<div class="layui-col-md10">
			<div class="layui-card">
				<div class="layui-card-body">
					<table id="organization-table" lay-filter="organization-table" class="layui-hide layui-table">
					</table>

				</div>
			</div>
		</div>
	</div>

	<script type="text/html" id="organization-toolbar">
			<button class="layui-btn  layui-btn-sm" lay-event="add">
			<i class="layui-icon layui-icon-add-1"></i>

			新增 
		</button>

		<!-- <button class="layui-btn layui-btn-sm btn-warm" lay-event="getCheckData" onclick="add()" load
		style="background-color: #36b368;"><i class="pear-icon pear-icon-add"></i>添加新闻</button> -->

		<!-- <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="batchRemove">
			<i class="layui-icon layui-icon-delete"></i>
			删除
		</button> -->
	</script>

	<script type="text/html" id="organization-bar">
			<button class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit"><i
				class="layui-icon layui-icon-edit"></i></button>
		    <button class="layui-btn layui-btn-primary layui-border-red layui-btn-xs" lay-event="remove"><i
				class="layui-icon layui-icon-delete " style="color:#ff5722"></i></button>
	    </script>


	<script type="text/html" id="table-user-oper">
			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="detail">详情</a>
	
		
	
		</script>



	<script src="../../component/layui/layui.js"></script>
	<script src="../../component/pear/pear.js"></script>
	<script>
		layui.use(['table', 'form', 'jquery', 'dtree', 'popup',"laydate"], function () {

			var popup = layui.popup
			var laytpl = layui.laytpl
			laytpl.config({
				open: '<%',
				close: '%>'
			});
			let table = layui.table;
			let form = layui.form;
			let $ = layui.jquery;
			let dtree = layui.dtree;
			var laydate = layui.laydate;
			//let MODULE_PATH = "operate/";


			
 // 日期范围
 laydate.render({
    elem: "#ID-laydate",
    range: true,
    shortcuts: [
      {
        text: "上个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month - 1, 1),
            new Date(year, month, 0)
          ];
        }
      },
      {
        text: "这个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month, 1),
            new Date(year, month + 1, 0)
          ];
        }
      },
      {
        text: "下个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month + 1, 1),
            new Date(year, month + 2, 0)
          ];
        }
      }
    ]
  });

			var DTree = dtree.render({
				elem: "#organizationTree",
				width: "100%",
				//data: data,
				initLevel: "2", //默认展开层级为1
				line: true, // 有线树
				ficon: ["1", "-1"], // 设定一级图标样式。0表示方形加减图标，8表示小圆点图标
				icon: ["0", "8"], // 设定二级图标样式。0表示文件夹图标，5表示叶子图标
				method: 'get',
				// selectInputName: {
				//   nodeId: "nodeId",
				//     context: "context"
				// },

				data: {{.jsonData }},
			       selectInitVal: "{{.search.classid }}", // 你可以在这里指定默认值
				});


		// 

		table.render({
			elem: '#organization-table',
			//	data: {{.newssdata}},  // 数据
			url: "/system/news/get-news-list",

			cols: [[
				// { type: 'checkbox', fixed: 'left' },
				{ field: 'nid', title: 'id', width: 100 },
				{ field: 'title', title: '标题' },
			
				{ field: 'time', title: '时间' },
				{
					title: '属性', align: 'center', fixed: 'right', templet: function (d) {
						var resault = "";
						if (d.IsLock == 1) {
							resault += ` <input type="checkbox" name="CCC" title="锁定|正常" lay-skin="switch" lay-filter="aaa" value="islock" data-id="` + d.nid + `" checked>`
						} else {
							resault += ` <input type="checkbox" name="CCC" title="锁定|正常" lay-skin="switch" lay-filter="aaa" value="islock" data-id="` + d.nid + `" >`
						}
						if (d.IsTop == 1) {
							resault += ` <input type="checkbox" name="CCC" title="推荐|非荐" lay-skin="switch" lay-filter="aaa" value="istop" data-id="` + d.nid + `" checked>`
						} else {
							resault += ` <input type="checkbox" name="CCC" title="推荐|非荐" lay-skin="switch" lay-filter="aaa" value="istop" data-id="` + d.nid + `" >`
						}
						if (d.IsRed == 1) {
							resault += ` <input type="checkbox" name="CCC" title="图片|非图" lay-skin="switch" lay-filter="aaa" value="isred" data-id="` + d.nid + `" checked>`
						} else {
							resault += ` <input type="checkbox" name="CCC" title="图片|非图" lay-skin="switch" lay-filter="aaa" value="isred" data-id="` + d.nid + `" >`
						}

						return resault

					}
				},
				{ field: 'Img', title: '图片', templet: ' <img src="<%= d.Img%>" height="30px">' },
				{ title: '操作', toolbar: '#organization-bar', align: 'center', width: 130 }
			]],



			height: 'full-150',
			page: true,

			skin: 'line',
			toolbar: '#organization-toolbar',
			defaultToolbar: [{
				title: '刷新',
				layEvent: 'refresh',
				icon: 'layui-icon-refresh',
			}, 'filter', 'print', 'exports']
		});

		// 绑定节点点击事件
		dtree.on("node(organizationTree)", function (obj) {
			console.log(obj.param.nodeId); // 点击当前节点传递的参数
			if (!obj.param.leaf) {
				var $div = obj.dom;
				DTree.clickSpread($div); //调用内置函数展开节点
			} else {

				//	layer.msg("叶子节点就不展开了,刷新右侧列表");
				//table.reload("organization-table")
			}
			table.reload('organization-table', {
				where: { classid: obj.param.nodeId, },
				page: {
					curr: 1 //重新从第 1 页开始
				}
			})
		});




		table.on('tool(organization-table)', function (obj) {
			if (obj.event === 'remove') {
				window.remove(obj);
			} else if (obj.event === 'edit') {
				window.edit(obj);
			}
		});

		table.on('toolbar(organization-table)', function (obj) {
			if (obj.event === 'add') {
				window.add();
			} else if (obj.event === 'refresh') {
				window.refresh();
			} else if (obj.event === 'batchRemove') {
				window.batchRemove(obj);
			}
		});

		form.on('submit(user-query)', function (data) {
			table.reload('organization-table', {
				where: data.field
			})
			return false;
		});


		form.on('switch(aaa)', function (chkbox) {
			layer.load(2, { shade: [0.35, '#ccc'] });
			console.log(chkbox.elem); //开关是否开启，true或者false
			console.log(chkbox.elem.checked); //开关是否开启，true或者false
			console.log(chkbox.value)
			console.log(">>>", chkbox.elem.dataset['id']); //开关是否开启，true或者false
			$.ajax({
				url: '/system/news/charge?nid=' + chkbox.elem.dataset['id'],
				data: { type: chkbox.value },
				dataType: 'json',
				type: 'post',
				success: function (result) {
					layer.closeAll('loading');
					if (result.code == 200) {
						popup.success("成功", function () {
							// location.reload();
						});
					} else {
						layer.msg(result.msg, { icon: 2, time: 1000 });
					}
				}
			})
			return false;

		});



		// window.add = function () {
		// 	layer.open({
		// 		type: 2,
		// 		title: '新增',
		// 		shade: 0.1,
		// 		area: ['500px', '400px'],
		// 		content: '/system/news/edit?NID=' + 0,
		// 	});
		// }

		window.add = function (obj) {
			layer.open({
				title: '添加',
				type: 2,
				area: ['90%', '95%'],
				content: '/system/news/edit?nid=0',
				end: function () {
					// location.reload();
					table.reload('organization-table', {})
				}
			})
		}

		window.edit = function (obj) {
			layer.open({
				type: 2,
				title: '修改 - id:' + obj.data['nid'],
				shade: 0.1,
				area: ['90%', '95%'],
				content: '/system/news/edit?nid=' + obj.data['nid'],
				end: function () {
					// location.reload();
					table.reload('organization-table', {})
				}
			});
		}


		// window.remove = function (obj) {
		//     layer.confirm('确定删除吗?',{
		// 			icon: 3,
		// 			title: '提示'
		//         }, function (index) {
		//           $.ajax({
		//             url: '/system/news/charge?NID=' + obj,
		//             data:{type:"del"},
		//             dataType: 'json',
		//             type: 'post',
		//             success: function (result) {
		//                 layer.closeAll('loading');
		//                 if (result.code == 200) {
		// 					             popup.success("删除成功", function () {
		//                   location.reload();
		// 				           });
		//                 } else {
		//                     layer.msg(result.msg, { icon: 2, time: 1000 });
		//                 }
		//             }
		//         })
		//   });
		// 	};
		window.remove = function (obj) {
			layer.confirm('确定要删除这些新闻', {
				icon: 3,
				title: '提示'
			}, function (index) {
				layer.close(index);
				let loading = layer.load();
				$.ajax({
					//	url: MODULE_PATH + "remove/" + obj.data['organizationId'],
					url: '/system/news/charge?nid=' + obj.data['nid'],
					data: { type: "del" },
					dataType: 'json',
					type: 'post',
					success: function (result) {
						layer.close(loading);
						if (result.success) {
							layer.msg(result.msg, {
								icon: 1,
								time: 1000
							}, function () {
								//	obj.del();
								popup.success("删除成功", function () {
									//location.reload();
									
								});
								table.reload('organization-table', {})
							});
						} else {
							layer.msg(result.msg, {
								icon: 2,
								time: 1000
							});
						}
					}
				})
			});
		}

		window.batchRemove = function (obj) {
			let data = table.checkStatus(obj.config.id).data;
			if (data.length === 0) {
				layer.msg("未选中数据", {
					icon: 3,
					time: 1000
				});
				return false;
			}
			let ids = "";
			for (let i = 0; i < data.length; i++) {
				ids += data[i].organizationId + ",";
			}
			ids = ids.substr(0, ids.length - 1);
			layer.confirm('确定要删除这些新闻', {
				icon: 3,
				title: '提示'
			}, function (index) {
				layer.close(index);
				let loading = layer.load();
				$.ajax({
					url: MODULE_PATH + "batchRemove/" + ids,
					dataType: 'json',
					type: 'delete',
					success: function (result) {
						layer.close(loading);
						if (result.success) {
							layer.msg(result.msg, {
								icon: 1,
								time: 1000
							}, function () {
								table.reload('organization-table');
							});
						} else {
							layer.msg(result.msg, {
								icon: 2,
								time: 1000
							});
						}
					}
				})
			});
		}

		window.refresh = function (param) {
			table.reload('organization-table');
		}
			})
	</script>
</body>

</html>