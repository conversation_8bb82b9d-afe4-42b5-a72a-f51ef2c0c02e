// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SStore is the golang structure of table S_Store for DAO operations like Where/Data.
type SStore struct {
	g.Meta     `orm:"table:S_Store, do:true"`
	Sid        interface{} //
	AreaID     interface{} //
	Sname      interface{} //
	Sstime     interface{} //
	Setime     interface{} //
	Stel       interface{} //
	Simg       interface{} //
	Saddress   interface{} //
	Slatitude  interface{} //
	Slongitude interface{} //
	Sintroduce interface{} //
	Stime      *gtime.Time //
	IsLock     interface{} //
	SType      interface{} //
	SPower     interface{} //
}
