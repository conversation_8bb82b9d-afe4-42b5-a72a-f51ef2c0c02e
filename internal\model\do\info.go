// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Info is the golang structure of table Info for DAO operations like Where/Data.
type Info struct {
	g.Meta     `orm:"table:Info, do:true"`
	Id         interface{} //
	Guid       interface{} //
	ClassId    interface{} //
	ClassName  interface{} //
	UserID     interface{} //
	Uname      interface{} //
	Title      interface{} //
	STitle     interface{} //
	Reviewer   interface{} //
	From       interface{} //
	Tag        interface{} //
	Viewsin    interface{} //
	Reply      interface{} //
	Img        interface{} //
	Content    interface{} //
	Url        interface{} //
	Time       *gtime.Time //
	CreatTime  *gtime.Time //
	IsTop      interface{} //
	IsHot      interface{} //
	IsSlide    interface{} //
	IsLock     interface{} //
	IsRed      interface{} //
	Img2       interface{} //
	Piclist    interface{} //
	PiclistTag interface{} //
	Fujian     interface{} //
	Isok       interface{} //
	TxtAddress interface{} //
	IsHistory  interface{} //
	Fujianpdf  interface{} //
	DelTime    *gtime.Time //
}
