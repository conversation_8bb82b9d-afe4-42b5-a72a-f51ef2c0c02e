server:
  address:     ":8001"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
    # 静态文件根目录
  serverRoot:  "./resource/public/html"
  # 静态文件其他目录
  searchPaths: ["./resource/public/Pear-Admin-<PERSON>ui","./resource/files","./resource/template/gov","./resource/template","./resource/template/uav","./resource/template/web","./resource/public/plugin"]   #linux 需要加个. 再前面  windows 不需要
  logPath: "./log/server"                 # 日志文件存储目录路径，建议使用绝对路径。默认为空，表示关闭
  maxHeaderBytes: "20KB"
  clientMaxBodySize: "200MB"
  
logger:
  level : "all"
  stdout: true
  path: "./log/"  # 添加这行，指定日志文件路径
  # 为不同级别设置不同的输出  
  levels:
    - level: "warning"
      file: "./log/server/warning.log"
    - level: "error"
      file: "./log/server/error.log"

database:
  logger:
    path: "./log/sql"
    level: "all"
    stdout: true
  default:
    #link: "sqlite::@file(manifest/50cms.db)"
    link:  "mysql:pdstudy:kZwtTB6s8epxrTi6@tcp(43.228.77.237:20006)/pdstudy?parseTime=true&loc=Asia%2FShanghai"
    debug: true
  # h4a:
  #   link:  "mssql:YnSmes2023:12345678@tcp(43.228.77.:14203)/YnSmes?encrypt=disable"
  #   debug: true


viewer:
  paths: ["/resource/template","/resource/admin"] # 模板文件搜索目录路径，建议使用绝对路径。默认为当前程序工作路径



50cms:
  # serverroot: "/home/<USER>"
  usession: manage   #guid session名称
  uploadsroot: "/resource/files"
  uploads: "/upload/"

priceRules:
  # 价格规则"
  basePrice: 300
  baseDistance: 20
  extraPrice": 10

# 审核人员配置
audit:
  # 审核人员电话号码列表（支付成功后会发送短信通知）
  phoneNumbers:
    - "13378719129"
    - "15398500312"