package checklogin

import (
	"50go/internal/utils"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
)

// 登录验证中间件
func CheckLogin(r *ghttp.Request) {
	fmt.Println("登录验证中间件", r.Request.URL.Path)

	// 放行设置
	urlItem := []string{"/captcha", "/login"}
	if !utils.InStringArray(r.Request.URL.Path, urlItem) {
		fmt.Println("拦截==:", r.Request.URL.Path)
		if !utils.GetSessionUserLogin(r) {
			// 跳转登录页,方式：301(永久移动),308(永久重定向),307(临时重定向)
			// 重定向至登录页面
			r.Response.RedirectTo("/login")
			return
		}
	}
	// 前置中间件
	r.Middleware.Next()
}

// 认证中间件
func MiddlewareAuth(r *ghttp.Request) {
	//r.Response.ClearBuffer()
	var ctx = gctx.New()
	var usession = g.Cfg().MustGet(ctx, "50cms.usession").String()
	hasse, err := r.Session.Contains(usession)
	fmt.Println(hasse, err, r.Request.URL.Path, "session=====>", &r.Session) //false <nil>
	if hasse {
		myusession, _ := r.Session.Get(usession)
		Passport, _ := r.Session.Get("Passport")
		Nickname, _ := r.Session.Get("Nickname")
		power, _ := r.Session.Get("power")
		rolesid, _ := r.Session.Get("rolesid")

		r.Session.Set(usession, myusession)
		r.Session.Set("Passport", Passport)
		r.Session.Set("Nickname", Nickname)
		r.Session.Set("power", power)
		r.Session.Set("rolesid", rolesid)

		r.Middleware.Next()
	} else {
		fmt.Println("ExitAll", r.Request.URL.Path) //false <nil>
		r.Response.RedirectTo("/system/login")

		// 获取用错误码
		// r.Response.WriteJson(g.Map{
		// 	"code": 403,
		// 	"msg":  "您访问超时或已登出",
		// })

		r.ExitAll()
		return

	}

}
