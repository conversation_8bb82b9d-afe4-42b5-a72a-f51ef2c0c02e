<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
</head>

<body>
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">
            <div class="main-container">
                <input name="TID" type="hidden" value="{{.newss.TID}}">


                <div class="layui-form-item">
                    <label class="layui-form-label">名称（调用）:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="TName" lay-verify="required" value="{{.newss.TName}}"
                            autocomplete="off" class="layui-input" style="max-width: 300px;">
                            
                    </div>
                </div>
         
                
             <div class="layui-form-item">
                    <label class="layui-form-label">内容:</label>
                    <div class="layui-input-block">
                        <textarea  name="TContent" lay-verify="required" style="    height: 400px;"
                            autocomplete="off"  class="layui-textarea">{{.newss.TContent}}</textarea>
                    </div>
             </div>
   

            <div class="layui-form-item">
                <label class="layui-form-label">备注:</label>
                <div class="layui-input-inline">
                    <textarea  name="TNotice"  value="{{.newss.TNotice}}"
                        autocomplete="off"  class="layui-textarea"></textarea>
                </div>
            </div>
           </div>

            
            <!-- 
            <div style="overflow: auto;">
                <ul id="dept_tree" class="dtree"></ul>
            </div> -->
            <div class="bottom">
                <div class="button-container">
                    <div class="layui-btn-container layui-col-xs12">
                        <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary">复位</button>
                    </div>

                </div>
            </div>
    </form>
    <!-- {{.jsonData}} -->
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>

  
 
    <script>


        layui.use(['form', 'jquery', 'laydate'], function () {
            let form = layui.form;
            let $ = layui.jquery;
            var laydate = layui.laydate;



            form.on('submit(user-save)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                $.ajax({
                    url: '/system//admin/tag_edit',
                    data: $('#news_edit').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");

                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });


        })
    </script>
</body>

</html>