// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TaskTeacherDao is the data access object for the table Task_Teacher.
type TaskTeacherDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TaskTeacherColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TaskTeacherColumns defines and stores column names for the table Task_Teacher.
type TaskTeacherColumns struct {
	Id          string //
	Uid         string //
	Guid        string //
	OParentId   string //
	ISvipleve   string //
	OState      string //
	OType       string //
	OCreatTime  string //
	Name        string //
	Tel         string //
	Tel2        string //
	Provance    string //
	City        string //
	District    string //
	Street      string //
	ADDress     string //
	BertheDay   string //
	Content     string //
	Photo       string //
	IDpics      string //
	School      string //
	ZhuanYe     string //
	ADDlocation string //
	ADDname     string //
	ADDtitle    string //
	ADDdetail   string //
	ADDlat      string //
	ADDlon      string //
	JiGuan      string //
}

// taskTeacherColumns holds the columns for the table Task_Teacher.
var taskTeacherColumns = TaskTeacherColumns{
	Id:          "ID",
	Uid:         "UID",
	Guid:        "Guid",
	OParentId:   "OParentId",
	ISvipleve:   "ISvipleve",
	OState:      "OState",
	OType:       "OType",
	OCreatTime:  "OCreatTime",
	Name:        "Name",
	Tel:         "Tel",
	Tel2:        "Tel2",
	Provance:    "Provance",
	City:        "City",
	District:    "District",
	Street:      "Street",
	ADDress:     "ADDress",
	BertheDay:   "BertheDay",
	Content:     "Content",
	Photo:       "Photo",
	IDpics:      "IDpics",
	School:      "School",
	ZhuanYe:     "ZhuanYe",
	ADDlocation: "ADDlocation",
	ADDname:     "ADDname",
	ADDtitle:    "ADDtitle",
	ADDdetail:   "ADDdetail",
	ADDlat:      "ADDlat",
	ADDlon:      "ADDlon",
	JiGuan:      "JiGuan",
}

// NewTaskTeacherDao creates and returns a new DAO object for table data access.
func NewTaskTeacherDao(handlers ...gdb.ModelHandler) *TaskTeacherDao {
	return &TaskTeacherDao{
		group:    "default",
		table:    "Task_Teacher",
		columns:  taskTeacherColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TaskTeacherDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TaskTeacherDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TaskTeacherDao) Columns() TaskTeacherColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TaskTeacherDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TaskTeacherDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TaskTeacherDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
