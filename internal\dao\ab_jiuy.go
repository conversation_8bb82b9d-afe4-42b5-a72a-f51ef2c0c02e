// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalABJiuyDao is internal type for wrapping internal DAO implements.
type internalABJiuyDao = *internal.ABJiuyDao

// aBJiuyDao is the data access object for table AB_jiuy.
// You can define custom methods on it to extend its functionality as you wish.
type aBJiuyDao struct {
	internalABJiuyDao
}

var (
	// ABJiuy is globally public accessible object for table AB_jiuy operations.
	ABJiuy = aBJiuyDao{
		internal.NewABJiuyDao(),
	}
)

// Fill with you ideas below.
