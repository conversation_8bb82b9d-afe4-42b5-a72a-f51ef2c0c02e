<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
</head>

<body>
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">
            <div class="main-container">
                <input name="CID" id="CID" type="hidden" value="{{.newss.id}}">


                <div class="layui-form-item" id="toolbarDiv">
                    <label class="layui-form-label">上级分类:</label>
                    <div class="layui-input-inline " style="width: 50%;">                      
                        <ul id="demoTree" class="dtree layui-input" data-id="0" style="width: 50%;"></ul>                      
                    </div>
                </div>
             <div class="layui-form-item">
                    <label class="layui-form-label">标题:</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" lay-verify="required" value="{{.newss.name}}"
                            autocomplete="off" class="layui-input" style="max-width: 300px;">
                    </div>
                </div>
            </div>
            <!-- 
            <div style="overflow: auto;">
                <ul id="dept_tree" class="dtree"></ul>
            </div> -->
            <div class="bottom">
                <div class="button-container">
                    <div class="layui-btn-container layui-col-xs12">
                        <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary">复位</button>
                    </div>

                </div>
            </div>
    </form>
    <!-- {{.jsonData}} -->
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>

  
 
    <script>

        layui.use(['jquery', 'tree','dtree'], function () {
            var tree = layui.tree;
            var mdata = {{.jsonData }};
            var treeSelect= layui.treeSelect;
            var dtree = layui.dtree,
            $ = layui.jquery;
            
    var DemoTree = dtree.render({
      elem: "#demoTree",
      data: mdata, // 使用data加载
      width:"50%",
      line:true,
      selectTips: "顶级部门",
      selectInitVal: "{{.newss.pid}}", // 你可以在这里指定默认值
      select:true,
      scroll:"#toolbarDiv", // 绑定div元素
      icon: ["0","-1"]  // 显示非最后一级节点图标，隐藏最后一级节点图标
    //   ficon: ["0","7"],  // 设定一级图标样式。0表示方形加减图标，7表示文件图标
    //   skin: "layui"  // layui主题风格
     
    });
    
    // 绑定节点点击
    dtree.on("node('demoTree')" ,function(obj){
      layer.msg(JSON.stringify(obj.param));
    });

});


        layui.use(['jquery', 'upload'], function () {
            $ = layui.jquery;
            //执行实例
            var uploadInst = layui.upload.render({
                elem: '.upimg',
                url: "/system/comme/upload-img", //上传接口
                done: function (res) {
                    // 给此img设置src属性
                    if (res.code == 200) {
                        $(".myimg").attr('src', res.data);
                        $(".myimginp").val(res.data);
                    }
                    else {
                        layer.msg('上传文件失败');
                    }
                },
                error: function () {
                    layer.msg('上传文件失败');
                }
            });
        });


        layui.use(['form', 'jquery', 'laydate'], function () {
            let form = layui.form;
            let $ = layui.jquery;
            var laydate = layui.laydate;



            form.on('submit(user-save)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                $.ajax({
                    url: '/system//admin/role_edit',
                    data: $('#news_edit').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");

                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });


        })
    </script>
</body>

</html>