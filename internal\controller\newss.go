package controller

import (
	"50go/internal/dao"
	"50go/internal/utils"
	mypage "50go/internal/utils"
	tree "50go/internal/utils"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
)

var Newss = cNewss{}

type cNewss struct {
}

func CDpowers(session *ghttp.Session) []string {

	// 返回相应的 session key 的 username key 的用户名
	re_val, err := session.Get("UCDpower")
	if err != nil {
		panic(err)
	}

	fmt.Println("re_val====================", &re_val)
	return strings.Split(re_val.String(), ",")
}

func (c *cNewss) Index(r *ghttp.Request) {
	r.Response.WriteTpl("news/edit.html", g.Map{
		"ncount":  2,
		"mainTpl": "ad/index.html",
	})

}

// //混编一体list  //layui 后期加载
func (c *cNewss) NewsList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	md := dao.LNewss.Ctx(r.Context())
	title := r.Get("title").String()
	time := r.Get("time").String()
	ctype := r.Get("ctype").String()
	classid := r.Get("classid").String()

	if title != "" {
		md = md.Where("title like ?", "%"+title+"%")
	}

	if ctype != "" {
		md = md.Where(ctype, "1")
	}
	if ctype != "ishistory" { //正常的
		md = md.Wheref("(ishistory!=? or ishistory is null)", 1)
	}

	if time != "" {
		_time := strings.Split(time, " - ")
		md = md.Where("time > ?", _time[0])
		md = md.Where("time < ?", _time[1])
	}

	if classid != "" && classid != "0" {
		md = md.Where("classid", classid)
	} else {
		md = md.Where("classid IN(?)", CDpowers(r.Session))
	}

	classlist, _ := dao.LClasss.Ctx(r.Context()).Where("cid IN(?)", CDpowers(r.Session)).All()

	var a int32 = 0

	zhdatra := classlist.List()
	var chridnum []map[string]interface{}
	if zhdatra != nil {
		for _, v := range zhdatra {
			v["id"] = v["cid"].(int32) //int 要转成 int64
			v["title"] = v["cnmae"]
			num, _ := strconv.ParseInt(classid, 10, 64)
			if v["cid"] == num { //当前id 不可选
				v["disabled"] = true
			}
			if v["cpareid"] == nil {
				v["pid"] = a
			} else {
				v["pid"] = int32(v["cpareid"].(int))
			}
			chridnum = append(chridnum, v)
		}
	}
	GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
	var mytree []map[string]interface{}
	temp := make(map[string]interface{})
	temp["title"] = "全部"
	temp["id"] = a
	temp["pid"] = nil
	temp["children"] = GetRuleTreeArrayLayui
	mytree = append(mytree, temp)

	jsonData, err := json.Marshal(mytree)

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页

		pagenews, _ := md.FieldsEx("content").OrderDesc("time").Page(temppage, pageSize).All()
		newssdata, _ := json.Marshal(pagenews)

		r.Response.WriteTpl("news/newslist.html", g.Map{
			"ncount":    newsscount,
			"search":    g.Map{"title": title, "time": time, "classid": classid, "ctype": ctype},
			"newss":     pagenews,
			"newssdata": string(newssdata),
			"classlist": classlist,
			"jsonData":  string(jsonData),
			"page":      mypage.WrapContent(page, 3),
			"page2":     mypage.PageContent(page),
		})
	}
}

// 新闻列表接口类型  layui
// 左侧有树形菜单   新闻接口
func (c *cNewss) GetNewsList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}

	md := dao.LNewss.Ctx(r.Context())
	title := r.Get("title").String()
	time := r.Get("time").String()
	ctype := r.Get("ctype").String()
	classid := r.Get("classid").String()

	if classid != "" && classid != "0" {
		md = md.Where("classid", classid)
	} else {
		md = md.Where("classid IN(?)", CDpowers(r.Session))
	}
	if ctype != "ishistory" { //正常的
		md = md.Wheref("(ishistory!=? or ishistory is null)", 1)
	}
	if ctype != "" {
		md = md.Where(ctype, "1")
	}

	if title != "" {
		md = md.Where("title like ?", "%"+title+"%")
	}
	if time != "" {
		_time := strings.Split(time, " - ")
		md = md.Where("time > ?", _time[0])
		md = md.Where("time < ?", _time[1])
	}

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页
		pagenews, _ := md.FieldsEx("content").OrderDesc("time").Page(temppage, pageSize).All()

		r.Response.WriteJson(g.Map{
			"code":   0,
			"msg":    "",
			"count":  newsscount,
			"search": g.Map{"title": title, "time": time, "classid": classid, "ctype": ctype},
			"data":   pagenews,
			"page":   mypage.WrapContent(page, 3),
			"page2":  mypage.PageContent(page),
		})
	}
}

// //混编一体list  原生表格渲染
//最新新闻

func (c *cNewss) List(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}
	md := dao.LNewss.Ctx(r.Context())
	title := r.Get("title").String()
	time := r.Get("time").String()
	ctype := r.Get("ctype").String()
	classid := r.Get("classid").String()
	if ctype != "" {
		md = md.Where(ctype, "1")
	}
	if ctype != "ishistory" { //正常的
		md = md.Wheref("(ishistory!=? or ishistory is null)", 1)
	}

	if title != "" {
		md = md.Where("title like ?", "%"+title+"%")
	}

	if time != "" {
		_time := strings.Split(time, " - ")
		md = md.Where("time > ?", _time[0])
		md = md.Where("time < ?", _time[1])
	}
	if classid != "" {
		md = md.Where("classid", classid)

	}

	classlist, _ := dao.LClasss.Ctx(r.Context()).Where("cid IN(?)", CDpowers(r.Session)).All()
	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页

		pagenews, _ := md.FieldsEx("content").OrderDesc("time").Page(temppage, pageSize).All()

		r.Response.WriteTpl("news/list.html", g.Map{
			"ncount":    2,
			"search":    g.Map{"title": title, "time": time, "classid": classid, "ctype": ctype},
			"newss":     pagenews,
			"classlist": classlist,
			"page":      mypage.WrapContent(page, 3),
			"page2":     mypage.PageContent(page),
		})
	}
}

// //
func (c *cNewss) Charge(r *ghttp.Request) {
	NID := r.Get("nid").Int()
	if NID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.LNewss.Ctx(r.Context()).Where("nid", NID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"ishistory": "1"})
		result, err := dao.LNewss.Ctx(r.Context()).Data(gdb.Map{"ishistory": "1"}).Where("nid", NID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.LNewss.Ctx(r.Context()).Where("nid", NID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// //混编一体edit加载
func (c *cNewss) Edit(r *ghttp.Request) {

	md := dao.LNewss.Ctx(r.Context())
	NID := r.Get("nid").Int()
	newss, err := md.Where("nid", NID).One()

	mdclas := dao.LClasss.Ctx(r.Context())
	nclass, _ := mdclas.Fields("cnmae,cpareid,cid").All()

	if err == nil {
		pics := strings.Split(newss["piclist"].String(), ",")
		Content := strings.ReplaceAll(newss["content"].String(), "\"", "\\\"")
		Content = strings.ReplaceAll(Content, "\n", "")
		Content = strings.ReplaceAll(Content, "\r", "")

		r.Response.WriteTpl("news/edit.html", g.Map{
			"newss":   newss,
			"Content": Content,
			"nclass":  nclass,
			"pics":    pics,
		})
	}
}

// 直接用匿名函数进行路由注册
// 混编一体edit 修改添加
func (c *cNewss) Editnews(r *ghttp.Request) {

	var ctx = gctx.New()

	md := dao.LNewss.Ctx(r.Context())
	fImg := r.GetForm("site_logo__upimage")
	fpiclist := r.GetForm("site_pic__upimgs")

	usession := g.Cfg().MustGet(ctx, "50cms.usession").String()
	sessionguid, _ := r.Session.Get(usession)
	Nickname, _ := r.Session.Get("Nickname")
	sessrolesid, _ := r.Session.Get("rolesid")

	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)

	nowtime := mytime
	ID := r.GetForm("nid").String()

	if ID == "0" || ID == "" {
		GetGuid := utils.GetGuid()
		mform := r.GetFormMap(map[string]interface{}{"title": "为空时候默认值", "url": "", "nsubnid": "", "zhaiyao": "", "nguid": GetGuid, "uid": sessionguid, "roleid": sessrolesid, "nickname": Nickname, "from": "", "author": "", "content": "", "time": "", "classid": "", "img": fImg, "piclist": fpiclist, "islock": 0, "istop": 0, "isred": 0, "creattime": nowtime})

		result, err := md.Data(mform).Insert()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "添加成功",
				"data": result,
			})
		}
	} else {

		mform := r.GetFormMap(map[string]interface{}{"title": "为空时候默认值", "url": "", "nsubnid": "", "zhaiyao": "", "from": "", "author": "", "content": "", "time": "", "classid": "", "img": fImg, "piclist": fpiclist, "islock": 0, "istop": 0, "isred": 0, "uidedit": sessionguid, "edittime": nowtime})

		result, err := md.Where("nid", ID).Update(mform)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "修改成功",
				"data": result,
			})
		}
	}
}

// 混编一体Classlist
func (c *cNewss) Class_list(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	md := dao.LClasss.Ctx(r.Context())
	classs, err := md.All()
	var a int32 = 0
	var chridnum []map[string]interface{}
	zhdatra := classs.List()
	if zhdatra != nil {
		for _, v := range zhdatra {
			v["id"] = v["cid"].(int32)
			v["title"] = v["cnmae"]
			v["name"] = v["cnmae"]
			if v["cpareid"] == nil {
				v["pid"] = a
			} else {
				v["pid"] = int32(v["cpareid"].(int))
			}
			chridnum = append(chridnum, v)
		}
	}
	GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
	treeData, err := json.Marshal(GetRuleTreeArrayLayui)

	if err == nil {
		r.Response.WriteTpl("news/class_list.html", g.Map{
			"header":   "This is header",
			"info":     classs,
			"treeData": string(treeData),
		})
	}

}

//混编一体编辑Class修改

// 混编一体Classlist
func (c *cNewss) Class_edit(r *ghttp.Request) {

	md := dao.LClasss.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("cid").String()

		mdclas := dao.LClasss.Ctx(r.Context())
		nclass, err := mdclas.Fields("cnmae,cpareid,cid").All()
		var a int32 = 0

		zhdatra := nclass.List()
		var chridnum []map[string]interface{}
		if zhdatra != nil {
			for _, v := range zhdatra {
				v["id"] = v["cid"].(int64) //int 要转成 int64
				v["title"] = v["cnmae"]
				num, _ := strconv.ParseInt(id, 10, 64)
				if v["cid"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["cpareid"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int32(v["cpareid"].(int64))
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		fmt.Println("jsonData==>", string(jsonData))

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("news/class_edit.html", g.Map{
					"header":   "This is header",
					"newss":    g.Map{"cid": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			news, err := md.Where("cid", id).One()
			if err == nil {
				r.Response.WriteTpl("news/class_edit.html", g.Map{
					"header":   "This is header",
					"newss":    news,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("cid").String()
		pid := r.Get("demoTree_select_nodeId")

		if id == "0" || id == "" {
			mform := r.GetFormMap(map[string]interface{}{"cnmae": "", "cpareid": pid, "csort": "0", "cskin": "", "csubskin": ""})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"cnmae": "", "cpareid": pid, "csort": "0", "cskin": "", "csubskin": ""})
			result, err := md.Where("cid", id).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}
