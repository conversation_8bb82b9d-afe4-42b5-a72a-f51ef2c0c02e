// 获取URL参数
function getQueryParam(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}

// 显示/隐藏骨架屏
function toggleSkeleton(show) {
    document.getElementById('articleSkeleton').style.display = show ? 'block' : 'none';
    document.getElementById('articleContent').style.display = show ? 'none' : 'block';
}

// 显示/隐藏错误提示
function toggleError(show, message = '加载失败') {
    const errorTip = document.getElementById('errorTip');
    errorTip.style.display = show ? 'block' : 'none';
    if (show) {
        errorTip.querySelector('.error-message').textContent = message;
    }
}

// 格式化日期
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

// 渲染文章内容
function renderArticle(data) {
    const article = document.getElementById('articleContent');
    article.querySelector('.article-title').textContent = data.Title;
    article.querySelector('.article-date').textContent = formatDate(data.CreateTime);
    article.querySelector('.article-views').textContent = `阅读 ${data.ViewNum}`;
    article.querySelector('.article-likes').textContent = `点赞 ${data.LikeNum}`;
    
    // 渲染文章内容和图片
    const contentHtml = data.Img 
        ? `<div class="article-image"><img src="${data.Img}" alt="${data.Title}"></div>${data.Content}`
        : data.Content;
    article.querySelector('.article-content').innerHTML = contentHtml;

    // 更新当前文章ID和点赞状态
    currentArticleId = data.Id;
    isLiked = data.IsLike;
    
    // 更新点赞状态
    const likeBtn = document.querySelector('.like-btn');
    likeBtn.classList.toggle('liked', isLiked);
    document.getElementById('likeCount').textContent = data.LikeNum;
    
    // 更新页面标题
    document.title = data.Title;
    
    // 初始化图片预览
    initImagePreview();
    // 加载评论
    loadComments();
}

// 加载文章详情
async function loadArticleDetail() {
    const id = getQueryParam('id');
    if (!id) {
        toggleError(true, '无效的文章ID');
        return;
    }

    toggleSkeleton(true);
    toggleError(false);

    try {
        const response = await fetch(`${API_BASE_URL}/info/get-info?id=${id}`);
        const data = await response.json();
console.log(data)
        if (data.code === 200 && data.data) {
            // 适配新的数据结构
            const articleData = data.data;
            renderArticle({
                Title: articleData.Title,
                Content: articleData.Content,
                Img: articleData.Img,
                CreateTime: articleData.CreateTime,
                ViewNum: articleData.ViewNum || 0,
                LikeNum: articleData.LikeNum || 0,
                IsLike: articleData.IsLike || false,
                Id: articleData.Id
            });
            toggleSkeleton(false);
        } else {
            throw new Error(data.message || '获取文章详情失败');
        }
    } catch (error) {
        console.error('加载文章详情失败：', error);
        toggleError(true, '加载失败，请重试');
    }
}

// 重试加载
function retryLoad() {
    toggleError(false);
    loadArticleDetail();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', loadArticleDetail); 

let currentArticleId = '';
let isLiked = false;

// 初始化图片预览
function initImagePreview() {
    const articleContent = document.querySelector('.article-content');
    articleContent.addEventListener('click', (e) => {
        if (e.target.tagName === 'IMG') {
            showPreview(e.target.src);
        }
    });
}

// 显示图片预览
function showPreview(imgSrc) {
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImage');
    previewImg.src = imgSrc;
    preview.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

// 关闭图片预览
function closePreview() {
    const preview = document.getElementById('imagePreview');
    preview.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 处理点赞
async function handleLike() {
    if (!currentArticleId) return;
    
    try {
        const response = await fetch(`${API_BASE_URL}/info/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: currentArticleId,
                type: isLiked ? 'unlike' : 'like'
            })
        });
        
        const data = await response.json();
        if (data.code === 200) {
            isLiked = !isLiked;
            const likeBtn = document.querySelector('.like-btn');
            const likeCount = document.getElementById('likeCount');
            
            likeBtn.classList.toggle('liked');
            likeCount.textContent = parseInt(likeCount.textContent) + (isLiked ? 1 : -1);
        }
    } catch (error) {
        console.error('点赞失败：', error);
        alert('操作失败，请重试');
    }
}

// 处理分享
function handleShare() {
    if (navigator.share) {
        // 使用原生分享API
        navigator.share({
            title: document.title,
            text: document.querySelector('.article-title').textContent,
            url: window.location.href
        }).catch(console.error);
    } else {
        // 复制链接
        const dummy = document.createElement('input');
        document.body.appendChild(dummy);
        dummy.value = window.location.href;
        dummy.select();
        document.execCommand('copy');
        document.body.removeChild(dummy);
        alert('链接已复制到剪贴板');
    }
}

// 提交评论
async function submitComment() {
    const commentInput = document.getElementById('commentInput');
    const content = commentInput.value.trim();
    
    if (!content) {
        alert('请输入评论内容');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/info/comment`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                articleId: currentArticleId,
                content: content
            })
        });
        
        const data = await response.json();
        if (data.code === 200) {
            commentInput.value = '';
            await loadComments();
        } else {
            throw new Error(data.message || '评论失败');
        }
    } catch (error) {
        console.error('提交评论失败：', error);
        alert('评论失败，请重试');
    }
}

// 加载评论列表
async function loadComments() {
    try {
        const response = await fetch(`${API_BASE_URL}/info/comments?articleId=${currentArticleId}`);
        const data = await response.json();
        
        if (data.code === 200) {
            renderComments(data.data);
        }
    } catch (error) {
        console.error('加载评论失败：', error);
    }
}

// 渲染评论列表
function renderComments(comments) {
    const commentsList = document.getElementById('commentsList');
    commentsList.innerHTML = comments.map(comment => `
        <div class="comment-item">
            <div class="comment-user">
                <img src="${comment.userAvatar}" alt="用户头像" class="user-avatar">
                <span class="user-name">${comment.userName}</span>
                <span class="comment-time">${formatDate(comment.createTime)}</span>
            </div>
            <div class="comment-content">${comment.content}</div>
        </div>
    `).join('');
} 