<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />
</head>

<body>
<div style="padding: 16px;">

  <div class="layui-card">
    <div class="layui-card-body">

      <div class="" style="height: 34px;">
        <div class="layui-table-tool-self ">
          <button class="layui-btn layui-btn-sm" lay-event="getCheckData" onclick="add()" load><i class="pear-icon pear-icon-add"></i>添加用户</button>
         <div class="layui-inline" title="提示" lay-event="LAYTABLE_TIPS"><i class="layui-icon layui-icon-tips"></i>
          </div>
        </div>
      </div>


      <table class="layui-table">
        <colgroup>
          <col width="150">
          <col width="150">
          <col width="200">
          <col>
        </colgroup>
        <thead>
          <tr>
            <th>登录名</th>
            <th>密码</th>
            <th>权限</th>
            <th>修改</th>
          </tr>
        </thead>
        <tbody>
          {{range $key, $value := .info}}
          <tr>
            <td>{{$value.MName}}</td>

            <td>{{$value.MPWD}}</td>
            <td>{{$value.MPower}}</td>

            <td><a class="layui-btn layui-btn-xs" onclick="edit({{$value.MID}})">编辑</a></td>
          </tr>
          {{end}}

        </tbody>
      </table>



      <table lay-filter="demo">
        <thead>
          <tr>
            <th lay-data="{field:'username', width:130, sort:true}">登录名</th>
            <th lay-data="{field:'experience', width:180}">密码</th>
            <th lay-data="{field:'sign'}">权限</th>
            <th lay-data="{field:'MID'}">修改</th>
          </tr> 
        </thead>
        <tbody>
          {{range $key, $value := .info}}
          <tr>
            <td>{{$value.MName}}</td>

            <td>{{$value.MPWD}}</td>
            <td>{{$value.MPower}}</td>

            <td><a class="layui-btn layui-btn-xs" onclick='edit({{$value.MID}})'>编辑</a></td>
          </tr>
          {{end}}
        </tbody>
      </table>



    </div>
  </div>

  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>

  <script>

    // layui.use(["button"], function () {
    //   var button = layui.button;

    //   button.load({
    //     elem: '[load]',
    //     time: 600,
    //     done: function () {
    //     //  popup.success("加载完成");
    //     }
    //   })
    // })


    layui.use(['jquery', 'layer'], function () {
      var table = layui.table;
 
 //转换静态表格
 table.init('demo', {
  //  height: 315 //设置高度
   limit: 10, //注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
   //支持所有基础参数
   defaultToolbar: ['filter', 'exports', 'print', {
                    title: '提示',
                    layEvent: 'LAYTABLE_TIPS',
                    icon: 'layui-icon-tips'
                }],

 });

      window.edit = function(obj) {
      layer.open({ 
        title: '编辑 - id:' + obj,
        type: 2,
        area: ['80%', '90%'],
        content: '/system/manage/edit?MID=' + obj,
        end: function(){
          location.reload();
        }
      })
    }

    window.add = function(obj) {
      layer.open({
        title: '添加',
        type: 2,
        area: ['80%', '90%'],
        content: '/system/manage/edit?MID=0',
        end: function(){
          location.reload();
        }
      })
    }

    })

    // layui.use(['notice', 'jquery', 'layer', 'code'], function () {
    //   var notice = layui.notice;

    //   notice.success("成功消息")
    //   notice.error("危险消息")
    //   notice.warning("警告消息")
    //   notice.info("通用消息")
    
    // })

  </script>
</body>

</html>