@charset "utf-8";
html, body {
	font-family: "微软雅黑"
}
/*
 * jQuery图片轮播(焦点图)插件
 * ADD.JENA.201206291027
 * EDIT.JENA.201206300904
 * Author: jena
 * Demo: http://ishere.cn/demo/jquery.slidebox/
 */
div.slideBox {
	position: relative;
	width: 670px;
	height: 300px;
	overflow: hidden;
}
div.slideBox ul.items {
	position: absolute;
	float: left;
	background: none;
	list-style: none;
	padding: 0px;
	margin: 0px;
}
div.slideBox ul.items li {
	float: left;
	background: none;
	list-style: none;
	padding: 0px;
	margin: 0px;
}
div.slideBox ul.items li a {
	float: left;
	line-height: normal !important;
	padding: 0px !important;
	border: none/*For IE.ADD.JENA.201206300844*/;
}
div.slideBox ul.items li a img {
	margin: 0px !important;
	padding: 0px !important;
	display: block;
	border: none/*For IE.ADD.JENA.201206300844*/;
}
div.slideBox div.tips {
	position: absolute;
	bottom: 0px;
	width: 100%;
	height: 32px;
	background-color: #000;
	overflow: hidden;
}
div.slideBox div.tips div.title {
	position: absolute;
	left: 0px;
	top: 0px;
	height: 100%;
}
div.slideBox div.tips div.title a {
	color: #FFF;
	font-size: 12px;
	line-height: 32px;
	margin-left: 10px;
	text-decoration: none;
}
div.slideBox div.tips div.title a:hover {
	text-decoration: underline !important;
}
div.slideBox div.tips div.nums {
	position: absolute;
	right: 0px;
	top: 0px;
	height: 100%;
}
div.slideBox div.tips div.nums a {
	display: inline-block;
>float:left/*For IE.ADD.JENA.201206300844*/;
	width: 10px;
	height: 10px;
	background-color: #FFF;
	text-indent: -99999px;
	margin: 12px 5px 0px 0px;
}
div.slideBox div.tips div.nums a.active {
	background-color: #093;
}
