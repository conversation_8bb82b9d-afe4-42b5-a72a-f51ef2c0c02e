package school

import (
	"50go/internal/dao"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SchoolController struct{}

type PostReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.<PERSON> `method:"post"  summary:"Post获得学校信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}

type GetReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.<PERSON>a `method:"get"  summary:"Get获得学校信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}
type GetListRes struct {
	Name string
	Id   int
}

func GetPageData(r *ghttp.Request) (int, int) {
	page := r.GetQuery("page", 1).Int()
	psize := r.GetQuery("psize", 5).Int()
	return page, psize
}

func (c *SchoolController) Categorylist(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	// r := g.RequestFromCtx(ctx)
	// name := r.GetQuery("name", "没参数的默认返回值!") //获取name
	r := g.RequestFromCtx(ctx)
	md := dao.SClasss.Ctx(r.Context())
	sclasss, _ := md.FieldsEx("GContent").All()
	mysclasss := sclasss.List()

	var categoryList []map[string]interface{}
	if mysclasss != nil {
		for _, v := range mysclasss {
			v["id"] = v["SCID"]
			v["name"] = v["SCNmae"]
			v["orderNum"] = v["SCID"]

			categoryList = append(categoryList, v)
		}
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code": 2000,
			"data": g.Map{"categoryList": categoryList},
		})
	return
}

func (c *SchoolController) HotPosting(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	// r := g.RequestFromCtx(ctx)
	// name := r.GetQuery("name", "没参数的默认返回值!") //获取name
	r := g.RequestFromCtx(ctx)
	md := dao.SGoods.Ctx(r.Context())
	data, _ := md.FieldsEx("GContent").All()
	total, _ := md.Count()

	mysclasss := data.List()

	var categoryList []map[string]interface{}
	if mysclasss != nil {
		for _, v := range mysclasss {
			v["id"] = v["GID"]
			v["categoryId"] = v["SCCLASSID"]
			v["abstractContent"] = v["G_Introduce"]
			v["createTime"] = v["GSales"]

			categoryList = append(categoryList, v)
		}
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code":  2000,
			"total": total,
			"rows":  categoryList,
		})
	return
}

func (c *SchoolController) ListPosting(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	// r := g.RequestFromCtx(ctx)
	// name := r.GetQuery("name", "没参数的默认返回值!") //获取name
	r := g.RequestFromCtx(ctx)
	md := dao.SGoods.Ctx(r.Context())
	data, _ := md.FieldsEx("GContent").All()
	total, _ := md.Count()

	mysclasss := data.List()

	var categoryList []map[string]interface{}
	if mysclasss != nil {
		for _, v := range mysclasss {
			v["id"] = v["GID"]

			v["avatarUrl"] = "http://localhost:8899/" + v["GImg"].(string)

			v["categoryId"] = v["SCCLASSID"]
			v["abstractContent"] = v["G_Introduce"]
			v["categoryName"] = v["GName"]

			v["createTime"] = v["GSales"]
			v["readNum"] = v["GPrice"]
			v["likeNum"] = v["GPrice"]

			categoryList = append(categoryList, v)
		}
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code":  2000,
			"total": total,
			"rows":  categoryList,
		})
	return
}
