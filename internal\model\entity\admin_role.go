// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminRole is the golang structure for table AdminRole.
type AdminRole struct {
	Id          int         `json:"id"           orm:"id"           ` //
	Aid         string      `json:"aid"          orm:"aid"          ` //
	Code        string      `json:"code"         orm:"code"         ` //
	Name        string      `json:"name"         orm:"name"         ` //
	Islock      string      `json:"islock"       orm:"islock"       ` //
	GroupId     string      `json:"group_id"     orm:"group_id"     ` //
	RedirectUrl string      `json:"redirect_url" orm:"redirect_url" ` //
	Pid         int         `json:"pid"          orm:"pid"          ` //
	LastTime    string      `json:"last_time"    orm:"last_time"    ` //
	TimeFlage   *gtime.Time `json:"time_flage"   orm:"time_flage"   ` //
	Attribute   string      `json:"attribute"    orm:"attribute"    ` //
	Mpower      string      `json:"mpower"       orm:"mpower"       ` //
}
