package wx

import (
	"50go/internal/dao"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// 规范路由
func (c *WxController) UserMoney(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)
	md := dao.SJf.Ctx(r.Context())

	mnewss, _ := md.Sum("JFen")

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"mymycount": 99,
			"page":      page,
			"psize":     psize,
			"data":      mnewss,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *WxController) UserFullInfo(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)
	md := dao.User.Ctx(r.Context())

	mnewss, _ := md.One()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"page":  page,
			"psize": psize,
			"data":  mnewss,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}
