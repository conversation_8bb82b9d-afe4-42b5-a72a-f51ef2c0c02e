// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserYgpdDao is the data access object for the table User_ygpd.
type UserYgpdDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserYgpdColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserYgpdColumns defines and stores column names for the table User_ygpd.
type UserYgpdColumns struct {
	Uid               string //
	UOpenID           string //
	UGuid             string //
	UparentId         string //
	UIndirectParentId string //
	Uwx               string //
	Uzfb              string //
	UTel              string //
	Umima             string //
	Utouxiang         string //
	Uname             string //
	UHeadImg          string //
	UNickName         string //
	UTrueName         string //
	IsMember          string //
	Ustate            string //
	Utype             string //
	UstateEndTime     string //
	LoginTime         string //
	RegTime           string //
	Ujifen            string //
	Upower            string //
	USex              string //
	UProvince         string //
	USession          string //
	SessionKey        string //
	Uwxapp            string //
}

// userYgpdColumns holds the columns for the table User_ygpd.
var userYgpdColumns = UserYgpdColumns{
	Uid:               "UID",
	UOpenID:           "UOpenID",
	UGuid:             "UGuid",
	UparentId:         "UparentId",
	UIndirectParentId: "UIndirectParentId",
	Uwx:               "Uwx",
	Uzfb:              "Uzfb",
	UTel:              "UTel",
	Umima:             "Umima",
	Utouxiang:         "Utouxiang",
	Uname:             "Uname",
	UHeadImg:          "UHeadImg",
	UNickName:         "UNickName",
	UTrueName:         "UTrueName",
	IsMember:          "isMember",
	Ustate:            "Ustate",
	Utype:             "Utype",
	UstateEndTime:     "UstateEndTime",
	LoginTime:         "LoginTime",
	RegTime:           "RegTime",
	Ujifen:            "Ujifen",
	Upower:            "Upower",
	USex:              "USex",
	UProvince:         "UProvince",
	USession:          "USession",
	SessionKey:        "SessionKey",
	Uwxapp:            "uwxapp",
}

// NewUserYgpdDao creates and returns a new DAO object for table data access.
func NewUserYgpdDao(handlers ...gdb.ModelHandler) *UserYgpdDao {
	return &UserYgpdDao{
		group:    "default",
		table:    "User_ygpd",
		columns:  userYgpdColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserYgpdDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserYgpdDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserYgpdDao) Columns() UserYgpdColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserYgpdDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserYgpdDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserYgpdDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
