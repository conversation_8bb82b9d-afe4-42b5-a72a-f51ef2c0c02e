// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// LLog is the golang structure for table L_Log.
type LLog struct {
	Lid    int         `json:"LID"     orm:"LID"     ` //
	LTiTLE string      `json:"L_TiTLE" orm:"L_TiTLE" ` //
	LIp    string      `json:"L_IP"    orm:"L_IP"    ` //
	LTime  *gtime.Time `json:"L_Time"  orm:"L_Time"  ` //
	LAct   string      `json:"L_Act"   orm:"L_Act"   ` //
	LUid   string      `json:"L_UID"   orm:"L_UID"   ` //
	LUname string      `json:"L_Uname" orm:"L_Uname" ` //
}
