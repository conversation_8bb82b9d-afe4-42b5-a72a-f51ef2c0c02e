// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SArea is the golang structure for table S_Area.
type SArea struct {
	Id       int    `json:"ID"       orm:"ID"       ` //
	AreaName string `json:"areaName" orm:"areaName" ` //
	PID      int    `json:"pID"      orm:"pID"      ` //
}
