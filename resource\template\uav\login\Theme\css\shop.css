﻿@charset "utf-8";
/* CSS Document */


/**********商城首页**********/
/**************************/
/******页头******/
/*------搜索------*/
.search_box{ width: 85%; height: 1.3rem; background: url(../images/shop/icon_search.svg) no-repeat .4rem .2rem; background-size: .9rem auto; background-color: #fff; border-radius: .65rem; position: relative; margin-top: .25rem; padding: 0 1.3rem 0 1.7rem;}
.search_box input{ width: 100%; height: 100%; font-size: .56rem; color: #333;}

.search_box .clear-keyword{ width: 1rem; height: 1rem; background: url(../images/shop/icon_clear.svg) no-repeat center; background-size: .8rem; display: block; position: absolute; right: .15rem; top: .15rem; display: none;}

/*------购物车图标------*/
.header-shopcart{ width:1.8rem; height: 1.8rem; background: url(../images/shop/icon_cart.svg) no-repeat center; background-size: 1rem auto; display: block; position: relative;z-index:1;}
.header-shopcart i{ width: .65rem; height: .65rem; line-height: .65rem; font-size: .45rem; color: #974efa; text-align: center; background: #fff; border-radius: 50%; position: absolute; right: .1rem; top: .2rem;}

/*------滚动的分类标题------*/
.scroll-title{ width:100%; height:1.7rem; background:#fff; overflow:hidden; padding:0 .5rem; position:fixed; left: 0; top: 1.8rem; z-index: 999;}
.scroll-title:after{ width:1rem; height:1.7rem; content:''; background:-webkit-linear-gradient(right,#fff,hsla(0,0%,100%,.6)); background:linear-gradient(270deg,#fff 0,hsla(0,0%,100%,.6)); position:absolute; right:0; top:0; z-index:1001;}

.scroll-title ul{ width:100%; height:100%; white-space:nowrap; overflow-x:auto; overflow-y:hidden; -webkit-overflow-scrolling:touch; padding-right: 1rem;}
.scroll-title li{ height:1.6rem; line-height:1.7rem; font-size:.56rem; color:#555; display:inline-block; padding:0 .6rem;}

.scroll-title li span{ height:1.7rem; color:#666; display:block; position:relative;}
.scroll-title li span.active{ color:#46a9fd;}
.scroll-title li span.active:after{ width:100%; height:.1rem; content:''; background:#46a9fd; border-radius: .05rem; position:absolute; left:0; bottom:0; z-index:1000;}


/******产品筛选******/
/*------主体------*/
.sort{ width: 100%; background: #fff; position: fixed; left: 0; top: 1.8rem; z-index: 999; /*padding: 0 3.6rem 0 0;*/}

/*------筛选------*/
.sort-bar{ width: 100%;}
.sort-bar li{ width: 33.33333%; line-height: 1.7rem; font-size: .56rem; color: #666; text-align: center; float: left; position: relative;}
.sort-bar li span, .sort-bar li i{ height: 1.7rem; display: inline-block; vertical-align: top;}
.sort-bar li i {width: .5rem; position: relative;}
.sort-bar li i:after{ content: ''; border-left: .2rem solid transparent; border-right: .2rem solid transparent; border-top: .2rem solid #aaa; position: absolute; left: 0; top: .77rem;}

.sort-bar li.active{ color: #46a9fd;}
/*.sort-bar li.active:after{ width: 60%; height: .1rem; content: ''; background: #46a9fd; border-radius: .05rem; position: absolute; left: 20%; bottom: 0;}*/
.sort-bar li.active i:after{ border-top: .2rem solid #46a9fd;}

.sort-bar li.active.open i:after{ border-bottom: .2rem solid #46a9fd; border-top: 0;}

/*------分类------*/
.sort-catalog{ width: 3.6rem; height: 1.7rem; border-left: #f4f4f4 .05rem solid; position: absolute; right: 0; top: 0;}
.sort-catalog a{ width: 100%; line-height: 1.7rem; font-size: .56rem; text-align: center; color:#333; display: block;}
.sort-catalog span, .sort-catalog i{ height: 1.7rem; display: inline-block; vertical-align: top;}
.sort-catalog i{ width:.6rem; background: url(../images/shop/icon_catalog.svg) no-repeat center; background-size: .6rem auto;}


/*.sort {width:100%;top:2.2rem;-webkit-box-align:center;padding:.4rem 0;background:#fff;-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;}
.sort:after {content:" ";position:absolute;bottom:0;left:0;right:0;border-bottom:1px solid #e7e7e7;}
.sort .item {position:relative;width:1%;display:table-cell;text-align:center;font-size:0.6rem;border-left:1px solid #e7e7e7;color:#666;}
.sort .item:first-child {border:0;}
.sort .item.on .text {color:#fd5454;}
.fui-content {top:4.4rem;}
.fui-mask-m {top:4.4rem;display:none;}
.sort .item .sorting {width:.2rem;height:.2rem;position:relative;}
.sort .item .sorting .icon {font-size:11px;position:absolute;-webkit-transform:scale(0.6);-ms-transform:scale(0.6);transform:scale(0.6);}
.sort .item-price .sorting .icon-sanjiao1 {top:.15rem;left:0;}
.sort .item-price .sorting .icon-sanjiao2 {top:-.15rem;left:0;}
.sort .item-price.desc .sorting .icon-sanjiao1 {color:#ef4f4f}
.sort .item-price.asc .sorting .icon-sanjiao2 {color:#ef4f4f}*/


/******产品筛选下拉框******/
/*------主体------*/
.sort-box{ width: 100%; background: rgba(0,0,0,.5); position: fixed; left: 0; top: 3.5rem; right: 0; bottom: 0; z-index: 9999; display: none;}
.open .sort-box{ display: block;}

/*------选项------*/
.sort-option{ width: 100%; background: #fff; border-top: #eee 1px solid; border-bottom: #eee 1px solid; overflow: hidden; padding: 0 0 0 .8rem;}
.sort-option li{ width: 100%; height: 1.7rem; line-height: 1.7rem; text-align: left; border-bottom: #eee 1px solid; position: relative;}
.sort-option li:last-child{ border-bottom: none;}

/*------选择框------*/

.sort-option li.active:after{ width: 1rem; height: 1rem; content: ''; background: url(../images/icon_check.svg) no-repeat center; background-size: .55rem auto; position: absolute; right: .5rem; top: .35rem;}


/******页面主体******/
.shop-container{ padding: 3.4rem 0 0 0;}


/******产品列表主体******/
/*------主体------*/
.goods{ width:100%; overflow:hidden; padding: .5rem .2rem 0 .2rem; position: relative;}
.goods .goods-box{ width:50%; overflow:hidden; float:left; padding: .2rem;}

.goods .goods-box a{ width:100%; background:#fff; border-radius: .2rem; overflow:hidden; display:block; padding:0 0 .5rem 0;}

/*------内容------*/
.goods .goods-box img{ width:100%; height:6.9rem;}
.goods .goods-box h3{ height:1.4rem; line-height: .7rem; font-size:.52rem; font-weight:normal; color:#333; -webkit-line-clamp:2; overflow:hidden; display:-webkit-box; -webkit-box-orient:vertical; padding:0 .4rem; margin-top:.4rem;}

.goods .goods-info{ width:auto; overflow:hidden; padding:0 .4rem;}
.goods .goods-info .goods-price{ height:1.2rem; line-height:1.2rem; color:#f80; overflow:hidden;}
.goods .goods-info .goods-price i{ font-style:normal; font-size:.64rem;}
.goods .goods-info .goods-price b{ font-weight: normal; font-size:.9rem; margin-left:.1rem;}
.goods .goods-info .goods-sale{ font-size:.52rem; color:#999; overflow:hidden; padding-top:.4rem;}

/* 商品组*/
.fui-goods-group {height:auto;overflow:hidden;background:#f9f9f9;padding: .2rem 0 0 0;}
.fui-goods-item {position:relative;height:auto;padding:0 0.5rem;border-bottom:1px solid #e7e7e7;background:#fff;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}
.fui-goods-item .image {height:4rem;width:4rem;float:left;background-size:100%;background-repeat:no-repeat;background-position:center center;overflow:hidden;position:relative;}
.fui-goods-item .image img {height:100%;width:100%;display:block;}
.fui-goods-group.block.one .fui-goods-item .image img {height:auto;width:100%;display:block;}
.fui-goods-item .detail {-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;background:#fff;padding-left:.5rem;}
.fui-goods-group .fui-goods-item .detail .name {height:1.6rem;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-top:0.3rem;font-size:0.55rem;color:#262626;}
.fui-goods-item .detail .price {position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;font-size:0.7rem;margin-top:0.3rem;}
.fui-goods-group.block .fui-goods-item .detail  .productprice ~  .price {margin-top:-0.3rem;}
/*.fui-goods-item .detail .productprice ~ .price {*//*margin-top:0;*//*}
*/.fui-goods-group.block .fui-goods-item .detail .productprice.noheight ~ .price {margin-top:0.3rem;}
.fui-goods-item .detail .price .text {-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;color:#ff5555;font-size:0.6rem;}
.fui-goods-item .detail .price .minprice {font-size:0.8rem;}
.fui-goods-group.block.three .fui-goods-item .detail .minprice {font-size:0.6rem;}
.fui-goods-item .detail .price .productprice {color:#999;font-size:0.6rem;}
.fui-goods-item .detail .price .buy {display:block;text-align:center;line-height:1rem;color:#fff;font-size:0.6rem;background:#fe5455;display:block;font-size:0.6rem;padding:0 .25rem;}
.fui-goods-item .detail .price .cycelbuy {display:block;text-align:center;line-height:1rem;color:#fff;font-size:0.6rem;background:#fe5455;display:block;font-size:0.6rem;padding:0 .25rem;}
.color {color:#fd5454;}
.bgcolor {background:#fd5454}
.fui-goods-group.block .fui-goods-item {width:50%;float:left;border-bottom:0;background:none;padding:0.25rem;display:block;}
.fui-goods-group.three .fui-goods-item {width:33.33%;}
.fui-goods-group.block .fui-goods-item .image {width:100%;height:0;overflow:hidden;margin:0;padding-bottom:100%;/* 关键就在这里 */    background-position:center;background-repeat:no-repeat;background-size:cover;position:relative;}
.fui-goods-group.block .fui-goods-item .image.auto {height:auto;padding-bottom:0;}
.fui-page-group .fui-goods-group.block .fui-goods-item .salez.diy,.fui-page-group .fui-goods-group .fui-goods-item .salez.diy {position:absolute;top:0;left:0;right:0;bottom:0;z-index:1;background-size:cover;background-position:center;}
.fui-goods-group.block .fui-goods-item .salez {position:absolute;top:0;left:0;right:0;bottom:0;z-index:1;background-size:cover;background-position:center;}
.fui-goods-group .fui-goods-item .salez {position:absolute;top:0;left:0;right:0;bottom:0;z-index:1;background-size:cover;background-position:center;}
.fui-swipe .salez {position:absolute;bottom:0;right:0;height:auto;width:auto;z-index:10;}
.fui-swipe .salez img {height:auto;width:auto;display:block;}
.fui-goods-group.block {padding:0.2rem;}
.fui-goods-group.block .fui-goods-item .image {float:none;}
.fui-goods-group.block .fui-goods-item .detail {padding:0.5rem;overflow:hidden;}
.fui-goods-group.block.three .fui-goods-item .detail {padding:0.5rem 0.3rem;}
.fui-goods-group.block .fui-goods-item .detail .name {height:1.65rem;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;font-size:0.65rem;line-height:0.9rem;margin-top:0;color:#262626;}
.fui-goods-group.block .fui-goods-item .center-image {max-width:100%;height:0;padding-bottom:100%;background-size:contain;}
.fui-goods-group .fui-goods-item .detail .productprice {width:auto;color:#999;font-size:0.55rem;overflow:hidden;/*height:2rem;line-height:2rem;*/text-align:center;}
.fui-goods-group.block .fui-goods-item .detail .productprice {height:1.2rem;line-height:1.2rem;}
.fui-goods-group.block .fui-goods-item .detail .productprice.noheight {display:none;}
.fui-goods-group.block .fui-goods-item .detail .name {height:1.7rem;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;font-size:0.55rem;line-height:0.9rem;}
.fui-goods-group.block.one .fui-goods-item .detail .name {height:1.75rem;font-size:0.55rem;line-height:0.95rem;}
.fui-goods-group.block.three .fui-goods-item .detail .name {font-size:0.6rem;}
.fui-goods-group.block.one .fui-goods-item {width:100%;float:none;}
/* 商品列表页*/.page-goods-list .fui-header {background:#fff;}
/*列表页分销*/.goods-Commission {position:absolute;height:0.9rem;line-height:0.9rem;text-align:center;width:100%;bottom:0;color:#fff;font-size:0.5rem;font-weight:bold;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;background:-moz-linear-gradient(left,#f0b938 0%,#f09938 100%);background:-webkit-linear-gradient(left,#f0b938 0%,#f09938 100%);background:-o-linear-gradient(left,#f0b938 0%,#f09938 100%);background:-ms-linear-gradient(left,#f0b938 0%,#f09938 100%);background:linear-gradient(to right,#f0b938 0%,#f09938 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#fff0b938,endColorstr=#fff09938,gradientType='1');}
.page-goods-list .block .goods-Commission,.shop-index-page .fui-goods-group.block .goods-Commission {height:1.1rem;line-height:1.1rem;font-size:0.55rem;}

/*.header .fui-header-right {position:absolute;right:0;padding-right:.3rem;height:2.2rem;line-height:2.2rem;z-index:2;font-size:.7rem}
.-header a.back:before {content:" ";display:inline-block;-webkit-transform:rotate(45deg);-ms-transform:rotate(45deg);transform:rotate(45deg);height:0.5rem;width:0.5rem;border-width:0 0 2px 2px;border-color:#666;border-style:solid;position:relative;top:0;}
.header a {height:2.2rem;line-height:2.2rem;padding:0;margin:0;top:0;color:#999;display:block;padding:0 .5rem;font-size:.7rem;}
.header a i {font-size:1.3rem;}
.header.fui-header-success {background-color:#04ab02;border:none;}
.header.fui-header-primary {background-color:#0290be;border:none;}
.header.fui-header-warning {background-color:#ff8000;border:none;}
.header.fui-header-danger {background-color:#ef4f4f;border:none;}
.header.fui-header-success .title,.fui-header.fui-header-success .btn,.fui-header.fui-header-primary .title,.fui-header.fui-header-primary .btn,.fui-header.fui-header-warning .title,.fui-header.fui-header-warning .btn,.fui-header.fui-header-danger .title,.fui-header.fui-header-danger .btn {color:#fff;}*/


/**********产品分类**********/
/**************************/
/******左侧的一级分类******/
.classify{ width:3.6rem; height:100%; background:#f6f6f6; overflow-x:hidden; overflow-y:auto; -webkit-overflow-scrolling:touch; padding:1.8rem 0 0 0; position:fixed; left:0; top:0; z-index:1;}
.classify li{ width:100%; height:2rem; line-height:2rem; font-size:.58rem; color:#555; text-align:center; overflow:visible; position:relative; z-index:3;}
.classify li span{ width:100%; height:100%; color:#555; display:block; position:relative;}

.classify li.active span{ color:#46a9fd; background:#fff;}
.classify li.active:after{ width:4px; height:60%; content:''; background:#46a9fd; position:absolute; left:0; top:20%; z-index:4;}


/******右侧的分类列表******/
/*------主体------*/
.classify-wrap{ background: #fff; overflow-x:hidden; overflow-y: auto; padding:0 .7rem; position: fixed; left: 3.6rem; right: 0; top: 1.8rem; bottom: 0;}
.classify-wrap .classify-item{ width: 100%; overflow: hidden; padding: .5rem 0;}

/*------列表标题------*/
.classify-wrap .classify-title{ width: 100%; line-height: 1rem; overflow: hidden; padding-bottom: .5rem;}
.classify-wrap .classify-title h3{ font-size: .6rem; font-weight: normal; color: #333; float: left;}
.classify-wrap .classify-title a{ font-size: .52rem; color: #999; display: block; float: right;}

/*------列表内容------*/
.classify-wrap .classify-item li{ width:24.49%; float:left; margin-right:13.26%; position:relative;}
.classify-wrap .classify-item li:nth-child(3n){ margin-right:0;}

.classify-wrap .classify-item li a{ height:100%; text-align:center; display:block; position:relative;}
.classify-wrap .classify-item li p{ width:100%; font-size:.52rem; color:#666; overflow:hidden; position:absolute; left:0; bottom:1rem;}

.classify-img{ width:100%; position:relative; margin-bottom:2rem; padding-top:100%;}
.classify-img img{ width:100%; position:absolute; left:0; top:0;}



/**********产品详情**********/
/**************************/
/******详情页头部和主体******/
.product-header{ background: none;}
.header.product-header .header-return{ background:url(../images/shop/return.svg) no-repeat center; background-size:.86rem auto;}

.product-container{ padding: 0 0 2rem 0;}


/******产品标题******/
/*------标题1------*/
.goods-title{ width:100%; line-height: .8rem; border-bottom:#f4f4f4 .18rem solid; overflow:hidden; padding:.3rem .5rem;}
.goods-title span{ font-size:.62rem; color: #333;}
.goods-title b{ font-size:.52rem; font-weight: normal; color: #999;}

.goods-title a{ font-size:.52rem; color:#aaa; overflow:hidden; padding:0 .5rem 0 0; display:block; position:relative;}
.goods-title a:after{ width:.3rem; height:.5rem; content:''; background: url(../images/icon_arrow_small.svg) no-repeat center; background-size: .26rem auto; display:block; position:absolute; right:0; top:.18rem;}

/*------标题2------*/
.goods .like-title{ width:100%; text-align:center; overflow:hidden;}
.goods .like-title h3{ line-height:1.4rem; font-size:.62rem; font-weight:normal; color:#333; display: inline-block; padding: 0 0 0 1rem; position: relative;}

.goods .like-title h3:after{ width: 1rem; height: 1.4rem; content: ''; background: url(../images/shop/icon_like_title.svg) no-repeat center; background-size: .8rem; position: absolute; left: 0; top: 0;}

/******产品信息******/
/*------名字和价格------*/
.goods-info{ width:100%; background:#fff; overflow:hidden; padding:.5rem; position: relative;}
.goods-info h3{ width:12rem; min-height: 2rem; font-size:.64rem; font-weight:normal; color:#111; padding:.3rem 0;}

.goods-info .goods-price{ font-size: 1rem; color:#f80;}
.goods-info .goods-price i{ font-style:normal; font-size:.7rem;}
.goods-info .goods-price .goods-original-price{ font-size:.64rem; text-decoration:line-through; color:#999; margin-left:.2rem;}

.goods-info .goods-sale{ font-size:.56rem; color:#999; padding-top:.55rem;}

/*------收藏------*/
.goods-collect{ width: 2rem; position: absolute; right: .3rem; top: .9rem;}

.goods-collect i, .goods-collect span{ display: block; margin: 0 auto;}
.goods-collect i{ width:1rem; height: 1rem; background: url(../images/shop/icon_like.svg) no-repeat center; background-size: 1rem auto;}
.goods-collect span{ font-size: .52rem; color: #777; text-align: center;}

.goods-collect.collected i{ background: url(../images/shop/icon_like_active.svg) no-repeat center; background-size: 1rem auto;}

/*------质量保证------*/
.guarantee{ width:100%; background:#f4f4f4; text-align: center; overflow:hidden; padding:.15rem .3rem;}
.guarantee li{ height:1.2rem; line-height:1.2rem; font-size:.52rem; color:#666; white-space: nowrap; display: inline-block; position:relative; padding:0 .4rem 0 .9rem;}
.guarantee li:after{ width:.8rem; height:.8rem; content:''; background:url(../images/shop/icon_guarantee.svg) no-repeat center; background-size:.7rem auto; position:absolute; left:0; top:.2rem;}


/******产品评论******/
/*------主体和标题------*/
.goods-comment{ width:100%; background:#fff; overflow:hidden;}

.goods-comment{ width:auto;}
.goods-comment .goods-comment-list{ width:auto; border-bottom:#e4e4e4 .05rem solid; overflow:hidden; padding: .4rem .5rem; position:relative;}
.goods-comment .goods-comment-list:last-child{ border-bottom: none;}

/*------作者------*/
.goods-comment .goods-comment-list .goods-comment-author{ width:100%; min-height: 1.6rem; overflow: hidden; position: relative; padding: 0 0 0 2rem;}
.goods-comment .goods-comment-list .goods-comment-author img{ width:1.6rem; height:1.6rem; border-radius:50%; position: absolute; left: 0; top: 0;}
.goods-comment .goods-comment-list .goods-comment-author span{ font-size:.56rem; color:#666; display:block;}

/*------评分------*/
.goods-comment .goods-comment-list .goods-comment-level{ padding-top: .2rem;}
.goods-comment .goods-comment-list .goods-comment-level i{ width:.7rem; height: .7rem; background: url(../images/shop/icon_like.svg) no-repeat; background-size: .6rem auto; display: block; float: left;}

.goods-comment .goods-comment-list .goods-comment-level i.active{ background: url(../images/shop/icon_like_active.svg) no-repeat; background-size: .6rem auto;}

/*------内容------*/
.goods-comment .goods-comment-list .goods-comment-content{ width:100%; overflow:hidden; clear:both; padding-top:.5rem;}
.goods-comment .goods-comment-list .goods-comment-content p{ width:100%; height:1.4rem; line-height: .7rem; font-size:.52rem; color:#333; -webkit-line-clamp:2; overflow:hidden; display:-webkit-box; -webkit-box-orient:vertical; padding:0 .3rem;}

/*------评论图片------*/
.goods-comment .goods-comment-list .goods-comment-photo{ width:100%; overflow:hidden;}
.goods-comment .goods-comment-list .goods-comment-photo li{ width:33.3333333%; overflow:hidden; float:left; margin-top:.8rem;}
.goods-comment .goods-comment-list .goods-comment-photo .comment-photo-box{ width:7rem; height:7rem; overflow:hidden; margin:0 auto;}
.goods-comment .goods-comment-list .goods-comment-photo .comment-photo-box img{ width:100%;}


/******产品详情文章******/
.goods-details{ width:100%; background:#fff; overflow:hidden;}
.goods-details .goods-details-content{ width:100%; overflow:hidden;}
.goods-details .goods-details-content img{ width:100%;}
.goods-details .goods-details-content p{ padding:.3rem;}


/******底部固定操作栏******/
/*------主体------*/
.goods-bar{ width:100%; height:2rem; background:rgba(255,255,255,.9); position:fixed; left:0; bottom:0; z-index:999; padding:0 0 0 6.6rem;}

/*------左侧小按钮------*/
.goods-bar .goods-operate{ height:2rem; position:absolute; left:0; top:0;}
.goods-bar .goods-operate .goods-operate-btn{ width:2.2rem; height:2rem; border-top:#eaeaea 1px solid; float:left;}

.goods-bar .goods-operate .goods-operate-btn a{ width:100%; height:100%; display:block; padding-top: .1rem;}
.goods-bar .goods-operate .goods-operate-btn i, .goods-bar .goods-operate-btn span{ display:block;}
.goods-bar .goods-operate .goods-operate-btn i{ width:1rem; height:1rem; background-repeat:no-repeat;  background-position:center; background-size:.92rem auto; margin:0 auto; margin-top:.06rem; position: relative;}
.goods-bar .goods-operate .goods-operate-btn span{ font-size:.5rem; color:#666; text-align:center;}
.goods-bar .goods-operate .goods-operate-btn em{ width: .76rem; height: .76rem; line-height: .8rem; font-size: .4rem; text-align: center; color: #fff; background: #fa5e6a; border-radius: 50%; display: block; position: absolute; right: -.4rem; top: -.3rem;}

/*------按钮图标------*/
.goods-bar .goods-operate .operate-btn-1 i{ background-image: url(../images/shop/menu_icon_1.svg);}
.goods-bar .goods-operate .operate-btn-2 i{ background-image: url(../images/shop/menu_icon_2.svg);}
.goods-bar .goods-operate .operate-btn-3 i{ background-image: url(../images/shop/menu_icon_3.svg);}

/*------右侧购买按钮------*/
.goods-bar .goods-btn{ width:100%; height:2rem; overflow:hidden;}
.goods-bar .goods-btn div{ width: 50%; height: 100%; float: left;}

.goods-bar .goods-btn .buy-btn{ text-align: center; color: #fff; background: linear-gradient(90deg,#00a2ff,#8e71f5); background: -webkit-linear-gradient(left,#00a2ff,#8e71f5); padding: 0 .5rem;}
.goods-bar .goods-btn .buy-btn p{ font-size:.6rem;}
.goods-bar .goods-btn .buy-btn i{ font-style:normal; font-size:.8rem; margin-left:.1rem;}
.goods-bar .goods-btn .buy-btn span{ font-size:.5rem; display: block;}

.goods-bar .goods-btn .cart-btn{ line-height: 2rem; font-size:.52rem; text-align: center; color: #fff; background: #15aefe;}

/*------加入购物车------*/
.flyer-img{ width: 1.4rem; height: 1.4rem; border-radius: 50%; display: block; position: fixed; z-index: 9999;}

.success-tips{ width:60%; font-size: .56rem; color: #fff; text-align: center; background: rgba(0,0,0,.7); border-radius: .16rem; padding: .3rem .5rem; position: fixed; top: 70%; left: 20%; z-index: 9999; display: none;}


/******购买弹窗******/
/*------主体------*/
.selector{ width:100%; height:100%; background:rgba(0,0,0,.7); position:fixed; left:0; top:0; z-index:99999; display:none;}
.selector-panel{ width:100%; height:12rem; background:#fff; position:absolute; left:0; bottom:0;}

.selector-close{ width:1rem; height:1rem; background:url(../images/shop/icon_clear.svg) no-repeat center; background-size:1rem auto; position:absolute; top:.3rem; right:.3rem;}

/*------产品信息------*/
.selector-product{ width:100%; border-bottom:#eee 1px solid; overflow:hidden; padding:.5rem 1.4rem .6rem 4.6rem;}
.selector-product .selector-product-photo{ width:3.5rem; height:3.5rem; background:#fff; border:#ddd .05rem solid; border-radius:.15rem; overflow:hidden; padding:.08rem; position:absolute; z-index:100000; left:.5rem; top:-1rem;}
.selector-product .selector-product-photo img{ width:100%; height:100%; border-radius:.15rem;}

.selector-product .selector-product-info{ width:100%;}
.selector-product .selector-product-price{ font-size:.76rem; color:#f80;}
.selector-product .selector-product-name{ height: .8rem; line-height: .8rem; font-size:.56rem; color:#555; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}

/*------选择数量------*/
.selector-number{ width:100%; overflow:hidden; padding:1rem .5rem;}
.selector-number h3{ line-height:1rem; font-size:.56rem; font-weight:normal; color:#333; float:left;}

.selector-number .number-box{ float:right;}
.selector-number .number-box span{ width:1rem; height:1rem; background:#e4e4e4; border-radius:.1rem; display:block; float:left; margin-left:.15rem; position:relative;}
.selector-number .number-box span:after{ width:1rem; height:1rem; content:''; position: absolute; left: 0; top: 0;}
.selector-number .number-box span.number-minus:after{ background: url(../images/shop/icon_minus.svg) no-repeat center; background-size: .56rem auto;}
.selector-number .number-box span.number-plus:after{ background: url(../images/shop/icon_plus.svg) no-repeat center; background-size: .56rem auto;}

.selector-number .number-box input{ width:2rem; height:1rem; font-size:.6rem; color:#333; text-align:center; background:#eee; border:none; border-radius:.1rem; float:left; margin-left:.15rem;}

/*------确定------*/
.selector-submit{ width:100%; position:absolute; left:0; bottom:0;}
.selector-submit a{ width:100%; line-height:1.8rem; font-size:.66rem; color:#fff; text-align:center; background: linear-gradient(90deg,#00a2ff,#8e71f5); background: -webkit-linear-gradient(left,#00a2ff,#8e71f5); display:block;}



/**********产品评价**********/
/**************************/
/******评论列表******/
/*------列表------*/
.comment-list .goods-comment-list .goods-comment-content p{ height:auto; line-height: .8rem; font-size:.56rem; display:block; padding: 0;}

/*------评论图片------*/
.comment-list .goods-comment-list .goods-comment-photo{ width:100%; overflow:hidden;}
.comment-list .goods-comment-list .goods-comment-photo li{ width:33.3333333%; overflow:hidden; float:left; margin-top:.8rem;}
.comment-list .goods-comment-list .goods-comment-photo .comment-photo-box{ width:4rem; height:4rem; overflow:hidden; margin:0 auto;}
.comment-list .goods-comment-list .goods-comment-photo .comment-photo-box img{ width:100%;}



/**********购物车**********/
/**************************/
/******内容主体******/
.shopcart-container{ padding:1.8rem 0;}


/******购物车列表******/
/*------主体------*/
.shopcart{ width: 100%; overflow: hidden;}

/*------选择的圆框------*/
.select-item{ display:block; position:absolute; left:.4rem; top:1.5rem;}

.select-item input[type=checkbox]{ width:.8rem; height:.8rem; border:#ddd .08rem solid; border-radius:50%; -webkit-appearance:none; appearance:none;}
.select-item input[type=checkbox]:checked{ background:url(../images/icon_check_white.svg) no-repeat center; background-size:.5rem auto; background-color: #46a9fd; border:none;}

/*------店铺名字------*/
.shopcart-store{ width: 100%; background: #fff; border-bottom: #eaeaea .05rem solid; overflow: hidden; padding: .35rem .5rem .35rem 1.8rem; position: relative;}
.shopcart-store a{ line-height: 1rem; font-size: .56rem; color: #111; overflow: hidden; display: block; padding: 0 0 0 1.1rem; position: relative;}
.shopcart-store a:after{ width:1rem; height: 1rem; content: ''; background: url(../images/icon_store.svg) no-repeat center; background-size: .7rem; position: absolute; left: 0; top: 0;}

.shopcart-store .select-item{ top:.45rem;}

/*------商品信息------*/
.shopcart-list{ width:auto; min-height:4rem; border-bottom:#f4f4f4 1px solid; overflow:hidden; padding:.5rem .5rem .5rem 5.4rem; position:relative;}
.shopcart-list img{ width: 3rem; height: 3rem; position:absolute; left:1.8rem; top:.4rem;}

.shopcart-info{ width:auto;}
.shopcart-info a{ line-height: .8rem; font-size:.56rem; color:#333;}
.shopcart-info p{ font-size:.64rem; color:#f80;}

/*----选择数量----*/
.choose-num{ padding:.5rem 0 0 0;}
.choose-num a{ width:1rem; height:1rem; background-color:#e0e4e7; display:block; float:left;}
.choose-num i{ width:1rem; height: 1rem; background-repeat: no-repeat; background-position: center; background-size: .5rem auto; display: block;}
.choose-num input{ width:2rem; height:1rem; font-size: .6rem; color: #555; text-align: center; border:#e4e4e4 1px solid; float:left; padding:0 .2rem;}

.choose-num i.minus-num{ background-image: url(../images/shop/icon_minus.svg);}
.choose-num i.plus-num{ background-image: url(../images/shop/icon_plus.svg);}

/*----删除----*/
.shopcart-delect{ font-size: .56rem; color: #aaa; position: absolute; bottom: .4rem; right: .5rem;}


/******购物车操作栏******/
/*------主体------*/
.shopcart-toolbar{ width:100%; height:1.8rem; background:#fff; position:fixed; bottom:0; left:0; z-index:9999;}

/*------全选------*/
.select-all{ float:left;}
.select-all p{ line-height:1.8rem; font-size:.6rem; color:#555; display:block; float:left; margin-left:.3rem;}

.select-all input[type=checkbox]{ width:.86rem; height:.86rem; border:#ccc 2px solid; border-radius:50%; -webkit-appearance:none; appearance:none; float:left; margin-left:.4rem; margin-top:.48rem;}
.select-all input[type=checkbox]:checked{ background:url(../images/icon_check_white.svg) no-repeat center; background-size:.5rem auto; background-color: #46a9fd; border:none;}

/*------总价------*/
.shopcart-sumbit{ overflow:hidden; float:right;}

.shopcart-summary{ line-height:1.8rem; overflow:hidden; float:left; padding:0 .4rem 0 0;}
.shopcart-summary b{ font-weight:normal; font-size:.52rem; color:#333;}
.shopcart-summary span{ font-size:.6rem; color:#f80;}

/*------提交------*/
.shopcart-submit-btn{ height:1.8rem; line-height:1.8rem; font-size:.6rem; color:#fff; background: linear-gradient(90deg,#00a2ff,#8e71f5); background: -webkit-linear-gradient(left,#00a2ff,#8e71f5); overflow:hidden; display:block; padding:0 .6rem; float:left;}
.shopcart-submit-btn span{ font-size:.52rem;}



/**********确认订单**********/
/**************************/
/******订单的收货地址******/
/*------列表------*/
.order-address{ width:100%; background:url(../images/shop/envelope.png) repeat-x bottom left; background-size:auto .16rem; background-color:#fff; overflow:hidden; display:block; padding:.45rem 3rem .8rem .5rem; position:relative;}

.order-address-text{ width:100%; font-size:.52rem; color:#555; overflow:hidden;}
.order-address-text p{ width: 100%; overflow: hidden; margin-top:.2rem;}
.order-address-text label{ width:3rem; float: left;}
.order-address-text span{ width: 8rem; display: block; float: left;}

/*------编辑地址按钮------*/
.order-address-edit{ width:1rem; height:1rem; background:url(../images/icon_edit.svg) no-repeat 0 0; background-size:1rem auto; display:block; position:absolute; top:.2rem; right:.4rem;}


/******订单内容的统计******/
/*------统计列表------*/
.order-confirm-details{ width:100%; background: #fff; overflow: hidden;}

.order-confirm-details ul{ width:100%; border-bottom:#eee .05rem solid; overflow: hidden; padding:0 .5rem; position:relative;}
.order-confirm-details ul li{ height:1.6rem; line-height:1.6rem; font-size:.52rem; color:#555; float: left;}

.order-confirm-details ul li.order-confirm-item{ width:3rem; color: #999;}
.order-confirm-details ul li.order-confirm-text{ width:10rem; text-align: right; float: right; position: relative;}
.order-confirm-details ul li.order-confirm-text input{ width:100%; height:1.6rem; text-align: right; border:none; background:none}

.order-confirm-details ul li.order-confirm-arrrow{ padding-right:.8rem;}
.order-confirm-details ul li.order-confirm-arrrow:after{ width: .5rem; height: 1.6rem; content: ''; background: url(../images/icon_arrow_small.svg) no-repeat center; background-size: .3rem auto; position: absolute; right: 0; top: 0;}

.select-payment{ width:100%; height:45px; color:#555; position:relative;}
.select-payment:after{ width:8px; height:8px; content:''; border-top:#777 1px solid; border-left:#777 1px solid; transform:rotate(135deg); -webkit-transform:rotate(135deg); -moz-transform:rotate(135deg); -o-transform:rotate(135deg); position:absolute; right:5px; top:17px;}

.select-payment select{ width:100%; height:45px; font-size:14px; color:#555; border:none; outline:none; -webkit-appearance:none; appearance:none;}


/******收货地址管理******/
/*------主体------*/
.address-container{ background:#f4f4f4; padding:50px 0 48px 0;}
.address{ width:100%; background:#fff; overflow:hidden; padding:1rem 0 .6rem 0;}

/*------收货人信息和地址------*/
.address-people{ color:#333; padding:0 .6rem;}
.address-people span{ font-size:1rem; display:block;}
.address-people span:first-child{ font-size:1.1rem;}

.address-text{ width:100%; font-size:.86rem; color:#555; overflow:hidden; padding:.6rem;}

/*------操作栏------*/
.address-toolbar{ width:100%; border-top:#eee 1px solid; overflow:hidden; padding:.6rem .6rem 0 .6rem;}

.address-default{ height:2rem; line-height:2rem; float:left;}
.address-default span{ font-size:.86rem; display:block; float:left; margin-left:.6rem;}

.address-default i{ width:1.2rem; height:1.2rem; background:url(../images/selected.png) no-repeat; background-size:1.2rem auto; border-radius:50%; display:block; float:left; margin-top:.4rem;}

.address-edit{ float:right; margin-top:.1rem;}
.address-edit a{ height:1.9rem; line-height:1.9rem; font-size:.86rem; color:#555; border:#ccc 1px solid; border-radius:.35rem; display:block; float:left; padding:0 1rem;}



/**********订单列表**********/
/**************************/
/******主体******/
.order-container{ padding: 3.4rem 0 0 0 ;}


/******切换标题******/
/*------主体------*/
.order-switch-title{ width: 100%; background: #fff; overflow: hidden; position: fixed; left: 0; top: 1.8rem; z-index: 999;}
.order-switch-title li{ width: 33.333%; line-height: 1.6rem; text-align: center; float: left; position: relative;}

.order-switch-title li a{ font-size: .56rem; color: #777; display: block;}
.order-switch-title li.active a{ color: #46a9fd;}
.order-switch-title li.active:after{ width: 70%; height: .1rem; content: ''; background: #46a9fd; border-radius: .05rem; position: absolute; left: 15%; bottom: 0;}


/******订单列表******/
/*------主体------*/
.order-list{ width: 100%; overflow: hidden; position: relative;}
.order{ width: 100%; overflow: hidden; margin-top: 0.4rem;}

/*------标题------*/
.order .order-title{ width: auto; height: 1.7rem; line-height: 1.7rem; color: #555; background: #fff; overflow: hidden; padding: 0 .5rem; position: relative;}
.order .order-title .order-shop{ font-size: .56rem; color: #111; overflow: hidden; display: block; padding: 0 0 0 1.1rem; position: relative;}
.order .order-title .order-shop:before{ width:1rem; height: 1rem; content: ''; background: url(../images/icon_store.svg) no-repeat center; background-size: .7rem; position: absolute; left: 0; top: .35rem;}

.order .order-title .order-num{ font-size: .56rem; color: #111; overflow: hidden; display: block;}
.order .order-title .order-status{ font-size: .52rem; color: #46a9fd; display: block;}

/*------订单产品------*/
.order .order-product{ width:auto; min-height:3.8rem; border-bottom:#f4f4f4 1px solid; overflow:hidden; padding:.4rem 3.4rem .4rem 3.6rem; position:relative;}

.order .order-product img{ width: 2.6rem; height: 2.6rem; position:absolute; left:.5rem; top:.4rem;}
.order .order-product a, .order .order-product h1{ font-size:.56rem; color:#333;}

.order .order-product .order-price{ text-align: right; position: absolute; right: .5rem; top: .4rem;}
.order .order-product .order-price p{ font-size: .52rem; color:#777; margin-top:.1rem;}
.order .order-product .order-price p.order-product-price{ font-size:.66rem; color:#f80;}

/*------订单总价------*/
.order-summary{ width: 100%; color: #111; text-align: right; background: #fff; border-top:#eee 1px solid; padding: .3rem .5rem; position: relative;}
.order-summary:after{ width:.4rem; height:.4rem; content:''; border-top:#e4e4e4 1px solid; border-right:#ccc 1px solid; background:#fff; transform:rotate(-45deg); -webkit-transform:rotate(-45deg); -ms-transform:rotate(-45deg); -o-transform:rotate(-45deg); position:absolute; right:1rem; top:-.2rem;}

.order-summary span{ font-size: .52rem; margin: 0 .1rem;}
.order-summary b{ font-size: .72rem; font-weight: normal; color: #f80;}
.order-summary span{ font-size: .52rem; margin: 0 .1rem;}

/*------订单操作------*/
.order-toolbar{ width: auto; background: #fff; overflow: hidden; padding: .3rem .4rem;}
.order-toolbar a{ height: 1.1rem; line-height: 1.1rem; font-size: .52rem; color: #555; border: #d4d4d4 1px solid; border-radius: .2rem; padding: 0 .5rem; display: block; float: right; margin-left: .3rem;}
.order-toolbar a.active{ color: #46a9fd;}



/**********订单详情**********/
/**************************/
/******订单状态******/
.order-details-status{ width: 100%; background: linear-gradient(90deg, #27a6fa, #8e71f5); background: -webkit-linear-gradient(left, #27a6fa, #8e71f5); overflow: hidden; padding: 0.8rem 0.4rem 0.8rem 1rem;}

.order-details-status h1{ font-size: .8rem; color: #fff; float: left;}
.order-details-status a{ font-size: .56rem; color: #fff; border: #fff 1px solid; border-radius: .2rem; padding: .2rem .5rem; display: block; float: right;}


/******收货人******/
/*------主体------*/
.order-details-recipient{ width:100%; background:#fff; overflow:hidden; padding:.5rem .3rem .56rem 1.8rem; position:relative;}
.order-details-recipient:before{ width:1rem; height:1rem; content:''; background:url(../images/icon_location.svg) no-repeat center; background-size:auto .8rem; position:absolute; left:.4rem; top:.6rem;}

/*------文字------*/
.order-details-recipient .recipient-people{ width:100%; color:#333; overflow:hidden;}
.order-details-recipient .recipient-people h3{ font-size:.66rem; font-weight:normal; float:left;}
.order-details-recipient .recipient-people span{ font-size:.56rem; display:block; float:left; margin-left:.5rem; margin-top:.2rem;}

.order-details-recipient .recipient-address{ width:100%; margin-top:.2rem;}
.order-details-recipient .recipient-address p{ font-size:.52rem; color:#666;}


/******详细信息******/

.order-details-info{ width: 100%; background: #fff; overflow: hidden; padding: .5rem;}
.order-details-info li{ width: 100%; line-height: 1rem; overflow: hidden;}

.order-details-info label, .order-details-info span{ font-size: .5rem; color: #999; display: block; float: left;}


