package ab

import (
	"50go/internal/dao"
	"50go/internal/utils"
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	controller "50go/internal/controller"
	sns "50go/internal/utils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
)

type ABController struct{}

type PostReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.Meta `method:"post"  summary:"获得系统信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}

type GetReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.<PERSON>a `method:"get"  summary:"获得系统信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}
type GetListRes struct {
	Name string
	Id   int
}

func GetPageData(r *ghttp.Request) (int, int) {
	page := r.GetQuery("page", 1).Int()
	psize := r.GetQuery("psize", 20).Int()
	return page, psize
}

// // 规范路由
// func (c *ABController) GetJiuy(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
// 	r := g.RequestFromCtx(ctx)
// 	id := r.Get("ID").String()

// 	md := dao.Task.Ctx(r.Context())
// 	data, err := md.Where("ID", id).One()

// 	imgs := strings.Split(data["imgs"].String(), ",")
// 	g.RequestFromCtx(ctx).Response.WriteJson(
// 		g.Map{
// 			"data": data,
// 			"code": 1,
// 			"imgs": imgs,
// 		})
// 	return
// 	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
// }

func (c *ABController) GetJyById(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	jyid := r.Get("jyid").String()
	md := dao.ABJiuy.Ctx(r.Context()).Where("Guid", jyid)

	md = md.LeftJoinOnFields("User", "UsjID", "=", "UID").LeftJoinOnFields("Admin", "shren", "=", "uguid").Fields("AB_jiuy.*,User.UTrueName as sname,User.UTel as sTel,User.UID as sID,Admin.uname as shren_name")

	data, _ := md.One()
	imgs := strings.Split(data["imgs"].String(), ",")
	BegainImg := strings.Split(data["BegainImg"].String(), ",")
	EndImg := strings.Split(data["EndImg"].String(), ",")

	jjimgs := strings.Split(data["jjimgs"].String(), ",")
	idimgs := strings.Split(data["idimgs"].String(), ",")
	didimgs := strings.Split(data["didimgs"].String(), ",")
	caridimgs := strings.Split(data["caridimgs"].String(), ",")

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)
	//CTimg := strings.Split(data["CTimg"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"imgs": imgs,

			"BegainImg": BegainImg,
			"EndImg":    EndImg,

			"jjimgs": jjimgs,

			"idimgs":    idimgs,
			"didimgs":   didimgs,
			"caridimgs": caridimgs,

			//"CTimg": CTimg,
			// "page":  page,
			// "psize": psize,
			"data": data,
			"code": 1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 用户获得救援列表
func (c *ABController) GetJyListByuid(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	pageindex := r.Get("page_index").Int()
	pagesize := r.Get("page_size").Int()
	thetype := r.Get("type")

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid

	md := dao.ABJiuy.Ctx(r.Context())

	if thetype.String() == "nopay" {
		md = md.Where("paystat", 0)
	}
	if thetype.String() == "service" {
		md = md.Where("paystat", 1).WhereNot("Endstate", "完成")
	}
	if thetype.String() == "complete" {
		md = md.Where("Endstate", "完成")
	}
	if thetype.String() == "unapproved" {
		md = md.Where("sh1", "0")
	}

	// ncount, err := md.Count()
	// order, _ := md.Where("UGUID", UID).Where("OParentId", 0).Page(pageindex, pagesize).All()
	md = md.Where("UID", UID).Order("JY_ID desc").Page(pageindex, pagesize)

	data, _ := md.All()
	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			// "page":  page,
			// "psize": psize,
			"data": data,
			"code": 1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 未通过的数量
func (c *ABController) GetJySh1Byuid(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid

	md := dao.ABJiuy.Ctx(r.Context()).Where("UID", UID).Where("sh1", "0")
	data, _ := md.Count()
	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			// "page":  page,
			// "psize": psize,
			"data": data,
			"code": 1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 司机获得救援列表
func (c *ABController) SjJyList(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)

	thetype := r.Get("type")
	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	md := dao.ABJiuy.Ctx(r.Context())

	// ncount, err := md.Count()

	if thetype.String() == "完成" {
		md = md.Where("Endstate", "完成")
	} else {
		md = md.Wheref("(Endstate!=? or Endstate is null)", "完成")

	}

	md = md.Where("UsjID", UID)
	ncount, err := md.Count()
	md = md.LeftJoinOnFields("User", "UsjID", "=", "UID").LeftJoinOnFields("Admin", "shren", "=", "uguid").Fields("AB_jiuy.*,User.UTrueName as sname,User.UTel as sTel,User.UID as sID,Admin.uname as shren_name")

	pageorder, _ := md.Page(page, psize).All()
	//md := dao.ABJiuy.Ctx(r.Context())
	//data, _ := md.All()
	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"page":  page,
			"psize": psize,
			"data":  pageorder,
			"count": ncount,
			"code":  1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

//修改订单

func (c *ABController) JyEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	jyid := r.Get("jyid").String()
	md := dao.ABJiuy.Ctx(r.Context())

	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid

	if jyid == "0" || jyid == "" {

		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := gtime.Now().Time.In(loc)

		Guid := utils.GetGuid()
		mform := r.GetFormMap(map[string]interface{}{"Guid": Guid, "addTime": mytime, "UID": UID, "imgs": "", "fcren": "", "fctel": "", "fctitle": "",
			"fclat": "", "fclong": "", "jcren": "", "jctel": "", "jctitle": "", "jcaddress": "", "jclat": "", "jclong": "", "distance": "", "carjz": "", "carzt": "", "Content": "", "state": "0", "paystat": "0",
			"type": "", "jjimgs": "", "idimgs": "", "didimgs": "", "caridimgs": "", "price": ""})

		result, err := md.Data(mform).Insert()
		fmt.Println("tt", err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "添加成功",
				"data": result,
				"jyid": Guid,
			})
		}

	} else {
		mform := r.GetFormMap(map[string]interface{}{"UID": UID, "imgs": "", "fcren": "", "fctel": "", "fctitle": "",
			"fclat": "", "fclong": "", "jcren": "", "jctel": "", "jctitle": "", "jcaddress": "", "jclat": "", "jclong": "", "distance": "", "carjz": "", "carzt": "", "Content": "", "state": "0", "paystat": "0",
			"type": "", "jjimgs": "", "idimgs": "", "didimgs": "", "caridimgs": "", "price": ""})
		result, err := md.Where("Guid", jyid).Update(mform)
		fmt.Println(jyid, err)
		if err == nil {
			// 异步发送“送车订单已修改”短信给审核员（模板5721）

			go func() {
				// 创建子上下文，避免受外部 ctx 取消的影响
				childCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()

				// 统一走阿里云短信服务：新订单通知审核员（模板 5714）
				code, erro, res, sendErr := controller.SendDeliverySms(childCtx, controller.SmsTypeOrderUpdated, jyid)
				if sendErr != nil {
					g.Log().Warningf(r.Context(), "修改订单后短信发送失败 - 订单号:%s, err:%v", jyid, sendErr)
					return
				}
				log.Printf("修改订单后短信发送结果 - 订单号:%s, code:%d, err:%v, resp:%s", jyid, code, erro, res)
			}()

			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "修改成功",
				"data": result,
				"jyid": jyid,
			})
		}
	}

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 规范路由
func (c *ABController) AbStore(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)
	md := dao.ABStore.Ctx(r.Context())
	ncount, err := md.Count()
	stores, _ := md.FieldsEx("Content").Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"page":   page,
			"psize":  psize,
			"data":   stores,
			"code":   1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *ABController) JyBeginEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	guid := r.GetForm("Guid").String()
	md := dao.ABJiuy.Ctx(r.Context())
	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)
	mform := r.GetFormMap(map[string]interface{}{"BegainUsjID": UID, "BegainTime": mytime, "BegainImg": "", "BegainContent": ""})
	result, err := md.Where("Guid", guid).Update(mform)
	if err == nil {
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "修改送前勘探成功",
			"data": result,
		})
	}
	return
}

func (c *ABController) JyEndEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	guid := r.GetForm("Guid").String()
	md := dao.ABJiuy.Ctx(r.Context())
	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)
	mform := r.GetFormMap(map[string]interface{}{"EndUsjID": UID, "EndTime": mytime, "EndImg": "", "EndContent": "", "Endstate": ""})
	result, err := md.Where("Guid", guid).Update(mform)
	if err == nil {
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "修改救援结果成功",
			"data": result,
		})
	}
	return
}

// 驳回后的修改 用户
func (c *ABController) JyShEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	guid := r.GetForm("Guid").String()
	md := dao.ABJiuy.Ctx(r.Context())
	// UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	// mytime := gtime.Now()
	mform := r.GetFormMap(map[string]interface{}{"jcren": "", "jctel": "", "jjimgs": "", "idimgs": "", "Content": "", "sh1": ""})
	result, err := md.Where("Guid", guid).Update(mform)
	if err == nil {
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "修改结果成功",
			"data": result,
		})
	}
	return
}

// NotifyTask 任务完成后发送通知
func (c *ABController) NotifyTask(r *ghttp.Request) {

	jyid := r.Get("jyid").String()

	if jyid == "" {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "未提供任务ID",
		})
		return
	}

	// 获取任务信息
	taskInfo, err := dao.ABJiuy.Ctx(r.Context()).Where("Guid", jyid).One()
	if err != nil {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "获取任务信息失败: " + err.Error(),
		})
		return
	}

	// 获取用户ID
	userID := taskInfo["UID"]
	if userID == nil {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "任务未关联用户",
		})
		return
	}

	// 构建自定义消息内容
	customData := make(map[string]map[string]string)
	customData["thing1"] = map[string]string{
		"value": "任务状态变更通知",
	}
	customData["time2"] = map[string]string{
		"value": time.Now().Format("2006-01-02 15:04"),
	}
	customData["thing3"] = map[string]string{
		"value": "您的任务已完成，请查看详情",
	}

	// 调用封装好的发送模板消息函数
	err = utils.SendWxTemplateMsg(userID, "", "pages/task/detail?id="+jyid, customData, r.Context())
	if err != nil {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "发送消息失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJson(g.Map{
		"code": 0,
		"msg":  "发送通知成功",
	})
}

// SendAuditSms 发送审核结果短信通知
func (c *ABController) SendAuditSms(r *ghttp.Request) {
	guid := r.GetForm("guid").String()
	auditResult := r.GetForm("audit_result").String()
	shmark := r.GetForm("shmark").String()

	// 如果审核备注超过100字符，进行截断
	if len(shmark) > 100 {
		shmark = shmark[:100]
	}

	if guid == "" {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "订单ID不能为空",
		})
		return
	}

	// 将审核结果与备注更新回订单，便于短信服务读取
	_, _ = dao.ABJiuy.Ctx(r.Context()).Where("Guid", guid).Update(g.Map{
		"sh1":    auditResult,
		"shmark": shmark,
	})

	// 使用统一短信服务：审核结果通知客户（模板 5715/5716）
	code, erro, resstring, err := controller.SendDeliverySms(r.Context(), controller.SmsTypeAuditResultCustomer, guid)

	if err != nil {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "短信发送失败: " + err.Error(),
		})
		return
	}

	if code == 200 {
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "短信发送成功",
			"data": resstring,
		})
	} else {
		errorMsg := ""
		if erro != nil {
			errorMsg = erro.Error()
		}
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "短信发送失败: " + errorMsg,
			"data": resstring,
		})
	}
}
