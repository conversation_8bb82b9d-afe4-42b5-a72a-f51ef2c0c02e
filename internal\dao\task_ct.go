// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalTaskCTDao is internal type for wrapping internal DAO implements.
type internalTaskCTDao = *internal.TaskCTDao

// taskCTDao is the data access object for table Task_CT.
// You can define custom methods on it to extend its functionality as you wish.
type taskCTDao struct {
	internalTaskCTDao
}

var (
	// TaskCT is globally public accessible object for table Task_CT operations.
	TaskCT = taskCTDao{
		internal.NewTaskCTDao(),
	}
)

// Fill with you ideas below.
