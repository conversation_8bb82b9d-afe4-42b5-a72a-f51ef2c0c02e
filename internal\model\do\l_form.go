// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// LForm is the golang structure of table L_Form for DAO operations like Where/Data.
type LForm struct {
	g.Meta `orm:"table:L_Form, do:true"`
	Fid    interface{} //
	FNmae  interface{} //
	FStat  interface{} //
	Fother interface{} //
	F1     interface{} //
	FMust  interface{} //
	FTime  interface{} //
	F1C    interface{} //
}
