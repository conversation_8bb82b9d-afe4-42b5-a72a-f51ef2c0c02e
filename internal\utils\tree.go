package utils

import (
	"fmt"
	"reflect"

	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// 获取树状数组
func GetTreeArray(num []map[string]interface{}, pid int32, itemprefix string) []map[string]interface{} {
	childs := ToolFar(num, pid) //获取pid下的所有数据
	var chridnum []map[string]interface{}
	if childs != nil {
		var number int = 1
		var total int = len(childs)
		for _, v := range childs {
			j := ""
			k := ""
			if number == total {
				j += "└"
				k = ""
				if itemprefix != "" {
					k = "&nbsp;"
				}

			} else {
				j += "├"
				k = ""
				if itemprefix != "" {
					k = "│"
				}
			}
			spacer := ""
			if itemprefix != "" {
				spacer = itemprefix + j
			}
			v["spacer"] = spacer
			v["children"] = GetTreeArray(num, v["CID"].(int32), itemprefix+k+"&nbsp;")
			chridnum = append(chridnum, v)
			number++
		}
	}
	return chridnum
}

// 获取菜单树形
func GetRuleTreeArray(num []map[string]interface{}, pid int32, itemprefix string) []map[string]interface{} {
	var chridnum []map[string]interface{}
	if num == nil {
		return chridnum
	} else {
		childs := ToolFar(num, pid) //获取pid下的所有数据
		if childs != nil {
			var number int = 1
			var total int = len(childs)
			for _, v := range childs {
				j := ""
				k := ""
				if number == total {
					j += "└"
					k = ""
					if itemprefix != "" {
						k = "&nbsp;"
					}

				} else {
					j += "├"
					k = ""
					if itemprefix != "" {
						k = "│"
					}
				}
				spacer := ""
				if itemprefix != "" {
					spacer = itemprefix + j
				}
				v["spacer"] = spacer
				v["children"] = GetTreeArray(num, v["CID"].(int32), itemprefix+k+"&nbsp;")
				chridnum = append(chridnum, v)
				number++
			}
		}
		return chridnum
	}
}

// 获取菜单树形-打包layui Tree菜单

// 默认字段 name  id pid  children
func GetRuleTreeArrayLayui(num []map[string]interface{}, pid int32) []map[string]interface{} {
	var chridnum []map[string]interface{}
	childs := ToolFar(num, pid) //获取pid下的所有数据
	if childs != nil {
		for _, v := range childs {
			newdata := GetRuleTreeArrayLayui(num, v["id"].(int32))
			if newdata != nil {
				v["children"] = GetRuleTreeArrayLayui(num, v["id"].(int32))
			} else {
				v["children"] = make([]string, 0)
			}

			chridnum = append(chridnum, v)
		}
	}
	return chridnum
}

// 获取菜单树形-打包代码菜单
func GetRuleTreeArrayByPack(num []map[string]interface{}, pid int32) []map[string]interface{} {
	childs := ToolFar(num, pid) //获取pid下的所有数据
	var chridnum []map[string]interface{}
	if childs != nil {
		for _, v := range childs {
			newdata := GetRuleTreeArrayByPack(num, v["CID"].(int32))
			if newdata != nil {

				v["children"] = GetRuleTreeArrayByPack(num, v["CID"].(int32))
			}
			v["id"] = v["CID"]
			v["title"] = v["CNmae"]
			chridnum = append(chridnum, v)
		}
	}
	return chridnum
}

// 获取菜单子树结构  有父id 那种 从数据库取出来的一维 转 树结构
func GetMenuChildrenArray(pdata []map[string]interface{}, parent_id int64, pid_file string) []map[string]interface{} {
	var returnList []map[string]interface{}
	for _, v := range pdata {
		if v[pid_file].(int64) == parent_id {
			children := GetMenuChildrenArray(pdata, v["CID"].(int64), pid_file)
			if children != nil {
				v["children"] = children
			}
			returnList = append(returnList, v)
		}
	}
	return returnList
}

// 获取菜单子树结构
func GetMenuChildrenArraylist(pdata []map[string]interface{}, parent_id int64) []map[string]interface{} {
	var returnList []map[string]interface{}
	for _, v := range pdata {
		if v["pid"].(int64) == parent_id {
			children := GetMenuChildrenArraylist(pdata, v["value"].(int64))
			if children != nil {
				v["children"] = children
			}
			returnList = append(returnList, v)
		}
	}
	return returnList
}

// 获取菜单子树结构  json 格式 格式化
func GetMenuChildrenlist(pdata []map[string]interface{}, items []string) []map[string]interface{} {
	var returnList []map[string]interface{}
	for _, v := range pdata {
		v["id"] = v["id"]
		v["spread"] = true
		_, found := Find(items, v["id"])
		if found {
			v["checked"] = true
		} else {
			v["checked"] = false
		}

		v["title"] = v["title"]

		chi, err := v["children"].([]interface{})
		if !err {
			fmt.Println("转换失败:", err)
		} else {
			mapSlice, err2 := ConvertToMapSlice(chi)
			if err2 != nil {
				fmt.Println("转换失败:", err2)
			}
			//fmt.Println("转换后的切片:", mapSlice)
			//tmp1 :=

			//var tmp2 = []map[string]interface{}{} //必可不少，分配内存
			// 2.将interface{}转map
			// for _, nchi := range mMap {
			// 	tmp2 = append(tmp2, nchi.(map[string]interface{}))
			// }

			mychildren := GetMenuChildrenlist(mapSlice, items)

			if mychildren != nil {
				v["children"] = mychildren
			}

		}

		// 转换操作

		returnList = append(returnList, v)
	}
	return returnList
}

// 获取菜单子树结构  json 格式 格式化 配权限
func GetMenulist(pdata []map[string]interface{}, items []string) []map[string]interface{} {
	var returnList []map[string]interface{}
	for _, v := range pdata {
		_, found := Find(items, v["id"])
		if !found {
			fmt.Println("无权限:")
		} else {
			chi, err := v["children"].([]interface{})
			if !err {
				fmt.Println("转换失败:", err)
			} else {
				mapSlice, err2 := ConvertToMapSlice(chi)
				if err2 != nil {
					fmt.Println("转换失败:", err2)
				}
				// fmt.Println("转换后的切片:", mapSlice)

				mychildren := GetMenulist(mapSlice, items)

				if mychildren != nil {
					v["children"] = mychildren
				}

			}
			returnList = append(returnList, v)
		}

		// 转换操作

	}
	return returnList
}

// 获取pid下所有数组
func ToolFar(data []map[string]interface{}, pid int32) []map[string]interface{} {
	var mapString []map[string]interface{}
	for _, v := range data {
		if v != nil && v["pid"] != nil {
			if v["pid"] == pid {
				mapString = append(mapString, v)
			}
		}
	}
	// fmt.Println(mapString)
	return mapString
}

// 2.将getTreeArray的结果返回为二维数组
func GetTreeList_txt(data []map[string]interface{}, field string) []map[string]interface{} {
	var midleArr []map[string]interface{}
	for _, v := range data {
		var children []map[string]interface{}
		if _, ok := v["children"]; ok {
			children = v["children"].([]map[string]interface{})
		} else {
			children = make([]map[string]interface{}, 0)
		}
		delete(v, "children")
		v[field+"_txt"] = v["spacer"].(string) + " " + v[field+""].(string)
		if len(children) > 0 {
			v["haschild"] = 1
		} else {
			v["haschild"] = 0
		}
		if _, ok := v["CID"]; ok {
			midleArr = append(midleArr, v)
		}
		if len(children) > 0 {
			newarr := GetTreeList_txt(children, field)
			midleArr = ArrayMerge_x(midleArr, newarr)
		}
	}
	return midleArr
}

// 数组拼接
func ArrayMerge_x(ss ...[]map[string]interface{}) []map[string]interface{} {
	n := 0
	for _, v := range ss {
		n += len(v)
	}
	s := make([]map[string]interface{}, 0, n)
	for _, v := range ss {
		s = append(s, v...)
	}
	return s
}

// Find获取一个切片并在其中查找元素。如果找到它，它将返回它的密钥，否则它将返回-1和一个错误的bool。
func Find(slice []string, val interface{}) (int, bool) {
	fmt.Println(slice, val)
	for i, item := range slice {
		if item == val {
			return i, true
		}
	}
	return -1, false
}

func ConvertToMapSlice(slice []interface{}) ([]map[string]interface{}, error) {
	var result []map[string]interface{}
	for _, item := range slice {
		if mapItem, ok := item.(map[string]interface{}); ok {
			result = append(result, mapItem)
		} else {
			return nil, fmt.Errorf("元素不是map[string]interface{}: %v", item)
		}
	}
	return result, nil
}

// ParentSonSort 有层级关系的数组,父级-》子级 排序
func ParentSonSort(list g.List, params ...interface{}) g.List {
	args := make([]interface{}, 8)
	for k, v := range params {
		if k == 8 {
			break
		}
		args[k] = v
	}
	var (
		pid       int    //父级id
		level     int    //层级数
		fieldName string //父级id键名
		id        string //id键名
		levelName string //层级名称
		title     string //标题名称
		breaks    int    //中断层级
		prefixStr string //字符串前缀
	)
	pid = gconv.Int(GetSliceByKey(args, 0, 0))
	level = gconv.Int(GetSliceByKey(args, 1, 0))
	fieldName = gconv.String(GetSliceByKey(args, 2, "pid"))
	id = gconv.String(GetSliceByKey(args, 3, "id"))
	levelName = gconv.String(GetSliceByKey(args, 4, "flg"))
	title = gconv.String(GetSliceByKey(args, 5, "title"))
	breaks = gconv.Int(GetSliceByKey(args, 6, -1))
	prefixStr = gconv.String(GetSliceByKey(args, 7, "─"))
	//定义一个新slice用于返回
	var returnSlice g.List
	for _, v := range list {
		if pid == gconv.Int(v[fieldName]) {
			v[levelName] = level
			levelClone := level
			titlePrefix := ""
			for {
				if levelClone < 0 {
					break
				}
				titlePrefix += prefixStr
				levelClone--
			}
			titlePrefix = "├" + titlePrefix
			if level == 0 {
				v["title_prefix"] = ""
			} else {
				v["title_prefix"] = titlePrefix
			}
			v["title_show"] = fmt.Sprintf("%s%s", v["title_prefix"], v[title])
			returnSlice = append(returnSlice, v)
			if breaks != -1 && breaks == level {
				continue
			}
			args[0] = v[id]
			args[1] = level + 1
			newSlice2 := ParentSonSort(list, args...)
			if len(newSlice2) > 0 {
				returnSlice = append(returnSlice, newSlice2...)
			}
		}
	}
	return returnSlice
}

// PushSonToParent 有层级关系的数组 ,将子级压入到父级（树形结构）
func PushSonToParent(list g.List, params ...interface{}) g.List {
	args := make([]interface{}, 7)
	for k, v := range params {
		if k == 7 {
			break
		}
		args[k] = v
	}
	var (
		pid         string      //父级id
		fieldName   string      //父级id键名
		id          string      //id键名
		key         string      //子级数组键名
		filter      string      //过滤键名
		filterVal   interface{} //过滤的值
		showNoChild bool        //是否显示不存在的子级健
	)
	pid = gconv.String(GetSliceByKey(args, 0, 0))
	fieldName = gconv.String(GetSliceByKey(args, 1, "pid"))
	id = gconv.String(GetSliceByKey(args, 2, "id"))
	key = gconv.String(GetSliceByKey(args, 3, "children"))
	filter = gconv.String(GetSliceByKey(args, 4, ""))
	filterVal = GetSliceByKey(args, 5, nil)
	showNoChild = gconv.Bool(GetSliceByKey(args, 6, true))
	var returnList g.List
	for _, v := range list {
		if gconv.String(v[fieldName]) == pid {
			if filter != "" {
				if reflect.DeepEqual(v[filter], filterVal) {
					args[0] = v[id]
					child := PushSonToParent(list, args...)
					if child != nil || showNoChild {
						v[key] = child
					}
					returnList = append(returnList, v)
				}
			} else {
				args[0] = v[id]
				child := PushSonToParent(list, args...)
				if child != nil || showNoChild {
					v[key] = child
				}
				returnList = append(returnList, v)
			}
		}
	}
	return returnList
}

// GetSliceByKey 获取切片里的值 若为nil 可设置默认值val
func GetSliceByKey(args []interface{}, key int, val interface{}) interface{} {
	var value interface{}
	if args[key] != nil {
		value = args[key]
	} else {
		value = val
	}
	return value
}

// FindSonByParentId 有层级关系的切片，通过父级id查找所有子级id数组
// parentId 父级id
// parentIndex 父级索引名称
// idIndex id索引名称
func FindSonByParentId(list g.List, parentId interface{}, parentIndex, idIndex string) g.List {
	newList := make(g.List, 0, len(list))
	for _, v := range list {
		if reflect.DeepEqual(v[parentIndex], parentId) {
			newList = append(newList, v)
			fList := FindSonByParentId(list, v[idIndex], parentIndex, idIndex)
			newList = append(newList, fList...)
		}
	}
	return newList
}

// GetTopPidList 获取最顶层 parent Id
func GetTopPidList(list g.List, parentIndex, idIndex string) *garray.Array {
	arr := garray.NewArray()
	for _, v1 := range list {
		tag := true
		for _, v2 := range list {
			if v1[parentIndex] == v2[idIndex] {
				tag = false
				break
			}
		}
		if tag {
			arr.PushRight(v1[parentIndex])
		}
	}
	return arr.Unique()
}

// FindParentBySonPid 有层级关系的数组，通过子级fid查找所有父级数组
func FindParentBySonPid(list g.List, id int, params ...interface{}) g.List {
	args := make([]interface{}, 4)
	for k, v := range params {
		if k == 4 {
			break
		}
		args[k] = v
	}
	var (
		filter      = gconv.String(GetSliceByKey(args, 0, "filter")) //过滤键名
		fPid        = gconv.String(GetSliceByKey(args, 1, "pid"))    //父级id字段键名
		filterValue = GetSliceByKey(args, 2, nil)                    //过滤键值
		fid         = gconv.String(GetSliceByKey(args, 3, "id"))     //id字段键名
	)
	rList := make(g.List, 0, len(list))
	for _, v := range list {
		if gconv.Int(v[fid]) == id {
			if fv, ok := v[filter]; ok {
				if reflect.DeepEqual(fv, filterValue) {
					rList = append(rList, v)
				}
			} else {
				rList = append(rList, v)
			}
			r := FindParentBySonPid(list, gconv.Int(v[fPid]), filter, fPid, filterValue, fid)
			rList = append(rList, r...)
		}
	}
	return rList
}

// FindTopParent
/**
 * 根据id查询最顶层父级信息
 * @param list 有层级关系的数组
 * @param id 查找的id
 * @param string fpid 父级id键名
 * @param string fid 当前id键名
 * @return g.Map
 */
func FindTopParent(list g.List, id int64, params ...interface{}) g.Map {
	if len(list) == 0 {
		return g.Map{}
	}
	args := make([]interface{}, 2)
	for k, v := range params {
		if k == 2 {
			break
		}
		args[k] = v
	}
	var (
		fPid = gconv.String(GetSliceByKey(args, 0, "pid")) //父级id字段键名
		fid  = gconv.String(GetSliceByKey(args, 1, "id"))  //id字段键名
	)
	hasParent := true
	top := g.Map{}
	//找到要查找id值的数组
	for _, v := range list {
		if gconv.Int64(v[fid]) == gconv.Int64(id) {
			top = v
			break
		}
	}
	for {
		if !hasParent {
			break
		}
		//查询最顶层
		for _, v := range list {
			if gconv.Int64(top[fPid]) == gconv.Int64(v[fid]) {
				top = v
				hasParent = true
				break
			}
			hasParent = false
		}
	}
	return top
}
