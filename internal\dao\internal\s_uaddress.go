// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SUaddressDao is the data access object for the table S_UADDRESS.
type SUaddressDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SUaddressColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SUaddressColumns defines and stores column names for the table S_UADDRESS.
type SUaddressColumns struct {
	Uaid           string //
	Uid            string //
	Uopenid        string //
	UPeopleName    string //
	UAPhone        string //
	UAProvance     string //
	UACity         string //
	UADistrict     string //
	UAStreet       string //
	UAddName       string //
	UAddress       string //
	UDetailAddress string //
	UAAddTime      string //
	UAState        string //
	Ulatng         string //
}

// sUaddressColumns holds the columns for the table S_UADDRESS.
var sUaddressColumns = SUaddressColumns{
	Uaid:           "UAID",
	Uid:            "UID",
	Uopenid:        "Uopenid",
	UPeopleName:    "UPeopleName",
	UAPhone:        "UAPhone",
	UAProvance:     "UAProvance",
	UACity:         "UACity",
	UADistrict:     "UADistrict",
	UAStreet:       "UAStreet",
	UAddName:       "UAddName",
	UAddress:       "UAddress",
	UDetailAddress: "UDetailAddress",
	UAAddTime:      "UAAddTime",
	UAState:        "UAState",
	Ulatng:         "Ulatng",
}

// NewSUaddressDao creates and returns a new DAO object for table data access.
func NewSUaddressDao(handlers ...gdb.ModelHandler) *SUaddressDao {
	return &SUaddressDao{
		group:    "default",
		table:    "S_UADDRESS",
		columns:  sUaddressColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SUaddressDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SUaddressDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SUaddressDao) Columns() SUaddressColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SUaddressDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SUaddressDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SUaddressDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
