// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// LForm is the golang structure for table L_Form.
type LForm struct {
	Fid    int    `json:"FID"    orm:"FID"    ` //
	FNmae  string `json:"FNmae"  orm:"FNmae"  ` //
	FStat  int    `json:"FStat"  orm:"FStat"  ` //
	Fother string `json:"Fother" orm:"Fother" ` //
	F1     string `json:"F1"     orm:"F1"     ` //
	FMust  int    `json:"FMust"  orm:"FMust"  ` //
	FTime  string `json:"FTime"  orm:"FTime"  ` //
	F1C    string `json:"F1C"    orm:"F1C"    ` //
}
