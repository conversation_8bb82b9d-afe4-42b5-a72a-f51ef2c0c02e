// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLNewssDao is internal type for wrapping internal DAO implements.
type internalLNewssDao = *internal.LNewssDao

// lNewssDao is the data access object for table L_Newss.
// You can define custom methods on it to extend its functionality as you wish.
type lNewssDao struct {
	internalLNewssDao
}

var (
	// LNewss is globally public accessible object for table L_Newss operations.
	LNewss = lNewssDao{
		internal.NewLNewssDao(),
	}
)

// Fill with you ideas below.
