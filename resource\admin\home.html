<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>工作台</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../admin/css/other/console.css" />
</head>

<body>
    <div class="pear-container">
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-row layui-col-space10">

                    <div class="layui-col-md12">
                        <div class="layui-card-header">
                            负载
                        </div>
                        <div class="layui-card">
                            <div class="layui-card-body">
                                <div class="layui-row layui-col-space10">
                                    <div class="layui-col-md2 layui-col-sm2 layui-col-xs2">

                                        <div class="cell">核心数:{{.sysInfo.cpuNum}}</div>

                                        <div class="cell">使用率:{{.sysInfo.cpuUsed}}%</div>
                                    </div>
                                    <div class="layui-col-md4 layui-col-sm4 layui-col-xs4">

                                        <div id="echarts-2" style="height:300px;"></div>


                                    </div>


                                    <div class="layui-col-md2 layui-col-sm2 layui-col-xs2">


                                        <div class="cell">总数:</div>

                                        <div class="cell">{{ .sysInfo.memTotal}}</div>




                                        <div class="cell">已使用:</div>


                                        <div class="cell">{{ .sysInfo.memUsed}}</div>




                                        <div class="cell">剩余:</div>


                                        <div class="cell">{{ .sysInfo.memFree}}</div>




                                        <div class="cell">GFast系统使用:</div>


                                        <div class="cell">{{ .sysInfo.goUsed}}</div>


                                    </div>
                                    <div class="layui-col-md4 layui-col-sm4 layui-col-xs4">

                                        <div id="echarts-1" style="height:300px;"></div>

                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                系统信息
                            </div>
                            <div class="layui-card-body">
                                <div class="layui-row layui-col-space10">
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-xs3">
                                        <div class="deputy">
                                            <div class="deputy-label"><i class="layui-icon layui-icon-app"></i>操作系统
                                            </div>
                                            <div class="">{{.sysInfo.sysOsName}}</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-xs3">
                                        <div class="deputy">
                                            <div class="deputy-label">系统架构</div>
                                            <div class="">{{.sysInfo.sysOsArch}}</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-xs3">
                                        <div class="deputy">
                                            <div class="deputy-label">服务器名称</div>
                                            <div class="">{{.sysInfo.sysComputerName}}</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-xs3">
                                        <div class="deputy">
                                            <div class="deputy-label">服务器IP</div>
                                            <div class="">{{.sysInfo.sysComputerIp}}</div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md3 layui-col-sm3 layui-col-xs3">
                                        <div class="deputy">
                                            <div class="deputy-label">Go语言版本</div>
                                            <div class="">{{.sysInfo.goVersion}}</div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md3 layui-col-sm3 layui-col-xs3">
                                        <div class="deputy">
                                            <div class="deputy-label">启动时间</div>
                                            <div class="">{{.sysInfo.goStartTime}}</div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md3 layui-col-sm3 layui-col-xs3">
                                        <div class="deputy">
                                            <div class="deputy-label">运行时长</div>
                                            <div class="">{{.sysInfo.goRunTime}}</div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md3 layui-col-sm3 layui-col-xs3">
                                        <div class="deputy">
                                            <div class="deputy-label">Go语言版本</div>
                                            <div class="">{{.sysInfo.goVersion}}</div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </div>
    <script>
        layui.use(['layer', 'echarts', 'carousel', 'element', 'table'], function () {
            var $ = layui.jquery,
                layer = layui.layer,
                element = layui.element,
                echarts = layui.echarts,
                table = layui.table,
                carousel = layui.carousel;




            var dom1 = document.getElementById('echarts-1');
            var myChart1 = echarts.init(dom1, null, {
                renderer: 'canvas',
                useDirtyRect: false
            });

            var option1;

            option1 = {
                color: '#ea7',
                tooltip: {
                    formatter: '{a} <br/>{b} : {c}%'
                },

                series: [
                    {
                        name: '百分比',
                        type: 'gauge',
                        progress: {
                            show: true
                        },
                        detail: {
                            valueAnimation: true,
                            formatter: '{value}'
                        },
                        data: [
                            {
                                value: {{.sysInfo.memUsage }},
                    name: '内存使用率'
        }
      ]
        }
  ]
};

        if (option1 && typeof option1 === 'object') {
            myChart1.setOption(option1);
        }



        var dom = document.getElementById('echarts-2');
        var myChart = echarts.init(dom, null, {
            renderer: 'canvas',
            useDirtyRect: false
        });

        var option;

        option = {
            tooltip: {
                formatter: '{a} <br/>{b} : {c}%'
            },
            color: '#ea7',
            series: [
                {
                    name: '百分比',
                    type: 'gauge',
                    progress: {
                        show: true
                    },
                    detail: {
                        valueAnimation: true,
                        formatter: '{value}'
                    },
                    data: [
                        {
                            value: {{.sysInfo.cpuUsed }},
                name: '使用率'
                 }
      ]
    }
  ]
};

        if (option && typeof option === 'object') {
            myChart.setOption(option);
        }


        setInterval(() => {

            $.ajax({
                url: "/system/comme/sys-info-lit",
                data: {},
                type: "get",
                dataType: "json",
                headers: { 'Content-Type': 'application/json;charset=utf-8' }, //接口json格式
                success: function (res) {

                    // var res= JSON.stringify(data)
                    console.log("res==", res.sysInfo);
                    option1 = myChart1.getOption();
                    let arr1 = option1.series[0].data;
                    arr1.shift(); // 从队头删除数据* 
                    // arr1.push(res.sysInfo.memUsage); // 从对尾添加数据*
                    var xx1 = { value: res.sysInfo.memUsage, name: '内存使用率' };
                    arr1.push(xx1);
                    console.log(arr1);
                    option = myChart.getOption();

                    let arr = option.series[0].data;
                    arr.shift(); // 从队头删除数据* 
                    //arr.push(res.sysInfo.cpuUsed); // 从对尾添加数据*
                    var xx = { value: res.sysInfo.cpuUsed, name: 'CPU使用率' };
                    arr.push(xx);

                    myChart1.setOption(option1);
                    myChart.setOption(option);

                },
                error: function (data) {
                    //   layer.alert(JSON.stringify(data), {
                    //     title: data
                    // });

                    console.log("error:", data)
                }
            });


        }, 5000);

        window.onresize = function () {
            myChart1.resize();
            myChart.resize();
        }

			});




        // function   memorySizeFormat(size:any){
        //     size  = parseFloat(size);
        //     let rank =0;
        //     let rankchar ='Bytes';
        //     while(size>1024&&rankchar!='TB'){
        //       size = size/1024;
        //       rank++;
        //       if(rank==1){
        //         rankchar="KB";
        //       }
        //       else if(rank==2){
        //         rankchar="MB";
        //       }
        //       else if(rank==3){
        //         rankchar="GB";
        //       }else if(rank==4){
        //         rankchar="TB";
        //       }
        //     }
        //     return size.toFixed(2)+ " "+ rankchar;
        //   }
    </script>
</body>

</html>