// API服务相关配置和方法
const API_BASE_URL = 'https://pd.50cms.com/api';

// 获取分类列表
async function fetchCategories() {
    try {
        const response = await fetch(`${API_BASE_URL}/info/classlist`);
        const data = await response.json();
        if (data.code === 200) {
            return data.data || [];
        } else {
            throw new Error(data.message || '获取分类失败');
        }
    } catch (error) {
        console.error('获取分类列表错误：', error);
        throw error;
    }
}

// 获取新闻列表
async function fetchSchoolList(categoryId = '', page = 1, pageSize = 10) {
    try {
        const url = new URL(`${API_BASE_URL}/info/list`);
        url.searchParams.append('page', page);
        url.searchParams.append('pageSize', pageSize);
        if (categoryId) {
            url.searchParams.append('classId', categoryId);
        }
        
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.code === 200) {
            return {
                data: data.data || [],
                total: data.total || 0,
                hasMore: (data.data || []).length === pageSize
            };
        } else {
            throw new Error(data.message || '获取数据失败');
        }
    } catch (error) {
        console.error('获取新闻列表错误：', error);
        throw error;
    }
}

const apiService = {
    async publishInfo(formData) {
        try {
            const response = await fetch('/api/publish', {
                method: 'POST',
                body: formData,
                headers: {
                    'Authorization': `Bearer ${this.getToken()}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error('发布信息失败：', error);
            throw error;
        }
    }
}; 