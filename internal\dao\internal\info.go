// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// InfoDao is the data access object for the table Info.
type InfoDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  InfoColumns        // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// InfoColumns defines and stores column names for the table Info.
type InfoColumns struct {
	Id         string //
	Guid       string //
	ClassId    string //
	ClassName  string //
	UserID     string //
	Uname      string //
	Title      string //
	STitle     string //
	Reviewer   string //
	From       string //
	Tag        string //
	Viewsin    string //
	Reply      string //
	Img        string //
	Content    string //
	Url        string //
	Time       string //
	CreatTime  string //
	IsTop      string //
	IsHot      string //
	IsSlide    string //
	IsLock     string //
	IsRed      string //
	Img2       string //
	Piclist    string //
	PiclistTag string //
	Fujian     string //
	Isok       string //
	TxtAddress string //
	IsHistory  string //
	Fujianpdf  string //
	DelTime    string //
}

// infoColumns holds the columns for the table Info.
var infoColumns = InfoColumns{
	Id:         "ID",
	Guid:       "GUID",
	ClassId:    "ClassId",
	ClassName:  "ClassName",
	UserID:     "UserID",
	Uname:      "Uname",
	Title:      "Title",
	STitle:     "sTitle",
	Reviewer:   "Reviewer",
	From:       "From",
	Tag:        "Tag",
	Viewsin:    "Viewsin",
	Reply:      "Reply",
	Img:        "Img",
	Content:    "Content",
	Url:        "Url",
	Time:       "Time",
	CreatTime:  "CreatTime",
	IsTop:      "IsTop",
	IsHot:      "IsHot",
	IsSlide:    "IsSlide",
	IsLock:     "IsLock",
	IsRed:      "IsRed",
	Img2:       "Img2",
	Piclist:    "piclist",
	PiclistTag: "piclistTag",
	Fujian:     "fujian",
	Isok:       "isok",
	TxtAddress: "txtAddress",
	IsHistory:  "IsHistory",
	Fujianpdf:  "fujianpdf",
	DelTime:    "delTime",
}

// NewInfoDao creates and returns a new DAO object for table data access.
func NewInfoDao(handlers ...gdb.ModelHandler) *InfoDao {
	return &InfoDao{
		group:    "default",
		table:    "Info",
		columns:  infoColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *InfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *InfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *InfoDao) Columns() InfoColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *InfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *InfoDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *InfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
