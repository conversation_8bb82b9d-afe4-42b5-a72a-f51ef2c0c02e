// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalInfoClassDao is internal type for wrapping internal DAO implements.
type internalInfoClassDao = *internal.InfoClassDao

// infoClassDao is the data access object for table Info-Class.
// You can define custom methods on it to extend its functionality as you wish.
type infoClassDao struct {
	internalInfoClassDao
}

var (
	// InfoClass is globally public accessible object for table Info-Class operations.
	InfoClass = infoClassDao{
		internal.NewInfoClassDao(),
	}
)

// Fill with you ideas below.
