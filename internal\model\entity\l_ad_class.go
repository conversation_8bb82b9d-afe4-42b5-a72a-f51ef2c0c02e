// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// LADClass is the golang structure for table L_ADClass.
type LADClass struct {
	Id         int         `json:"ID"         orm:"ID"         ` //
	Cguid      string      `json:"CGUID"      orm:"CGUID"      ` //
	CNmae      string      `json:"CNmae"      orm:"CNmae"      ` //
	CpareID    int         `json:"CpareID"    orm:"CpareID"    ` //
	CKeyWord   string      `json:"CKeyWord"   orm:"CKeyWord"   ` //
	CUrl       string      `json:"CUrl"       orm:"CUrl"       ` //
	Csort      int         `json:"Csort"      orm:"Csort"      ` //
	CisLock    int         `json:"CisLock"    orm:"CisLock"    ` //
	CcreatTime *gtime.Time `json:"CcreatTime" orm:"CcreatTime" ` //
	DelTime    *gtime.Time `json:"delTime"    orm:"delTime"    ` //
}
