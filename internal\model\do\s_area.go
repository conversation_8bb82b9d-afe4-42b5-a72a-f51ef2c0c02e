// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SArea is the golang structure of table S_Area for DAO operations like Where/Data.
type SArea struct {
	g.Meta   `orm:"table:S_Area, do:true"`
	Id       interface{} //
	AreaName interface{} //
	PID      interface{} //
}
