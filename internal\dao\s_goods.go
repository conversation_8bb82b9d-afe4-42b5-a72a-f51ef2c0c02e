// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalSGoodsDao is internal type for wrapping internal DAO implements.
type internalSGoodsDao = *internal.SGoodsDao

// sGoodsDao is the data access object for table S_Goods.
// You can define custom methods on it to extend its functionality as you wish.
type sGoodsDao struct {
	internalSGoodsDao
}

var (
	// SGoods is globally public accessible object for table S_Goods operations.
	SGoods = sGoodsDao{
		internal.NewSGoodsDao(),
	}
)

// Fill with you ideas below.
