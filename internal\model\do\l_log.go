// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// LLog is the golang structure of table L_Log for DAO operations like Where/Data.
type LLog struct {
	g.Meta `orm:"table:L_Log, do:true"`
	Lid    interface{} //
	LTiTLE interface{} //
	LIp    interface{} //
	LTime  *gtime.Time //
	LAct   interface{} //
	LUid   interface{} //
	LUname interface{} //
}
