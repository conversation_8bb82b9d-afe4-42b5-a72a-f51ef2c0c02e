// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ABJiuy is the golang structure for table AB_jiuy.
type ABJiuy struct {
	JyId          int         `json:"JY_ID"          orm:"JY_ID"          ` //
	Guid          string      `json:"Guid"           orm:"Guid"           ` //
	OpenID        int         `json:"OpenID"         orm:"OpenID"         ` //
	Uid           string      `json:"UID"            orm:"UID"            ` //
	Fcren         string      `json:"fcren"          orm:"fcren"          ` //
	Fctel         string      `json:"fctel"          orm:"fctel"          ` //
	Fctitle       string      `json:"fctitle"        orm:"fctitle"        ` //
	Fclat         int         `json:"fclat"          orm:"fclat"          ` //
	Fclong        int         `json:"fclong"         orm:"fclong"         ` //
	Jcren         string      `json:"jcren"          orm:"jcren"          ` //
	Jctel         string      `json:"jctel"          orm:"jctel"          ` //
	Jctitle       string      `json:"jctitle"        orm:"jctitle"        ` //
	Jcaddress     string      `json:"jcaddress"      orm:"jcaddress"      ` //
	Jclat         string      `json:"jclat"          orm:"jclat"          ` //
	Jclong        string      `json:"jclong"         orm:"jclong"         ` //
	Distance      string      `json:"distance"       orm:"distance"       ` //
	Carjz         string      `json:"carjz"          orm:"carjz"          ` //
	Carzt         string      `json:"carzt"          orm:"carzt"          ` //
	Cartype       string      `json:"cartype"        orm:"cartype"        ` //
	Content       string      `json:"Content"        orm:"Content"        ` //
	AddTime       *gtime.Time `json:"addTime"        orm:"addTime"        ` //
	EditTime      *gtime.Time `json:"editTime"       orm:"editTime"       ` //
	State         int         `json:"state"          orm:"state"          ` //
	DelTime       *gtime.Time `json:"delTime"        orm:"delTime"        ` //
	Paystat       int         `json:"paystat"        orm:"paystat"        ` //
	Payway        string      `json:"payway"         orm:"payway"         ` //
	Paytime       *gtime.Time `json:"paytime"        orm:"paytime"        ` //
	OutTradeNo    string      `json:"out_trade_no"   orm:"out_trade_no"   ` //
	OOrderNumber  string      `json:"OOrderNumber"   orm:"OOrderNumber"   ` //
	Paynotice     string      `json:"paynotice"      orm:"paynotice"      ` //
	Imgs          string      `json:"imgs"           orm:"imgs"           ` //
	Jjimgs        string      `json:"jjimgs"         orm:"jjimgs"         ` //
	Idimgs        string      `json:"idimgs"         orm:"idimgs"         ` //
	Didimgs       string      `json:"didimgs"        orm:"didimgs"        ` //
	Caridimgs     string      `json:"caridimgs"      orm:"caridimgs"      ` //
	Price         float64     `json:"price"          orm:"price"          ` //
	Price2        float64     `json:"price2"         orm:"price2"         ` //
	Type          string      `json:"type"           orm:"type"           ` //
	TransactionId string      `json:"transaction_id" orm:"transaction_id" ` //
	Uwxapp        string      `json:"uwxapp"         orm:"uwxapp"         ` //
	UsjID         string      `json:"UsjID"          orm:"UsjID"          ` //
	BegainUsjID   int         `json:"BegainUsjID"    orm:"BegainUsjID"    ` //
	BegainTime    *gtime.Time `json:"BegainTime"     orm:"BegainTime"     ` //
	BegainImg     string      `json:"BegainImg"      orm:"BegainImg"      ` //
	BegainContent string      `json:"BegainContent"  orm:"BegainContent"  ` //
	EndUsjID      string      `json:"EndUsjID"       orm:"EndUsjID"       ` //
	EndTime       *gtime.Time `json:"EndTime"        orm:"EndTime"        ` //
	EndImg        string      `json:"EndImg"         orm:"EndImg"         ` //
	EndContent    string      `json:"EndContent"     orm:"EndContent"     ` //
	Yytime        *gtime.Time `json:"yytime"         orm:"yytime"         ` //
	Yydate        *gtime.Time `json:"yydate"         orm:"yydate"         ` //
	Isdel         string      `json:"isdel"          orm:"isdel"          ` //
	Endstate      string      `json:"Endstate"       orm:"Endstate"       ` //
	Sh1           string      `json:"sh1"            orm:"sh1"            ` //
	Shtime        *gtime.Time `json:"shtime"         orm:"shtime"         ` //
	Shmark        string      `json:"shmark"         orm:"shmark"         ` //
	Shren         string      `json:"shren"          orm:"shren"          ` //
}
