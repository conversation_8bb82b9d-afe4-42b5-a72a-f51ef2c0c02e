let currentCategoryId = '';
let currentPage = 1;
let isLoading = false;
let hasMore = true;

// 渲染新闻列表
function renderNewsList(newsData, isAppend = false) {
    const newsListContainer = document.getElementById('newsList');
    const newsHtml = newsData.map(item => `
        <div class="news-item">
            <a href="news-detail.html?id=${item.id}">
                <div class="news-item-content">
                    ${item.coverImg ? `<img src="${item.coverImg}" class="news-image" alt="${item.Title}">` : ''}
                    <div class="news-info">
                        <h3 class="news-title">${item.Title}</h3>
                        <p class="news-abstract">${item.description || ''}</p>
                        <div class="news-meta">
                            <span class="news-time">${formatDate(item.createTime)}</span>
                            <div class="news-stats">
                                <span class="view-count">
                                    <i class="icon-view">👁️</i>${item.viewNum || 0}
                                </span>
                                <span class="like-count">
                                    <i class="icon-like">❤️</i>${item.likeNum || 0}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    `).join('');

    if (isAppend) {
        newsListContainer.innerHTML += newsHtml;
    } else {
        newsListContainer.innerHTML = newsHtml;
    }
}

// 渲染分类列表
function renderCategories(categories) {
    const categoryList = document.getElementById('categoryList');
    const categoryItems = categories.map(category => `
        <a href="javascript:void(0)" 
           class="category-item" 
           data-id="${category.id}">${category.name}</a>
    `).join('');
    
    categoryList.innerHTML = `
        <a href="javascript:void(0)" class="category-item active" data-id="">全部</a>
        ${categoryItems}
    `;

    // 分类点击事件
    categoryList.addEventListener('click', async (e) => {
        if (e.target.classList.contains('category-item')) {
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });
            e.target.classList.add('active');

            currentCategoryId = e.target.dataset.id;
            currentPage = 1;
            await loadNewsData();
        }
    });
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

// 显示/隐藏骨架屏
function toggleSkeleton(show) {
    document.getElementById('skeletonList').style.display = show ? 'block' : 'none';
    document.getElementById('newsList').style.display = show ? 'none' : 'block';
}

// 显示/隐藏加载提示
function toggleLoading(show) {
    document.getElementById('loadingTip').style.display = show ? 'block' : 'none';
}

// 显示/隐藏错误提示
function toggleError(show, message = '加载失败') {
    const errorTip = document.getElementById('errorTip');
    errorTip.style.display = show ? 'block' : 'none';
    if (show) {
        errorTip.querySelector('.error-message').textContent = message;
    }
}

// 初始化页面数据
async function initPageData() {
    try {
        const categories = await fetchCategories();
        if (categories && categories.length > 0) {
            renderCategories(categories);
        }
        await loadNewsData();
    } catch (error) {
        console.error('初始化页面数据失败：', error);
        toggleError(true, '加载失败，请重试');
    }
}

// 加载更多数据
async function loadMoreData() {
    if (isLoading || !hasMore) return;
    currentPage++;
    await loadNewsData(true);
}

// 重试加载
function retryLoad() {
    toggleError(false);
    loadNewsData();
}

// 初始化无限滚动
function initInfiniteScroll() {
    const observer = new IntersectionObserver(async (entries) => {
        if (entries[0].isIntersecting && !isLoading && hasMore) {
            await loadMoreData();
        }
    });

    observer.observe(document.querySelector('#loadingTip'));
}

// 加载新闻数据
async function loadNewsData(isLoadMore = false) {
    if (isLoading) return;
    isLoading = true;
    
    if (!isLoadMore) {
        toggleSkeleton(true);
    }
    toggleLoading(isLoadMore);
    toggleError(false);

    try {
        const result = await fetchSchoolList(currentCategoryId, currentPage);
        
        if (result && result.data && result.data.length > 0) {
            hasMore = result.hasMore;
            renderNewsList(result.data, isLoadMore);
        } else if (!isLoadMore) {
            toggleError(true, '暂无数据');
        }
    } catch (error) {
        console.error('加载新闻数据失败：', error);
        if (!isLoadMore) {
            toggleError(true, '加载失败，请重试');
        }
    } finally {
        isLoading = false;
        toggleLoading(false);
        toggleSkeleton(false);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPageData();
    initInfiniteScroll();
}); 