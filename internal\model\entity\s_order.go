// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SOrder is the golang structure for table S_Order.
type SOrder struct {
	OrderID                   int         `json:"OrderID"                   orm:"OrderID"                   ` //
	GuidNU                    string      `json:"GuidNU"                    orm:"GuidNU"                    ` //
	OParentId                 int         `json:"OParentId"                 orm:"OParentId"                 ` //
	ParentOopenID             string      `json:"ParentOopenID"             orm:"ParentOopenID"             ` //
	ParentParentOopenID       string      `json:"ParentParentOopenID"       orm:"ParentParentOopenID"       ` //
	ParentParentParentOopenID string      `json:"ParentParentParentOopenID" orm:"ParentParentParentOopenID" ` //
	Uguid                     string      `json:"UGUID"                     orm:"UGUID"                     ` //
	Spid                      int         `json:"SPID"                      orm:"SPID"                      ` //
	OStoreId                  int         `json:"OStoreId"                  orm:"OStoreId"                  ` //
	ISvipleve                 int         `json:"ISvipleve"                 orm:"ISvipleve"                 ` //
	OState                    int         `json:"OState"                    orm:"OState"                    ` //
	OType                     string      `json:"OType"                     orm:"OType"                     ` //
	OpayType                  string      `json:"OpayType"                  orm:"OpayType"                  ` //
	OPayState                 int         `json:"OPayState"                 orm:"OPayState"                 ` //
	OpayTime                  *gtime.Time `json:"OpayTime"                  orm:"OpayTime"                  ` //
	OCreatTime                *gtime.Time `json:"OCreatTime"                orm:"OCreatTime"                ` //
	SPImgUrl                  string      `json:"SPImgUrl"                  orm:"SPImgUrl"                  ` //
	SPName                    string      `json:"SPName"                    orm:"SPName"                    ` //
	SPrice                    string      `json:"SPrice"                    orm:"SPrice"                    ` //
	STruePrice                string      `json:"STruePrice"                orm:"STruePrice"                ` //
	OLastPrice                string      `json:"OLastPrice"                orm:"OLastPrice"                ` //
	OFinalPrice               string      `json:"OFinalPrice"               orm:"OFinalPrice"               ` //
	OpostPrice                string      `json:"OpostPrice"                orm:"OpostPrice"                ` //
	ONumber                   int         `json:"ONumber"                   orm:"ONumber"                   ` //
	OUsedNumber               int         `json:"OUsedNumber"               orm:"OUsedNumber"               ` //
	Oweight                   int         `json:"Oweight"                   orm:"Oweight"                   ` //
	OALLweight                int         `json:"OALLweight"                orm:"OALLweight"                ` //
	ParentFxprice             string      `json:"ParentFxprice"             orm:"ParentFxprice"             ` //
	ParentParentFxprice       string      `json:"ParentParentFxprice"       orm:"ParentParentFxprice"       ` //
	ParentThreeFxprice        string      `json:"ParentThreeFxprice"        orm:"ParentThreeFxprice"        ` //
	TempleParentOopenID       string      `json:"TempleParentOopenID"       orm:"TempleParentOopenID"       ` //
	OopenID                   string      `json:"OopenID"                   orm:"OopenID"                   ` //
	OPeople                   string      `json:"OPeople"                   orm:"OPeople"                   ` //
	OTel                      string      `json:"OTel"                      orm:"OTel"                      ` //
	OProvance                 string      `json:"OProvance"                 orm:"OProvance"                 ` //
	OCity                     string      `json:"OCity"                     orm:"OCity"                     ` //
	ODistrict                 string      `json:"ODistrict"                 orm:"ODistrict"                 ` //
	OStreet                   string      `json:"OStreet"                   orm:"OStreet"                   ` //
	OAddress                  string      `json:"OAddress"                  orm:"OAddress"                  ` //
	OCount                    int         `json:"OCount"                    orm:"OCount"                    ` //
	OCredits                  int         `json:"OCredits"                  orm:"OCredits"                  ` //
	OPostway                  string      `json:"OPostway"                  orm:"OPostway"                  ` //
	OPostState                int         `json:"OPostState"                orm:"OPostState"                ` //
	OPostCode                 string      `json:"OPostCode"                 orm:"OPostCode"                 ` //
	OMsg                      string      `json:"OMsg"                      orm:"OMsg"                      ` //
	OTCredits                 int         `json:"OTCredits"                 orm:"OTCredits"                 ` //
	OYudingText               string      `json:"OYudingText"               orm:"OYudingText"               ` //
	OYudingTime               *gtime.Time `json:"OYudingTime"               orm:"OYudingTime"               ` //
	OgetGoodsImgs             string      `json:"OgetGoodsImgs"             orm:"OgetGoodsImgs"             ` //
	OgetGoodsRemarks          string      `json:"OgetGoodsRemarks"          orm:"OgetGoodsRemarks"          ` //
	OgetGoodsTime             *gtime.Time `json:"OgetGoodsTime"             orm:"OgetGoodsTime"             ` //
	OgetGoodsImgsBack         string      `json:"OgetGoodsImgsBack"         orm:"OgetGoodsImgsBack"         ` //
	OgetGoodsRemarksBack      string      `json:"OgetGoodsRemarksBack"      orm:"OgetGoodsRemarksBack"      ` //
	OgetGoodsTimeBack         *gtime.Time `json:"OgetGoodsTimeBack"         orm:"OgetGoodsTimeBack"         ` //
	OgetOrderOpenId           string      `json:"OgetOrderOpenId"           orm:"OgetOrderOpenId"           ` //
	OgetOrderTime             *gtime.Time `json:"OgetOrderTime"             orm:"OgetOrderTime"             ` //
	OgetOrderOpenIdBack       string      `json:"OgetOrderOpenIdBack"       orm:"OgetOrderOpenIdBack"       ` //
	OgetOrderTimeBack         *gtime.Time `json:"OgetOrderTimeBack"         orm:"OgetOrderTimeBack"         ` //
	OBackState                int         `json:"OBackState"                orm:"OBackState"                ` //
	OstartOpenId              string      `json:"OstartOpenId"              orm:"OstartOpenId"              ` //
	OstartDescribe            string      `json:"OstartDescribe"            orm:"OstartDescribe"            ` //
	OstartImgs                string      `json:"OstartImgs"                orm:"OstartImgs"                ` //
	OstartTime                *gtime.Time `json:"OstartTime"                orm:"OstartTime"                ` //
	OendDescribe              string      `json:"OendDescribe"              orm:"OendDescribe"              ` //
	OendImgs                  string      `json:"OendImgs"                  orm:"OendImgs"                  ` //
	OendTime                  *gtime.Time `json:"OendTime"                  orm:"OendTime"                  ` //
	OselfSendOrder            int         `json:"OselfSendOrder"            orm:"OselfSendOrder"            ` //
	OSuggestMark              string      `json:"OSuggestMark"              orm:"OSuggestMark"              ` //
	OSuggestText              string      `json:"OSuggestText"              orm:"OSuggestText"              ` //
	OSuggestStars             int         `json:"OSuggestStars"             orm:"OSuggestStars"             ` //
	OSuggestTime              *gtime.Time `json:"OSuggestTime"              orm:"OSuggestTime"              ` //
	OCustomSureDescribe       string      `json:"OCustomSureDescribe"       orm:"OCustomSureDescribe"       ` //
	OCustomSureImgs           string      `json:"OCustomSureImgs"           orm:"OCustomSureImgs"           ` //
	OCustomSureTime           *gtime.Time `json:"OCustomSureTime"           orm:"OCustomSureTime"           ` //
	OrderNumber               string      `json:"OrderNumber"               orm:"OrderNumber"               ` //
	OCompanyId                int         `json:"OCompanyId"                orm:"OCompanyId"                ` //
	OCancleText               string      `json:"OCancleText"               orm:"OCancleText"               ` //
	OCancleTime               *gtime.Time `json:"OCancleTime"               orm:"OCancleTime"               ` //
	OBusinessId               int         `json:"OBusinessId"               orm:"OBusinessId"               ` //
	ORemarks                  string      `json:"ORemarks"                  orm:"ORemarks"                  ` //
	OOrderNumber              string      `json:"OOrderNumber"              orm:"OOrderNumber"              ` //
	OHxOpenId                 string      `json:"OHxOpenId"                 orm:"OHxOpenId"                 ` //
	OHxTime                   string      `json:"OHxTime"                   orm:"OHxTime"                   ` //
	IsViolate                 uint        `json:"IsViolate"                 orm:"IsViolate"                 ` //
	IsRefund                  uint        `json:"IsRefund"                  orm:"IsRefund"                  ` //
	TransactionId             string      `json:"transaction_id"            orm:"transaction_id"            ` //
	OutTradeNo                string      `json:"out_trade_no"              orm:"out_trade_no"              ` //
	RefundFee                 string      `json:"refund_fee"                orm:"refund_fee"                ` //
	OPostCom                  string      `json:"OPostCom"                  orm:"OPostCom"                  ` //
}
