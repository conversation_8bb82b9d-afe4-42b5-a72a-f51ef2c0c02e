// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SClasss is the golang structure for table S_Classs.
type SClasss struct {
	Scid      int    `json:"SCID"      orm:"SCID"      ` //
	SCNmae    string `json:"SCNmae"    orm:"SCNmae"    ` //
	SCpareID  int    `json:"SCpareID"  orm:"SCpareID"  ` //
	SCKeyWord string `json:"SCKeyWord" orm:"SCKeyWord" ` //
	SCUrl     string `json:"SCUrl"     orm:"SCUrl"     ` //
	SCTag     string `json:"SCTag"     orm:"SCTag"     ` //
	SCTag2    string `json:"SCTag2"    orm:"SCTag2"    ` //
	SCTag3    string `json:"SCTag3"    orm:"SCTag3"    ` //
	SCType    int    `json:"SCType"    orm:"SCType"    ` //
	Scimg     string `json:"SCIMG"     orm:"SCIMG"     ` //
	Scsimg    string `json:"SCSIMG"    orm:"SCSIMG"    ` //
}
