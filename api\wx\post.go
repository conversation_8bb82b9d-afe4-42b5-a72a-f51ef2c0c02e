package wx

import (
	"50go/internal/dao"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/hitailang/poster/core"
	"github.com/hitailang/poster/handler"
	"github.com/rs/xid"
)

// 规范路由
func (c *WxController) GetPoster(newctx context.Context, req *GetReq) (res *GetListRes, newerr error) {

	r := g.RequestFromCtx(newctx)
	USession := r.GetHeader("access_token")
	user := dao.User.Ctx(r.Context())
	if USession == "" {
		return
	}

	userdata, _ := user.Where("USession", USession).One()

	UGuid := userdata["UGuid"]
	//UHeadImg := userdata["UHeadImg"]
	//UGuid := "cc"
	UTrueName := userdata["UTrueName"]

	fmt.Println(UGuid, UTrueName)

	nullHandler := &handler.NullHandler{}
	ctx := &handler.Context{
		//图片都绘在这个PNG载体上
		PngCarrier: core.NewPNG(0, 0, 750, 1334),
	}
	//绘制背景图
	backgroundHandler := &handler.BackgroundHandler{
		X:    0,                                 // 图片x坐标
		Y:    0,                                 // 图片y坐标
		Path: "./resource/files/background.png", //图片路径 背景必须是png
	}

	//绘制圆形图像
	// imageCircleLocalHandler := &handler.ImageCircleLocalHandler{
	// 	X: 30, // 图片x坐标
	// 	Y: 50, // 图片y坐标
	// 	//Path: UHeadImg,
	// 	Path: "http://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLJT9ncWLPov6rAzn4VCPSC4QoAvdangHRB1JgszqCvffggAysvzpm5MDb72Io4g9YAScHEw7xSWg/132", //图片路径
	// }
	//绘制本地图像
	imageLocalHandler := &handler.ImageLocalHandler{
		X:    130,                         // 图片x坐标
		Y:    400,                         // 图片y坐标
		Path: "./resource/files/logo.png", //图片路径
	}

	//绘制二维码
	qrCodeHandler := &handler.QRCodeHandler{
		X:   50,                                         // 二维码x坐标
		Y:   1110,                                       // 二维码y坐标
		URL: "http://pd.50cms.com/wx/" + UGuid.String(), // 二维码跳转URL地址
	}
	//绘制文字
	textHandler1 := &handler.TextHandler{
		Next:     handler.Next{},
		X:        70,   // 文字x坐标
		Y:        1260, // 文字y坐标
		Size:     20,   // 文字大小
		R:        109,  // 文字颜色RGB值
		G:        138,
		B:        250,
		Text:     UTrueName.String() + "推荐",   // 文字内容
		FontPath: "./resource/files/msyh.ttf", // 字体文件
	}
	//绘制文字
	// textHandler2 := &handler.TextHandler{
	// 	Next:     handler.Next{},
	// 	X:        180,
	// 	Y:        150,
	// 	Size:     22,
	// 	R:        255,
	// 	G:        241,
	// 	B:        250,
	// 	Text:     "请随意赞赏~~",
	// 	FontPath: "./resource/files/msyh.ttf",
	// }

	picname := xid.New().String()
	var picpath = "./resource/files/poster/" + picname + ".png"
	//结束绘制，把前面的内容合并成一张图片,输出到build目录
	endHandler := &handler.EndHandler{
		Output: picpath,
	}

	// 链式调用绘制过程
	nullHandler.
		SetNext(backgroundHandler).
		//SetNext(imageCircleLocalHandler).
		SetNext(textHandler1).
		//	SetNext(textHandler2).
		SetNext(imageLocalHandler).
		SetNext(qrCodeHandler).
		SetNext(endHandler)

	// 开始执行业务
	if err := nullHandler.Run(ctx); err != nil {
		// 异常
		fmt.Println(err)
		return
	}
	// 成功
	fmt.Println("Success")
	// r := g.RequestFromCtx(newctx)
	// md := dao.User.Ctx(r.Context())
	g.RequestFromCtx(newctx).Response.WriteJson(
		g.Map{
			// "page":  page,
			// "psize": psize,
			"data": "/poster/" + picname + ".png",
		})
	return
}
