// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SOrderDao is the data access object for the table S_Order.
type SOrderDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SOrderColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SOrderColumns defines and stores column names for the table S_Order.
type SOrderColumns struct {
	OrderID                   string //
	GuidNU                    string //
	OParentId                 string //
	ParentOopenID             string //
	ParentParentOopenID       string //
	ParentParentParentOopenID string //
	Uguid                     string //
	Spid                      string //
	OStoreId                  string //
	ISvipleve                 string //
	OState                    string //
	OType                     string //
	OpayType                  string //
	OPayState                 string //
	OpayTime                  string //
	OCreatTime                string //
	SPImgUrl                  string //
	SPName                    string //
	SPrice                    string //
	STruePrice                string //
	OLastPrice                string //
	OFinalPrice               string //
	OpostPrice                string //
	ONumber                   string //
	OUsedNumber               string //
	Oweight                   string //
	OALLweight                string //
	ParentFxprice             string //
	ParentParentFxprice       string //
	ParentThreeFxprice        string //
	TempleParentOopenID       string //
	OopenID                   string //
	OPeople                   string //
	OTel                      string //
	OProvance                 string //
	OCity                     string //
	ODistrict                 string //
	OStreet                   string //
	OAddress                  string //
	OCount                    string //
	OCredits                  string //
	OPostway                  string //
	OPostState                string //
	OPostCode                 string //
	OMsg                      string //
	OTCredits                 string //
	OYudingText               string //
	OYudingTime               string //
	OgetGoodsImgs             string //
	OgetGoodsRemarks          string //
	OgetGoodsTime             string //
	OgetGoodsImgsBack         string //
	OgetGoodsRemarksBack      string //
	OgetGoodsTimeBack         string //
	OgetOrderOpenId           string //
	OgetOrderTime             string //
	OgetOrderOpenIdBack       string //
	OgetOrderTimeBack         string //
	OBackState                string //
	OstartOpenId              string //
	OstartDescribe            string //
	OstartImgs                string //
	OstartTime                string //
	OendDescribe              string //
	OendImgs                  string //
	OendTime                  string //
	OselfSendOrder            string //
	OSuggestMark              string //
	OSuggestText              string //
	OSuggestStars             string //
	OSuggestTime              string //
	OCustomSureDescribe       string //
	OCustomSureImgs           string //
	OCustomSureTime           string //
	OrderNumber               string //
	OCompanyId                string //
	OCancleText               string //
	OCancleTime               string //
	OBusinessId               string //
	ORemarks                  string //
	OOrderNumber              string //
	OHxOpenId                 string //
	OHxTime                   string //
	IsViolate                 string //
	IsRefund                  string //
	TransactionId             string //
	OutTradeNo                string //
	RefundFee                 string //
	OPostCom                  string //
}

// sOrderColumns holds the columns for the table S_Order.
var sOrderColumns = SOrderColumns{
	OrderID:                   "OrderID",
	GuidNU:                    "GuidNU",
	OParentId:                 "OParentId",
	ParentOopenID:             "ParentOopenID",
	ParentParentOopenID:       "ParentParentOopenID",
	ParentParentParentOopenID: "ParentParentParentOopenID",
	Uguid:                     "UGUID",
	Spid:                      "SPID",
	OStoreId:                  "OStoreId",
	ISvipleve:                 "ISvipleve",
	OState:                    "OState",
	OType:                     "OType",
	OpayType:                  "OpayType",
	OPayState:                 "OPayState",
	OpayTime:                  "OpayTime",
	OCreatTime:                "OCreatTime",
	SPImgUrl:                  "SPImgUrl",
	SPName:                    "SPName",
	SPrice:                    "SPrice",
	STruePrice:                "STruePrice",
	OLastPrice:                "OLastPrice",
	OFinalPrice:               "OFinalPrice",
	OpostPrice:                "OpostPrice",
	ONumber:                   "ONumber",
	OUsedNumber:               "OUsedNumber",
	Oweight:                   "Oweight",
	OALLweight:                "OALLweight",
	ParentFxprice:             "ParentFxprice",
	ParentParentFxprice:       "ParentParentFxprice",
	ParentThreeFxprice:        "ParentThreeFxprice",
	TempleParentOopenID:       "TempleParentOopenID",
	OopenID:                   "OopenID",
	OPeople:                   "OPeople",
	OTel:                      "OTel",
	OProvance:                 "OProvance",
	OCity:                     "OCity",
	ODistrict:                 "ODistrict",
	OStreet:                   "OStreet",
	OAddress:                  "OAddress",
	OCount:                    "OCount",
	OCredits:                  "OCredits",
	OPostway:                  "OPostway",
	OPostState:                "OPostState",
	OPostCode:                 "OPostCode",
	OMsg:                      "OMsg",
	OTCredits:                 "OTCredits",
	OYudingText:               "OYudingText",
	OYudingTime:               "OYudingTime",
	OgetGoodsImgs:             "OgetGoodsImgs",
	OgetGoodsRemarks:          "OgetGoodsRemarks",
	OgetGoodsTime:             "OgetGoodsTime",
	OgetGoodsImgsBack:         "OgetGoodsImgsBack",
	OgetGoodsRemarksBack:      "OgetGoodsRemarksBack",
	OgetGoodsTimeBack:         "OgetGoodsTimeBack",
	OgetOrderOpenId:           "OgetOrderOpenId",
	OgetOrderTime:             "OgetOrderTime",
	OgetOrderOpenIdBack:       "OgetOrderOpenIdBack",
	OgetOrderTimeBack:         "OgetOrderTimeBack",
	OBackState:                "OBackState",
	OstartOpenId:              "OstartOpenId",
	OstartDescribe:            "OstartDescribe",
	OstartImgs:                "OstartImgs",
	OstartTime:                "OstartTime",
	OendDescribe:              "OendDescribe",
	OendImgs:                  "OendImgs",
	OendTime:                  "OendTime",
	OselfSendOrder:            "OselfSendOrder",
	OSuggestMark:              "OSuggestMark",
	OSuggestText:              "OSuggestText",
	OSuggestStars:             "OSuggestStars",
	OSuggestTime:              "OSuggestTime",
	OCustomSureDescribe:       "OCustomSureDescribe",
	OCustomSureImgs:           "OCustomSureImgs",
	OCustomSureTime:           "OCustomSureTime",
	OrderNumber:               "OrderNumber",
	OCompanyId:                "OCompanyId",
	OCancleText:               "OCancleText",
	OCancleTime:               "OCancleTime",
	OBusinessId:               "OBusinessId",
	ORemarks:                  "ORemarks",
	OOrderNumber:              "OOrderNumber",
	OHxOpenId:                 "OHxOpenId",
	OHxTime:                   "OHxTime",
	IsViolate:                 "IsViolate",
	IsRefund:                  "IsRefund",
	TransactionId:             "transaction_id",
	OutTradeNo:                "out_trade_no",
	RefundFee:                 "refund_fee",
	OPostCom:                  "OPostCom",
}

// NewSOrderDao creates and returns a new DAO object for table data access.
func NewSOrderDao(handlers ...gdb.ModelHandler) *SOrderDao {
	return &SOrderDao{
		group:    "default",
		table:    "S_Order",
		columns:  sOrderColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SOrderDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SOrderDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SOrderDao) Columns() SOrderColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SOrderDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SOrderDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SOrderDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
