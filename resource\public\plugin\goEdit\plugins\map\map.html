﻿<!doctype html>
<html>
<head>
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
    <style>
        html { height: 100%; }
        body { height: 100%; margin: 0; padding: 0; background-color: #FFF; }
        #map_canvas { height: 100%; }
    </style>
    <script src="http://maps.googleapis.com/maps/api/js?sensor=false&language=zh_CN"></script>
    <script>
        var map, geocoder;
        function initialize() {
            var latlng = new google.maps.LatLng(-34.397, 150.644);
            var options = {
                zoom: 11,
                center: latlng,
                disableDefaultUI: true,
                panControl: true,
                zoomControl: true,
                mapTypeControl: true,
                scaleControl: true,
                streetViewControl: false,
                overviewMapControl: true,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            };
            map = new google.maps.Map(document.getElementById("map_canvas"), options);
            geocoder = new google.maps.Geocoder();
            geocoder.geocode({ latLng: latlng }, function (results, status) {
                if (status == google.maps.GeocoderStatus.OK) {
                    if (results[3]) {
                        parent.document.getElementById("kindeditor_plugin_map_address").value = results[3].formatted_address;
                    }
                }
            });
        }
        function search(address) {
            if (!map) return;
            geocoder.geocode({ address: address }, function (results, status) {
                if (status == google.maps.GeocoderStatus.OK) {
                    map.setZoom(11);
                    map.setCenter(results[0].geometry.location);
                    var marker = new google.maps.Marker({
                        map: map,
                        position: results[0].geometry.location
                    });
                } else {
                    alert("Invalid address: " + address);
                }
            });
        }
    </script>
</head>
<body onload="initialize();">
    <div id="map_canvas" style="width:100%; height:100%"></div>
</body>
</html>