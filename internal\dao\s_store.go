// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalSStoreDao is internal type for wrapping internal DAO implements.
type internalSStoreDao = *internal.SStoreDao

// sStoreDao is the data access object for table S_Store.
// You can define custom methods on it to extend its functionality as you wish.
type sStoreDao struct {
	internalSStoreDao
}

var (
	// SStore is globally public accessible object for table S_Store operations.
	SStore = sStoreDao{
		internal.NewSStoreDao(),
	}
)

// Fill with you ideas below.
