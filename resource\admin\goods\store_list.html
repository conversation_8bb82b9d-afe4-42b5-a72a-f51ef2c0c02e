<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />
  <link rel="stylesheet" href="/admin/css/admin.css" />
  <link rel="stylesheet" href="/admin/css/admin.dark.css" />
  <link rel="stylesheet" href="/admin/css/variables.css" />
  <link rel="stylesheet" href="/admin/css/reset.css" />
  <style>
   .GPageSpan{ 
    /* background-color: #009688!important; */
    /* color: #009688!important; */
  }

  .GPageLink
  {
    /* background-color: #009688!important;; */
    color: #009688!important;
  }
.layui-laypage-default  li
{
float:left
}
.layui-form-pane .layui-form-label {
    width: 90px;
  
}

  </style>
</head>

<body class="pear-container pear-admin">
<div style="padding: 16px;" >


  <div class="layui-card">
    <div class="layui-card-body">


      <div class="" style="height: 34px;">
        <div class="layui-table-tool-self ">
          <button class="layui-btn layui-btn-sm" lay-event="getCheckData" onclick="add()" load><i class="pear-icon pear-icon-add"></i>添加新闻</button>
         <div class="layui-inline" title="提示" lay-event="LAYTABLE_TIPS"><i class="layui-icon layui-icon-tips"></i>
          </div>
        </div>
      </div>

      <table class="layui-table"  id="role-table" >
        <thead>
          <tr>
            <th>名称</th>
            <th>分类</th>
            <th>时间</th>
            <th>属性</th>
       
            <th>删除</th>
            <th>修改</th>
          </tr>
        </thead>
        <tbody>
          {{range $key, $value := .data}}
          <tr>
            <td>{{$value.Sname}}</td>
            <td>{{$value.areaID}}</td>
            <td>{{$value.Saddress}}</td>
            <td class="layui-form">
             
              <input type="checkbox" name="CCC" title="锁定|正常" lay-skin="switch" lay-filter="aaa" value="IsLock" data-id="{{$value.SID}}" {{if eq $value.IsLock 1}} checked{{end}} alt="{{$value.IsLock}}">
             
            </td>
            <td><a class="layui-btn layui-btn-xs " onclick="remove({{$value.SID}})"><i class="pear-icon pear-icon-error"></i>删除</a></td>

            <td><a class="layui-btn layui-btn-xs" onclick="edit({{$value.SID}})" style="background-color: #36b368;"><i class="pear-icon pear-icon-edit"></i>编辑</a></td>
           
          </tr>
          {{end}}

        </tbody>
      </table>
      <div>{{.page}}</div> 
   
      
    </div>
  </div>

  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>

  <script>

// layui.use('form', function(){
//     var form = layui.form
//     form.render();

//     form.on('switch(aaa)', function(chkbox){

//   }
// });


    layui.use(["button","jquery"], function () {
   
      var button = layui.button;

      let $ = layui.jquery;

      button.load({
        elem: '[load]',
        time: 600,
        done: function () {
        //  popup.success("加载完成");
        }
      })
    })

    layui.use(['toast', 'jquery', 'layer',"form","popup"], function () {
      // var toast = layui.toast;
      let $ = layui.jquery;
      var form = layui.form
      var popup=layui.popup
    form.render();
      // toast.error({ title: "2危险消息", message: "消息描述" })
      // toast.warning({ title: "警告消息", message: "消息描述" })
      // toast.info({ title: "通知消息", message: "消息描述" })
      window.edit = function(obj) {
      layer.open({ 
        title: '编辑 - id:' + obj,
        type: 2,
        area: ['90%', '95%'],
        content: '/system/goods/store_edit?SID=' + obj,
        end: function(){
          location.reload();
        }
      })
    }

    form.on('switch(aaa)', function(chkbox){
      layer.load(2, { shade: [0.35, '#ccc'] });
           console.log(chkbox.elem); //开关是否开启，true或者false
           console.log(chkbox.elem.checked); //开关是否开启，true或者false
           console.log(chkbox.value)
           console.log(">>>",chkbox.elem.dataset['id']); //开关是否开启，true或者false
           $.ajax({
                    url:  '/system/goods/store-charge?GID='+chkbox.elem.dataset['id'],
                    data:{type:chkbox.value},
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							          popup.success("成功", function () {
                           // location.reload();
					            	});
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
             return false;

    });
        window.remove = function (obj) {
		    layer.confirm('确定删除吗?',{
					icon: 3,
					title: '提示'
                }, function (index) {
                  $.ajax({
                    url: '/system/goods/store-charge?GID=' + obj,
                    data:{type:"del"},
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							             popup.success("删除成功", function () {
                          location.reload();
						           });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
          });
			}

    })
  

    window.add = function(obj) {
      layer.open({
        title: '添加',
        type: 2,
        area: ['90%', '95%'],
        content: '/system/goods/store_edit?SID=0',
        end: function(){
          location.reload();
        }
      })
    }




    // layui.use(['notice', 'jquery', 'layer', 'code'], function () {
    //   var notice = layui.notice;

    //   notice.success("成功消息")
    //   notice.error("危险消息")
    //   notice.warning("警告消息")
    //   notice.info("通用消息")
    
    // })

  </script>
</body>

</html>