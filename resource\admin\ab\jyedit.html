<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>订单审核</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />

  <style>
    .img-preview {
      max-width: 200px;
      max-height: 200px;
      margin: 5px;
      cursor: pointer;
    }
    .img-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    .info-container {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      padding: 0 15px;
    }
    .info-item {
      width: calc(50% - 15px);
      min-width: 300px;
    }
    .info-label {
      color: #666;
      margin-right: 5px;
    }
    .info-value {
      color: #333;
      font-weight: 500;
    }
    .full-width {
      width: 100%;
      padding: 0 15px;
      margin-bottom: 15px;
    }
    
    /* 媒体文件显示样式 */
    .media-preview {
      cursor: pointer;
      border-radius: 4px;
      overflow: hidden;
      transition: all 0.3s ease;
      margin: 5px;
    }
    
    .media-preview:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .media-preview img,
    .media-preview video {
      max-width: 200px;
      max-height: 200px;
      object-fit: cover;
      border-radius: 4px;
    }
    
    .unknown-file {
      width: 200px;
      height: 200px;
      line-height: 200px;
      text-align: center;
      background: #f5f5f5;
      color: #999;
      border-radius: 4px;
      font-size: 14px;
      margin: 5px;
    }
    
    /* 查看模式样式 */
    .view-mode {
      background-color: #f8f9fa;
    }
    
    .view-mode .layui-form-item {
      opacity: 0.8;
    }
    
    .view-mode input:disabled,
    .view-mode textarea:disabled,
    .view-mode select:disabled {
      background-color: #f5f5f5;
      color: #666;
      cursor: not-allowed;
    }
    
    .view-mode .info-label {
      font-weight: bold;
      color: #333;
    }
    
    .view-mode .info-value {
      color: #666;
      padding: 5px 0;
    }

    /* 发送记录列表样式 */
    #smsRecordsList {
      margin-top: 10px;
    }
    
    #smsRecordsList .layui-card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 10px 15px;
      font-weight: 500;
    }
    
    #smsRecordsList .layui-card-body {
      padding: 15px;
      min-height: 100px;
    }
    
    #smsRecordsList .layui-table {
      margin: 0;
    }
    
    #smsRecordsList .layui-table th {
      background-color: #fafafa;
      font-weight: 500;
    }
    
    #smsRecordsList .layui-badge {
      font-size: 12px;
      padding: 2px 8px;
    }
    
    #smsRecordsList .layui-btn-xs {
      padding: 0 8px;
      font-size: 12px;
      height: 20px;
      line-height: 20px;
    }
  </style>
</head>
<body class="pear-container pear-admin {{if eq .mode "view"}}view-mode{{end}}">
  <div class="layui-card">
 
    <div class="layui-card-body">
      <form class="layui-form" lay-filter="audit-form">
        <input type="hidden" name="guid" value="{{.data.Guid}}">
        
        <div class="info-container">
          <div class="info-item">
            <span class="info-label">订单编号：</span>
            <span class="info-value">{{.data.JY_ID}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">订单金额：</span>
            <span class="info-value">{{.data.price}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">下单时间：</span>
            <span class="info-value">{{.data.addTime}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">支付时间：</span>
            <span class="info-value">{{.data.paytime}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">支付状态：</span>
            <span class="info-value">{{if eq .data.paystat 1}}已支付{{else}}未支付{{end}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">客户信息：</span>
            <span class="info-value">{{.data.jcren}} - {{.data.jctel}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">送车地址：</span>
            <span class="info-value">{{.data.jctitle}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">发车车场：</span>
            <span class="info-value">{{.data.fctitle}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">车辆信息：</span>
            <span class="info-value">{{.data.carzt}} - {{.data.cartype}}</span>
          </div>
        </div>

        <div class="full-width">
          <span class="info-label">留言内容：</span>
          <span class="info-value">{{.data.Content}}</span>
        </div>

        <div class="full-width">
          <div class="info-label">现场图片：</div>
          <div class="img-container">
            {{range $index, $img := .data.arrBegainImgs}}
              {{$type := index $.data.arrBegainImgs_types $index}}
              {{if eq $type "image"}}
                <div class="media-preview">
                  <img src="{{$img}}" onclick="previewMedia('{{$img}}', 'image')">
                </div>
              {{else if eq $type "video"}}
                <div class="media-preview">
                  <video src="{{$img}}" controls onclick="previewMedia('{{$img}}', 'video')"></video>
                </div>
              {{else}}
                <div class="unknown-file">未知文件</div>
              {{end}}
            {{end}}
          </div>
        </div>

        <div class="full-width">
          <div class="info-label">送达图片：</div>
          <div class="img-container">
            {{range $index, $img := .data.arrendinImgs}}
              {{$type := index $.data.arrendinImgs_types $index}}
              {{if eq $type "image"}}
                <div class="media-preview">
                  <img src="{{$img}}" onclick="previewMedia('{{$img}}', 'image')">
                </div>
              {{else if eq $type "video"}}
                <div class="media-preview">
                  <video src="{{$img}}" controls onclick="previewMedia('{{$img}}', 'video')"></video>
                </div>
              {{else}}
                <div class="unknown-file">未知文件</div>
              {{end}}
            {{end}}
          </div>
        </div>

        <div class="full-width">
          <div class="info-label">交警放行条：</div>
          <div class="img-container">
            {{range $index, $img := .data.arrjjimgs}}
              {{$type := index $.data.arrjjimgs_types $index}}
              {{if eq $type "image"}}
                <div class="media-preview">
                  <img src="{{$img}}" onclick="previewMedia('{{$img}}', 'image')">
                </div>
              {{else if eq $type "video"}}
                <div class="media-preview">
                  <video src="{{$img}}" controls onclick="previewMedia('{{$img}}', 'video')"></video>
                </div>
              {{else}}
                <div class="unknown-file">未知文件</div>
              {{end}}
            {{end}}
          </div>
        </div>

        <div class="full-width">
          <div class="info-label">身份证：</div>
          <div class="img-container">
            {{range $index, $img := .data.arridimgs}}
              {{$type := index $.data.arridimgs_types $index}}
              {{if eq $type "image"}}
                <div class="media-preview">
                  <img src="{{$img}}" onclick="previewMedia('{{$img}}', 'image')">
                </div>
              {{else if eq $type "video"}}
                <div class="media-preview">
                  <video src="{{$img}}" controls onclick="previewMedia('{{$img}}', 'video')"></video>
                </div>
              {{else}}
                <div class="unknown-file">未知文件</div>
              {{end}}
            {{end}}
          </div>
        </div>


        
        <div class="full-width">
          <div class="info-label">驾驶证：</div>
          <div class="img-container">
            {{range $index, $img := .data.arrdidimgs}}
              {{$type := index $.data.arrdidimgs_types $index}}
              {{if eq $type "image"}}
                <div class="media-preview">
                  <img src="{{$img}}" onclick="previewMedia('{{$img}}', 'image')">
                </div>
              {{else if eq $type "video"}}
                <div class="media-preview">
                  <video src="{{$img}}" controls onclick="previewMedia('{{$img}}', 'video')"></video>
                </div>
              {{else}}
                <div class="unknown-file">未知文件</div>
              {{end}}
            {{end}}
          </div>
        </div>



        
        <div class="full-width">
          <div class="info-label">行车证：</div>
          <div class="img-container">
            {{range $index, $img := .data.arrcaridimgs}}
              {{$type := index $.data.arrcaridimgs_types $index}}
              {{if eq $type "image"}}
                <div class="media-preview">
                  <img src="{{$img}}" onclick="previewMedia('{{$img}}', 'image')">
                </div>
              {{else if eq $type "video"}}
                <div class="media-preview">
                  <video src="{{$img}}" controls onclick="previewMedia('{{$img}}', 'video')"></video>
                </div>
              {{else}}
                <div class="unknown-file">未知文件</div>
              {{end}}
            {{end}}
          </div>
        </div>



        {{if ne .mode "view"}}
        <div class="layui-form-item">
          <label class="layui-form-label">审核结果</label>
          <div class="layui-input-block">
            <input type="radio" name="audit_result" value="1" title="通过" {{if eq .data.sh1 1}}checked{{end}}>
            <input type="radio" name="audit_result" value="0" title="拒绝" {{if eq .data.sh1 0}}checked{{end}}>
          </div>
        </div>

        <div class="layui-form-item layui-form-text">
          <label class="layui-form-label">审核备注</label>
          <div class="layui-input-block">
            <textarea name="shmark" placeholder="请输入审核备注" class="layui-textarea">{{.data.shmark}}</textarea>
          </div>
        </div>

                <div class="layui-form-item">
          <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="audit-submit">
              <i class="layui-icon layui-icon-ok"></i>提交审核
            </button>
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="audit-submit-sms">
              <i class="layui-icon layui-icon-notice"></i>提交并发送结果短信
            </button>
            <button type="button" class="layui-btn layui-btn-warm" onclick="testSmsSending()">
              <i class="layui-icon layui-icon-test"></i>测试短信发送
            </button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
          </div>
        </div>

        <!-- 发送记录列表 -->
        <div class="layui-form-item">
          <label class="layui-form-label">发送记录</label>
          <div class="layui-input-block">
            <div id="smsRecordsList" class="layui-card">
              <div class="layui-card-header">
                <span>短信发送记录</span>
                <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" onclick="refreshSmsRecords()" style="float: right;">
                  <i class="layui-icon layui-icon-refresh"></i>刷新
                </button>
              </div>
              <div class="layui-card-body">
                {{if .smsRecords}}
                  {{if gt (len .smsRecords) 0}}
                    <table class="layui-table">
                      <thead>
                        <tr>
                          <th>发送时间</th>
                          <th>发送内容</th>
                          <th>发送状态</th>
                          <th>审核结果</th>
                          <th>审核备注</th>
                          <th>操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        {{range .smsRecords}}
                        <tr>
                          <td>{{if .send_time}}{{.send_time}}{{else}}未知{{end}}</td>
                          <td>{{.content}}</td>
                          <td>
                            {{if eq .status "success"}}
                              <span class="layui-badge layui-bg-green">成功</span>
                            {{else}}
                              <span class="layui-badge layui-bg-red">失败</span>
                            {{end}}
                          </td>
                          <td>
                            {{if eq .audit_result "1"}}通过{{else}}拒绝{{end}}
                          </td>
                          <td>{{if .shmark}}{{.shmark}}{{else}}-{{end}}</td>
                          <td>
                            {{if eq .status "failed"}}
                              <button type="button" class="layui-btn layui-btn-xs" onclick="resendSms('{{.id}}')">重发</button>
                            {{end}}
                          </td>
                        </tr>
                        {{end}}
                      </tbody>
                    </table>
                  {{else}}
                    <div class="layui-text" style="text-align: center; color: #999; padding: 20px;">暂无发送记录</div>
                  {{end}}
                {{else}}
                  <div class="layui-text" style="text-align: center; color: #999; padding: 20px;">暂无发送记录</div>
                {{end}}
              </div>
            </div>
          </div>
        </div>
        {{else}}
        <!-- 查看模式：显示审核信息 -->
        <div class="full-width">
          <span class="info-label">审核结果：</span>
          <span class="info-value">
            {{if eq .data.sh1 1}}通过{{else if eq .data.sh1 0}}拒绝{{else}}未审核{{end}}
          </span>
        </div>

        <div class="full-width">
          <span class="info-label">审核备注：</span>
          <span class="info-value">{{.data.shmark}}</span>
        </div>

        <div class="full-width">
          <span class="info-label">审核人：</span>
          <span class="info-value">{{.data.shren_name}}</span>
        </div>

        <div class="full-width">
          <span class="info-label">审核时间：</span>
          <span class="info-value">{{.data.shtime}}</span>
        </div>
        {{end}}
        
        {{if eq .mode "view"}}
        <!-- 查看模式：添加关闭按钮 -->
        <div class="layui-form-item">
          <div class="layui-input-block">
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeWindow()">关闭</button>
          </div>
        </div>
        {{end}}
      </form>
    </div>
  </div>

  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>
  

  
  <script>
    layui.use(['form', 'jquery', 'layer'], function() {
      var form = layui.form;
      var $ = layui.jquery;
      var layer = layui.layer;

      // 检查是否为查看模式
      var isViewMode = '{{.mode}}' === 'view';
      
      // 渲染表单元素
      form.render();
      
      // 确保shmark字段被正确添加到表单中
      var shmarkField = $('textarea[name="shmark"]');
      if (shmarkField.length > 0) {
        console.log('shmark field found:', shmarkField.val());
      } else {
        console.log('shmark field not found');
      }
      
      if (isViewMode) {
        // 查看模式：禁用所有表单元素
        $('input, textarea, select').prop('disabled', true);
        $('button[lay-submit]').hide();
        $('button[type="reset"]').hide();
      } else {
        // 编辑模式：启用表单提交
        // 提交审核（不发送短信）
        form.on('submit(audit-submit)', function(data) {
          var loading = layer.load(2);
          $.ajax({
            url: '/system/ab/jy-edit',
            type: 'POST',
            data: data.field,
            success: function(res) {
              layer.close(loading);
              if(res.code === 200) {
                layer.msg('审核成功', {icon: 1}, function() {
                  // 关闭当前页面并刷新父页面
                  var index = parent.layer.getFrameIndex(window.name);
                  parent.layer.close(index);
                  parent.location.reload();
                });
              } else {
                layer.msg(res.msg || '审核失败', {icon: 2});
              }
            },
            error: function() {
              layer.close(loading);
              layer.msg('系统错误', {icon: 2});
            }
          });
          return false;
        });

        // 提交审核并发送短信
        form.on('submit(audit-submit-sms)', function(data) {
          // 调试：打印表单数据
          console.log('Form submission data:', data.field);
          console.log('shmark in form data:', data.field.shmark);
          
          // 手动检查表单字段值
          var shmarkValue = $('textarea[name="shmark"]').val();
          var auditResultValue = $('input[name="audit_result"]:checked').val();
          var guidValue = $('input[name="guid"]').val();
          
          console.log('Manual field check:');
          console.log('shmark:', shmarkValue);
          console.log('audit_result:', auditResultValue);
          console.log('guid:', guidValue);
          
          // 如果shmark为空，使用手动获取的值
          if (!data.field.shmark && shmarkValue) {
            data.field.shmark = shmarkValue;
            console.log('Fixed shmark value:', data.field.shmark);
          }
          
          // 确保所有必要字段都存在
          var formData = {
            guid: data.field.guid || guidValue,
            audit_result: data.field.audit_result || auditResultValue,
            shmark: data.field.shmark || shmarkValue
          };
          
          console.log('Final form data:', formData);
          
          var loading = layer.load(2);
          $.ajax({
            url: '/system/ab/jy-edit',
            type: 'POST',
            data: formData,
            success: function(res) {
              layer.close(loading);
              if(res.code === 200) {
                // 审核成功后自动发送短信
                sendAuditSmsAfterSuccess(formData, function() {
                  layer.msg('审核成功并已发送短信通知', {icon: 1}, function() {
                    // 关闭当前页面并刷新父页面
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    parent.location.reload();
                  });
                });
              } else {
                layer.msg(res.msg || '审核失败', {icon: 2});
              }
            },
            error: function() {
              layer.close(loading);
              layer.msg('系统错误', {icon: 2});
            }
          });
          return false;
        });
      }
    });

    // 图片预览功能
    function previewImage(src) {
      layer.photos({
        photos: {
          data: [{
            src: src
          }]
        }
      });
    }

    // 预览媒体文件（图片或视频）
    function previewMedia(url, type) {
      if (!url) return;
      
      var title = type === 'image' ? '图片预览' : '视频预览';
      var content = '';
      
      if (type === 'image') {
        content = '<div style="text-align:center;"><img src="' + url + '" style="max-width:100%;max-height:80vh;" /></div>';
      } else if (type === 'video') {
        content = '<div style="text-align:center;"><video src="' + url + '" controls style="max-width:100%;max-height:80vh;"></video></div>';
      }
      
      layer.open({
        type: 1,
        title: title,
        area: ['80%', '80%'],
        content: content,
        shadeClose: true,
        anim: 1
      });
    }
    
    // 关闭窗口函数
    function closeWindow() {
      var index = parent.layer.getFrameIndex(window.name);
      parent.layer.close(index);
    }

    // 测试短信发送功能
    function testSmsSending() {
      var guid = layui.jquery('input[name="guid"]').val();
      var auditResult = layui.jquery('input[name="audit_result"]:checked').val();
      var shmark = layui.jquery('textarea[name="shmark"]').val();
      
      console.log('Test SMS sending:');
      console.log('guid:', guid);
      console.log('audit_result:', auditResult);
      console.log('shmark:', shmark);
      
      if (!auditResult) {
        layui.layer.msg('请先选择审核结果', {icon: 2});
        return;
      }
      
      if (!shmark) {
        layui.layer.msg('请先填写审核备注', {icon: 2});
        return;
      }
      
      layui.layer.confirm('确定要发送测试短信吗？', {
        icon: 3,
        title: '确认发送'
      }, function(index) {
        layui.layer.close(index);
        
        var loading = layui.layer.load(2, {text: '正在发送测试短信...'});
        layui.jquery.ajax({
          url: '/api/ab/send-audit-sms',
          type: 'POST',
          data: {
            guid: guid,
            audit_result: auditResult,
            shmark: shmark
          },
          success: function(res) {
            layui.layer.close(loading);
            if(res.code === 200) {
              layui.layer.msg('测试短信发送成功', {icon: 1});
            } else {
              layui.layer.msg(res.msg || '测试短信发送失败', {icon: 2});
            }
          },
          error: function() {
            layui.layer.close(loading);
            layui.layer.msg('系统错误', {icon: 2});
          }
        });
      });
    }

    // 发送审核结果短信（独立按钮调用）
    function sendAuditSms() {
      var guid = layui.jquery('input[name="guid"]').val();
      var auditResult = layui.jquery('input[name="audit_result"]:checked').val();
      var shmark = layui.jquery('textarea[name="shmark"]').val();
      
      if (!auditResult) {
        layui.layer.msg('请先选择审核结果', {icon: 2});
        return;
      }
      
      var loading = layui.layer.load(2);
      layui.jquery.ajax({
        url: '/api/ab/send-audit-sms',
        type: 'POST',
        data: {
          guid: guid,
          audit_result: auditResult,
          shmark: shmark
        },
        success: function(res) {
          layui.layer.close(loading);
          if(res.code === 200) {
            layui.layer.msg('短信发送成功', {icon: 1});
          } else {
            layui.layer.msg(res.msg || '短信发送失败', {icon: 2});
          }
        },
        error: function() {
          layui.layer.close(loading);
          layui.layer.msg('系统错误', {icon: 2});
        }
      });
    }

    // 审核成功后自动发送短信
    function sendAuditSmsAfterSuccess(formData, callback) {
      // 调试：打印表单数据
      console.log('Form data received:', formData);
      console.log('shmark value:', formData.shmark);
      
      // 如果shmark为空，手动获取值
      var shmarkValue = formData.shmark;
      if (!shmarkValue) {
        shmarkValue = layui.jquery('textarea[name="shmark"]').val();
        console.log('Manually retrieved shmark:', shmarkValue);
      }
      
      var loading = layui.layer.load(2, {text: '正在发送短信通知...'});
      layui.jquery.ajax({
        url: '/api/ab/send-audit-sms',
        type: 'POST',
        data: {
          guid: formData.guid,
          audit_result: formData.audit_result,
          shmark: shmarkValue
        },
        success: function(res) {
          layui.layer.close(loading);
          if(res.code === 200) {
            layui.layer.msg('短信发送成功', {icon: 1});
            if (callback) callback();
          } else {
            layui.layer.msg('短信发送失败：' + (res.msg || '未知错误'), {icon: 2});
            // 即使短信发送失败，也要执行回调关闭页面
            if (callback) callback();
          }
        },
        error: function() {
          layui.layer.close(loading);
          layui.layer.msg('短信发送失败，但审核已成功', {icon: 2});
          // 即使短信发送失败，也要执行回调关闭页面
          if (callback) callback();
        }
      });
    }

    



		// 刷新发送记录 - 简化版本，直接刷新页面
		function refreshSmsRecords() {
			window.location.reload();
		}

    		// 重发短信
		function resendSms(recordId) {
			layui.layer.confirm('确定要重新发送这条短信吗？', {
				icon: 3,
				title: '确认重发'
			}, function(index) {
				layui.layer.close(index);

				var loading = layui.layer.load(2, {text: '正在重发短信...'});
				layui.jquery.ajax({
					url: '/system/ab/jy-edit-resend-sms',
					type: 'POST',
					data: { record_id: recordId },
					success: function(res) {
						layui.layer.close(loading);
						if (res.code === 200) {
							layui.layer.msg('短信重发成功', {icon: 1});
							// 刷新发送记录
							refreshSmsRecords();
						} else {
							layui.layer.msg(res.msg || '短信重发失败', {icon: 2});
						}
					},
					error: function() {
						layui.layer.close(loading);
						layui.layer.msg('系统错误', {icon: 2});
					}
				});
			});
		}
  </script>
</body>
</html>