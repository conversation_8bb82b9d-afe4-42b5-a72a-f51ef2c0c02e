// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TaskStudentDao is the data access object for the table Task_Student.
type TaskStudentDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TaskStudentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TaskStudentColumns defines and stores column names for the table Task_Student.
type TaskStudentColumns struct {
	Id          string //
	SGuid       string //
	Tid         string //
	Fid         string //
	ISvipleve   string //
	OState      string //
	OType       string //
	OCreatTime  string //
	Name        string //
	Tel         string //
	Provance    string //
	City        string //
	District    string //
	Street      string //
	ADDress     string //
	BertheDay   string //
	ORemarks    string //
	Com         string //
	School      string //
	Class       string //
	ADDlocation string //
	ADDname     string //
	ADDtitle    string //
	ADDdetail   string //
	ADDlat      string //
	ADDlon      string //
}

// taskStudentColumns holds the columns for the table Task_Student.
var taskStudentColumns = TaskStudentColumns{
	Id:          "ID",
	SGuid:       "SGuid",
	Tid:         "TID",
	Fid:         "FID",
	ISvipleve:   "ISvipleve",
	OState:      "OState",
	OType:       "OType",
	OCreatTime:  "OCreatTime",
	Name:        "Name",
	Tel:         "Tel",
	Provance:    "Provance",
	City:        "City",
	District:    "District",
	Street:      "Street",
	ADDress:     "ADDress",
	BertheDay:   "BertheDay",
	ORemarks:    "ORemarks",
	Com:         "Com",
	School:      "School",
	Class:       "Class",
	ADDlocation: "ADDlocation",
	ADDname:     "ADDname",
	ADDtitle:    "ADDtitle",
	ADDdetail:   "ADDdetail",
	ADDlat:      "ADDlat",
	ADDlon:      "ADDlon",
}

// NewTaskStudentDao creates and returns a new DAO object for table data access.
func NewTaskStudentDao(handlers ...gdb.ModelHandler) *TaskStudentDao {
	return &TaskStudentDao{
		group:    "default",
		table:    "Task_Student",
		columns:  taskStudentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TaskStudentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TaskStudentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TaskStudentDao) Columns() TaskStudentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TaskStudentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TaskStudentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TaskStudentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
