package info

import (
	"50go/internal/dao"
	"context"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type InfoController struct{}

type PostReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.Meta `method:"post"  summary:"Post获得信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}

type GetReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.Meta `method:"get"  summary:"Get获得学校信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}
type GetListRes struct {
	Name string
	Id   int
}

func GetPageData(r *ghttp.Request) (int, int) {
	page := r.GetQuery("page", 1).Int()
	psize := r.GetQuery("psize", 5).Int()
	return page, psize
}

func (c *InfoController) Classlist(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	md := dao.InfoClass.Ctx(r.Context())
	sclasss, _ := md.FieldsEx("GContent").All()
	mysclasss := sclasss.List()

	var categoryList []map[string]interface{}
	if mysclasss != nil {
		for _, v := range mysclasss {
			v["id"] = v["CID"]
			v["name"] = v["CNmae"]
			v["CKeyWord"] = v["CKeyWord"]

			categoryList = append(categoryList, v)
		}
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code":  200,
			"data1": g.Map{"categoryList": categoryList},
			"data":  categoryList,
		})
	return
}

func (c *InfoController) HotPosting(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	// r := g.RequestFromCtx(ctx)
	// name := r.GetQuery("name", "没参数的默认返回值!") //获取name
	r := g.RequestFromCtx(ctx)
	md := dao.Info.Ctx(r.Context())
	data, _ := md.FieldsEx("GContent").All()
	page, psize := GetPageData(r)

	ncount, err := md.Count()

	mysclasss := data.List()

	var categoryList []map[string]interface{}
	if mysclasss != nil {
		for _, v := range mysclasss {
			v["id"] = v["CID"]
			v["categoryId"] = v["SCCLASSID"]
			v["abstractContent"] = v["G_Introduce"]
			v["createTime"] = v["GSales"]

			categoryList = append(categoryList, v)
		}
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code":  200,
			"page":  page,
			"psize": psize,
			"total": ncount,
			"rows":  categoryList,
		})
	return
}

func (c *InfoController) List(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	// r := g.RequestFromCtx(ctx)
	// name := r.GetQuery("name", "没参数的默认返回值!") //获取name
	r := g.RequestFromCtx(ctx)
	md := dao.Info.Ctx(r.Context())

	total, _ := md.Count()
	//orderingField := GetOrdering(r)
	page, psize := GetPageData(r)
	data, _ := md.FieldsEx("Content").Page(page, psize).All()

	mysclasss := data.List()

	var categoryList []map[string]interface{}
	if mysclasss != nil {
		for _, v := range mysclasss {
			v["id"] = v["ID"]

			v["avatarUrl"] = v["Img"].(string)

			v["ClassId"] = v["ClassId"]
			v["ClassName"] = v["ClassName"]
			v["Tag"] = v["Tag"]
			v["Title"] = v["Title"]

			v["CreatTime"] = v["CreatTime"]

			categoryList = append(categoryList, v)
		}
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code":  200,
			"total": total,
			"page":  page,
			"psize": psize,
			"data":  categoryList,
		})
	return
}

func (c *InfoController) GetInfo(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	NID := r.Get("id")
	mnewss, _ := dao.Info.Ctx(r.Context()).Where("ID", NID).One()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	Img := strings.Split(mnewss["Img"].String(), ",")
	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code": 200,
			"data": mnewss,
			"Imgs": Img,
		})
	return
}
