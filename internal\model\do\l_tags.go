// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// LTags is the golang structure of table L_Tags for DAO operations like Where/Data.
type LTags struct {
	g.Meta   `orm:"table:L_Tags, do:true"`
	Tid      interface{} //
	TName    interface{} //
	TContent interface{} //
	TNotice  interface{} //
}
