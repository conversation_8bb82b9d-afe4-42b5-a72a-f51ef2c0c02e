﻿using Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class shop_Login_login : System.Web.UI.Page
{

    public string reurl = "../user.aspx", mimg, guid, protocol = "";
    protected void Page_Load(object sender, EventArgs e)
    {

        if (Request.QueryString["reurl"] != null && Request.QueryString["reurl"] != "")
        {
            reurl = Request.QueryString["reurl"].ToString();
        }
        if (Session["SOPENID"] == null || Session["SOPENID"].ToString() == string.Empty || Session["UGUID"] == null || Session["UGUID"].ToString() == string.Empty)
        {
            //  到wxProces中取OpenId，取到后再跳回当前页面
            if (Request.QueryString["pid"] != null && Request.QueryString["pid"] != "")
            {
                string pid = Request.QueryString["pid"].ToString();
                Utils.WriteCookie("pid", "lncms", pid);
            }
            Response.Redirect("/4g/login/wxProcess.aspx?reurl=" + HttpUtility.UrlEncode(Request.RawUrl));
            Response.End();
        }


       // var sysset = new SysSet().loadCacheConfig(Utils.GetMapPath("~/App_Data/WebSet.config"));
        //  
        mimg = "/4g/rkylogo.png";
        guid = Session["UGUID"].ToString();
        using (Entities bll = new Entities())
        {

            var wxuser = bll.User.SingleOrDefault(p => p.UGuid == guid);
            if (wxuser == null)
            {

                //  到wxProces中取OpenId，取到后再跳回当前页面
                if (Request.QueryString["pid"] != null && Request.QueryString["pid"] != "")
                {
                    string pid = Request.QueryString["pid"].ToString();
                    Utils.WriteCookie("pid", "lncms", pid);
                }

                Response.Redirect("/4g/login/wxProcess.aspx?reurl=" + HttpUtility.UrlEncode(Request.RawUrl));
                Response.End();

            }
            else
            { 
            if (string.IsNullOrEmpty(wxuser.UIndirectParentId))
            {
                this.Panel1.Visible = true;
                this.Panel2.Visible = false;

            }
            else
            {
            	if(string.IsNullOrEmpty(wxuser.UTel))
            	{

                this.Panel1.Visible = false;
                this.Panel2.Visible = true;
                txtUdirectParent.Text = wxuser.UIndirectParentId;
                
     using (Entities db = new Entities())
            {
                 var _protocol = db.L_Newss.FirstOrDefault(p => p.NID == 2);
                if (_protocol != null)
                {
                   // title = _protocol.Title;
                    protocol = _protocol.Content;
                }

               }
                
                
                }
                else
                {
                	Response.Redirect("../user.aspx");
                	
                	}

            }
            }


        }

    }
    protected void wxlogin_Click(object sender, EventArgs e)
    {
        //到wxProces中取OpenId，取到后再跳回当前页面
        if (Session["OpenId"] == null || Session["OpenId"].ToString() == String.Empty)
        {
            Response.Write("<script>window.open('../Login/wxProcess.aspx?reurl=" + HttpUtility.UrlEncode(Request.RawUrl) + "','_top');</script>");
        }
        else
        {
            if (Session["nologin"] == null || Session["nologin"].ToString() == String.Empty)
            {
                string wxid = Session["OpenId"].ToString();

                if (new UserBase().ULogin(wxid))
                {
                    //保存日志
                    new UserBase().SaveLogs(wxid, "[wx登录]：登录成功！");
                    Response.Redirect(reurl);
                }
            }

        }
    }

        


    protected void LoginBUT_Click(object sender, EventArgs e)
    {
        string UserName = Tel.Text.Trim();
      //  string UserPwd = Base64.Base64Decode(txtUserPwd.Text.Trim());
        if (UserName.Equals("") || txtCode.Equals(""))
        {  
            Page.ClientScript.RegisterClientScriptBlock(GetType(), "js", "<script> layer.open({content: '请输入您要登录用户名,密码,以及验证码'});</script>");
        }
        else
        {
         

                if (Session["AdminLoginSun"] == null)
                {
                    Session["AdminLoginSun"] = 1;
                }
                else
                {
                    Session["AdminLoginSun"] = Convert.ToInt32(Session["AdminLoginSun"]) + 1;
                }
                //判断登录
                if (Session["AdminLoginSun"] != null && Convert.ToInt32(Session["AdminLoginSun"]) > 5)
                {
                   Page.ClientScript.RegisterClientScriptBlock(GetType(), "js", "<script> layer.open({content: '登录错误超过5次，请关闭浏览器重新登录。'});</script>");
                 
                 }

             
                else
                {

                Page.ClientScript.RegisterClientScriptBlock(GetType(), "js", "<script> layer.open({content: '您输入的用户名或密码不正确。'});</script>");
   
                    //保存日志
                 //   new UserBase().SaveErrorLogs(UserName+" 状态：登录失败！" + txtUserPwd.Text);

                }

            
        }

    }



}