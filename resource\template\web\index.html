<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Frameset//EN" "http://www.w3.org/TR/html4/frameset.dtd">
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <link rel="stylesheet" href="/css/normalize.css">
  <link rel="stylesheet" href="/css/main.css">
</head>

<body>

  <!-- page-bg -->
  <image class="page-bg" src="/images/page_bg.jpeg" />
  
  <!-- header --->
  <div class="page-1200 page-header">
    <image class="logo" src="/images/page_header_logo.png" />
    <div class="web-name">昆明海关</div>
    <div class="today">今天是2024年06月04日</div>
  </div>

  <!-- 页面标题 -->
  <div class="page-1200 page-title">
    <image class="title-img" src="/images/page_title.png" />
  </div>

  <!-- top 版块 --->
  <div class="page-1200 top-section">
    <image class="line" src="/images/top_line.png" />
    <image class="top-header" src="/images/top_header.png" />
    <div class="swiper">
      <div class="swiper-item" id="noticeSwiper">
        <div class="title">昆明海关“海关重点领域廉洁风险”专项治理公告111</div>
        <div class="title">昆明海关“海关重点领域廉洁风险”专项治理公告222</div>
        <div class="title">昆明海关“海关重点领域廉洁风险”专项治理公告333</div>
        <div class="title">昆明海关“海关重点领域廉洁风险”专项治理公告444</div>
      </div>
      <div class="swiper-dots"  id="noticeDots">
        <image class="prev" id="noticePrev" src="/images/icon_arrow_up.png" />
        <div class="dot">1/6</div>
        <image class="next" id="noticeNext" src="/images/icon_arrow_dn.png" />
      </div>
    </div>
  </div>

  <!-- main news -->
  <div class="page-1200 main-news-section">
    <!-- worker -->
    <div class="col-worker">
      <div class="user-plate">
        <div class="user">
          <image class="icon" src="/images/icon_avatar.png" />
          <div class="name"><span>叶锐</span>，你好！</div>
        </div>
        <div class="links">
          <div class="link">
            <image class="icon" src="/images/icon_user.png" />
            <span>个人信息</span>
          </div>
          <div class="link">
            <image class="icon" src="/images/icon_fav.png" />
            <span>我的收藏</span>
          </div>
        </div>
      </div>
      <div class="worker-plate red">
        <div class="title">
          <image class="icon" src="/images/icon_worker_red.png" />
          <span>HB2012</span>
        </div>
        <div class="link">待办事项<span>11</span></div>
        <div class="link">待办事项<span>11</span></div>
      </div>
      <div class="worker-plate orange">
        <div class="title">
          <image class="icon" src="/images/icon_worker_orange.png" />
          <span>HB2011</span>
        </div>
        <div class="link">待办事项<span>11</span></div>
        <div class="link">待办事项<span>11</span></div>
      </div>
      <div class="buttons">
        <div class="btn">总署通讯录</div>
        <div class="btn">昆关通讯录</div>
      </div>
    </div>
    <!-- tabpanel -->
    <div class="col-tabpanel">
      <div class="tabs" id="tabs">
        <div class="item active">总署快报</div>
        <div class="item">
          <image class="icon" src="/images/icon_tab_media.png" />
          <span>媒体报道</span>
        </div>
        <div class="item">昆关快报</div>
        <div class="item">基层动态</div>
      </div>
      <div class="tab-conts">
        <div class="cont" id="tabsContent">
          <ul>
            <li><a href="#"><span>•</span>11111111111关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
          </ul>
          <ul>
            <li><a href="#"><span>•</span>222222222222议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
          </ul>
          <ul>
            <li><a href="#"><span>•</span>33333333333议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
          </ul>
          <ul>
            <li><a href="#"><span>•</span>444444444444评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
            <li><a href="#"><span>•</span>关于公布2023年度群众评议昆明海关机关作风评议二维码的公告</a></li>
          </ul>
        </div>
      </div>
    </div>
    <!-- swiper -->
    <div class="col-swiper">
      <div id="swiperCover">
        <div class="swiper-item">
          <image class="img" src="/images/news_swiper_case_1.jpeg" />
          <p class="title"><a href="#">今年一季度中老铁路进出口货物138万吨111</a></p>
        </div>
        <div class="swiper-item">
          <image class="img" src="/images/news_swiper_case_2.jpeg" />
          <p class="title"><a href="#">今年一季度中老铁路进出口货物138万吨222</a></p>
        </div>
        <div class="swiper-item">
          <image class="img" src="/images/news_swiper_case_3.jpeg" />
          <p class="title"><a href="#">今年一季度中老铁路进出口货物138万吨333</a></p>
        </div>
      </div>
      <div class="dots" id="swiperCoverDots"></div>
    </div>
  </div>

  <!-- banners -->
  <!-- <div class="page-1200 banners-swiper">
    <div class="swiper-item">
      <image class="img" src="/images/banner_case.jpeg" />
    </div>
    <div class="dots">
    </div>
  </div> -->

  <div class="page-1200 banners-swiper">
    <div class="swiper-item" id="bannersSwiper">
      <div><image class="img" src="/images/banner_case_1.jpeg" /></div>
      <div><image class="img" src="/images/banner_case_2.jpeg" /></div>
      <div><image class="img" src="/images/banner_case_3.jpeg" /></div>
    </div>
    <div class="dots" id="bannersSwiperDots">
    </div>
  </div>

  <!-- content panel -->
  <table class="content-panel">
    <tr>
      <td class="col col-left">
        <div class="plate">
          <div class="title">关务公开</div>
          <div class="title-line"></div>
          <div class="more"><a href="#"><image class="more-img" src="/images/icon_more.png"></a></div>
          <ul class="list">
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
          </ul>
        </div>
      </td>
      <td class="col col-mid">
        <div class="plate">
          <div class="title">机关公告</div>
          <div class="title-line"></div>
          <div class="more"><a href="#"><image class="more-img" src="/images/icon_more.png"></a></div>
            <ul class="list">
              <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
              <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
              <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
              <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            </ul>
        </div>
      </td>
      <td class="col col-right">
        <div class="plate">
          <div class="title">
            <image class="icon" src="/images/icon_plate_calendar.png" />
            <span>领导日程</span>
          </div>
          <div class="title-line blue"></div>
          <div class="more"><a href="#"><image class="more-img" src="/images/icon_more.png"></a></div>
            <ul class="calendar-list">
              <!-- <li>
                <div class="day">19</div>
                <div class="tt">
                  <p class="date">2024年06</p>
                  <p class="t"><a href="#">江兴培 赴基层开展工作</a></p>
                </div>
              </li>
              <li>
                <div class="day">19</div>
                <div class="tt">
                  <p class="date">2024年06</p>
                  <p class="t"><a href="#">江兴培 赴基层开展工作</a></p>
                </div>
              </li>
              <li>
                <div class="day">19</div>
                <div class="tt">
                  <p class="date">2024年06</p>
                  <p class="t"><a href="#">江兴培 赴基层开展工作</a></p>
                </div>
              </li> -->

              <div class="container"></div>
            </ul>
        </div>
      </td>
    </tr>
    <tr>
      <td class="col col-left">
        <div class="plate">
          <div class="title">总署载体采用</div>
          <div class="title-line"></div>
          <div class="more"><a href="#"><image class="more-img" src="/images/icon_more.png"></a></div>
          <ul class="list">
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
          </ul>
        </div>
      </td>
      <td class="col col-mid">
        <div class="plate">
          <div class="title">政研参考</div>
          <div class="title-line"></div>
          <div class="more"><a href="#"><image class="more-img" src="/images/icon_more.png"></a></div>
          <ul class="list">
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
          </ul>
        </div>
      </td>
      <td class="col col-right">
        <div class="plate">
          <div class="title">
            <image class="icon" src="/images/icon_plate_leader.png" />
            <span>领导讲话</span>
          </div>
          <div class="title-line blue"></div>
          <div class="more"><a href="#"><image class="more-img" src="/images/icon_more.png"></a></div>
          <ul class="list">
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
            <li><a href="#"><span>•</span>于召开昆明海关第8届南博会工作协调会的通知</a></li>
          </ul>
        </div>
      </td>
    </tr>
  </table>

  <!-- unit -->
  <div class="page-1200 unit-panel">
    <div class="title">
      <div class="line-left"><span></span></div>
      <div class="tt">处室动态</div>
      <div class="line-right"><span></span></div>
    </div>
    <div class="unit-cont">
      <div class="item"><a href="#">法规处</a></div>
      <div class="item"><a href="#">综合业务处</a></div>
      <div class="item"><a href="#">关税处</a></div>
      <div class="item"><a href="#">卫生检疫处</a></div>
      <div class="item"><a href="#">动植物检疫处</a></div>
      <div class="item"><a href="#">进出口食品安全处</a></div>
      <div class="item"><a href="#">商品检验处</a></div>
      <div class="item"><a href="#">口岸监管处</a></div>
      <div class="item"><a href="#">行邮监管处</a></div>
      <div class="item"><a href="#">统计分析处</a></div>
      <div class="item"><a href="#">企业管理和稽查处</a></div>
      <div class="item"><a href="#">缉私局</a></div>
      <div class="item"><a href="#">财务处</a></div>
      <div class="item"><a href="#">科技处</a></div>
      <div class="item"><a href="#">督察内审处</a></div>
      <div class="item"><a href="#">人事处</a></div>
      <div class="item"><a href="#">机关党委</a></div>
      <div class="item"><a href="#">监察室</a></div>
      <div class="item"><a href="#">昆明海关风险防控分局</a></div>
      <div class="item"><a href="#">昆明海关学会</a></div>
      <div class="item"><a href="#">后勤管理中心</a></div>
      <div class="item"><a href="#">技术中心</a></div>
      <div class="item"><a href="#">保健中心</a></div>
    </div>
  </div>

  <!-- feature-and-info -->
  <div class="page-1200 feature-and-info">
    <!-- feature -->
    <div class="feature-plate">
      <div class="headers">
        <div class="title">专题网站</div>
        <div class="more">
          <a href="#">更多</a>
          <image class="more-img" src="/images/icon_more.png" />
        </div>
      </div>
      <div class="f-banners">
        <image class="banner" src="/images/f_1.jpeg" />
        <image class="banner" src="/images/f_2.jpeg" />
      </div>
    </div>
    <!-- infomation -->
    <div class="info-plate">
      <div class="headers">
        <div class="title">信息资料</div>
        <div class="more">
          <a href="#">更多</a>
          <image class="more-img" src="/images/icon_more.png" />
        </div>
      </div>
      <div class="buttons">
        <div class="btn">
          <image class="icon" src="/images/icon_floder.png" />
          <span>软件资源库</span>
        </div>
        <div class="btn">
          <image class="icon" src="/images/icon_floder.png" />
          <span>关级领导小组</span>
        </div>
        <div class="btn">
          <image class="icon" src="/images/icon_floder.png" />
          <span>下载中心</span>
        </div>
        <div class="btn">
          <image class="icon" src="/images/icon_floder.png" />
          <span>智慧海关资料</span>
        </div>
        <div class="btn">
          <image class="icon" src="/images/icon_floder.png" />
          <span>业务规范性文件系统</span>
        </div>
      </div>
    </div>
  </div>

  <!-- apps -->
  <div class="page-1200 apps">
    <div class="headers">
      <div class="title">信息资料</div>
      <div class="count">12</div>
    </div>
    <div class="conts" id="maskCont">
      <div class="btn"><a href="#">中国海关•风险预警处置和设计监督平台</a></div>
      <div class="btn"><a href="#">队伍建设综合管理平台</a></div>
      <div class="btn"><a href="#">中国海关•业务网邮件系统</a></div>
      <div class="btn"><a href="#">昆明海关•“不贰过”监督平台</a></div>
      <div class="btn"><a href="#">中国海关•风险预警处置和设计监督平台</a></div>
      <div class="btn"><a href="#">队伍建设综合管理平台</a></div>
      <div class="btn"><a href="#">中国海关•风险预警处置和设计监督平台</a></div>
      <div class="btn"><a href="#">队伍建设综合管理平台</a></div>
      <div class="btn"><a href="#">中国海关•业务网邮件系统</a></div>
      <div class="btn"><a href="#">昆明海关•“不贰过”监督平台</a></div>
      <div class="btn"><a href="#">中国海关•风险预警处置和设计监督平台</a></div>
      <div class="btn"><a href="#">队伍建设综合管理平台</a></div>
      <div class="btn"><a href="#">中国海关•风险预警处置和设计监督平台</a></div>
      <div class="btn"><a href="#">队伍建设综合管理平台</a></div>
      <div class="btn"><a href="#">中国海关•业务网邮件系统</a></div>
      <div class="btn"><a href="#">昆明海关•“不贰过”监督平台</a></div>
      <div class="btn"><a href="#">中国海关•风险预警处置和设计监督平台</a></div>
      <div class="btn"><a href="#">队伍建设综合管理平台</a></div>
    </div>
    <div class="mask-button" id="maskBtn">
      <image class="mask to-show" id="toShow" src="/images/apps_mask_2show.png" />
      <image class="mask to-hide" id="toHide" src="/images/apps_mask_2hide.png" />
    </div>
  </div>

  <!-- copy -->
  <div class="copy">
    <image class="copy-top-line" src="/images/copy_line.png"></image>
    <image class="copy-bg" src="/images/copy_bg.jpeg"></image>
    <div class="page-1200 copy-panel">
      <div class="select-plate">
        <div class="selector" id="guideSelector">部门及隶属海关网站
        </div>
        <div class="selector" id="linksSelector">友情链接</div>
      </div>
      <div class="info-plate">
        <image class="logo" src="/images/copy_logo.png" />
        <div class="txt">
          <p>Copgright© 2019中华人民共和国昆明海关 版权所有</p>
          <p>电话:0871-63016999 邮编:650051 地址:云南省昆明市北京路618号</p>
        </div>
      </div>
    </div>
  </div>

</body>

<link rel="stylesheet" href="/dist/css/calendar-price-jquery.min.css">
<script src="http://www.jq22.com/jquery/jquery-1.10.2.js"></script>
<script src="/dist/js/calendar-price-jquery.min.js"></script>


<script>
  $(function () {

    var mockData = [
      {
        date: "2024-8-21",
        stock: 9000,
        buyNumMax: 50,
        buyNumMin: 1,
        price: "0.12",
        priceMarket: "100.00",
        priceSettlement: "90.00",
        priceRetail: "99.00"
      },{
        date: "2024-8-22",
        stock: 900,
        buyNumMax: 50,
        buyNumMin: 1,
        price: "12.00",
        priceMarket: "100.00",
        priceSettlement: "90.00",
        priceRetail: "99.00"
      }
    ];

    for (var i = 0; i < 100; i++) {
      mockData.push({
        date: '2018-'+ fd(i%8 + 1) +'-' + fd(randNum(30)),
        stock: i*21,
        buyNumMax: "50",
        buyNumMin: "1",
        price: randNum(i) + '.00',
        priceMarket: "100.00",
        priceSettlement: "90.00",
        priceRetail: "99.00"
      });
    }

    $.CalendarPrice({
      el: '.container',
      startDate: '2024-08-23',
      endDate: '2024-08-24',
      data: mockData,
      // 配置需要设置的字段名称
      config: [
        {
          key: 'buyNumMax',
          name: '最多购买数'
        },
        {
          key: 'buyNumMin',
          name: '最少购买数'
        },
        {
          key: 'price',
          name: '分销售价'
        },
        {
          key: 'priceMarket',
          name: '景区挂牌价'
        },
        {
          key: 'priceSettlement',
          name: '分销结算价'
        },
        {
          key: 'priceRetail',
          name: '建议零售价'
        },
        {
          key: 'cashback',
          name: '返现'
        },
        {
          key: 'stock',
          name: '当天库存'
        }
      ],
      // 配置在日历中要显示的字段
      show: [
        {
          key: 'price',
          name: '分:￥'
        },
        {
          key: 'priceSettlement',
          name: '采:￥'
        },
        {
          key: 'stock',
          name: '库:'
        }
      ],
      callback: function (data) {
        console.log('callback ....');
        console.log(data);
      },
      cancel: function () {
        console.log('取消设置 ....');
        // 取消设置
        // 这里可以触发关闭设置窗口
        // ...
      },
      reset: function () {
        console.log('数据重置成功！');
      },
      error: function (err) {
        console.error(err.msg);
        alert(err.msg);
      },
      // 自定义颜色
      style: {
        // 头部背景色
        headerBgColor: '#098cc2',
        // 头部文字颜色
        headerTextColor: '#fff',
        // 周一至周日背景色，及文字颜色
        weekBgColor: '#098cc2',
        weekTextColor: '#fff',
        // 周末背景色，及文字颜色
        weekendBgColor: '#098cc2',
        weekendTextColor: '#fff',
        // 有效日期颜色
        validDateTextColor: '#333',
        validDateBgColor: '#fff',
        validDateBorderColor: '#eee',
        // Hover
        validDateHoverBgColor: '#098cc2',
        validDateHoverTextColor: '#fff',
        // 无效日期颜色
        invalidDateTextColor: '#ccc',
        invalidDateBgColor: '#fff',
        invalidDateBorderColor: '#eee',
        // 底部背景颜色
        footerBgColor: '#fff',
        // 重置按钮颜色
        resetBtnBgColor: '#77c351',
        resetBtnTextColor: '#fff',
        resetBtnHoverBgColor: '#55b526',
        resetBtnHoverTextColor: '#fff',
        // 确定按钮
        confirmBtnBgColor: '#098cc2',
        confirmBtnTextColor: '#fff',
        confirmBtnHoverBgColor: '#00649a',
        confirmBtnHoverTextColor: '#fff',
        // 取消按钮
        cancelBtnBgColor: '#fff',
        cancelBtnBorderColor: '#bbb',
        cancelBtnTextColor: '#999',
        cancelBtnHoverBgColor: '#fff',
        cancelBtnHoverBorderColor: '#bbb',
        cancelBtnHoverTextColor: '#666'
      }
      // 点击有效的某一触发的回调函数
      // 注意：配置了此参数，设置窗口无效，即不能针对日期做参数设置
      // 返回每天的数据
//        everyday: function (dayData) {
//            console.log('点击某日，返回当天的数据');
//            console.log(dayData);
//        },
      // 隐藏底部按钮（重置、确定、取消），前台使用该插件时，则需要隐藏底部按钮
//        hideFooterButton: true
    });

  });

  function randNum(max) {
    return Math.round(Math.random() * max);
  }

  function fd(n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  }
</script>

<script type="text/javascript">
  // 图片禁止拖拽，放置用作背景的图片被拖拽位移
  window.ondragstart = function(){
    return false;
  }

  // 标签切换内容方法
  function tabs(tabPannelId) {
    var _titles = document.getElementById(tabPannelId).getElementsByTagName('div');
    var _conts = document.getElementById(tabPannelId + 'Content').getElementsByTagName('ul');
    // 初始化
    _conts[0].style.display = "block";
    // tab点击事件
    for (var i = 0; i < _titles.length; i++) {
      _titles[i].setAttribute('v-index', i);
      _titles[i].onclick = function () {
        var _i = this.getAttribute('v-index')
        for (var j = 0; j < _titles.length; j++) {
          _titles[j].className = "item";
          _conts[j].style.display = "none";
        };
        this.className = "item active";
        _conts[parseInt(_i)].style.display = "block";
      };
    };
  };
  tabs('tabs');

  function noticeSwiper (noticeSwiperID, noticeDotsID) {
    var _noticeSwiper = document.getElementById(noticeSwiperID).getElementsByTagName('div');
    var _noticeCount = document.getElementById(noticeDotsID).getElementsByTagName('div')[0];
    var _noticePrev = document.getElementById('noticePrev');
    var _noticeNext = document.getElementById('noticeNext');
    var timer;
    var _noticeIndex = 0;
    // 初始化
    _noticeCount.innerHTML = _noticeIndex + 1 + '/' + _noticeSwiper.length;
    _noticeSwiper[_noticeIndex].style.display = 'block';
    // 上一个点击事件
    _noticePrev.onclick = function () {
      clearInterval(timer)
      _noticeIndex == 0 ? _noticeIndex = _noticeSwiper.length - 1 : _noticeIndex--;
      _noticeCount.innerHTML = _noticeIndex + 1 + '/' + _noticeSwiper.length;
      for (var i = 0; i < _noticeSwiper.length; i++) {
        _noticeSwiper[i].style.display = 'none';
      }
      _noticeSwiper[_noticeIndex].style.display = 'block';
      timer = setInterval(function () {
        _noticeIndex == _noticeSwiper.length - 1 ? _noticeIndex = 0 : _noticeIndex++;
        _noticeCount.innerHTML = _noticeIndex + 1 + '/' + _noticeSwiper.length;
        for (var i = 0; i < _noticeSwiper.length; i++) {
          _noticeSwiper[i].style.display = 'none';
        }
        _noticeSwiper[_noticeIndex].style.display = 'block';
      }, 3000);
    }
    // 上一个点击事件
    _noticeNext.onclick = function () {
      _noticeIndex == _noticeSwiper.length - 1 ? _noticeIndex = 0 : _noticeIndex++;
      _noticeCount.innerHTML = _noticeIndex + 1 + '/' + _noticeSwiper.length;
      for (var i = 0; i < _noticeSwiper.length; i++) {
        _noticeSwiper[i].style.display = 'none';
      }
      _noticeSwiper[_noticeIndex].style.display = 'block';
    }
    // 定时器
    timer = setInterval(function () {
      _noticeIndex == _noticeSwiper.length - 1 ? _noticeIndex = 0 : _noticeIndex++;
      _noticeCount.innerHTML = _noticeIndex + 1 + '/' + _noticeSwiper.length;
      for (var i = 0; i < _noticeSwiper.length; i++) {
        _noticeSwiper[i].style.display = 'none';
      }
      _noticeSwiper[_noticeIndex].style.display = 'block';
    }, 3000);
  }
  // 新闻轮播
  noticeSwiper('noticeSwiper', 'noticeDots');

  function swiperCover (swiperCoverID, time) {
    var _swiperCover = document.getElementById(swiperCoverID).getElementsByTagName('div');
    var _swiperDot = document.getElementById(swiperCoverID + 'Dots');
    var _dots;
    var timer;
    var _swiperIndex = 0;
    // 初始化dot
    for(var i=0; i<_swiperCover.length; i++) {
      _swiperDot.innerHTML += '<div class="dot" v-index="' + i + '">' + (i + 1) + '</div>';
    }
    _dots = document.getElementById(swiperCoverID + 'Dots').getElementsByTagName('div');
    // 初始化显示封面和dot颜色
    _swiperCover[_swiperIndex].style.display = 'block';
    _dots[_swiperIndex].className = 'dot current';
    // 启动自动播放
    timer = setInterval(function () {
      _swiperIndex < _swiperCover.length - 1 ? _swiperIndex++ : _swiperIndex = 0;
      for(var j=0; j<_swiperCover.length; j++) {
        _swiperCover[j].style.display = 'none';
        _dots[j].className = 'dot';
      }
      _swiperCover[_swiperIndex].style.display = 'block';
      _dots[_swiperIndex].className = 'dot current';
    }, time);
    // dot的click事件
    for(var k=0; k<_swiperCover.length; k++) {
      _dots[k].onclick = function () {
        clearInterval(timer)
        _swiperIndex = this.getAttribute('v-index');
        for(var j=0; j<_swiperCover.length; j++) {
        _swiperCover[j].style.display = 'none';
        _dots[j].className = 'dot';
      }
      _swiperCover[_swiperIndex].style.display = 'block';
      _dots[_swiperIndex].className = 'dot current';
      timer = setInterval(function () {
        _swiperIndex < _swiperCover.length - 1 ? _swiperIndex++ : _swiperIndex = 0;
        for(var j=0; j<_swiperCover.length; j++) {
          _swiperCover[j].style.display = 'none';
            _dots[j].className = 'dot';
          }
          _swiperCover[_swiperIndex].style.display = 'block';
          _dots[_swiperIndex].className = 'dot current';
        }, time);
      }
    }
  }
  // 轮播图实例化
  swiperCover('swiperCover', 5000);
  swiperCover('bannersSwiper', 3000);

  // 显示app更多
  document.getElementById('maskBtn').onclick = function () {
    var conts = document.getElementById('maskCont')
    var mask = document.getElementById('maskBtn')
    if (conts.className == 'conts') {
      conts.className = 'conts show'
      mask.className = 'mask-button show'
      document.getElementById('toHide').style.display = 'block'
      document.getElementById('toShow').style.display = 'none'
    } else {
      conts.className = 'conts'
      mask.className = 'mask-button'
      document.getElementById('toHide').style.display = 'none'
      document.getElementById('toShow').style.display = 'block'
    }
  }

  // 部门导航及友情链接
  function initSelector (id, array) {
    var selector = document.getElementById(id);
    var options = document.createElement('div')
    options.className = 'options'
    options.id = 'options'
    selector.onclick = function () {
      if (document.getElementById('options')) {
        id == 'guideSelector' || document.getElementById('guideSelector').removeChild(document.getElementById('options'))
        id == 'linksSelector' || document.getElementById('linksSelector').removeChild(document.getElementById('options'))
      }
      for (var i=0; i<array.length; i++) {
        options.innerHTML += '<a href="'+ array[i].href +'">' + array[i].name + '</a>'
      }
      selector.appendChild(options)
      var timer
      this.onmouseleave = function () {
        timer = setTimeout(function () {
          options.innerHTML = ''
          selector.removeChild(options)
        }, 1000)
      }
      options.onmouseenter = function () {
        clearTimeout(timer)
      }
      options.onmouseleave = function () {
        options.innerHTML = ''
        selector.removeChild(options)
      }
    }
  }
  var guide = [
    {
    name: '部门导航网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '部门导航网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '部门导航网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '部门导航网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '部门导航网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '部门导航网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '部门导航网站名称',
    href: 'https://www.baidu.com'
    }
  ]
  var links = [
    {
    name: '网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '网站名称',
    href: 'https://www.baidu.com'
    },
    {
    name: '网站名称',
    href: 'https://www.baidu.com'
    }
  ]
  initSelector('guideSelector', guide);
  initSelector('linksSelector', links);
</script>

</html>