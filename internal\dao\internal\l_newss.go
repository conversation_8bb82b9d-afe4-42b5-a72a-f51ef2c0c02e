// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LNewssDao is the data access object for the table L_Newss.
type LNewssDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LNewssColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LNewssColumns defines and stores column names for the table L_Newss.
type LNewssColumns struct {
	Nid         string //
	ClassId     string //
	NsubclassId string //
	Nsubnid     string //
	Title       string //
	Stitle      string //
	Author      string //
	Teviewer    string //
	From        string //
	Tag         string //
	Zhaiyao     string //
	Img         string //
	Content     string //
	Click       string //
	Url         string //
	Time        string //
	Creattime   string //
	Istop       string //
	Ishot       string //
	Isslide     string //
	Islock      string //
	Isred       string //
	Img2        string //
	Piclist     string //
	Piclisttag  string //
	Fujian      string //
	Isok        string //
	Txtaddress  string //
	Ishistory   string //
	Fujianpdf   string //
	Fwzh        string //
	Fwsybh      string //
	Fwcwdate    string //
	Fwjg        string //
	Fwsy        string //
	Uid         string //
	Toleid      string //
	Nickname    string //
	Uidedit     string //
	Nguid       string //
	Edittime    string //
	HistoryTime string //
}

// lNewssColumns holds the columns for the table L_Newss.
var lNewssColumns = LNewssColumns{
	Nid:         "nid",
	ClassId:     "classId",
	NsubclassId: "nsubclassId",
	Nsubnid:     "nsubnid",
	Title:       "title",
	Stitle:      "stitle",
	Author:      "author",
	Teviewer:    "teviewer",
	From:        "from",
	Tag:         "tag",
	Zhaiyao:     "zhaiyao",
	Img:         "img",
	Content:     "content",
	Click:       "click",
	Url:         "url",
	Time:        "time",
	Creattime:   "creattime",
	Istop:       "istop",
	Ishot:       "ishot",
	Isslide:     "isslide",
	Islock:      "islock",
	Isred:       "isred",
	Img2:        "img2",
	Piclist:     "piclist",
	Piclisttag:  "piclisttag",
	Fujian:      "fujian",
	Isok:        "isok",
	Txtaddress:  "txtaddress",
	Ishistory:   "Ishistory",
	Fujianpdf:   "fujianpdf",
	Fwzh:        "fwzh",
	Fwsybh:      "fwsybh",
	Fwcwdate:    "fwcwdate",
	Fwjg:        "fwjg",
	Fwsy:        "fwsy",
	Uid:         "uid",
	Toleid:      "toleid",
	Nickname:    "nickname",
	Uidedit:     "uidedit",
	Nguid:       "nguid",
	Edittime:    "edittime",
	HistoryTime: "historyTime",
}

// NewLNewssDao creates and returns a new DAO object for table data access.
func NewLNewssDao(handlers ...gdb.ModelHandler) *LNewssDao {
	return &LNewssDao{
		group:    "default",
		table:    "L_Newss",
		columns:  lNewssColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LNewssDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LNewssDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LNewssDao) Columns() LNewssColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LNewssDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LNewssDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LNewssDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
