<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>
</head>

<body>

    <div class="pear-container">
    <div class="layui-card">
 
    <form class="layui-form" action="" id="news_edit">
        <div class="layui-card-body">
            <div class="main-container">
                <input name="MID"  type="hidden" value="">
                <div class="layui-form-item">
                    <label class="layui-form-label">系统名字:</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" lay-verify="required" value="{{.thesystem.name}}"
                            autocomplete="off" class="layui-input" style="max-width: 600px;">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">管理员电话:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="tel" id="date" autocomplete="off" class="layui-input" value="{{.thesystem.tel}}"> 
                        </div>
                    </div>
                </div>

           
                <div class="layui-form-item">
                    <label class="layui-form-label">网站LOGO：</label>

                    <!-- 单图 -->
                    <style type="text/css">
                        .layui-upload-drag {
                            position: relative;
                            padding: 10px;
                            border: 1px dashed #e2e2e2;
                            background-color: #fff;
                            text-align: center;
                            cursor: pointer;
                            color: #999;
                        }
                    </style>
                    <div class="layui-input-block">
                        <div class="layui-upload-drag"><img id="site_logo__upimage_show_id"
                                src="{{.thesystem.logo}}"
                                alt="上传头像" width="90" height="90"><input type="hidden" id="site_logo__upimage"
                                name="site_logo__upimage"
                                value="{{.thesystem.logo}}">
                        </div>
                        <div style="margin-top:10px;">
                            <button type="button" class="layui-btn" id="upload_site_logo__upimage"><i
                                    class="layui-icon"></i>上传头像</button><input class="layui-upload-file" type="file"
                                accept="image/*" name="file">
                        </div>
                        <div class="layui-form-mid layui-word-aux">建议尺寸：建议上传尺寸450x450</div>
                    </div>
                    <script type="text/javascript">
                        layui.use(['upload'], function () {
                            //声明变量
                            var layer = layui.layer
                                , upload = layui.upload
                                // , croppers = layui.croppers
                                , $ = layui.$;

                            // if (2 == 1) {

                            //     //图片裁剪组件
                            //     croppers.render({
                            //         elem: '#upload_site_logo__upimage'
                            //         , name: "site_logo__upimage"
                            //         , saveW: 300     //保存宽度
                            //         , saveH: 300
                            //         , mark: 1    //选取比例
                            //         , area: ['750px', '500px']  //弹窗宽度
                            //         , url: "/upload/uploadImage"
                            //         , done: function (data) {
                            //             //上传完毕回调
                            //             $('#site_logo__upimage').val(data.data);
                            //             $('#site_logo__upimage_show_id').attr('src', data.data);
                            //         }
                            //     });

                            // } else {

                                /**
                                 * 普通图片上传
                                 */
                                var uploadInst = upload.render({
                                    elem: '#upload_site_logo__upimage'
                                    , url: "/system/comme/upload-img"
                                    , accept: 'images'
                                    , acceptMime: 'image/*'
                                    , exts: "jpg|png|gif|bmp|jpeg"
                                    , field: 'file'//文件域字段名
                                    , size: 10240 //最大允许上传的文件大小
                                    , before: function (obj) {
                                        //预读本地文件
                                    }
                                    , done: function (res) {
                                        //上传完毕回调

                                        if (res.code <= 0) {
                                            layer.msg(res.msg, { icon: 5 });
                                            return false;
                                        }

                                        //上传成功
                                        $('#site_logo__upimage_show_id').attr('src', res.data);
                                        $('#site_logo__upimage').val(res.data);
                                    }
                                    , error: function () {
                                        //请求异常回调
                                        return layer.msg('数据请求异常');
                                    }
                                });

                            

                        });

                    </script>

                </div>



                <div class="layui-form-item cover">
                    <label class="layui-form-label">权限：</label>
                    <div class="layui-form">
                        <input type="checkbox" name="type" title="推荐">
                        <input type="checkbox" name="BBB" lay-text="启用" checked>
                        <input type="checkbox" name="CCC" lay-text="图片" checked>

                    </div>
                </div>

            </div>
        </div>
        <div class="bottom">
            <div class="button-container">
                <div class="layui-btn-container layui-col-xs12">
                    <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">清空</button>
                </div>

            </div>
        </div>
    </form>

</div>

</div>


    <script>


        layui.use(['form', 'jquery', 'toast'], function () {
            let form = layui.form;
            let $ = layui.jquery;
          
            var toast = layui.toast;

            toast.info({ title: "警告", message: "系统信息请认真修改" })
       //   toast.error({ title: "2危险消息", message: "消息描述" })
       //   toast.warning({ title: "警告消息", message: "消息描述" })
       //   toast.info({ title: "通知消息", message: "消息描述" })
            form.verify({

                ip: [
                    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
                    , 'IP地址不符合规则'
                ]
            });

            form.on('radio(type)', function (data) {
                if (data.value == 1) {
                    $(".conn-pwd").show();
                    $(".conn-key").hide();
                } else {
                    $(".conn-key").show();
                    $(".conn-pwd").hide();
                }
            });

            form.on('submit(user-save)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                $.ajax({
                    url: '/system/comme/system-edit',
                    data: $('#news_edit').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");
                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });



        })

     
    </script>
</body>

</html>