// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// LFeedback is the golang structure of table L_Feedback for DAO operations like Where/Data.
type LFeedback struct {
	g.Meta     `orm:"table:L_Feedback, do:true"`
	Fid        interface{} //
	Nid        interface{} //
	FName      interface{} //
	FTel       interface{} //
	Fqq        interface{} //
	FMail      interface{} //
	FTitle     interface{} //
	FContent   interface{} //
	FTime      interface{} //
	FReContent interface{} //
	FReTime    interface{} //
	FLock      interface{} //
}
