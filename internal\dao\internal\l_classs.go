// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LClasssDao is the data access object for the table L_Classs.
type LClasssDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LClasssColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LClasssColumns defines and stores column names for the table L_Classs.
type LClasssColumns struct {
	Cid         string //
	Cnmae       string //
	Cpareid     string //
	Ckeyword    string //
	Curl        string //
	Ctag        string //
	Ctag2       string //
	Ctag3       string //
	Ctype       string //
	Csubskin    string //
	Cskin       string //
	Csort       string //
	CsIndexshow string //
	Czhaiyao    string //
	Cisfw       string //
}

// lClasssColumns holds the columns for the table L_Classs.
var lClasssColumns = LClasssColumns{
	Cid:         "cid",
	Cnmae:       "cnmae",
	Cpareid:     "cpareid",
	Ckeyword:    "ckeyword",
	Curl:        "curl",
	Ctag:        "ctag",
	Ctag2:       "ctag2",
	Ctag3:       "ctag3",
	Ctype:       "ctype",
	Csubskin:    "csubskin",
	Cskin:       "cskin",
	Csort:       "csort",
	CsIndexshow: "csIndexshow",
	Czhaiyao:    "czhaiyao",
	Cisfw:       "cisfw",
}

// NewLClasssDao creates and returns a new DAO object for table data access.
func NewLClasssDao(handlers ...gdb.ModelHandler) *LClasssDao {
	return &LClasssDao{
		group:    "default",
		table:    "L_Classs",
		columns:  lClasssColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LClasssDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LClasssDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LClasssDao) Columns() LClasssColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LClasssDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LClasssDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LClasssDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
