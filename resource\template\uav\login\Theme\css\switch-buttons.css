
/* toggle switch css start */
.switch-btn {position: relative; display: block; width: 40px; height: .82rem; border-radius: 18px; cursor: pointer; float: right; margin-top: .4rem;}

.checked-switch {position: absolute; top: 0; left: 0; opacity: 0;}

.text-switch {background-color: #eee; border: 1px solid #e4e4e4; border-radius: inherit; color: #fff; display: block; font-size: 15px; height: inherit; position: relative; text-transform: uppercase;}
.text-switch:before, .text-switch:after {position: absolute; top: 50%; margin-top: -.5em; line-height: 1; -webkit-transition: inherit; -moz-transition: inherit; -o-transition: inherit; transition: inherit;}
/*.text-switch:before {content: attr(data-no); right: 11px;}
.text-switch:after {content: attr(data-yes); left: 11px; color: #FFFFFF; opacity: 0;}*/
.checked-switch:checked ~ .text-switch {background-color: #4dc06a; border: 1px solid #28a528;}
.checked-switch:checked ~ .text-switch:before {opacity: 0;}
.checked-switch:checked ~ .text-switch:after {opacity: 1;}
.toggle-btn {background:#fff; box-shadow: 0 0 6px #999; border-radius: 100%; left: 0; position: absolute; top: 0; width: .8rem; height: .8rem;}
/*.toggle-btn::before {color: #aaaaaa; content: "|||"; display: inline-block; font-size: 12px; letter-spacing: -2px; padding: 4px 0; vertical-align: middle;}*/
.checked-switch:checked ~ .toggle-btn {left: 18px;}
 .text-switch, .toggle-btn {transition: All 0.3s ease; -webkit-transition: All 0.3s ease; -moz-transition: All 0.3s ease; -o-transition: All 0.3s ease;}

/*= add for "no-radius" =*/
.no-radius, .no-radius .toggle-btn{border-radius: 0;}
/*= add for "no-radius" end =*/

/*= add for "top-to-bottom" =*/
.circle-style .toggle-btn::before{background: linear-gradient(#dedede, #cacaca); border-radius: 50%; content: ""; height: 14px; margin-top: 6px; padding: 0; width: 14px;}
/* add for "top-to-bottom" end */

/* toggle switch css end */