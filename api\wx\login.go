package wx

import (
	"context"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"

	"50go/internal/consts"
	"50go/internal/dao"
	"50go/internal/utils"
)

// 会话过期时间（秒）
const (
	SessionExpireDuration int64 = 100000 // 约一天的秒数
)

// 统一API响应结构
func responseJson(r *ghttp.Request, code int, data interface{}, msg ...string) {
	msgStr := ""
	if len(msg) > 0 {
		msgStr = msg[0]
	}

	res := g.Map{
		"code": code,
		"data": data,
	}

	if msgStr != "" {
		res["msg"] = msgStr
	}

	r.Response.WriteJson(res)
}

// 获取分页参数
func GetPageData(r *ghttp.Request) (int, int) {
	page := r.GetQuery("page", 1).Int()
	psize := r.GetQuery("psize", 5).Int()
	return page, psize
}

// 生成用户会话信息
func generateUserSession(openID string) (string, int64, g.Map) {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)
	uSession := GetMD5Encode(openID + mytime.String())
	expireTime := utils.NowTimestamp() + SessionExpireDuration
	token := g.Map{
		"access_token":              uSession,
		"user_id":                   uSession,
		"access_token_expired_time": expireTime,
	}

	return uSession, expireTime, token
}

// 将一个字符串进行MD5加密后返回加密后的字符串
func GetMD5Encode(data string) string {
	h := md5.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// 校验微信返回的用户数据
func ValidateUserInfo(rawData, sessionKey, signature string) bool {
	signature2 := GetSha1(rawData + sessionKey)
	return signature == signature2
}

// SHA-1 加密
func GetSha1(str string) string {
	data := []byte(str)
	has := sha1.Sum(data)
	return fmt.Sprintf("%x", has) // 将[]byte转成16进制
}

// 根据USession获取用户信息
func getUserBySession(ctx context.Context, uSession string) (gdb.Record, error) {
	if uSession == "" {
		return nil, gerror.New("未提供access_token")
	}

	md := dao.User.Ctx(ctx)
	return md.Where("USession", uSession).One()
}

// 检查会话是否过期
func isSessionExpired(dataUser gdb.Record) (bool, error) {
	if dataUser == nil {
		return true, gerror.New("用户数据不存在")
	}

	nowTime := utils.NowTimestamp()
	endTimeStr, ok := dataUser["UstateEndTime"]
	if !ok {
		return true, gerror.New("无效的会话状态")
	}

	endTime, err := strconv.ParseInt(endTimeStr.String(), 10, 64)
	if err != nil {
		return true, err
	}

	return nowTime > endTime, nil
}

// 刷新用户会话
func refreshUserSession(ctx context.Context, openID string) (gdb.Record, g.Map, error) {
	md := dao.User.Ctx(ctx)

	// 获取用户数据
	dataUser, err := md.Where("UOpenID", openID).One()
	if err != nil || dataUser == nil {
		return nil, nil, gerror.New("用户不存在")
	}

	// 生成新会话
	uSession, expireTime, token := generateUserSession(openID)
	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)
	// 更新数据库
	mform := map[string]interface{}{
		"USession":      uSession,
		"LoginTime":     mytime,
		"UstateEndTime": expireTime,
	}
	_, err = md.Where("UOpenID", openID).Update(mform)
	if err != nil {
		return nil, nil, err
	}

	// 获取更新后的用户数据
	dataUser, err = md.Where("UOpenID", openID).One()
	if err != nil {
		return nil, nil, err
	}

	return dataUser, token, nil
}

// 微信小程序登录
func (c *WxController) WechatLogin(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	code := r.GetQuery("code").String() // 获取code

	// 根据code获取 openID 和 session_key
	wxLoginResp, err := utils.Code2Session(code, consts.AppID, consts.AppSecret)
	if err != nil {
		responseJson(r, 400, err.Error())
		return
	}

	md := dao.User.Ctx(ctx)
	dataUser, err := md.Where("UOpenID", wxLoginResp.OpenID).One()
	if err != nil {
		responseJson(r, 400, err.Error())
		return
	}

	// 生成会话信息
	uSession, expireTime, token := generateUserSession(wxLoginResp.OpenID)

	if dataUser == nil { // 新用户注册
		// 创建新用户
		SGuid := utils.GetGuid()
		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := gtime.Now().Time.In(loc)
		mform := map[string]interface{}{
			"UGuid":         SGuid,
			"UOpenID":       wxLoginResp.OpenID,
			"SessionKey":    wxLoginResp.SessionKey,
			"USession":      uSession,
			"isMember":      0,
			"Utype":         0,
			"RegTime":       mytime,
			"UstateEndTime": expireTime,
		}
		md.Data(mform).Insert()

		responseJson(r, 0, token)
		return
	} else { // 已有用户登录
		if dataUser["SessionKey"].String() != wxLoginResp.SessionKey { // SessionKey已变更
			// 更新会话信息
			loc, _ := time.LoadLocation("Asia/Shanghai")
			mytime := gtime.Now().Time.In(loc)
			mform := map[string]interface{}{
				"SessionKey":    wxLoginResp.SessionKey,
				"USession":      uSession,
				"LoginTime":     mytime,
				"UstateEndTime": expireTime,
			}
			md.Where("UOpenID", wxLoginResp.OpenID).Update(mform)

			responseJson(r, 0, token)
			return
		} else { // SessionKey未变
			// 使用现有会话
			existingToken := g.Map{
				"access_token":              dataUser["USession"].String(),
				"user_id":                   dataUser["USession"].String(),
				"access_token_expired_time": dataUser["UstateEndTime"].String(),
			}

			responseJson(r, 0, existingToken)
			return
		}
	}
}

// 获取用户信息
func (c *WxController) GetUserInfo(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	uSession := r.GetHeader("access_token")

	// 打印请求信息，帮助调试
	g.Log().Info(ctx, "GetUserInfo请求，access_token:", uSession)

	// 检查是否提供会话标识
	if uSession == "" {
		g.Log().Error(ctx, "GetUserInfo失败: 未提供access_token")
		responseJson(r, -1, nil, "未提供access_token")
		return
	}

	// 获取用户数据
	md := dao.User.Ctx(ctx)
	dataUser, err := md.Where("USession", uSession).One()

	// 记录数据库查询结果
	if err != nil {
		g.Log().Error(ctx, "数据库查询错误:", err)
	}
	if dataUser == nil {
		g.Log().Warning(ctx, "USession查询无结果:", uSession)
	} else {
		g.Log().Info(ctx, "查询成功，用户OpenID:", dataUser["UOpenID"])
	}

	// 检查会话是否有效
	if err != nil || dataUser == nil {
		// 尝试从请求中获取其他可能的标识，例如微信code
		code := r.GetQuery("code").String()
		if code != "" {
			g.Log().Info(ctx, "尝试使用code获取用户:", code)
			// 尝试通过code获取用户
			wxLoginResp, codeErr := utils.Code2Session(code, consts.AppID, consts.AppSecret)
			if codeErr != nil {
				g.Log().Error(ctx, "Code2Session错误:", codeErr)
				responseJson(r, -1, nil, "会话已失效，请重新登录")
				return
			}

			// 使用OpenID查找用户
			dataUser, queryErr := md.Where("UOpenID", wxLoginResp.OpenID).One()
			if queryErr != nil || dataUser == nil {
				g.Log().Error(ctx, "通过OpenID查找用户失败:", wxLoginResp.OpenID)
				responseJson(r, -1, nil, "用户不存在，请重新登录")
				return
			}

			// 生成新会话
			uSession, expireTime, token := generateUserSession(wxLoginResp.OpenID)

			// 更新数据库
			loc, _ := time.LoadLocation("Asia/Shanghai")
			mytime := gtime.Now().Time.In(loc)
			mform := map[string]interface{}{
				"USession":      uSession,
				"SessionKey":    wxLoginResp.SessionKey,
				"LoginTime":     mytime,
				"UstateEndTime": expireTime,
			}
			_, updateErr := md.Where("UOpenID", wxLoginResp.OpenID).Update(mform)
			if updateErr != nil {
				g.Log().Error(ctx, "更新会话失败:", updateErr)
				responseJson(r, -1, nil, "更新会话失败")
				return
			}

			// 获取更新后的数据
			dataUser, refreshErr := md.Where("UOpenID", wxLoginResp.OpenID).One()
			if refreshErr != nil || dataUser == nil {
				g.Log().Error(ctx, "获取更新后的用户数据失败:", refreshErr)
				responseJson(r, -1, nil, "获取用户信息失败")
				return
			}

			g.Log().Info(ctx, "成功通过code重新生成会话:", uSession)

			// 返回用户数据和新token
			g.RequestFromCtx(ctx).Response.WriteJson(g.Map{
				"code":              0,
				"data":              dataUser,
				"token":             token,
				"session_refreshed": true,
			})
			return
		}

		g.Log().Error(ctx, "用户会话不存在，需要重新登录")
		responseJson(r, -1, nil, "用户会话不存在，请重新登录")
		return
	}

	// 检查会话是否过期
	nowTime := utils.NowTimestamp()
	expired := false

	endTimeStr, ok := dataUser["UstateEndTime"]
	if !ok {
		g.Log().Warning(ctx, "UstateEndTime字段不存在")
		// 强制设为过期，后续会刷新
		expired = true
	} else {
		endTimeVal := endTimeStr.String()
		g.Log().Info(ctx, "当前时间:", nowTime, "过期时间:", endTimeVal)

		endTime, parseErr := strconv.ParseInt(endTimeVal, 10, 64)
		if parseErr != nil {
			g.Log().Error(ctx, "解析UstateEndTime失败:", parseErr)
			// 强制设为过期，后续会刷新
			expired = true
		} else if nowTime > endTime {
			g.Log().Info(ctx, "会话已过期，将刷新")
			expired = true
		}
	}

	// 如果会话过期，自动刷新
	if expired {
		openIDValue, ok := dataUser["UOpenID"]
		if !ok || openIDValue.String() == "" {
			g.Log().Error(ctx, "无法获取用户OpenID")
			responseJson(r, -1, nil, "用户数据异常，请重新登录")
			return
		}

		openID := openIDValue.String()
		g.Log().Info(ctx, "刷新会话，用户OpenID:", openID)

		// 刷新会话
		uSession, expireTime, token := generateUserSession(openID)

		// 更新数据库
		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := gtime.Now().Time.In(loc)

		mform := map[string]interface{}{
			"USession":      uSession,
			"LoginTime":     mytime,
			"UstateEndTime": expireTime,
		}
		result, updateErr := md.Where("UOpenID", openID).Update(mform)
		if updateErr != nil {
			g.Log().Error(ctx, "更新会话失败:", updateErr)
			responseJson(r, -1, nil, "更新会话失败")
			return
		}

		affectedRows, rowErr := result.RowsAffected()
		if rowErr != nil || affectedRows == 0 {
			g.Log().Warning(ctx, "会话更新未影响任何行:", openID)
		}

		// 获取更新后的数据
		dataUser, refreshErr := md.Where("UOpenID", openID).One()
		if refreshErr != nil || dataUser == nil {
			g.Log().Error(ctx, "获取更新后的用户数据失败:", refreshErr)
			responseJson(r, -1, nil, "获取用户信息失败")
			return
		}

		g.Log().Info(ctx, "会话刷新成功，新USession:", uSession)

		// 返回用户数据和新token
		g.RequestFromCtx(ctx).Response.WriteJson(g.Map{
			"code":              0,
			"data":              dataUser,
			"token":             token,
			"session_refreshed": true,
		})
		return
	}

	// 会话有效，返回用户数据
	g.Log().Info(ctx, "会话有效，返回用户数据")
	responseJson(r, 0, dataUser)
	return
}

// 获取手机号
func (c *WxController) GetPhone(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	encryptedData := r.GetForm("encryptedata").String()
	iv := r.GetForm("iv").String()
	uSession := r.GetHeader("access_token")

	// 获取用户数据
	md := dao.User.Ctx(ctx)
	dataUser, err := md.Where("USession", uSession).One()
	if err != nil || dataUser == nil {
		responseJson(r, -1, nil, "用户会话不存在")
		return
	}

	sessionKey := dataUser["SessionKey"].String()

	// 解密手机号
	phoneInfo, err := utils.DecryptPhone(sessionKey, encryptedData, iv, consts.AppID)
	if err != nil {
		responseJson(r, -1, err, "解密手机号失败")
		return
	}

	// 更新用户手机号
	phoneNumber := phoneInfo.PurePhoneNumber
	mform := map[string]interface{}{"Tel": phoneNumber}
	md.Where("USession", uSession).Update(mform)

	responseJson(r, 1, phoneInfo.PhoneNumber)
	return
}

// 获取短信验证码
func (c *WxController) GetSmscode(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	phone := r.Get("phone").String()

	// 生成验证码
	smskey := (strconv.FormatInt(utils.NowTimestamp(), 10)[6:])
	msg := "你的验证码是：" + smskey

	// 发送短信
	code, erro, resstring := utils.SmsMsgok(phone, msg)

	g.RequestFromCtx(ctx).Response.WriteJson(g.Map{
		"code":             code,
		"data":             resstring,
		"erro":             erro,
		"verificationCode": smskey,
	})
	return
}

// 保存用户信息
func (c *WxController) SaveUserInfo(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	uSession := r.GetHeader("access_token")
	md := dao.User.Ctx(ctx)

	// 获取表单数据并更新
	mform := r.GetFormMap(map[string]interface{}{
		"UTrueName": "未填写",
		"UTel":      "",
		"UHeadImg":  "",
		"isMember":  1,
	})

	_, err = md.Where("USession", uSession).Update(mform)
	if err != nil {
		responseJson(r, -1, nil, "更新用户信息失败")
		return
	}

	responseJson(r, 1, "ok")
	return
}

// 检查用户状态
func (c *WxController) JudgeUStatus(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	code := r.GetForm("code").String()

	// 通过Code获取会话信息
	data, _ := utils.Code2Session(code, consts.AppID, consts.AppSecret)

	// 获取用户信息
	md := dao.User.Ctx(ctx)
	wxuser, _ := md.One()

	r.Response.WriteJson(g.Map{
		"code":         0,
		"userinfo":     wxuser,
		"Code2Session": data,
	})
	return
}

// 测试接口
func (c *WxController) SetUserP(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	name := r.GetQuery("name", "没参数的默认返回值!") // 获取name
	res = &GetListRes{
		Name: name.String(),
		Id:   10,
	}
	return res, gerror.NewCode(gcode.CodeNotImplemented)
}
