// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// WeChatConfig is the golang structure for table WeChatConfig.
type WeChatConfig struct {
	Id         int    `json:"Id"         orm:"Id"         ` //
	WeChatName string `json:"WeChatName" orm:"WeChatName" ` //
	Appid      string `json:"APPID"      orm:"APPID"      ` //
	Mchid      string `json:"MCHID"      orm:"MCHID"      ` //
	Key        string `json:"KEY"        orm:"KEY"        ` //
	Appsecret  string `json:"APPSECRET"  orm:"APPSECRET"  ` //
	PayUrl     string `json:"PayUrl"     orm:"PayUrl"     ` //
	IsUser     bool   `json:"IsUser"     orm:"IsUser"     ` //
	PayAPI     string `json:"PayAPI"     orm:"PayAPI"     ` //
}
