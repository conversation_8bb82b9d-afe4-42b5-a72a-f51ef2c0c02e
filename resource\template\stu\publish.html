<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布信息</title>
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="publish-container">
        <h2>发布信息</h2>
        <form id="publishForm">
            <div class="form-group">
                <label for="title">标题</label>
                <input type="text" id="title" name="title" required>
            </div>
            <div class="form-group">
                <label for="content">内容</label>
                <textarea id="content" name="content" rows="6" required></textarea>
            </div>
            <div class="form-group">
                <label for="category">分类</label>
                <select id="category" name="category" required>
                    <option value="">请选择分类</option>
                    <!-- 分类选项将通过 JavaScript 动态插入 -->
                </select>
            </div>
            <div class="form-group">
                <label for="images">图片上传</label>
                <div class="image-upload-container">
                    <div class="image-upload-list" id="imagePreviewList">
                        <div class="image-upload-item upload-button">
                            <input type="file" id="images" multiple accept="image/*" style="display: none;">
                            <div class="upload-icon">+</div>
                            <div class="upload-text">上传图片</div>
                        </div>
                    </div>
                </div>
                <div class="upload-tip">最多可上传9张图片</div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn-draft" id="saveDraft">保存草稿</button>
                <button type="button" class="btn-draft" id="loadDraft">加载草稿</button>
                <button type="submit" class="btn-publish">发布</button>
            </div>
        </form>
    </div>
    <nav class="nav-bottom">
        <a href="index.html" class="nav-item">
            <div class="nav-icon icon-home"></div>
            <span class="nav-text">首页</span>
        </a>
        <a href="publish.html" class="nav-item active">
            <div class="nav-icon icon-publish"></div>
            <span class="nav-text">发布</span>
        </a>
        <a href="profile.html" class="nav-item">
            <div class="nav-icon icon-profile"></div>
            <span class="nav-text">我的</span>
        </a>
    </nav>
    <script src="js/api-service.js"></script>
    <script src="js/publish.js"></script>
</body>
</html> 