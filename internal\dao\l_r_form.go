// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLRFormDao is internal type for wrapping internal DAO implements.
type internalLRFormDao = *internal.LRFormDao

// lRFormDao is the data access object for table L_RForm.
// You can define custom methods on it to extend its functionality as you wish.
type lRFormDao struct {
	internalLRFormDao
}

var (
	// LRForm is globally public accessible object for table L_RForm operations.
	LRForm = lRFormDao{
		internal.NewLRFormDao(),
	}
)

// Fill with you ideas below.
