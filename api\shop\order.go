package shop

import (
	"50go/internal/dao"
	"50go/internal/utils"
	sns "50go/internal/utils"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// 规范路由
func (c *ShopController) Order(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	pageindex := r.Get("guid").Int()
	pagesize := r.Get("guid").Int()
	//	thetype := r.Get("type")
	UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
	md := dao.SOrder.Ctx(r.Context())
	ncount, err := md.Count()
	order, _ := md.Where("UGUID", UID).Where("OParentId", 0).Page(pageindex, pagesize).All()

	myorder := order.List()

	var listgoodorder []map[string]interface{}
	if myorder != nil {
		for _, v := range myorder {

			childrenorder, _ := md.Where("OParentId", v["OrderID"]).All()
			childrenorderlist := childrenorder.List()
			var listOrder []map[string]interface{}

			temp := make(map[string]interface{})
			temp["SPImgUrl"] = v["SPImgUrl"]
			temp["SPName"] = v["SPName"]
			temp["SPrice"] = v["SPrice"]
			temp["STruePrice"] = v["STruePrice"]
			temp["ONumber"] = v["ONumber"]
			temp["OUsedNumber"] = v["OUsedNumber"]
			temp["Oweight"] = v["Oweight"]

			listOrder = append(listOrder, temp)

			for _, gg := range childrenorderlist {
				fmt.Println(gg["SCCLASSID"], v["SCID"], gg)

				tempgg := make(map[string]interface{})
				tempgg["SPImgUrl"] = gg["SPImgUrl"]
				tempgg["SPName"] = gg["SPName"]
				tempgg["SPrice"] = gg["SPrice"]
				tempgg["STruePrice"] = gg["STruePrice"]
				tempgg["ONumber"] = gg["ONumber"]
				tempgg["OUsedNumber"] = gg["OUsedNumber"]
				tempgg["Oweight"] = gg["Oweight"]

				listOrder = append(listOrder, tempgg)
			}

			v["Ordergoods"] = listOrder
			listgoodorder = append(listgoodorder, v)
		}
	}

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"data":   listgoodorder,
			"code":   1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

// 规范路由
func (c *ShopController) OrderDetail(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	guid := r.Get("guid")
	md := dao.SOrder.Ctx(r.Context())

	order, _ := md.Where("GuidNU", guid).One()
	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"data": order,
			"code": 1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *ShopController) OrderAdd(ctx context.Context, req *PostReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	listItem := r.Get("listItem").String()

	var categoryList []map[string]interface{}
	json.Unmarshal([]byte(listItem), &categoryList)
	for _, v := range categoryList {

		good, _ := v["good"]
		mygood := good.(map[string]interface{})
		number, errot := v["number"]
		mynumber, myerr := number.(float64)

		GID, gerr := mygood["GID"]
		fmt.Println("number", number, errot, myerr, "GID：", GID, gerr, good)
		onegood, _ := dao.SGoods.Ctx(r.Context()).Where("GID", GID).FieldsEx("Content").One()

		goodprice := onegood["GPrice"].Float64()
		OLastPrice := goodprice * mynumber

		UID := sns.GetUIDbyHeader(ctx) //通过header取得uid
		Guid := utils.GetGuid()

		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := gtime.Now().Time.In(loc)

		mytime3day := mytime.AddDate(0, 0, 3)

		out_trade_no := getOrderNumber(UID+gconv.String(GID), ctx)

		//OrderInfo.GuidNU = _shop.getuserNumber(openid) //单

		mform := r.GetFormMap(map[string]interface{}{"UGUID": UID, "OOrderNumber": out_trade_no, "GuidNU": Guid, "OCreatTime": mytime, "ONumber": mynumber,
			"OStoreId": onegood["StoreID"], "myprice": onegood["GPrice"], "OLastPrice": OLastPrice, "STruePrice": onegood["GPriceYJ"],

			"Oweight": onegood["Oweight"], "SPName": onegood["GName"], "SPImgUrl": onegood["GImg"], "SPID": GID,

			"OendTime": mytime3day, "OState": -1, "OPayState": 0, "OpostPrice": 0, "OParentId": 0, "out_trade_no": out_trade_no,
		})
		md := dao.SOrder.Ctx(r.Context())
		result, err := md.Data(mform).Insert()
		OrderID, err := result.LastInsertId()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code":        200,
				"status_name": "待付款",
				"type":        "goods",
				"data":        result,
				"OrderID":     OrderID,
				"id":          Guid,
			})
		}
	}

	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func getOrderNumber(ugid string, ctx context.Context) string {

	r := g.RequestFromCtx(ctx)
	//1,前4位:月日
	ordderNumber := ""

	loc, _ := time.LoadLocation("Asia/Shanghai")
	mytime := gtime.Now().Time.In(loc)

	frontFour := gconv.String(mytime.Year()) + gconv.String(mytime.Month()) + gconv.String(mytime.Day())

	//2,中4位：uid
	middleFour := ugid
	//3,后4位：今日第几条订单
	gt := gtime.New(mytime)
	todaybegan := gt.StartOfDay().String()
	todayend := gt.EndOfDay().String()
	md := dao.SOrder.Ctx(r.Context()).WhereBetween("OCreatTime", todaybegan, todayend)
	ncount, _ := md.Count()

	ordderNumber = frontFour + middleFour + gconv.String(ncount+1)
	return ordderNumber
}

func (c *ShopController) Orderlist(ctx context.Context, req *PostReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	name := r.GetQuery("name", "没参数的默认返回值!") //获取name

	listgood := g.Map{
		"GPrice":      3213,
		"GPrice2":     12,
		"G_Introduce": "颜色分类：透明-抽象猫咪生产企业：深圳款式：保护壳",
		"Name":        "抽象猫咪手机壳",
		"gid":         1,
		"numb":        0,
		"pic":         "/Up/2",
	}

	listjson := g.Map{
		"typeName":    "a4",
		"GetImgs":     "img",
		"menuContent": listgood,
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"businessend":   "21:00",
			"logourl":       "/images/logo.png",
			"name":          "小熊工厂洗--演示系统 请勿下单",
			"minamount":     "99",
			"businessstart": "11:03",
			"address":       "2东路269号",
			"notice":        "洗衣洗鞋新时尚",
			"telephone":     "***********",
			"tel":           "0871-66227317",
			"expressfee":    "1",
			"ad":            name,
			"seller":        listjson,
		})
	return
}
