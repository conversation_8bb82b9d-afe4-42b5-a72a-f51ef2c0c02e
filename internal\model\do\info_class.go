// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// InfoClass is the golang structure of table Info-Class for DAO operations like Where/Data.
type InfoClass struct {
	g.Meta     `orm:"table:Info-Class, do:true"`
	Cid        interface{} //
	Cguid      interface{} //
	CNmae      interface{} //
	CpareID    interface{} //
	CKeyWord   interface{} //
	CUrl       interface{} //
	Csort      interface{} //
	CisLock    interface{} //
	CcreatTime *gtime.Time //
	DelTime    *gtime.Time //
}
