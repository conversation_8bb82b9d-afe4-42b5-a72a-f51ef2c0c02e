<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <link rel="stylesheet" href="/component/pear/css/pear.css" />
  <link rel="stylesheet" href="/admin/css/admin.css" />
  <link rel="stylesheet" href="/admin/css/admin.dark.css" />
  <link rel="stylesheet" href="/admin/css/variables.css" />
  <link rel="stylesheet" href="/admin/css/reset.css" />
  <style>
   .GPageSpan{ 
    /* background-color: #009688!important; */
    /* color: #009688!important; */
  }

  .GPageLink
  {
    /* background-color: #009688!important;; */
    color: #009688!important;
  }
.layui-laypage-default  li
{
float:left
}
  .layui-form-pane .layui-form-label {
    width: 90px;
  
}

  /* 媒体文件显示样式 */
  .media-preview {
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s ease;
  }
  
  .media-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }
  
  .media-preview img,
  .media-preview video {
    width: 100%;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
  }
  
  .unknown-file {
    height: 80px;
    line-height: 80px;
    text-align: center;
    background: #f5f5f5;
    color: #999;
    border-radius: 4px;
    font-size: 12px;
  }

  </style>
</head>

<body class="pear-container pear-admin">
<div style="padding: 16px;" >

  <div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form"   method="get" action="#"  id="newslist">
            <div class="layui-form-pane">

                <div class="layui-form-item layui-inline">
                    <label class="layui-form-label">标题</label>
                    <div class="layui-input-inline">
                        <input type="text" name="title" placeholder="" class="layui-input" value="{{.search.title}}">
                    </div>
                </div>

         

                

                <div class="layui-form-item layui-inline">             
                  <div class="layui-input-inline" style="width: 100px;">
                    <select name="ctype" >
                      <option value="">属性 {{.search.ctype}}</option>
                      <option value="IsTop" {{if eq $.search.ctype "IsTop"}}selected{{end}} >推荐</option>
                      <option value="IsLock" {{if eq $.search.ctype "IsLock"}}selected{{end}}>锁定</option>
                      <option value="IsRed" {{if eq $.search.ctype "IsRed"}}selected{{end}}>图片</option>
                      <option value="IsHistory" {{if eq $.search.ctype "IsHistory"}}selected{{end}}>已删除</option>
                      
                  </select>  

                  </div>
              </div>
                <div class="layui-form-item layui-inline">
                    <label class="layui-form-label">发布日期</label>
                    <div class="layui-input-inline">
                        <input type="text" name="time"  autocomplete="off" id="ID-laydate" placeholder=" - " class="layui-input"  value="{{.search.time}}" >
                    </div>
                </div>
                <div class="layui-form-item layui-inline">
                    <button class="layui-btn layui-btn-sm " lay-submit lay-filter="user-query">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <!-- <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary"  style="color: var(--global-primary-color)">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button> -->
                </div>
            </div>

              <!-- <div class="layui-table-tool-self ">
                <button class="layui-btn layui-btn-sm ayui-btn-warm" lay-event="getCheckData" onclick="add()" load style="background-color: #36b368;"><i class="pear-icon pear-icon-add"></i>添加</button>
              </div> -->
       
      

        </form>
    </div>
</div>

  <div class="layui-card">
    <div class="layui-card-body">
     
      <table class="layui-table"  id="role-table" >
        <thead>
          <tr>
            <th>开始地点</th>
            <th>到达地点</th>
            <th>车主</th>
  
          
            <th>现场图片</th>
            <th>送达图片</th>
            <th>交警放行条</th>         
            <th>身份证</th>

            <th>驾驶证</th>         
            <th>行车证</th>

            <th>时间</th>
            <th>距离/价格</th>
            <th>状态</th>
            <th>备注</th>
           
            <th>审核</th>
            <th>司机</th>
            <th>指派司机</th>

            {{ $outerUser := .user }}
          </tr>
        </thead>
        <tbody>
          {{range $key, $value := .data}}
          <tr>
            <td>{{$value.fctitle}}  
<br> {{$value.fclat}}
<br> {{$value.fclong}}
            </td>

            <td>
              {{$value.jctitle}}  
              <br> {{$value.jclat}}
              <br> {{$value.jclong}}
                          </td>

            <td>{{$value.jcren}}  
              <br> {{$value.jctel}}

                          </td>
      
            <!-- <td><img src="{{$value.arrimgs}}" height="80px"></td>  -->
            <td>
              {{if $value.arrBegainImgs}}
                {{if eq $value.arrBegainImgs_type "image"}}
                  <div class="media-preview">
                    <img src="{{$value.arrBegainImgs}}" onclick="previewMedia('{{$value.arrBegainImgs}}', 'image')">
                  </div>
                {{else if eq $value.arrBegainImgs_type "video"}}
                  <div class="media-preview">
                    <video src="{{$value.arrBegainImgs}}"  onclick="previewMedia('{{$value.arrBegainImgs}}', 'video')"></video>
                  </div>
                {{else}}
                  <div class="unknown-file">未知文件</div>
                {{end}}
              {{end}}
            </td> 
            <td>
              {{if $value.arrendinImgs}}
                {{if eq $value.arrendinImgs_type "image"}}
                  <div class="media-preview">
                    <img src="{{$value.arrendinImgs}}" onclick="previewMedia('{{$value.arrendinImgs}}', 'image')">
                  </div>
                {{else if eq $value.arrendinImgs_type "video"}}
                  <div class="media-preview">
                    <video src="{{$value.arrendinImgs}}"  onclick="previewMedia('{{$value.arrendinImgs}}', 'video')"></video>
                  </div>
                {{else}}
                  <div class="unknown-file">未知文件</div>
                {{end}}
              {{end}}
            </td> 

            <td>
              {{if $value.arrjjimgs}}
                {{if eq $value.arrjjimgs_type "image"}}
                  <div class="media-preview">
                    <img src="{{$value.arrjjimgs}}" onclick="previewMedia('{{$value.arrjjimgs}}', 'image')">
                  </div>
                {{else if eq $value.arrjjimgs_type "video"}}
                  <div class="media-preview">
                    <video src="{{$value.arrjjimgs}}"  onclick="previewMedia('{{$value.arrjjimgs}}', 'video')"></video>
                  </div>
                {{else}}
                  <div class="unknown-file">未知文件</div>
                {{end}}
              {{end}}
            </td>
            <td>
              {{if $value.arridimgs}}
                {{if eq $value.arridimgs_type "image"}}
                  <div class="media-preview">
                    <img src="{{$value.arridimgs}}" onclick="previewMedia('{{$value.arridimgs}}', 'image')">
                  </div>
                {{else if eq $value.arridimgs_type "video"}}
                  <div class="media-preview">
                    <video src="{{$value.arridimgs}}"  onclick="previewMedia('{{$value.arridimgs}}', 'video')"></video>
                  </div>
                {{else}}
                  <div class="unknown-file">未知文件</div>
                {{end}}
              {{end}}
            </td>
            
            <td>
              {{if $value.arrdidimgs}}
                {{if eq $value.arrdidimgs_type "image"}}
                  <div class="media-preview">
                    <img src="{{$value.arrdidimgs}}" onclick="previewMedia('{{$value.arrdidimgs}}', 'image')">
                  </div>
                {{else if eq $value.arrdidimgs_type "video"}}
                  <div class="media-preview">
                    <video src="{{$value.arrdidimgs}}"  onclick="previewMedia('{{$value.arrdidimgs}}', 'video')"></video>
                  </div>
                {{else}}
                  <div class="unknown-file">未知文件</div>
                {{end}}
              {{end}}
            </td>
            <td>
              {{if $value.arrcaridimgs}}
                {{if eq $value.arrcaridimgs_type "image"}}
                  <div class="media-preview">
                    <img src="{{$value.arrcaridimgs}}" onclick="previewMedia('{{$value.arrcaridimgs}}', 'image')">
                  </div>
                {{else if eq $value.arrcaridimgs_type "video"}}
                  <div class="media-preview">
                    <video src="{{$value.arrcaridimgs}}"  onclick="previewMedia('{{$value.arrcaridimgs}}', 'video')"></video>
                  </div>
                {{else}}
                  <div class="unknown-file">未知文件</div>
                {{end}}
              {{end}}
            </td>

            <td>
              
            下单:{{$value.addTime}}<br>             
            支付:{{$value.paytime}}</td>
            <td>{{$value.distance}} {{$value.price}}</td>
   
            <td>{{$value.PJstate}}</td>
            <td>{{$value.Content}}</td>
  
        
            <td>
   
              
              {{if eq $outerUser.issh 1}}
              
              {{$value.shren_name}}   <br>      {{$value.shtime}}      <br>  
         
              <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                <a class="layui-btn layui-btn-xs layui-btn-normal" onclick="jyedit('{{$value.Guid}}')" style="background-color: #1E9FFF;"><i class="pear-icon pear-icon-edit"></i>审核</a>
                <a class="layui-btn layui-btn-xs layui-btn-primary" onclick="jyview('{{$value.Guid}}')" style="background-color: #5FB878;"><i class="pear-icon pear-icon-view"></i>查看</a>
              </div>
           
              {{end}}
            </td>
       
            <td>{{$value.UsjID}}<br>
              {{$value.sTel}}<br>
              {{$value.sname}}           
            </td>
         
            <td> 
         
              {{if eq $value.sh1 1}}审核通过{{end}}
              {{if eq $value.sh1 0}}未通过{{end}}
              <br>
              {{if eq $outerUser.ispc 1}}
              <br>
              {{if eq $value.sh1 1}}
              {{if ne $value.PJstate "完成"}}
              <a class="layui-btn layui-btn-xs" onclick="editSJ('{{$value.Guid}}')" style="background-color: #36b368;"><i class="pear-icon pear-icon-edit"></i>指派司机</a>
              <br>
              <a class="layui-btn layui-btn-xs layui-btn-warm" onclick="sendDriverSms('{{$value.Guid}}', '{{$value.UsjID}}', '{{$value.sTel}}', '{{$value.sname}}')" style="background-color: #FFB800;"><i class="layui-icon layui-icon-notice"></i>发送司机通知</a>
              {{end}}
              {{end}}
              {{end}}
              </td>
         
          </tr>
          {{end}}

        </tbody>
      </table>
      <div>{{.page}}</div> 
   
      
    </div>
  </div>


    <!-- 审核备注弹窗 -->
    <div id="auditDialog" style="display:none;padding:20px;">
      <form class="layui-form">
          <div class="layui-form-item layui-form-text">
              <label class="layui-form-label">审核备注</label>
              <div class="layui-input-block">
                  <textarea name="remarks" placeholder="请输入审核备注" class="layui-textarea"></textarea>
              </div>
          </div>
      </form>
  </div>
  

  


  <script src="/component/layui/layui.js"></script>
  <script src="/component/pear/pear.js"></script>

  <script>

// layui.use('form', function(){
//     var form = layui.form
//     form.render();

//     form.on('switch(aaa)', function(chkbox){

//   }
// });


    layui.use(["button","form","jquery","laydate"], function () {
      var admin = layui.admin;
      var button = layui.button;
      let form = layui.form;
      let $ = layui.jquery;
      var laydate = layui.laydate;

 // 日期范围
 laydate.render({
    elem: "#ID-laydate",
    range: true,
    shortcuts: [
      {
        text: "上个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month - 1, 1),
            new Date(year, month, 0)
          ];
        }
      },
      {
        text: "这个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month, 1),
            new Date(year, month + 1, 0)
          ];
        }
      },
      {
        text: "下个月",
        value: function(){
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth();
          return [
            new Date(year, month + 1, 1),
            new Date(year, month + 2, 0)
          ];
        }
      }
    ]
  });

      button.load({
        elem: '[load]',
        time: 600,
        done: function () {
        //  popup.success("加载完成");
        }
      })
    })

    layui.use(['toast', 'jquery', 'layer',"form","popup"], function () {
      // var toast = layui.toast;
      let $ = layui.jquery;
      var form = layui.form
      var popup=layui.popup
    form.render();
      // toast.error({ title: "2危险消息", message: "消息描述" })
      // toast.warning({ title: "警告消息", message: "消息描述" })
      // toast.info({ title: "通知消息", message: "消息描述" })
      window.edit = function(obj) {
      layer.open({ 
        title: '编辑 - id:' + obj,
        type: 2,
        area: ['90%', '95%'],
        content: '/system/ab/sj-edit?guid=' + obj,
        end: function(){
          location.reload();
        }
      })
    }

    window.editSJ = function(guid) {
      layer.open({ 
        title: '编辑 - 任务ID:' + guid,
        type: 2,
        area: ['80%', '50%'],
        content: '/system/ab/sj-edit?guid=' + guid,
        end: function(){
          location.reload();
        }
      })
    }

        // 审核操作
window.jyedit = function(guid) {
  layer.open({ 
    title: '订单审核 - ID:' + guid,
    type: 2,
    area: ['90%', '95%'],
    content: '/system/ab/jy-edit?guid=' + guid,
    end: function(){
      location.reload();
    }
  })
}

        // 查看操作
window.jyview = function(guid) {
  layer.open({ 
    title: '订单查看 - ID:' + guid,
    type: 2,
    area: ['90%', '95%'],
    content: '/system/ab/jy-edit?guid=' + guid + '&mode=view',
    end: function(){
      location.reload();
    }
  })
}


    form.on('switch(aaa)', function(chkbox){
      layer.load(2, { shade: [0.35, '#ccc'] });
           console.log(chkbox.elem); //开关是否开启，true或者false
           console.log(chkbox.elem.checked); //开关是否开启，true或者false
           console.log(chkbox.value)
           console.log(">>>",chkbox.elem.dataset['id']); //开关是否开启，true或者false
           layui.jquery.ajax({
                    url:  '/system/ab/t-charge?guid='+chkbox.elem.dataset['id'],
                    data:{type:chkbox.value},
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							          popup.success("成功", function () {
                           // location.reload();
					            	});
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
             return false;

    });


    window.audit= function(guid,type, title) {
        layer.open({
            type: 1,
            title: title,
            area: ['500px', '300px'],
            content: $('#auditDialog'),
            btn: ['确定', '取消'],
            yes: function(index, layero){
                var remarks = layero.find('textarea[name="remarks"]').val();
         
                layui.jquery.post('/system/ab/j-charge', {
                    guid: guid,
                    ctype: type,
                    title: title,
                    remarks: remarks
                }, function(res){
                    if(res.code === 200){
                        layer.msg('操作成功');
                        layer.close(index);
                        location.reload();
                    } else {
                        layer.msg(res.msg || '操作失败');
                    }
                });
            }
        });
    };

        window.remove = function (obj) {
		    layer.confirm('确定删除吗?',{
					icon: 3,
					title: '提示'
                }, function (index) {
                  layui.jquery.ajax({
                    url: '/system/ab/t-charge?guid=' + obj,
                    data:{type:"del"},
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
							             popup.success("删除成功", function () {
                          location.reload();
						           });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
          });
			}

    })
  

    window.add = function(obj) {
      layer.open({
        title: '添加',
        type: 2,
        area: ['90%', '95%'],
        content: '/system/ab/sj-edit?guid=0',
        end: function(){
          location.reload();
        }
      })
    }

    // 预览媒体文件（图片或视频）
    window.previewMedia = function(url, type) {
      if (!url) return;
      
      var title = type === 'image' ? '图片预览' : '视频预览';
      var content = '';
      
      if (type === 'image') {
        content = '<div style="text-align:center;"><img src="' + url + '" style="max-width:100%;max-height:80vh;" /></div>';
      } else if (type === 'video') {
        content = '<div style="text-align:center;"><video src="' + url + '" controls style="max-width:100%;max-height:80vh;"></video></div>';
      }
      
      layer.open({
        type: 1,
        title: title,
        area: ['80%', '80%'],
        content: content,
        shadeClose: true,
        anim: 1
      });
    }

    // 发送司机任务通知短信
    window.sendDriverSms = function(guid, driverId, driverPhone, driverName) {
      layer.confirm('确定要发送司机任务通知短信吗？', {
        icon: 3,
        title: '确认发送'
      }, function(index) {
        layer.close(index);
        var loading = layer.load(2);
        layui.jquery.ajax({
          url: '/system/ab/send-driver-sms',
          type: 'POST',
          data: {
            guid: guid,
            driver_id: driverId
          },
          success: function(res) {
            layer.close(loading);
            if(res.code === 200) {
              layer.msg('司机通知短信发送成功', {icon: 1});
            } else {
              layer.msg(res.msg || '短信发送失败', {icon: 2});
            }
          },
          error: function() {
            layer.close(loading);
            layer.msg('系统错误', {icon: 2});
          }
        });
      });
    }






    // layui.use(['notice', 'jquery', 'layer', 'code'], function () {
    //   var notice = layui.notice;

    //   notice.success("成功消息")
    //   notice.error("危险消息")
    //   notice.warning("警告消息")
    //   notice.info("通用消息")
    
    // })

  </script>
</body>

</html>