package pay

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

var (
	mchID                      string = "190000****"                               // 商户号
	mchCertificateSerialNumber string = "3775************************************" // 商户证书序列号
	mchAPIv3Key                string = "2ab9****************************"         // 商户APIv3密钥
	Appid                      string = "dddddddddd"                               //
	MchPKFileName              string = ".mals"
)

// 规范路由
func (c *WxController) Getjspai(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	ctx, client, err := getWechatClient()
	svc := jsapi.JsapiApiService{Client: client}
	// 得到prepay_id，以及调起支付所需的参数和签名
	resp, result, err := svc.PrepayWithRequestPayment(ctx,
		jsapi.PrepayRequest{
			Appid:       core.String("wxd678efh567hg6787"),
			Mchid:       core.String("1900009191"),
			Description: core.String("Image形象店-深圳腾大-QQ公仔"),
			OutTradeNo:  core.String("1217752501201407033233368018"),
			Attach:      core.String("自定义数据说明"),
			NotifyUrl:   core.String("https://www.weixin.qq.com/wxpay/pay.php"),
			Amount: &jsapi.Amount{
				Total: core.Int64(100),
			},
			Payer: &jsapi.Payer{
				Openid: core.String("oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"),
			},
		},
	)

	if err == nil {
		//log.Println(resp, result)
		log.Printf("status=%d resp=%s", result.Response.StatusCode, resp)
	} else {
		log.Println(err)
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"code": result.Response.StatusCode,
			"data": resp,
			"erro": err,
		})
	return //两种返回方式1

}

// 获取加解密处理
func getWechatClient() (context.Context, *core.Client, error) {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(MchPKFileName)
	if err != nil {
		log.Print("load merchant private key error")
		return nil, nil, err
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, mchCertificateSerialNumber, mchPrivateKey, mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Printf("new wechat pay client err:%s", err)
		return nil, nil, err
	}
	return ctx, client, nil
}

// 创建并生成待支付信息
func CreateWechatPrepayWithPayment(outTradeNo, description, attach, spOpenid string, amount int64) (map[string]interface{}, error) {
	notifyUrl := "/api/thirdParty/wechat/pay/getPayResult"
	ctx, client, err := getWechatClient()
	if err != nil {
		return nil, err
	}

	tmp, _ := time.ParseDuration("5m")
	endTime := time.Now().Add(tmp)
	svc := jsapi.JsapiApiService{Client: client}
	resp, _, err := svc.PrepayWithRequestPayment(ctx,
		jsapi.PrepayRequest{
			Appid:         core.String(Appid),
			Mchid:         core.String(mchID),
			Description:   core.String(description),
			OutTradeNo:    core.String(outTradeNo),
			TimeExpire:    core.Time(endTime),
			Attach:        core.String(attach),
			NotifyUrl:     core.String(notifyUrl),
			GoodsTag:      core.String("WXG"),
			LimitPay:      []string{"no_credit"},
			SupportFapiao: core.Bool(false),
			Amount: &jsapi.Amount{
				Currency: core.String("CNY"),
				Total:    core.Int64(amount),
			},
			Payer: &jsapi.Payer{
				Openid: core.String(spOpenid),
			},
			SettleInfo: &jsapi.SettleInfo{
				ProfitSharing: core.Bool(false),
			},
		},
	)

	if err != nil {
		// 处理错误
		return nil, err
	} else {
		// 处理返回结果
		//log.Printf("status=%d resp=%s", result.Response.StatusCode, resp)
	}
	result := make(map[string]interface{})
	//	tmpJson := utils.GetJsonStr(resp)
	//	json.Unmarshal([]byte(tmpJson), &result)
	json.Unmarshal([]byte(resp), &result)
	return result, nil
	//return result, nil
}

func JsapiApiService_Prepay() {
	var (
		mchID                      string = "190000****"                               // 商户号
		mchCertificateSerialNumber string = "3775************************************" // 商户证书序列号
		mchAPIv3Key                string = "2ab9****************************"         // 商户APIv3密钥
	)

	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath("/path/to/merchant/apiclient_key.pem")
	if err != nil {
		log.Print("load merchant private key error")
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, mchCertificateSerialNumber, mchPrivateKey, mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Printf("new wechat pay client err:%s", err)
	}

	svc := jsapi.JsapiApiService{Client: client}
	resp, result, err := svc.Prepay(ctx,
		jsapi.PrepayRequest{
			Appid:         core.String("wxd678efh567hg6787"),
			Mchid:         core.String("1230000109"),
			Description:   core.String("Image形象店-深圳腾大-QQ公仔"),
			OutTradeNo:    core.String("1217752501201407033233368018"),
			TimeExpire:    core.Time(time.Now()),
			Attach:        core.String("自定义数据说明"),
			NotifyUrl:     core.String("https://www.weixin.qq.com/wxpay/pay.php"),
			GoodsTag:      core.String("WXG"),
			LimitPay:      []string{"LimitPay_example"},
			SupportFapiao: core.Bool(false),
			Amount: &jsapi.Amount{
				Currency: core.String("CNY"),
				Total:    core.Int64(100),
			},
			Payer: &jsapi.Payer{
				Openid: core.String("oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"),
			},
			Detail: &jsapi.Detail{
				CostPrice: core.Int64(608800),
				GoodsDetail: []jsapi.GoodsDetail{jsapi.GoodsDetail{
					GoodsName:        core.String("iPhoneX 256G"),
					MerchantGoodsId:  core.String("ABC"),
					Quantity:         core.Int64(1),
					UnitPrice:        core.Int64(828800),
					WechatpayGoodsId: core.String("1001"),
				}},
				InvoiceId: core.String("wx123"),
			},
			SceneInfo: &jsapi.SceneInfo{
				DeviceId:      core.String("013467007045764"),
				PayerClientIp: core.String("*************"),
				StoreInfo: &jsapi.StoreInfo{
					Address:  core.String("广东省深圳市南山区科技中一道10000号"),
					AreaCode: core.String("440305"),
					Id:       core.String("0001"),
					Name:     core.String("腾讯大厦分店"),
				},
			},
			SettleInfo: &jsapi.SettleInfo{
				ProfitSharing: core.Bool(false),
			},
		},
	)

	if err != nil {
		// 处理错误
		log.Printf("call Prepay err:%s", err)
	} else {
		// 处理返回结果
		log.Printf("status=%d resp=%s", result.Response.StatusCode, resp)
	}
}
