// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ABStore is the golang structure of table AB_Store for DAO operations like Where/Data.
type ABStore struct {
	g.Meta     `orm:"table:AB_Store, do:true"`
	Sid        interface{} //
	AreaID     interface{} //
	Sname      interface{} //
	Sstime     interface{} //
	Setime     interface{} //
	Stel       interface{} //
	Simg       interface{} //
	Saddress   interface{} //
	Slatitude  interface{} //
	Slongitude interface{} //
	Sintroduce interface{} //
	Stime      *gtime.Time //
	IsLock     interface{} //
	SType      interface{} //
	SPower     interface{} //
}
