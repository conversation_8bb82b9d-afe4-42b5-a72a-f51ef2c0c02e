package controller

import (
	"50go/internal/dao"
	tree "50go/internal/utils"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/shopspring/decimal"
)

var Goods = cGoodsClass{}

type cGoodsClass struct{}

// 混编一体Classlist
func (c *cGoodsClass) Class_list(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	md := dao.SClasss.Ctx(r.Context())
	classs, err := md.All()
	var a int32 = 0
	var chridnum []map[string]interface{}
	zhdatra := classs.List()
	if zhdatra != nil {
		for _, v := range zhdatra {
			v["id"] = v["SCID"].(int32)
			v["title"] = v["SCNmae"]
			v["name"] = v["SCNmae"]
			if v["SCpareID"] == nil {
				v["pid"] = a
			} else {
				v["pid"] = int32(v["SCpareID"].(int))
			}
			chridnum = append(chridnum, v)
		}
	}
	GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
	treeData, err := json.Marshal(GetRuleTreeArrayLayui)

	if err == nil {
		r.Response.WriteTpl("goods/class_list.html", g.Map{
			"header":   "This is header",
			"info":     classs,
			"treeData": string(treeData),
		})
	}

}

//混编一体编辑Class修改

// 混编一体Classlist
func (c *cGoodsClass) Class_edit(r *ghttp.Request) {

	md := dao.SClasss.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		cid := r.Get("CID").String()

		nclass, err := md.Fields("SCNmae,SCpareID,SCID").All()
		var a int32 = 0

		zhdatra := nclass.List()
		var chridnum []map[string]interface{}
		if zhdatra != nil {
			for _, v := range zhdatra {
				v["id"] = v["SCID"].(int32) //int 要转成 int64
				v["title"] = v["SCNmae"]
				num, _ := strconv.ParseInt(cid, 10, 64)
				if v["SCID"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["SCpareID"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int32(v["SCpareID"].(int))
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		fmt.Println("jsonData==>", string(jsonData))

		if cid == "0" || cid == "" { //新增
			if err == nil {
				r.Response.WriteTpl("goods/class_edit.html", g.Map{
					"header":   "This is header",
					"newss":    g.Map{"SCID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			news, err := md.Where("SCID", cid).One()
			if err == nil {
				r.Response.WriteTpl("goods/class_edit.html", g.Map{
					"header":   "This is header",
					"newss":    news,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		cid := r.GetForm("SCID").String()
		if cid == "0" || cid == "" {

			mform := r.GetFormMap(map[string]interface{}{"SCNmae": "667", "SCpareID": 0})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"SCNmae": "", "SCpareID": 0})
			result, err := md.Where("SCID", cid).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// 混编一体部门（角色）列表
func (c *cGoodsClass) Goods_list(r *ghttp.Request) {

	model, _ := dao.SGoods.Ctx(r.Context()).FieldsEx("Content").All()
	data := model.List()

	var chridnum []map[string]interface{}
	if data != nil {
		for _, v := range data {
			decimalValue, _ := decimal.NewFromString(v["GPrice"].(string))
			v["gpirce64"] = decimalValue
			chridnum = append(chridnum, v)
		}
	}

	r.Response.WriteTpl("goods/goods_list.html", g.Map{
		"header": "This is header",
		"data":   chridnum,
	})

}

// 混编一体商品修改
func (c *cGoodsClass) Goods_edit(r *ghttp.Request) {
	md := dao.SGoods.Ctx(r.Context())
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("GID").String()

		storelist, _ := dao.SStore.Ctx(r.Context()).All()

		goodsclass, err := dao.SClasss.Ctx(r.Context()).All()
		goodsclasslist := goodsclass.List()
		var a int32 = 0
		var chridnum []map[string]interface{}
		if goodsclasslist != nil {
			for _, v := range goodsclasslist {
				v["id"] = v["SCID"].(int32) //int 要转成 int64
				v["title"] = v["SCNmae"]
				num, _ := strconv.ParseInt(id, 10, 64)
				if v["id"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["SCpareID"] == nil {
					v["pid"] = a
				} else {

					v["pid"] = int32(v["SCpareID"].(int))
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("goods/goods_edit.html", g.Map{
					"header":    "This is header",
					"newss":     g.Map{"GID": 0},
					"jsonData":  string(jsonData),
					"storelist": storelist,
				})
			}
		} else { //修改
			newss, err := md.Where("GID", id).One()

			pics := strings.Split(newss["GImg"].String(), ",")
			Content := strings.ReplaceAll(newss["GContent"].String(), "\"", "\\\"")

			Content = strings.ReplaceAll(Content, "\n", "")
			Content = strings.ReplaceAll(Content, "\r", "")

			if err == nil {
				r.Response.WriteTpl("goods/goods_edit.html", g.Map{
					"header":    "This is header",
					"newss":     newss,
					"jsonData":  string(jsonData),
					"Content":   Content,
					"pics":      pics,
					"storelist": storelist,
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("GID").String()
		pid := r.Get("demoTree_select_nodeId")
		fpiclist := r.GetForm("site_pic__upimgs")
		fIsLock := r.GetForm("BBB")
		GContent := r.GetForm("GContent")
		GTime := r.GetForm("GTime")

		if id == "0" || id == "" {
			mform := r.GetFormMap(map[string]interface{}{"GName": "", "SCCLASSID": pid, "GContent": GContent, "GImg": fpiclist, "GIState": fIsLock, "GPrice": "0", "GPriceYJ": "0", "Time": GTime, "GSort": "0", "G_Introduce": "", "Gdw": "", "StoreID": ""})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"GName": "", "SCCLASSID": pid, "GContent": GContent, "GImg": fpiclist, "GIState": fIsLock, "GPrice": "0", "GPriceYJ": "0", "Time": GTime, "GSort": "0", "G_Introduce": "", "Gdw": "", "StoreID": ""})
			result, err := md.Where("GID", id).Update(mform)

			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// //  列表页面修改
func (c *cGoodsClass) Charge(r *ghttp.Request) {
	GID := r.Get("GID").Int()
	if GID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.SGoods.Ctx(r.Context()).Where("GID", GID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.SGoods.Ctx(r.Context()).Data(gdb.Map{"IsHistory": "1"}).Where("GID", GID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.SGoods.Ctx(r.Context()).Where("GID", GID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// 混编一体Classlist
func (c *cGoodsClass) Store_list(r *ghttp.Request) {
	data, err := dao.SStore.Ctx(r.Context()).All()
	if err == nil {
		r.Response.WriteTpl("goods/store_list.html", g.Map{
			"header": "This is header",
			"data":   data,
		})
	}
}

//混编一体编辑Class修改

// 混编一体Classlist
func (c *cGoodsClass) Store_edit(r *ghttp.Request) {
	md := dao.SStore.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("SID").String()
		if id == "0" || id == "" { //新增
			r.Response.WriteTpl("goods/store_edit.html", g.Map{
				"newss": g.Map{"SID": 0},
			})
		} else { //修改
			data, err := md.Where("SID", id).One()
			if err == nil {
				pics := strings.Split(data["Simg"].String(), ",")

				Content := strings.ReplaceAll(data["Sintroduce"].String(), "\"", "\\\"")
				Content = strings.ReplaceAll(Content, "\n", "")
				Content = strings.ReplaceAll(Content, "\r", "")

				r.Response.WriteTpl("goods/store_edit.html", g.Map{
					"header":  "This is header",
					"newss":   data,
					"pics":    pics,
					"Content": Content,
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("SID").String()

		site_pic__upimgs := r.GetForm("site_pic__upimgs")

		Address := r.GetForm("Address")

		if id == "0" || id == "" {

			mform := r.GetFormMap(map[string]interface{}{"Sname": "", "areaID": "", "Stel": "", "Stime": "", "Simg": site_pic__upimgs, "Sintroduce": "", "where": "", "Saddress": Address, "Slatitude": "", "Slongitude": ""})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"Sname": "", "areaID": "", "Stel": "", "Stime": "", "Simg": site_pic__upimgs, "Sintroduce": "", "where": "", "Saddress": Address, "Slatitude": "", "Slongitude": ""})
			result, err := md.Where("SID", id).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}
