// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// InfoClassDao is the data access object for the table Info-Class.
type InfoClassDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  InfoClassColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// InfoClassColumns defines and stores column names for the table Info-Class.
type InfoClassColumns struct {
	Cid        string //
	Cguid      string //
	CNmae      string //
	CpareID    string //
	CKeyWord   string //
	CUrl       string //
	Csort      string //
	CisLock    string //
	CcreatTime string //
	DelTime    string //
}

// infoClassColumns holds the columns for the table Info-Class.
var infoClassColumns = InfoClassColumns{
	Cid:        "CID",
	Cguid:      "CGUID",
	CNmae:      "CNmae",
	CpareID:    "CpareID",
	CKeyWord:   "CKeyWord",
	CUrl:       "CUrl",
	Csort:      "Csort",
	CisLock:    "CisLock",
	CcreatTime: "CcreatTime",
	DelTime:    "delTime",
}

// NewInfoClassDao creates and returns a new DAO object for table data access.
func NewInfoClassDao(handlers ...gdb.ModelHandler) *InfoClassDao {
	return &InfoClassDao{
		group:    "default",
		table:    "Info-Class",
		columns:  infoClassColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *InfoClassDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *InfoClassDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *InfoClassDao) Columns() InfoClassColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *InfoClassDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *InfoClassDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *InfoClassDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
