// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SGoods is the golang structure of table S_Goods for DAO operations like Where/Data.
type SGoods struct {
	g.Meta      `orm:"table:S_Goods, do:true"`
	Gid         interface{} //
	StoreID     interface{} //
	AreaID      interface{} //
	Scclassid   interface{} //
	GName       interface{} //
	Gday        interface{} //
	GPriceYJ    interface{} //
	GPrice      interface{} //
	GPrice2     interface{} //
	GPrice3     interface{} //
	FXPrice2    interface{} //
	FXPrice     interface{} //
	FXPrice3    interface{} //
	GCounts     interface{} //
	GSoldCount  interface{} //
	GImg        interface{} //
	GSImg       interface{} //
	GContent    interface{} //
	GPost       interface{} //
	GPostCost   interface{} //
	GTime       *gtime.Time //
	GIState     interface{} //
	GTop        interface{} //
	GIntroduce  interface{} //
	CodeClassID interface{} //
	GSort       interface{} //
	GvipLeve    interface{} //
	GArea       interface{} //
	GSales      interface{} //
	GWeight     interface{} //
	Gdw         interface{} //
}
