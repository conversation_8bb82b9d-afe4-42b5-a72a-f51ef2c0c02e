<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />

    <style>
        .uploadimg {

            height: 100px;
            margin: 1px;
            border-radius: 3px;
            /* background: lightblue; */
            text-align: center;
            border: 1px solid #ccc;
            font-weight: 400;
            font-size: 15px;
            color: #fff;
            padding: 5px;
        }

        .upimg {

            border: 1px solid #ccc;
        }

        .uploadimg:after {
            content: "请上传文件";
            line-height: 120px;
        }

        .el-icon {
            --color: inherit;
            height: 10em;
            width: 10em;
            line-height: 10em;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            position: relative;
            fill: currentColor;
            color: var(--color);
            font-size: inherit
        }

        .el-upload {
            /* border: 1px dashed var(--el-border-color); */
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: var(--el-transition-duration-fast);
        }

        .el-icon.is-loading {
            -webkit-animation: rotating 2s linear infinite;
            animation: rotating 2s linear infinite
        }

        .el-icon svg {
            height: 4em;
            width: 4em;
        }

        .el-affix--fixed {
            position: fixed
        }

        .treeSelect .layui-select-title span {
            line-height: 38px;
        }

        .layui-form-select dl {
            padding: 0;
        }

    </style>
</head>

<body>
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">
            <div class="main-container">
                <input name="SCID" id="SCID" type="hidden" value="{{.newss.SCID}}">



                <div class="layui-form-item" id="toolbarDiv">
                    <label class="layui-form-label">上级分类:</label>
                    <div class="layui-input-block">
                       
                        <ul id="demoTree" class="dtree" data-id="0" style="width: 50%;"></ul>
                        
                    </div>
                </div>


                <!-- <div class="layui-form-item">
                    <label class="layui-form-label">上级分类:</label>
                    <div class="layui-input-inline">


                        <select name="CpareID" lay-verify="required">
                            <option value="0">顶级分类</option>
                            {{range $key, $value := .list}}
                            <option value="{{$value.CID}}" {{if eq $.newss.CpareID $value.CID }}selected{{end}} {{if eq
                                $.newss.CID $value.CID }}disabled{{end}}>{{$value.spacer}} {{$value.CNmae}}</option>
                            {{end}}

                        </select>


                    </div>
                </div> -->



                <div class="layui-form-item">
                    <label class="layui-form-label">标题:</label>
                    <div class="layui-input-block">
                        <input type="text" name="SCNmae" lay-verify="required" value="{{.newss.SCNmae}}"
                            autocomplete="off" class="layui-input" style="max-width: 600px;">
                    </div>
                </div>


            </div>
<!-- 
            <div style="overflow: auto;">
                <ul id="dept_tree" class="dtree"></ul>
            </div> -->


            <div class="bottom">
                <div class="button-container">
                    <div class="layui-btn-container layui-col-xs12">
                        <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary">Reset</button>
                    </div>

                </div>
            </div>
    </form>

    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>

  
    <style type="text/css">
        body {
            height: 100%;
            width: 100%;
            background-size: cover;
            margin: 0 auto;
        }
        td {
            font-size: 12px !important;
        }

        .layui-form-checkbox span {
            height: 30px;
        }
        .layui-field-title {
            border-top: 1px solid white;
        }
        table {
            width: 100% !important;
        }

    </style>
    <script>



        layui.use(['jquery','dtree'], function () {
            var tree = layui.tree;
            var mdata = {{.jsonData }};
            var treeSelect= layui.treeSelect;
            var dtree = layui.dtree,
            $ = layui.jquery;


    var DemoTree = dtree.render({
      elem: "#demoTree",
      data: mdata, // 使用data加载
      width:"50%",
      line:true,
      selectTips: "请选择分类",

      selectInitVal: "{{.newss.SCpareID}}", // 你可以在这里指定默认值
      select:true,
      scroll:"#toolbarDiv", // 绑定div元素
      icon: ["0","-1"]  // 显示非最后一级节点图标，隐藏最后一级节点图标
    //   ficon: ["0","7"],  // 设定一级图标样式。0表示方形加减图标，7表示文件图标
    //   skin: "layui"  // layui主题风格
     
    });
    
    // 绑定节点点击
    dtree.on("node('demoTree')" ,function(obj){
      layer.msg(JSON.stringify(obj.param));
    });



 
});


        layui.use(['jquery', 'upload'], function () {
            $ = layui.jquery;
            //执行实例
            var uploadInst = layui.upload.render({
                elem: '.upimg',
                url: "/system/comme/upload-img", //上传接口
                done: function (res) {
                    // 给此img设置src属性
                    if (res.code == 200) {
                        $(".myimg").attr('src', res.data);
                        $(".myimginp").val(res.data);
                    }
                    else {
                        layer.msg('上传文件失败');
                    }
                },
                error: function () {
                    layer.msg('上传文件失败');
                }
            });
        });


        layui.use(['form', 'jquery', 'laydate'], function () {
            let form = layui.form;
            let $ = layui.jquery;
            var laydate = layui.laydate;



            form.on('submit(user-save)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                $.ajax({
                    url: '/system//goods/class_edit',
                    data: $('#news_edit').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");

                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });


        })
    </script>
</body>

</html>