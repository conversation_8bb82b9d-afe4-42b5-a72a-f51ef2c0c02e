﻿
html, body {
    width: 100%;
    height: 100%;
}

.hide {
    display: none;
    /*opacity: 0;*/
}

.draw-gift {
    width: 100%;
    height: 100%;
    /*overflow: hidden;*/ position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 10
}

    .draw-gift .draw-img {
        width: 100%;
        position: relative;
        margin-top: 4.5rem;
    }

        .draw-gift .draw-img .draw-down {
            position: absolute;
            width: 100%;
            height: 18.9rem;
            background-position: center;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-image: url(/Theme/image/open-no.png);
            /*left: 50%;
                    margin-left: -4.5rem;*/
            z-index: 11;
            /*-webkit-animation:draw-down 0.5s ease 1.2s 1 alternate;*/
            /*-webkit-animation-timing-function:cubic-bezier(0.25,0.1,0.25,1);*/
        }

.draw-logo {
    position: absolute;
    width: 9rem;
    height: 9.9rem;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url(/Theme/images/logo_png.png);
    left: 50%;
    top: 100%;
    margin-left: -4.5rem;
    z-index: 11;
    /*-webkit-animation:draw-down 0.5s ease 1.2s 1 alternate;*/
    /*-webkit-animation-timing-function:cubic-bezier(0.25,0.1,0.25,1);*/
}

@-webkit-keyframes draw-down {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 0;
    }
}

.draw-gift .draw-img .draw-down2 {
    position: absolute;
    width: 100%;
    height: 18.9rem;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url(/Theme/image/open-before.png);
    /*left: 50%;
            margin-left: -4.5rem;*/
    z-index: 10;
    /*-webkit-animation:draw-down2 0.5s ease 1.4s 1 alternate;*/
    /*-webkit-animation-timing-function:cubic-bezier(0.25,0.1,0.25,1);*/
}

@-webkit-keyframes draw-down2 {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.draw-gift .draw-img .draw-mid {
    position: absolute;
    width: 4rem;
    height: 4rem;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url(/Theme/image/open.png);
    left: 50%;
    margin-left: -2rem;
    z-index: 100;
    top: 4.3rem;
    /*animation:open 0.3s linear 1s 3 alternate;*/
}

.draw-gift .draw-img .draw-mid-move {
    -webkit-animation: open 0.2s linear 0.5s infinite alternate;
    -webkit-animation-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
    animation: open 0.2s linear 0.5s infinite alternate;
    animation-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
}

@keyframes open {
    0% {
        transform: scale(1);
    }

    100% {
        transform: scale(0.9);
    }
}

@-webkit-keyframes open {
    0% {
        -webkit-transform: scale(1);
    }

    100% {
        -webkit-transform: scale(0.9);
    }
}

.draw-gift .draw-img .draw-up {
    position: absolute;
    width: 100%;
    height: 7.15rem;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url(/Theme/image/open-back-1.png);
    /*left: 50%;
            margin-left: -4.5rem;*/
    top: 0;
    z-index: 8;
    opacity: 0;
}

.draw-gift .draw-img .draw-up-up {
    position: absolute;
    width: 100%;
    height: 8.12rem;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url(/Theme/image/open-back-2.png);
    /*left: 50%;
            margin-left: -4.5rem;*/
    top: 0;
    /*-webkit-transform: rotateX(180deg);*/
    /*-webkit-transform-style: flat;*/
    /*-webkit-transform-origin: bottom;*/
    /*-webkit-animation: draw-up-up 1s linear 0s 0.5 normal;*/
    /*backface-visibility: visible;*/
    /*-webkit-animation-timing-function:cubic-bezier(0.25,0.1,0.25,1);*/
    opacity: 0;
    z-index: 3;
}

.draw-gift .draw-img .draw-list {
    position: absolute;
    width: 8rem;
    height: 7rem;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url(/Theme/image/draw-list-bg.jpg);
    left: 25%;
    /*margin-left: -2.5rem;*/
    top: 3.5rem;
    opacity: 0;
    z-index: 8;
}

.draw-list h1 {
    text-align: center;
    width: 100%;
    font-weight: bold;
    font-size: 1.2rem;
    color: #f5b08c;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}

.draw-list p {
    text-align: center;
    font-size: 17px;
    width: 100%;
    color: #f5b08c;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}

.draw-gift .draw-img .draw-list img {
    vertical-align: middle;
    position: absolute;
    width: 70%;
    top: 1.6rem;
    left: 15%;
}

#draw-btn {
    position: absolute;
    width: 100%;
    text-align: center;
    font-size: 0.65rem;
    color: #333;
    top: 17rem;
}

.aui_close {
    width: 40px;
    height: 40px;
    line-height: 40px;
    display: block;
    position: absolute;
    left: 43.5%;
    bottom: 5%;
    font-family: Helvetica, STHeiti;
    _font-family: '\u9ed1\u4f53', 'Book Antiqua', Palatino;
    font-size: 30px;
    border-radius: 100%;
    background: #999;
    color: #FFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, .3);
    -moz-transition: linear .06s;
    -webkit-transition: linear .06s;
    transition: linear .06s;
    padding: 0;
    text-align: center;
    text-decoration: none;
    outline: none;
    cursor: pointer;
    z-index: 11;
}

    .aui_close:hover {
        width: 44px;
        height: 44px;
        line-height: 44px;
        left: 43.5%;
        bottom: 5%;
        color: #FFF;
        box-shadow: 0 1px 3px rgba(209, 40, 42, .5);
        background: #d1282a;
        border-radius: 100%;
        transition: all 0.2s ease-out;
        opacity: 0.8;
    }
