// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// WeChatConfig is the golang structure of table WeChatConfig for DAO operations like Where/Data.
type WeChatConfig struct {
	g.Meta     `orm:"table:WeChatConfig, do:true"`
	Id         interface{} //
	WeChatName interface{} //
	Appid      interface{} //
	Mchid      interface{} //
	Key        interface{} //
	Appsecret  interface{} //
	PayUrl     interface{} //
	IsUser     interface{} //
	PayAPI     interface{} //
}
