// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SOrder is the golang structure of table S_Order for DAO operations like Where/Data.
type SOrder struct {
	g.Meta                    `orm:"table:S_Order, do:true"`
	OrderID                   interface{} //
	GuidNU                    interface{} //
	OParentId                 interface{} //
	ParentOopenID             interface{} //
	ParentParentOopenID       interface{} //
	ParentParentParentOopenID interface{} //
	Uguid                     interface{} //
	Spid                      interface{} //
	OStoreId                  interface{} //
	ISvipleve                 interface{} //
	OState                    interface{} //
	OType                     interface{} //
	OpayType                  interface{} //
	OPayState                 interface{} //
	OpayTime                  *gtime.Time //
	OCreatTime                *gtime.Time //
	SPImgUrl                  interface{} //
	SPName                    interface{} //
	SPrice                    interface{} //
	STruePrice                interface{} //
	OLastPrice                interface{} //
	OFinalPrice               interface{} //
	OpostPrice                interface{} //
	ONumber                   interface{} //
	OUsedNumber               interface{} //
	Oweight                   interface{} //
	OALLweight                interface{} //
	ParentFxprice             interface{} //
	ParentParentFxprice       interface{} //
	ParentThreeFxprice        interface{} //
	TempleParentOopenID       interface{} //
	OopenID                   interface{} //
	OPeople                   interface{} //
	OTel                      interface{} //
	OProvance                 interface{} //
	OCity                     interface{} //
	ODistrict                 interface{} //
	OStreet                   interface{} //
	OAddress                  interface{} //
	OCount                    interface{} //
	OCredits                  interface{} //
	OPostway                  interface{} //
	OPostState                interface{} //
	OPostCode                 interface{} //
	OMsg                      interface{} //
	OTCredits                 interface{} //
	OYudingText               interface{} //
	OYudingTime               *gtime.Time //
	OgetGoodsImgs             interface{} //
	OgetGoodsRemarks          interface{} //
	OgetGoodsTime             *gtime.Time //
	OgetGoodsImgsBack         interface{} //
	OgetGoodsRemarksBack      interface{} //
	OgetGoodsTimeBack         *gtime.Time //
	OgetOrderOpenId           interface{} //
	OgetOrderTime             *gtime.Time //
	OgetOrderOpenIdBack       interface{} //
	OgetOrderTimeBack         *gtime.Time //
	OBackState                interface{} //
	OstartOpenId              interface{} //
	OstartDescribe            interface{} //
	OstartImgs                interface{} //
	OstartTime                *gtime.Time //
	OendDescribe              interface{} //
	OendImgs                  interface{} //
	OendTime                  *gtime.Time //
	OselfSendOrder            interface{} //
	OSuggestMark              interface{} //
	OSuggestText              interface{} //
	OSuggestStars             interface{} //
	OSuggestTime              *gtime.Time //
	OCustomSureDescribe       interface{} //
	OCustomSureImgs           interface{} //
	OCustomSureTime           *gtime.Time //
	OrderNumber               interface{} //
	OCompanyId                interface{} //
	OCancleText               interface{} //
	OCancleTime               *gtime.Time //
	OBusinessId               interface{} //
	ORemarks                  interface{} //
	OOrderNumber              interface{} //
	OHxOpenId                 interface{} //
	OHxTime                   interface{} //
	IsViolate                 interface{} //
	IsRefund                  interface{} //
	TransactionId             interface{} //
	OutTradeNo                interface{} //
	RefundFee                 interface{} //
	OPostCom                  interface{} //
}
