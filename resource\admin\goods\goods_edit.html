<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="/component/pear/css/pear.css" />
    <link rel="stylesheet" href="/admin/css/admin.css" />
    <link rel="stylesheet" href="/admin/css/admin.dark.css" />
    <link rel="stylesheet" href="/admin/css/variables.css" />
    <link rel="stylesheet" href="/admin/css/reset.css" />
    <script src="/component/layui/layui.js"></script>
    <script src="/component/pear/pear.js"></script>
</head>

<body class="pear-admin">
    <form class="layui-form" action="" id="news_edit">
        <div class="mainBox">

            {{.newss}}
            <div class="main-container">
                <input name="GID" id="GID" type="hidden" value="{{.newss.GID}}">


                <div class="layui-form-item">
                    <label class="layui-form-label">名称:</label>
                    <div class="layui-input-block">
                        <input type="text" name="GName" lay-verify="required" value="{{.newss.GName}}"
                            autocomplete="off" class="layui-input" style="max-width: 600px;">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">分类：</label>
                    <div class="layui-input-block" style="max-width:300px;min-width:200px">
                        <ul id="demoTree" class="dtree" data-id="0"></ul>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">所属店铺</label>
                    <div class="layui-input-inline" style="max-width: 300px;">
                 
                        <select name="StoreID" lay-verify="required">
                            <option value="0">顶级分类</option>
                            {{range $key, $value := .storelist}}
                            <option value="{{$value.SID}}" {{if eq $.newss.StoreID $value.SID }}selected{{end}} >{{$value.Sname}}</option>
                            {{end}}

                        </select>

                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">单位</label>
                        <div class="layui-input-inline">
                            <input type="text" name="Gdw" id="Gdw" class="layui-input" value="{{.newss.Gdw}}">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label">商城价格</label>
                        <div class="layui-input-inline">
                            <input type="text" name="GPrice" id="GPrice" class="layui-input" value="{{.newss.GPrice}}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><del>原价市场价</del></label>
                        <div class="layui-input-inline">
                            <input type="text" name="GPriceYJ" id="GPriceYJ" class="layui-input"
                                value="{{.newss.GPriceYJ}}">
                        </div>
                    </div>
                </div>



                <div class="layui-form-item">
                    <label class="layui-form-label">多图：</label>

                    <!-- 多图 -->
                    <div class="layui-input-block">
                        <style type="text/css">
                            .layui-upload-drag {
                                position: relative;
                                padding: 10px;
                                border: 1px dashed #e2e2e2;
                                background-color: #fff;
                                text-align: center;
                                cursor: pointer;
                                color: #999;
                                margin-right: 10px;
                                margin-bottom: 10px;
                            }

                            .del_img {
                                position: absolute;
                                z-index: 99;
                                right: 0;
                                top: 0;
                                width: 25px;
                                height: 25px;
                                display: block;
                            }

                            .del_img img {
                                position: absolute;
                                z-index: 9;
                                right: 0px;
                                top: 0px;
                                width: 25px;
                                height: 25px;
                                display: inline-block;
                            }
                        </style>

                        {{range $key, $value := .pics}}
                        <div class="layui-upload-drag">
                            <div class="del_img" onclick="remove_image_site_pic__upimgs(this);">
                                <img src="/assets/delete.png">
                            </div>
                            <a href="{{$value}}" target="_blank">
                                <img name="img_src_site_pic__upimgs" src="{{$value}}" alt="建议上传尺寸450x450(点击放大预览)"
                                    title="建议上传尺寸450x450(点击放大预览)" width="90" height="90">
                            </a>
                        </div>

                        {{end}}


                        <div class="layui-upload-drag img_upload_site_pic__upimgs">
                            <img id="upload_album_site_pic__upimgs" src="/assets/default_upload.png"
                                alt="上传建议上传尺寸450x450" title="上传建议上传尺寸450x450" width="90" height="90"><input
                                class="layui-upload-file" type="file" accept="image/*" name="file" multiple="">
                            <input type="hidden" id="site_pic__upimgs" name="site_pic__upimgs"
                                value="{{.newss.piclist}}">
                        </div>
                        <script type="text/javascript">

                            layui.use(['upload'], function () {

                                //声明变量
                                var layer = layui.layer
                                    , upload = layui.upload
                                    , $ = layui.$;

                                // 初始化图片隐藏域
                                var ids = '';
                                $('img[name="img_src_site_pic__upimgs"]').each(function () {
                                    ids += $(this).attr('src') + ","
                                });
                                ids = ids.substr(0, (ids.length - 1));
                                $("#site_pic__upimgs").val(ids);

                                    /**
                                     * 普通图片上传
                                     */
                                    var uploadInst = upload.render({
                                        elem: '#upload_album_site_pic__upimgs'
                                        , url: "/system/comme/upload-img"
                                        , accept: 'images'
                                        , acceptMime: 'image/*'
                                        , exts: "jpg|png|gif|bmp|jpeg"
                                        , field: 'file'//文件域字段名
                                        , size: 10240 //最大允许上传的文件大小
                                        , multiple: true
                                        , number: 20 //最大上传张数
                                        , before: function (obj) {
                                            //预读本地文件
                                        }
                                        , done: function (res) {
                                            //上传完毕回调

                                            var hideStr = $("#site_pic__upimgs").attr("value");
                                            var itemArr = hideStr.split(',');
                                            if (itemArr.length >= "20") {
                                                layer.msg("最多上传20张图片", { icon: 5, time: 1000 }, function () {
                                                    //TODO...
                                                });
                                                return false;
                                            }

                                            //如果上传失败
                                            if (res.status <= 0) {
                                                return layer.msg('上传失败');
                                            }

                                            //渲染界面
                                            var attStr = '<div class="layui-upload-drag">' +
                                                '<div class="del_img" onclick="remove_image_site_pic__upimgs(this);">' +
                                                '<img src="/assets/delete.png"></img>' +
                                                '</div>' +
                                                '<a href="' + res.data + '" target="_blank">' +
                                                '<img name="img_src_site_pic__upimgs" src="' + res.data + '" alt="建议上传尺寸450x450(点击放大预览)" title="建议上传尺寸450x450(点击放大预览)" width="90" height="90">' +
                                                '</a>' +
                                                '</div>';
                                            $(".img_upload_site_pic__upimgs").before(attStr);

                                            //获取最新的图集
                                            var ids = '';
                                            $('img[name="img_src_site_pic__upimgs"]').each(function () {
                                                ids += $(this).attr('src') + ","
                                            });
                                            ids = ids.substr(0, (ids.length - 1));
                                            //给隐藏域赋值
                                            $("#site_pic__upimgs").val(ids);

                                            return false;
                                        }
                                        , error: function () {
                                            //请求异常回调
                                            return layer.msg('数据请求异常');
                                        }
                                    });
                                }

                            );

                            // 删除图片
                            function remove_image_site_pic__upimgs(obj) {
                                //obj.remove();
                                layui.$(obj).parent().remove();

                                //获取最新的图集
                                var ids = '';
                                layui.$('img[name="img_src_site_pic__upimgs"]').each(function () {
                                    ids += layui.$(this).attr('src') + ","
                                });
                                ids = ids.substr(0, (ids.length - 1));
                                //给隐藏域赋值
                                layui.$("#site_pic__upimgs").val(ids);
                            }

                        </script>
                    </div>

                </div>



                <div class="layui-form-item cover">
                    <label class="layui-form-label">属性：</label>
                    <div class="layui-form">
                        <input type="checkbox" name="AAA" title="推荐">
                        <input type="checkbox" name="BBB" lay-text="图片" checked>
                        <input type="checkbox" name="CCC" lay-text="置顶" checked>
                        <input type="checkbox" name="CCC" lay-text="锁定" checked>
                    </div>
                </div>



    

                


                <div class="layui-form-item" style="max-width:1200px;">
                    <label class="layui-form-label">内容：</label>
                    <div class="layui-input-block">
                      <textarea id="lnContent" name="lnContent" style="width:900px;height:300px;">{{.newss.GContent}}</textarea>
             
                      <input type="hidden" id="Content" name="GContent" >
                    </div>     
                </div>


              

                <div class="layui-form-item layui-form-text" style="width:665px;">
                    <label class="layui-form-label">描述：</label>
                    <div class="layui-input-block">
                        <textarea name="G_Introduce" placeholder="请输入描述"
                            class="layui-textarea">{{.newss.G_Introduce}}</textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布时间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="Time" id="Time" lay-verify="datetime" placeholder="yyyy-MM-dd"
                            autocomplete="off" value="{{.newss.Time}}" class="layui-input">
                    </div>
                </div>
            </div>
        </div>



        <div class="bottom">
            <div class="button-container">
                <div class="layui-btn-container layui-col-xs12">
                    <button class="layui-btn" lay-submit lay-filter="user-save">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">清空</button>
                </div>

            </div>
        </div>
    </form>


  

    
<script src="/goEdit/kindeditor.js"></script>
   
   
<script>  
            var editor;
            KindEditor.ready(function (K) {
            editor = K.create('#lnContent', {
                resizeType: 2,
                uploadJson: '/system/comme/upload-file', // 相对于当前页面的路径
                //fileManagerJson: '../yaEditor/Tools/file_manager_json.ashx',
                //allowFileManager: true,
                fillDescAfterUploadImage: true,

            });
        });

        
          //  【编辑器获取焦点】 

</script>
    <script>
        layui.use(['jquery', 'dtree'], function () {

            var tree = layui.tree;
            var mdata = {{.jsonData }};
        var treeSelect = layui.treeSelect;
        var dtree = layui.dtree,
            $ = layui.jquery;


        var DemoTree = dtree.render({
            elem: "#demoTree",
            data: mdata, // 使用data加载
            width: "50%",
            line: true,
            selectTips: "请选择分类",

            selectInitVal: "{{.newss.SCCLASSID}}", // 你可以在这里指定默认值
            select: true,
            scroll: "#toolbarDiv", // 绑定div元素
            icon: ["0", "-1"]  // 显示非最后一级节点图标，隐藏最后一级节点图标
            //   ficon: ["0","7"],  // 设定一级图标样式。0表示方形加减图标，7表示文件图标
            //   skin: "layui"  // layui主题风格

        });

        // 绑定节点点击
        dtree.on("node('demoTree')", function (obj) {
            layer.msg(JSON.stringify(obj.param));
        });

    });

    </script>


    <script>
        layui.use(['form', 'jquery', 'laydate'], function () {
            let form = layui.form;
            let $ = layui.jquery;
            var laydate = layui.laydate;


            laydate.render({
                elem: '#Time'
                , type: 'datetime'
            });

            form.verify({

                ip: [
                    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
                    , 'IP地址不符合规则'
                ]
            });

            form.on('radio(type)', function (data) {
                if (data.value == 1) {
                    $(".conn-pwd").show();
                    $(".conn-key").hide();
                } else {
                    $(".conn-key").show();
                    $(".conn-pwd").hide();
                }
            });

            form.on('submit(user-save)', function (data) {
                layer.load(2, { shade: [0.35, '#ccc'] });
                $.ajax({
                    url: '/system/goods/goods_edit',
                    data: $('#news_edit').serialize(),
                    dataType: 'json',
                    type: 'post',
                    success: function (result) {
                        layer.closeAll('loading');
                        if (result.code == 200) {
                            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                                // parent.layui.table.reload("role-table");
                            });
                        } else {
                            layer.msg(result.msg, { icon: 2, time: 1000 });
                        }
                    }
                })
                return false;
            });


        })
    </script>
</body>

</html>