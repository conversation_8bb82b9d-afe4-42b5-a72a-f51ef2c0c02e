html,
body,
.layui-layout {
	height: 100%;
}

.pear-admin .layui-body,
.pear-admin .layui-logo,
.pear-admin .layui-side,
.pear-admin .layui-header,
.pear-admin .layui-header .layui-layout-left {
	transition: all .3s;
}

.pear-admin.banner-layout .layui-side {
	top: 60px !important;
}

.pear-admin.banner-layout .layui-side .layui-logo {
	display: none;
}

.pear-admin.banner-layout .layui-header .layui-logo {
	display: inline-block;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100% !important;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100% !important;
}

.pear-admin .layui-header.dark-theme .layui-layout-control .layui-this * {
	background-color: rgba(0, 0, 0, .1) !important;
}

.pear-admin .layui-header .layui-logo {
	display: none;
}

.pear-admin .layui-logo .title {
	font-size: 20px;
}

.pear-admin .layui-layout-right .layui-nav-child {
	border: 1px solid whitesmoke;
	border-radius: 4px;
	width: auto;
	left: auto;
	right: -23px;
}

.pear-admin .layui-header {
	left: 230px;
	width: calc(100% - 230px);
	background-color: white;
}

.pear-admin .layui-layout-control {
	left: 140px;
	position: absolute;
}

.pear-admin .layui-layout-control .layui-nav {
	padding: 0px;
}

.pear-admin .layui-logo {
	height: 60px;
	line-height: 60px;
	border-bottom: 1px solid rgba(0, 0, 0, .12);
	box-sizing: border-box;
	position: relative;
	background-color: #28333E;
	width: 230px;
}

.pear-admin .layui-logo img {
	width: 34px;
	height: 34px;
}

.pear-admin .layui-logo .title {
	font-size: 21px;
	font-weight: 550;
	color: var(--global-primary-color);
	position: relative;
	top: 5px;
	margin-left: 5px;
}

.pear-admin .layui-logo .logo {
	display: none;
}

.pear-admin .layui-side {
	top: 0px;
	width: 230px;
	box-shadow: 2px 0 6px rgba(0, 21, 41, .20);
	z-index: 9999;
}

.pear-admin .layui-side-scroll::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

.pear-admin .layui-side-scroll {
	height: calc(100% - 60px) !important;
	background-color: #28333E;
	width: 247px;

}

.pear-admin .layui-header .layui-nav .layui-nav-item>a {
	color: black;
	font-size: 15px;
}

.pear-admin .layui-body {
	bottom: 0px;
	padding-bottom: 0px;
	background-color: whitesmoke;
	height: calc(100% - 60px);
	overflow-y: auto;
	left: 230px;
}

.pear-admin .layui-body>div {
	height: 100%;
}

.pear-admin .layui-layout-left {
	left: 0px;
}

.pear-admin .layui-footer {
	position: absolute;
	display: flex;
	justify-content: space-between;
	left: 230px;
	background: #fff;
	border-top: 1px solid #F2F2F2;
	box-shadow: none;
	-webkit-transition: left .3s;
	transition: left .3s;
	overflow: hidden;
	color: #3c3c3cb3;
	font-weight: 300;
	font-size: 13.6px;
}

.pear-admin .layui-footer.close {
	display: none;
}

/** 通栏布局 */

.pear-admin.banner-layout .layui-header {
	left: 0px;
	z-index: 99999;
	width: 100%;
}

.pear-admin.banner-layout .layui-header.light-theme {
	border-bottom: 1px solid whitesmoke;
}

.pear-admin.banner-layout .layui-header.auto-theme,
.pear-admin.banner-layout .layui-header.dark-theme {
	box-shadow: 0 1px 4px rgba(0, 0, 0, .1);
}

.pear-admin.banner-layout .layui-header .layui-layout-left {
	left: 230px;
}

.pear-admin.banner-layout .layui-header .layui-logo .title {
	top: 2px;
}

.pear-admin.banner-layout .layui-header .layui-layout-control {
	display: inline-block;
	left: 370px;
}

/** 头部主题 */
.pear-admin .auto-theme {
	background-color: var(--global-primary-color);
	color: white;
}

.pear-admin .auto-theme .layui-logo {
	background-color: var(--global-primary-color);
	border: none;
}

.pear-admin .auto-theme .layui-logo .title {
	color: white;
}

.pear-admin .auto-theme .layui-nav * {
	color: white !important;
}

.pear-admin .auto-theme .layui-nav.pear-nav-control .layui-this * {
	color: black !important;
}

.pear-admin .auto-theme .layui-nav .layui-nav-child a {
	color: #5f5f5f !important;
	color: rgba(0, 0, 0, .8) !important;
}

/** 收缩布局 */
.pear-mini .layui-side .layui-logo .title {
	display: none;
}

.pear-mini .layui-side .layui-logo .logo {
	display: inline-block;
}

.pear-mini .layui-side {
	width: 60px;
}

.pear-mini .layui-header {
	left: 60px;
	width: calc(100% - 60px);
}

.pear-mini .layui-body {
	left: 60px;
}

.pear-mini .layui-side .layui-logo {
	width: 60px;
}

.pear-mini .layui-footer {
	left: 60px;
}

.pear-mini .layui-nav-tree .layui-nav-item span {
	display: none;
}

.pear-mini .layui-side-scroll {
	height: calc(100% - 60px);
}

.pear-admin .layui-header .layui-nav .layui-nav-bar {
	top: 0px !important;
	background-color: var(--global-primary-color);
	height: 2px !important;
}

.pear-admin .layui-header .layui-nav .layui-this:after {
	display: none;
}

.pear-admin .layui-header .layui-nav-more {
	display: none;
}

.pear-collapsed-pe {
	right: 30px;
	bottom: 30px;
	z-index: 400000;
	position: absolute;
	background-color: var(--global-primary-color) !important;
	box-shadow: 2px 0 6px rgba(0, 21, 41, .20);
	text-align: center;
	border-radius: 4px;
	line-height: 50px;
	display: none;
	height: 50px;
	width: 50px;
}

.pear-collapsed-pe a {
	color: white !important;
}

@media screen and (min-width: 768px) {
	.layui-hide-sm {
		display: inline-block !important;
	}
}

@media screen and (min-width: 769px) {
	.layui-hide-sm {
		display: none !important;
	}
}

/** 新增兼容 */
@media screen and (max-width:768px) {
	.collapse {
		display: none !important;
	}

	.pear-collapsed-pe {
		display: inline-block !important;
	}

	.layui-layout-control {
		left: 45px !important;
	}

	.layui-layout-left {
		padding-left: 10px;
		padding-right: 10px;
	}

	.pear-mini .layui-side-scroll {
		height: calc(100% - 62px);
	}

	.pear-mini .layui-side {
		width: 0px;
	}

	.pear-mini .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-mini .layui-body {
		left: 0px;
	}

	.pear-mini .layui-footer {
		left: 0px;
	}

	.pear-mini .layui-logo {
		width: 0px;
	}

	.pear-admin .layui-body {
		left: 0px;
	}

	.pear-admin .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-admin .pear-cover {
		width: 100%;
		height: 100%;
		background-color: #1E1E1E;
		display: block;
		position: absolute;
		z-index: 1000;
		opacity: 0;
		margin-top: -60px;
	}

	.pear-mini .pear-cover {
		display: none;
	}
}

@-webkit-keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(2000px);
		transform: translateX(2000px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		transform: translateX(0)
	}
}

@keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(800px);
		-ms-transform: translateX(800px);
		transform: translateX(800px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		-ms-transform: translateX(0);
		transform: translateX(0)
	}
}

.layer-anim-right {
	-webkit-animation: am-horizontal-roll_show .5s ease-out;
	animation: am-horizontal-roll_show .5s ease-out;

}

/** 侧边主题 (亮) */
.light-theme.layui-side {
	box-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05) !important;
}

.light-theme.layui-side .layui-logo {
	background-color: white !important;
	color: black !important;
	border-bottom: 1px whitesmoke solid;
}

.light-theme.layui-side .layui-side-scroll {
	background-color: white !important;
	color: black !important;
}

.dark-theme.layui-header {
	border-bottom: none;
	background-color: #28333E;
	color: whitesmoke;
}

.dark-theme.layui-header li>a {
	color: whitesmoke !important;
}

.dark-theme.layui-header .layui-logo {
	box-shadow: none;
	border: none;
}

/** 顶部主题 (白) */
.light-theme.layui-header .layui-logo {
	background-color: white;
	border: none;
	box-shadow: none;
}

/** 主题面板 */
.pearone-color .set-text {
	height: 42px;
	line-height: 42px;
}

.pearone-color .color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.pearone-color .color-content {
	padding: 15px 10px 0 20px;
}

.pearone-color .color-content ul {
	list-style: none;
	padding: 0px;
}

.pearone-color .color-content ul li {
	position: relative;
	display: inline-block;
	vertical-align: top;
	width: 70px;
	height: 50px;
	margin: 0 20px 20px 0;
	padding: 2px 2px 2px 2px;
	background-color: #f2f2f2;
	cursor: pointer;
	font-size: 12px;
	color: #666;
}

.pearone-color .color-content li.layui-this:after,
.pearone-color .color-content li:hover:after {
	width: 100%;
	height: 100%;
	padding: 4px;
	top: -6px;
	left: -6px;
	border: var(--global-primary-color) 2px solid;
	opacity: 1;
	border-radius: 4px;
}

.pearone-color .color-content li:after {
	content: '';
	position: absolute;
	z-index: 20;
	top: 50%;
	left: 50%;
	width: 1px;
	height: 0;
	border: 2px solid #F2F2F2;
	transition: all .3s;
	-webkit-transition: all .3s;
	opacity: 0;
}

.select-color {
	margin-bottom: 30px;
}

.select-color .select-color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.select-color .select-color-content {
	padding: 20px 0 0px 0px;
	margin-bottom: 4px;
}

.select-color .select-color-content .select-color-item {
	width: 24px;
	height: 24px;
	color: white;
	margin-left: 24px;
	border-radius: 6px;
	background-color: gray;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
	text-align: center;
	line-height: 24px;
	font-size: 12px;
	float: left;
}

.message .layui-tab-title li:not(:last-child) {
	border-right: 1px solid #eee;
}

/** 首屏加载 */
.loader-wrapper {
	position: fixed;
	width: 100%;
	height: 100%;
	background-color: whitesmoke;
	z-index: 9999999;
}

.loader {
	width: 50px;
	height: 50px;
	margin: 30px auto 40px;
	margin-top: 20%;
	position: relative;
	z-index: 999999;
	background-color: whitesmoke;
}

.loader:before {
	content: "";
	width: 50px;
	height: 7px;
	border-radius: 50%;
	background: #000;
	opacity: 0.1;
	position: absolute;
	top: 59px;
	left: 0;
	animation: shadow .5s linear infinite;
}

.loader:after {
	content: "";
	width: 50px;
	height: 50px;
	border-radius: 10px;
	background-color: var(--global-primary-color);
	position: absolute;
	top: 0;
	left: 0;
	animation: loading .5s linear infinite;
}

@-webkit-keyframes loading {
	17% {
		border-bottom-right-radius: 3px;
	}

	25% {
		transform: translateY(9px) rotate(22.5deg);
	}

	50% {
		transform: translateY(18px) scale(1, 0.9) rotate(45deg);
		border-bottom-right-radius: 40px;
	}

	75% {
		transform: translateY(9px) rotate(67.5deg);
	}

	100% {
		transform: translateY(0) rotate(90deg);
	}
}

@keyframes loading {
	17% {
		border-bottom-right-radius: 3px;
	}

	25% {
		transform: translateY(9px) rotate(22.5deg);
	}

	50% {
		transform: translateY(18px) scale(1, 0.9) rotate(45deg);
		border-bottom-right-radius: 40px;
	}

	75% {
		transform: translateY(9px) rotate(67.5deg);
	}

	100% {
		transform: translateY(0) rotate(90deg);
	}
}

@-webkit-keyframes shadow {

	0%,
	100% {
		transform: scale(1, 1);
	}

	50% {
		transform: scale(1.2, 1);
	}
}

@keyframes shadow {

	0%,
	100% {
		transform: scale(1, 1);
	}

	50% {
		transform: scale(1.2, 1);
	}
}