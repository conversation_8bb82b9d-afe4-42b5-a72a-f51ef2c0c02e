// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalSOrderDao is internal type for wrapping internal DAO implements.
type internalSOrderDao = *internal.SOrderDao

// sOrderDao is the data access object for table S_Order.
// You can define custom methods on it to extend its functionality as you wish.
type sOrderDao struct {
	internalSOrderDao
}

var (
	// SOrder is globally public accessible object for table S_Order operations.
	SOrder = sOrderDao{
		internal.NewSOrderDao(),
	}
)

// Fill with you ideas below.
