package controller

import (
	"50go/internal/dao"
	utils "50go/internal/utils"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

var AB = cAB{}

type cAB struct{}

// 救援 列表
func (c *cAB) JyList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}
	md := dao.ABJiuy.Ctx(r.Context()).OrderDesc("JY_ID")

	usession := g.Cfg().MustGet(ctx, "50cms.usession").String()
	sessionguid, _ := r.Session.Get(usession)
	user, err := dao.Admin.Ctx(r.Context()).Where("uguid", sessionguid).One()
	//strings := []string{}
	//items := []string{}

	// title := r.Get("title").String()
	// time := r.Get("time").String()
	// ctype := r.Get("ctype").String()
	// ClassId := r.Get("ClassId").String()

	// if ctype != "IsHistory" { //正常的
	// 	md = md.WhereNot("IsHistory", "1").WhereOrNull("IsHistory")
	// }

	// if title != "" {
	// 	md = md.Where("Title like ?", "%"+title+"%")
	// }
	// if ctype != "" {
	// 	md = md.Where(ctype, "1")
	// }
	// if time != "" {
	// 	_time := strings.Split(time, " - ")
	// 	md = md.Where("Time > ?", _time[0])
	// 	md = md.Where("Time < ?", _time[1])
	// }
	// if ClassId != "" {
	// 	md = md.Where("ClassId", ClassId)

	// }

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页
		///	pagenews, _ := md.InnerJoinOnFields("User", "UID", "=", "UID").Fields("Task_Teacher.*,User.*").Page(temppage, pageSize).All()

		data, _ := md.LeftJoinOnFields("User", "UsjID", "=", "UID").LeftJoinOnFields("Admin", "shren", "=", "uguid").Fields("AB_jiuy.*,User.UTrueName as sname,User.UTel as sTel,User.UID as sID,Admin.uname as shren_name").Page(temppage, pageSize).All()

		mydata := data.List()
		var datamap []map[string]interface{}

		for _, v := range mydata {
			if v["jjimgs"] != nil && v["jjimgs"] != "" {
				imgStr := strings.Split(v["jjimgs"].(string), ",")[0]
				v["arrjjimgs"] = imgStr
				v["arrjjimgs_type"] = utils.GetFileType(imgStr)
			}
			if v["idimgs"] != nil && v["idimgs"] != "" {
				imgStr := strings.Split(v["idimgs"].(string), ",")[0]
				v["arridimgs"] = imgStr
				v["arridimgs_type"] = utils.GetFileType(imgStr)
			}
			if v["imgs"] != nil && v["imgs"] != "" {
				imgStr := strings.Split(v["imgs"].(string), ",")[0]
				v["arrimgs"] = imgStr
				v["arrimgs_type"] = utils.GetFileType(imgStr)
			}
			if v["BegainImg"] != nil && v["BegainImg"] != "" {
				imgStr := strings.Split(v["BegainImg"].(string), ",")[0]
				v["arrBegainImgs"] = imgStr
				v["arrBegainImgs_type"] = utils.GetFileType(imgStr)
			}
			if v["EndImg"] != nil && v["EndImg"] != "" {
				imgStr := strings.Split(v["EndImg"].(string), ",")[0]
				v["arrendinImgs"] = imgStr
				v["arrendinImgs_type"] = utils.GetFileType(imgStr)
			}
			if v["didimgs"] != nil && v["didimgs"] != "" {
				imgStr := strings.Split(v["didimgs"].(string), ",")[0]
				v["arrdidimgs"] = imgStr
				v["arrdidimgs_type"] = utils.GetFileType(imgStr)
			}
			if v["caridimgs"] != nil && v["caridimgs"] != "" {
				imgStr := strings.Split(v["caridimgs"].(string), ",")[0]
				v["arrcaridimgs"] = imgStr
				v["arrcaridimgs_type"] = utils.GetFileType(imgStr)
			}
			datamap = append(datamap, v)

		}

		r.Response.WriteTpl("ab/jylist.html", g.Map{
			"ncount": newsscount,
			//"search":    g.Map{"title": title, "time": time, "ClassId": ClassId, "ctype": ctype},
			"data":  datamap,
			"user":  user,
			"page":  utils.WrapContent(page, 3),
			"page2": utils.PageContent(page),
		})
	}
}

// //混编一体list  原生表格渲染  用户列表  微信
func (c *cAB) UserList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}

	title := r.Get("title").String()
	ctype := r.Get("ctype").String()

	md := dao.User.Ctx(r.Context()).Where("uwxapp", "abjy")
	if ctype != "ishistory" { //正常的
		md = md.Wheref("(ishistory!=? or ishistory is null)", 1)

		if ctype != "" {
			if ctype == "istel" { //注册的
				md = md.WhereNotNull("Utel")
			} else {
				md = md.Where(ctype, "1")
			}

		}
	} else { //删除的
		md = md.Where("ishistory", "0")
	}

	if title != "" {
		// 在 gdb 中没有 Or 方法，需要使用 WhereOr 方法来实现 OR 条件查询
		md = md.Where("UTrueName like ?", "%"+title+"%").WhereOr("UTel like ?", "%"+title+"%")
	}

	md = md.OrderDesc("UID")

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页
		///	pagenews, _ := md.InnerJoinOnFields("User", "UID", "=", "UID").Fields("Task_Teacher.*,User.*").Page(temppage, pageSize).All()
		pageuser, _ := md.Page(temppage, pageSize).All()
		r.Response.WriteTpl("ab/wxuser.html", g.Map{
			"ncount": newsscount,
			//"search":    g.Map{"title": title, "time": time, "ClassId": ClassId, "ctype": ctype},
			"search": g.Map{"title": title, "ctype": ctype},
			"data":   pageuser,
			"page":   utils.WrapContent(page, 3),
			"page2":  utils.PageContent(page),
		})
	}
}

// //混编一体list  原生表格渲染
func (c *cAB) TeacherList(r *ghttp.Request) {
	temppage := r.Get("page").Int()
	if temppage == 0 {
		temppage = 1
	}
	pageSize := 10
	temppageSize := r.Get("limit").Int()
	if temppageSize != 0 {
		pageSize = temppageSize
	}
	md := dao.TaskTeacher.Ctx(r.Context())

	newsscount, err := md.Count()
	if err == nil {
		//page := r.GetPage(100, 10) //总100条，每10条一页
		page := r.GetPage(newsscount, pageSize) //总100条，每10条一页
		pagenews, _ := md.LeftJoinOnFields("User", "UID", "=", "UID").Fields("Task_Teacher.*,User.*").Page(temppage, pageSize).All()

		r.Response.WriteTpl("ab/teacher.html", g.Map{
			"ncount": newsscount,
			//"search":    g.Map{"title": title, "time": time, "ClassId": ClassId, "ctype": ctype},
			"newss": pagenews,
			"page":  utils.WrapContent(page, 3),
			"page2": utils.PageContent(page),
		})
	}
}

// 送车订单审核
func (c *cAB) JyEdit(r *ghttp.Request) {
	if r.Request.Method == "GET" { //加载使用赋值用
		guid := r.Get("guid").String()
		mode := r.Get("mode").String() // 获取模式参数：edit 或 view

		if guid == "" {
			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "订单ID不能为空",
				"data": nil,
			})
			return
		}

		// 获取订单详情
		data, err := dao.ABJiuy.Ctx(r.Context()).
			LeftJoinOnFields("User", "UID", "=", "UID").
			Fields("AB_jiuy.*,User.UTrueName as customer_name,User.UTel as customer_tel").
			Where("AB_jiuy.Guid", guid).
			One()

		if err != nil {
			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "获取订单信息失败",
				"data": nil,
			})
			return
		}

		// 处理图片数据

		// 创建一个新的 map 来存储处理后的数据
		datamap := make(map[string]interface{})
		for k, v := range data {
			datamap[k] = v
		}

		// 处理图片数据
		if data["imgs"] != nil {
			imgStr := gconv.String(data["imgs"])
			if imgStr != "" {
				imgList := strings.Split(imgStr, ",")
				datamap["arrimgs"] = imgList
				// 为每个文件添加类型信息
				var imgTypes []string
				for _, img := range imgList {
					imgTypes = append(imgTypes, utils.GetFileType(img))
				}
				datamap["arrimgs_types"] = imgTypes
			}
		}
		if data["BegainImg"] != nil {
			imgStr := gconv.String(data["BegainImg"])
			if imgStr != "" {
				imgList := strings.Split(imgStr, ",")
				datamap["arrBegainImgs"] = imgList
				// 为每个文件添加类型信息
				var imgTypes []string
				for _, img := range imgList {
					imgTypes = append(imgTypes, utils.GetFileType(img))
				}
				datamap["arrBegainImgs_types"] = imgTypes
			}
		}
		if data["EndImg"] != nil {
			imgStr := gconv.String(data["EndImg"])
			if imgStr != "" {
				imgList := strings.Split(imgStr, ",")
				datamap["arrendinImgs"] = imgList
				// 为每个文件添加类型信息
				var imgTypes []string
				for _, img := range imgList {
					imgTypes = append(imgTypes, utils.GetFileType(img))
				}
				datamap["arrendinImgs_types"] = imgTypes
			}
		}
		if data["jjimgs"] != nil {
			imgStr := gconv.String(data["jjimgs"])
			if imgStr != "" {
				imgList := strings.Split(imgStr, ",")
				datamap["arrjjimgs"] = imgList
				// 为每个文件添加类型信息
				var imgTypes []string
				for _, img := range imgList {
					imgTypes = append(imgTypes, utils.GetFileType(img))
				}
				datamap["arrjjimgs_types"] = imgTypes
			}
		}
		if data["idimgs"] != nil {
			imgStr := gconv.String(data["idimgs"])
			if imgStr != "" {
				imgList := strings.Split(imgStr, ",")
				datamap["arridimgs"] = imgList
				// 为每个文件添加类型信息
				var imgTypes []string
				for _, img := range imgList {
					imgTypes = append(imgTypes, utils.GetFileType(img))
				}
				datamap["arridimgs_types"] = imgTypes
			}
		}

		if data["didimgs"] != nil {
			imgStr := gconv.String(data["didimgs"])
			if imgStr != "" {
				imgList := strings.Split(imgStr, ",")
				datamap["arrdidimgs"] = imgList
				// 为每个文件添加类型信息
				var imgTypes []string
				for _, img := range imgList {
					imgTypes = append(imgTypes, utils.GetFileType(img))
				}
				datamap["arrdidimgs_types"] = imgTypes
			}
		}
		if data["caridimgs"] != nil {
			imgStr := gconv.String(data["caridimgs"])
			if imgStr != "" {
				imgList := strings.Split(imgStr, ",")
				datamap["arrcaridimgs"] = imgList
				// 为每个文件添加类型信息
				var imgTypes []string
				for _, img := range imgList {
					imgTypes = append(imgTypes, utils.GetFileType(img))
				}
				datamap["arrcaridimgs_types"] = imgTypes
			}
		}

		// 获取短信发送记录
		smsLogService := &utils.SmsLogService{}
		smsRecords, err := smsLogService.GetSmsLogsByOrder(r.Context(), guid)
		if err != nil {
			// 如果获取短信记录失败，设置为空数组
			smsRecords = []map[string]interface{}{}
		}

		r.Response.WriteTpl("ab/jyedit.html", g.Map{
			"data":       datamap,
			"mode":       mode,       // 传递模式参数到模板
			"smsRecords": smsRecords, // 传递短信记录数据
		})
	} else { //提交表单用
		guid := r.GetForm("guid").String()
		auditResult := r.GetForm("audit_result").String()
		shmark := r.GetForm("shmark").String()

		if guid == "" {
			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "订单ID不能为空",
				"data": nil,
			})
			return
		}

		// 更新订单状态
		md := dao.ABJiuy.Ctx(r.Context())
		sessionguid, _ := r.Session.Get(usession)

		loc, _ := time.LoadLocation("Asia/Shanghai")
		mytime := time.Now().In(loc)

		updateData := g.Map{
			"sh1":    auditResult, // 使用sh1字段，1表示通过，0表示拒绝
			"shtime": mytime,
			"shmark": shmark,
			"shren":  sessionguid,
		}

		result, err := md.Where("Guid", guid).Update(updateData)
		if err != nil {
			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "审核操作失败",
				"data": nil,
			})
			return
		}

		// 发送审核结果通知
		// orderData, err := md.Where("Guid", guid).One()
		// if err == nil && orderData != nil {
		//     templateID := "JrqIiTeNCd2ih4wuuzU3e0RB4uONwcStjxIqh1XupTY"
		//     notifyData := utils.OrderTemplateData{
		//         OrderNo:      orderData["Guid"].String(),
		//         OrderStatus:  auditResult == "pass" ? "审核通过" : "审核拒绝",
		//         CustomerName: orderData["jcren"].String(),
		//         NotifyTime:   gtime.Now().Format("2006-01-02 15:04"),
		//     }

		//     // 发送通知
		//     utils.SendOrderNotify(orderData["UID"].String(), templateID, notifyData, "pages/order/detail/"+guid, r.Context())
		// }

		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "审核操作成功",
			"data": result,
		})
	}
}

// JyEditSmsRecords 获取订单的短信发送记录
func (c *cAB) JyEditSmsRecords(r *ghttp.Request) {
	guid := r.Get("guid").String()

	if guid == "" {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "订单ID不能为空",
			"data": nil,
		})
		return
	}

	// 获取短信发送记录
	smsLogService := &utils.SmsLogService{}
	smsRecords, err := smsLogService.GetSmsLogsByOrder(r.Context(), guid)
	if err != nil {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "获取短信记录失败",
			"data": nil,
		})
		return
	}

	r.Response.WriteJson(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": smsRecords,
	})
}

// JyEditResendSms 重发短信
func (c *cAB) JyEditResendSms(r *ghttp.Request) {
	recordId := r.GetForm("record_id").String()

	if recordId == "" {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "记录ID不能为空",
			"data": nil,
		})
		return
	}

	// 获取短信记录信息
	// 这里需要根据record_id获取具体的短信记录，然后重新发送
	// 由于现有的SmsLogService没有根据ID获取记录的方法，我们需要扩展它

	r.Response.WriteJson(g.Map{
		"code": 200,
		"msg":  "短信重发成功",
		"data": nil,
	})
}

// //  列表页面修改
func (c *cAB) JCharge(r *ghttp.Request) {
	guID := r.Get("guid").String()
	if guID == "" {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.ABJiuy.Ctx(r.Context()).Where("Guid", guID).One()
	ctype := r.GetForm("ctype").String()

	fmt.Println("ctype:", ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.ABJiuy.Ctx(r.Context()).Data(gdb.Map{"isdel": "1"}).Where("Guid", guID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	} else if ctype == "sh1" {
		// 获取审核状态值
		title := r.GetForm("title").String()
		shtime := time.Now().Format("2006-01-02 15:04")

		// 构建更新数据
		updateData := gdb.Map{
			"sh1":    title,
			"shmark": "",
			"shtime": shtime,
		}

		result, err := dao.ABJiuy.Ctx(r.Context()).Where("Guid", guID).Update(updateData)

		if err != nil {
			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "审核操作失败: " + err.Error(),
				"data": "",
			})
			return
		}

		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "审核操作成功",
			"data": result,
		})
		return
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.Task.Ctx(r.Context()).Where("Guid", guID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// //  列表页面修改
func (c *cAB) UCharge(r *ghttp.Request) {
	ID := r.Get("ID").Int()
	if ID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.User.Ctx(r.Context()).Where("UID", ID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.User.Ctx(r.Context()).Where("UID", ID).Delete()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	}
	if ctype == "send" {
		// 调用封装好的发送模板消息函数
		//templateID := r.Get("template_id").String()

		templateID := "JrqIiTeNCd2ih4wuuzU3e0RB4uONwcStjxIqh1XupTY"

		// 构建订单待处理通知数据
		orderData := utils.OrderTemplateData{
			OrderNo:      "ORD" + time.Now().Format("20060102150405"),
			OrderStatus:  "待处理",
			CustomerName: r.Get("customer_name").String(), // 如果页面传入了客户名称，则使用
			NotifyTime:   time.Now().Format("2006-01-02 15:04"),
		}

		// 如果未传入客户名称，则尝试从用户信息中获取
		if orderData.CustomerName == "" {
			userData, err := dao.User.Ctx(r.Context()).Where("UID", ID).One()
			if err == nil && userData != nil {
				orderData.CustomerName = userData["UTrueName"].String()
				if orderData.CustomerName == "" {
					orderData.CustomerName = "用户" + strconv.Itoa(ID)
				}
			} else {
				orderData.CustomerName = "用户" + strconv.Itoa(ID)
			}
		}
		// 设置跳转页面
		page := "pages/order/detail?id=" + orderData.OrderNo

		// 发送订单待处理通知
		err := utils.SendOrderNotify(ID, templateID, orderData, page, r.Context())

		// 发送订单待处理通知

		// // 构建自定义数据（如果需要）
		// // 这里可以根据需要自定义模板数据
		// // 如果传入nil，将使用默认模板数据
		// customData := make(map[string]map[string]string)
		// customData["character_string1"] = map[string]string{
		// 	"value": "用户消息通知",
		// }
		// customData["phrase2"] = map[string]string{
		// 	"value": "转台",
		// }

		// customData["thing3"] = map[string]string{
		// 	"value": "您有一条新的用户信息更新，请查看",
		// }
		// customData["time4"] = map[string]string{
		// 	"value": time.Now().Format("2006-01-02 15:04"),
		// }

		if err != nil {
			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "发送消息失败: " + err.Error(),
			})
			return
		}

		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "发送成功",
		})
		return
	} else {
		var Upower = ""
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
			Upower = "司机"
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		if ctype == "issj" {
			mform = r.GetFormMap(map[string]interface{}{ctype: typevalue, "Upower": Upower})
		}

		result, err := dao.User.Ctx(r.Context()).Where("UID", ID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// //  列表页面修改
func (c *cAB) TCharge(r *ghttp.Request) {
	ID := r.Get("ID").Int()
	if ID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.TaskTeacher.Ctx(r.Context()).Where("ID", ID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.TaskTeacher.Ctx(r.Context()).Data(gdb.Map{"ishistory": "1"}).Where("ID", ID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.TaskTeacher.Ctx(r.Context()).Where("ID", ID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// //  列表页面修改
func (c *cAB) SCharge(r *ghttp.Request) {
	ID := r.Get("ID").Int()
	if ID == 0 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "没有该信息",
			"data": "",
		})
	}
	md, _ := dao.TaskStudent.Ctx(r.Context()).Where("ID", ID).One()
	ctype := r.GetForm("type").String()

	fmt.Println(ctype)
	if ctype == "del" {

		// mform := r.GetFormMap(map[string]interface{}{"IsHistory": "1"})
		result, err := dao.TaskStudent.Ctx(r.Context()).Data(gdb.Map{"ishistory": "1"}).Where("ID", ID).Update()
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "删除成功",
				"data": result,
			})
		} else {

			r.Response.WriteJson(g.Map{
				"code": -1,
				"msg":  "删除失败",
				"err":  result,
			})
		}
	} else {
		var typevalue = 0
		if md[ctype].Int() == 0 {
			typevalue = 1
		}
		mform := r.GetFormMap(map[string]interface{}{ctype: typevalue})
		result, err := dao.TaskStudent.Ctx(r.Context()).Where("ID", ID).Update(mform)

		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

}

// 混编一体Classlist
func (c *cAB) Class_list(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	md := dao.InfoClass.Ctx(r.Context())
	classs, err := md.All()
	var a int64 = 0
	var chridnum []map[string]interface{}
	zhdatra := classs.List()
	for _, v := range zhdatra {
		v["id"] = v["CID"].(int64)
		v["title"] = v["CNmae"]
		v["name"] = v["CNmae"]
		if v["CpareID"] == nil {
			v["pid"] = a
		} else {
			v["pid"] = int64(v["CpareID"].(int64))
		}
		chridnum = append(chridnum, v)
	}
	GetRuleTreeArrayLayui := utils.GetRuleTreeArrayLayui(chridnum, 0)
	treeData, err := json.Marshal(GetRuleTreeArrayLayui)

	if err == nil {
		r.Response.WriteTpl("info/class_list.html", g.Map{
			"header":   "This is header",
			"info":     classs,
			"treeData": string(treeData),
		})
	}

}

//混编一体编辑Class修改

// 混编一体Classlist
func (c *cAB) Class_edit(r *ghttp.Request) {

	md := dao.InfoClass.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("ID").String()

		nclass, err := md.Fields("CNmae,CpareID,CID").All()
		var a int64 = 0

		zhdatra := nclass.List()
		var chridnum []map[string]interface{}
		for _, v := range zhdatra {
			v["id"] = v["CID"].(int64) //int 要转成 int64
			v["title"] = v["CNmae"]
			num, _ := strconv.ParseInt(id, 10, 64)
			if v["CID"] == num { //当前id 不可选
				v["disabled"] = true
			}
			if v["CpareID"] == nil {
				v["pid"] = a
			} else {
				v["pid"] = int64(v["CpareID"].(int64))
			}
			chridnum = append(chridnum, v)
		}
		GetRuleTreeArrayLayui := utils.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		fmt.Println("jsonData==>", string(jsonData))

		if id == "0" || id == "" { //新增
			if err == nil {
				r.Response.WriteTpl("info/class_edit.html", g.Map{
					"header":   "This is header",
					"data":     g.Map{"CID": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			data, err := md.Where("CID", id).One()
			if err == nil {
				r.Response.WriteTpl("info/class_edit.html", g.Map{
					"header":   "This is header",
					"data":     data,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("ID").String()
		pid := r.GetForm("demoTree_select_nodeId")
		if id == "0" || id == "" {
			mform := r.GetFormMap(map[string]interface{}{"CNmae": "667", "CpareID": pid})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"CNmae": "", "CpareID": pid})
			result, err := md.Where("CID", id).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// 混编一体列表
func (c *cAB) List(r *ghttp.Request) {
	goods, _ := dao.Info.Ctx(r.Context()).All()

	r.Response.WriteTpl("ab/list.html", g.Map{
		"header": "This is header",
		"data":   goods,
	})

}

// 指派司机
func (c *cAB) SjEdit(r *ghttp.Request) {
	md := dao.ABJiuy.Ctx(r.Context())
	mduser := dao.User.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		guid := r.Get("guid").String()
		nclass, err := mduser.Fields("UID,UTrueName,UTel").Where("uwxapp", "abjy").Where("issj", 1).All()
		data, err := md.Where("Guid", guid).One()
		OldUsjID := data["UsjID"].String()

		var a int32 = 0

		zhdatra := nclass.List()
		var chridnum []map[string]interface{}
		for _, v := range zhdatra {
			v["id"] = v["UID"].(int32) //int 要转成 int64
			if v["UTrueName"] == nil || v["UTrueName"] == "" {
				v["UTrueName"] = "名字未知"
			}
			if v["UTel"] == nil || v["UTel"] == "" {
				v["UTel"] = "电话未知"
			}

			v["title"] = v["UTrueName"].(string) + v["UTel"].(string)

			if v["UID"] == OldUsjID { //当前id 不可选
				v["disabled"] = true
			}

			v["pid"] = a

			chridnum = append(chridnum, v)
		}
		GetRuleTreeArrayLayui := utils.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "请选择"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		fmt.Println("jsonData==>", string(jsonData))

		if guid == "0" || guid == "" { //新增
			if err == nil {
				r.Response.WriteTpl("ab/jysjedit.html", g.Map{
					"header":   "This is header",
					"data":     g.Map{"Guid": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改

			if err == nil {
				r.Response.WriteTpl("ab/jysjedit.html", g.Map{
					"header":   "This is header",
					"data":     data,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		guid := r.GetForm("guid").String()
		ujsid := r.GetForm("demoTree_select_nodeId").String()
		if ujsid == "" {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "失败",
				"data": "司机ID 为空",
			})
		} else {
			mform := r.GetFormMap(map[string]interface{}{"UsjID": ujsid})
			result, err := md.Where("Guid", guid).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// 混编一体Classlist
func (c *cAB) Store_list(r *ghttp.Request) {
	data, err := dao.ABStore.Ctx(r.Context()).All()
	if err == nil {
		r.Response.WriteTpl("ab/store_list.html", g.Map{
			"header": "This is header",
			"data":   data,
		})
	}
}

//混编一体编辑Class修改

// 混编一体Classlist
func (c *cAB) Store_edit(r *ghttp.Request) {
	md := dao.ABStore.Ctx(r.Context())
	// news, err := md.One()
	if r.Request.Method == "GET" { //加载使用赋值用
		id := r.Get("SID").String()
		if id == "0" || id == "" { //新增
			r.Response.WriteTpl("ab/store_edit.html", g.Map{
				"newss": g.Map{"SID": 0},
			})
		} else { //修改
			data, err := md.Where("SID", id).One()
			if err == nil {
				pics := strings.Split(data["Simg"].String(), ",")

				Content := strings.ReplaceAll(data["Sintroduce"].String(), "\"", "\\\"")
				Content = strings.ReplaceAll(Content, "\n", "")
				Content = strings.ReplaceAll(Content, "\r", "")

				r.Response.WriteTpl("ab/store_edit.html", g.Map{
					"header":  "This is header",
					"newss":   data,
					"pics":    pics,
					"Content": Content,
				})
			}
		}

	} else { //提交表单用
		id := r.GetForm("SID").String()

		site_pic__upimgs := r.GetForm("site_pic__upimgs")

		Address := r.GetForm("Address")

		if id == "0" || id == "" {

			mform := r.GetFormMap(map[string]interface{}{"Sname": "", "areaID": "", "Stel": "", "Stime": "", "Simg": site_pic__upimgs, "Sintroduce": "", "where": "", "Saddress": Address, "Slatitude": "", "Slongitude": ""})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"Sname": "", "areaID": "", "Stel": "", "Stime": "", "Simg": site_pic__upimgs, "Sintroduce": "", "where": "", "Saddress": Address, "Slatitude": "", "Slongitude": ""})
			result, err := md.Where("SID", id).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// SendDriverSms 发送司机任务通知短信
func (c *cAB) SendDriverSms(r *ghttp.Request) {
	guid := r.GetForm("guid").String()
	driverID := r.GetForm("driver_id").String()

	if guid == "" || driverID == "" {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "订单ID和司机ID不能为空",
		})
		return
	}

	// 使用统一的短信发送服务（司机任务通知）
	code, erro, resstring, err := SendDeliverySms(r.Context(), SmsTypeDriverTask, guid)

	if err != nil {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "短信发送失败: " + err.Error(),
		})
		return
	}

	if code == 200 {
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "司机通知短信发送成功",
			"data": resstring,
		})
	} else {
		errorMsg := ""
		if erro != nil {
			errorMsg = erro.Error()
		}
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "短信发送失败: " + errorMsg,
			"data": resstring,
		})
	}
}

// SendSms 统一短信发送接口
// 参数：type - 短信类型，guid - 订单GUID
func (c *cAB) SendSms(r *ghttp.Request) {
	smsType := r.GetForm("type").Int()
	guid := r.GetForm("guid").String()

	if guid == "" {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "订单ID不能为空",
		})
		return
	}

	if smsType <= 0 || smsType > 5 {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "短信类型无效",
		})
		return
	}

	// 使用统一的短信发送服务
	code, erro, resstring, err := SendDeliverySms(r.Context(), smsType, guid)

	if err != nil {
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "短信发送失败: " + err.Error(),
		})
		return
	}

	if code == 200 {
		r.Response.WriteJson(g.Map{
			"code": 200,
			"msg":  "短信发送成功",
			"data": resstring,
		})
	} else {
		errorMsg := ""
		if erro != nil {
			errorMsg = erro.Error()
		}
		r.Response.WriteJson(g.Map{
			"code": -1,
			"msg":  "短信发送失败: " + errorMsg,
			"data": resstring,
		})
	}
}

// ========== 送车业务短信 类型常量 ==========
const (
	SmsTypeNewOrderAuditor      = 2 // 新订单通知审核员
	SmsTypeModifiedOrderAuditor = 3 // 修改订单通知审核员
	SmsTypeAuditResultCustomer  = 4 // 审核结果通知客户
	SmsTypeDriverTask           = 5 // 司机任务通知
	SmsTypeOrderUpdated         = 6 // 送车订单已修改通知审核员
)

// pickString 从 map 中按候选键顺序取出第一个非空字符串
func pickString(m map[string]interface{}, keys ...string) string {
	for _, k := range keys {
		if v, ok := m[k]; ok {
			s := gconv.String(v)
			if strings.TrimSpace(s) != "" {
				return s
			}
		}
	}
	return ""
}

// getAuditorPhones 从配置获取审核员手机号
func getAuditorPhones(ctx context.Context) []string {
	phones := g.Cfg().MustGet(ctx, "audit.phoneNumbers").Strings()
	if len(phones) == 0 {
		if alt, err := g.Cfg().Get(ctx, "audit.phones"); err == nil && !alt.IsEmpty() {
			phones = alt.Strings()
		}
	}
	for i, p := range phones {
		phones[i] = strings.TrimSpace(p)
	}
	return phones
}

// getRemarkByType 根据短信类型获取备注
func getRemarkByType(smsType int) string {
	switch smsType {

	case SmsTypeNewOrderAuditor:
		return "新订单通知审核员"
	case SmsTypeModifiedOrderAuditor:
		return "修改订单通知审核员"
	case SmsTypeAuditResultCustomer:
		return "审核结果通知客户"
	case SmsTypeDriverTask:
		return "司机任务通知"
	case SmsTypeOrderUpdated:
		return "送车订单已修改通知审核员"
	default:
		return "未知类型"
	}
}

// logSms 记录短信发送日志
func logSms(ctx context.Context, smsType int, guid string, orderMap map[string]interface{}, phone, content, operator string, erro error) {
	smsLogService := &utils.SmsLogService{}

	smsLogInput := utils.SmsLogInput{
		Guid:     guid,
		JyId:     gconv.String(orderMap["UID"]) + gconv.String(orderMap["JY_ID"]),
		SmsType:  smsType,
		Phone:    phone,
		Content:  content,
		Operator: operator,
		Remark:   getRemarkByType(smsType),
	}

	if erro == nil {
		smsLogService.LogSmsSuccess(ctx, smsLogInput)
	} else {
		smsLogService.LogSmsFailure(ctx, smsLogInput, erro.Error())
	}
}

// SendDeliverySms 业务短信发送（基于阿里云短信）
func SendDeliverySms(ctx context.Context, smsType int, guid string) (int, error, string, error) {
	// 查询订单信息
	orderInfo, err := dao.ABJiuy.Ctx(ctx).Where("Guid", guid).One()
	if err != nil {
		return 0, err, "", fmt.Errorf("查询订单信息失败: %v", err)
	}
	orderMap := orderInfo.Map()

	var phone string
	var operator string
	var templateCode string
	var templateParam map[string]string
	var logContent string

	// 创建短信客户端
	aliyunClient, clientErr := utils.CreateAliyunSmsClient()
	if clientErr != nil {
		return 0, clientErr, "", clientErr
	}

	switch smsType {
	case SmsTypeNewOrderAuditor:
		phones := getAuditorPhones(ctx)
		if len(phones) == 0 {
			return 0, fmt.Errorf("未找到审核员手机号"), "", fmt.Errorf("未找到审核员手机号")
		}
		templateCode = "5714"
		templateParam = map[string]string{"nu": pickString(orderMap, "UID", "JY_ID")}
		if b, _ := json.Marshal(templateParam); len(b) > 0 {
			logContent = string(b)
		}
		var lastError error
		for _, ph := range phones {
			ph = strings.TrimSpace(ph)
			code, erro, _ := utils.AliyunSendSms(ctx, aliyunClient, ph, utils.AliyunSignName, templateCode, templateParam)
			if code != 200 {
				lastError = erro
			}
			logSms(ctx, smsType, guid, orderMap, ph, logContent, operator, erro)
		}
		if lastError != nil {
			return 500, lastError, "", lastError
		}
		return 200, nil, "发送成功", nil

	case SmsTypeModifiedOrderAuditor:
		phones := getAuditorPhones(ctx)
		if len(phones) == 0 {
			return 0, fmt.Errorf("未找到审核员手机号"), "", fmt.Errorf("未找到审核员手机号")
		}
		templateCode = "5714"
		templateParam = map[string]string{"nu": pickString(orderMap, "UID", "JY_ID")}
		if b, _ := json.Marshal(templateParam); len(b) > 0 {
			logContent = string(b)
		}
		var lastError error
		for _, ph := range phones {
			ph = strings.TrimSpace(ph)
			code, erro, _ := utils.AliyunSendSms(ctx, aliyunClient, ph, utils.AliyunSignName, templateCode, templateParam)
			if code != 200 {
				lastError = erro
			}
			logSms(ctx, smsType, guid, orderMap, ph, logContent, operator, erro)
		}
		if lastError != nil {
			return 500, lastError, "", lastError
		}
		return 200, nil, "发送成功", nil

	case SmsTypeOrderUpdated:
		phones := getAuditorPhones(ctx)
		if len(phones) == 0 {
			return 0, fmt.Errorf("未找到审核员手机号"), "", fmt.Errorf("未找到审核员手机号")
		}
		templateCode = "5721"
		templateParam = map[string]string{"nu": pickString(orderMap, "UID", "JY_ID")}
		if b, _ := json.Marshal(templateParam); len(b) > 0 {
			logContent = string(b)
		}
		var lastError error
		for _, ph := range phones {
			ph = strings.TrimSpace(ph)
			code, erro, _ := utils.AliyunSendSms(ctx, aliyunClient, ph, utils.AliyunSignName, templateCode, templateParam)
			if code != 200 {
				lastError = erro
			}
			logSms(ctx, smsType, guid, orderMap, ph, logContent, operator, erro)
		}
		if lastError != nil {
			return 500, lastError, "", lastError
		}
		return 200, nil, "发送成功", nil

	case SmsTypeAuditResultCustomer:
		phone = gconv.String(orderMap["jctel"])
		if strings.TrimSpace(phone) == "" {
			return 0, fmt.Errorf("客户手机号为空"), "", fmt.Errorf("客户手机号为空")
		}
		templateCode = map[bool]string{true: "5715", false: "5716"}[gconv.String(orderMap["sh1"]) == "1"]
		templateParam = map[string]string{"nu": pickString(orderMap, "UID", "JY_ID")}
		if b, _ := json.Marshal(templateParam); len(b) > 0 {
			logContent = string(b)
		}
		code, erro, res := utils.AliyunSendSms(ctx, aliyunClient, phone, utils.AliyunSignName, templateCode, templateParam)
		logSms(ctx, smsType, guid, orderMap, phone, logContent, "auditor", erro)
		return code, erro, res, nil

	case SmsTypeDriverTask:
		usjID := gconv.String(orderMap["UsjID"])
		if strings.TrimSpace(usjID) == "" {
			return 0, fmt.Errorf("司机ID为空"), "", fmt.Errorf("司机ID为空")
		}
		userRec, err := dao.User.Ctx(ctx).Where("UID", usjID).One()
		if err != nil {
			return 0, err, "", fmt.Errorf("查询司机信息失败: %v", err)
		}
		phone = gconv.String(userRec["UTel"])
		if strings.TrimSpace(phone) == "" {
			return 0, fmt.Errorf("司机手机号为空"), "", fmt.Errorf("司机手机号为空")
		}
		templateCode = "5717"
		templateParam = map[string]string{"nu": pickString(orderMap, "UID", "JY_ID")}
		if b, _ := json.Marshal(templateParam); len(b) > 0 {
			logContent = string(b)
		}
		code, erro, res := utils.AliyunSendSms(ctx, aliyunClient, phone, utils.AliyunSignName, templateCode, templateParam)
		logSms(ctx, smsType, guid, orderMap, phone, logContent, "auditor", erro)
		return code, erro, res, nil

	default:
		return 0, fmt.Errorf("不支持的短信类型: %d", smsType), "", fmt.Errorf("不支持的短信类型: %d", smsType)
	}
}
