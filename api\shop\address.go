package shop

import (
	"50go/internal/dao"
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

// 规范路由
func (c *ShopController) Address(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	page, psize := GetPageData(r)
	md := dao.SUaddress.Ctx(r.Context())
	ncount, err := md.Count()
	order, _ := md.Page(page, psize).All()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"ncount": ncount,
			"page":   page,
			"psize":  psize,
			"data":   order,
			"code":   1,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *ShopController) GetAddress(ctx context.Context, req *GetReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)
	id := r.GetQuery("aid") //获取name
	md, _ := dao.SUaddress.Ctx(r.Context()).Where("UAID", id).One()

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"data": md,
		})
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}

func (c *ShopController) AddressEdit(ctx context.Context, req *PostReq) (res *GetListRes, err error) {
	r := g.RequestFromCtx(ctx)

	formdata := r.GetForm("paramJson").String()
	var result map[string]interface{}
	// 将JSON字符串转换为map
	err = json.Unmarshal([]byte(formdata), &result)
	fmt.Println(formdata)

	id := result["id"].(string)
	md := dao.SUaddress.Ctx(r.Context())

	// mid := r.GetForm("MID").String()
	// Power := r.GetForm("Power")
	// Pass := r.GetForm("Pass")

	name := result["name"]
	UAPhone := result["iphone"]
	title := result["iphone"]

	// var addname map[string]interface{}
	// 将JSON字符串转换为map
	// err = json.Unmarshal([]byte((result["addname"].(string))), &addname)

	// province := addname["province"]
	// city := addname["city"]
	// district := addname["district"]

	if id == "0" || id == "" {

		mform := map[string]interface{}{"UPeopleName": name, "UAPhone": UAPhone, "UAddress": title}
		result, err := md.Data(mform).Insert()
		fmt.Println("tt", err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}

	} else {
		mform := map[string]interface{}{"UPeopleName": name, "UAPhone": UAPhone, "UAddress": title}
		result, err := md.Where("UAID", id).Update(mform)
		fmt.Println(id, err)
		if err == nil {
			r.Response.WriteJson(g.Map{
				"code": 200,
				"msg":  "成功",
				"data": result,
			})
		}
	}

	//err := g.DB().Table(df_goods_sku.Table).Where(filterMap).Order(orderingField).Page(page, page_size).Structs(&goods)
	return
	//return res, gerror.NewCode(gcode.CodeNotImplemented)  //两种返回方式
}
func (c *ShopController) Addresslist(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	name := r.GetQuery("name", "没参数的默认返回值!") //获取name

	listgood := g.Map{
		"GPrice":      3213,
		"GPrice2":     12,
		"G_Introduce": "颜色分类：透明-抽象猫咪生产企业：深圳款式：保护壳",
		"Name":        "抽象猫咪手机壳",
		"gid":         1,
		"numb":        0,
		"pic":         "/Up/2",
	}

	listjson := g.Map{
		"typeName":    "a4",
		"GetImgs":     "img",
		"menuContent": listgood,
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"businessend":   "21:00",
			"logourl":       "/images/logo.png",
			"name":          "小熊工厂洗--演示系统 请勿下单",
			"minamount":     "99",
			"businessstart": "11:03",
			"address":       "2东路269号",
			"notice":        "洗衣洗鞋新时尚",
			"telephone":     "***********",
			"tel":           "0871-66227317",
			"expressfee":    "1",
			"ad":            name,
			"seller":        listjson,
		})
	return
}
