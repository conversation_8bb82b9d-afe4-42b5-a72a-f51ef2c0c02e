// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminRole is the golang structure of table AdminRole for DAO operations like Where/Data.
type AdminRole struct {
	g.Meta      `orm:"table:AdminRole, do:true"`
	Id          interface{} //
	Aid         interface{} //
	Code        interface{} //
	Name        interface{} //
	Islock      interface{} //
	GroupId     interface{} //
	RedirectUrl interface{} //
	Pid         interface{} //
	LastTime    interface{} //
	TimeFlage   *gtime.Time //
	Attribute   interface{} //
	Mpower      interface{} //
}
