package controller

import (
	"50go/internal/dao"
	"50go/internal/utils"
	tree "50go/internal/utils"
	"encoding/json"
	"fmt"
	"os"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

var Admin = cAdmin{}

type cAdmin struct{}

// 混编一体Classlist
func (c *cAdmin) List(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	md := dao.Admin.Ctx(r.Context())
	classs, err := md.All()
	if err == nil {
		r.Response.WriteTpl("manage/adminlist.html", g.Map{
			"header": "This is header",
			"info":   classs,
		})
	}

}

// 混编一体Classlist
func (c *cAdmin) Edit(r *ghttp.Request) {
	md := dao.Admin.Ctx(r.Context())
	// news, err := md.One()

	if r.Request.Method == "GET" { //加载使用赋值用
		cid := r.Get("myguid").String()

		//菜单开始
		content, err := os.ReadFile("./manifest/config/menu.json")
		if err != nil {
			fmt.Println("Error when opening file: ", err)
		}
		var payload []map[string]interface{}
		err = json.Unmarshal(content, &payload)
		if err != nil {
			fmt.Println("Error during Unmarshal(): ", err)
		}
		//菜单结束

		//部门 分类开始
		rolemd := dao.AdminRole.Ctx(r.Context())
		role, err := rolemd.All()
		zhdatra := role.List()
		var a int64 = 0
		var chridnum []map[string]interface{}
		if zhdatra != nil {
			for _, v := range zhdatra {
				v["title"] = v["name"]
				v["id"] = v["id"].(int32) //int 要转成 int64
				if v["pid"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int32(v["pid"].(int)) //int 要转成 int64
				}
				chridnum = append(chridnum, v)
			}
		}

		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)
		//部门 分类end

		/// 新闻分类
		classlist, _ := dao.LClasss.Ctx(r.Context()).Fields("cnmae,cpareid,cid").All()
		myclasslist := classlist.List()
		var Classchridnum []map[string]interface{}
		if myclasslist != nil {
			for _, v := range myclasslist {

				v["id"] = v["cid"].(int32) //int 要转成 int64
				v["title"] = v["cnmae"]
				if v["cpareid"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int32(v["cpareid"].(int))
				}
				Classchridnum = append(Classchridnum, v)

			}
		}

		CLassRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(Classchridnum, 0)
		ClassjsonData, err := json.Marshal(CLassRuleTreeArrayLayui)

		if cid == "0" || cid == "" { //新增
			// items := []string{}

			// GetMenuChild := tree.GetMenuChildrenlist(payload, items)
			// jsGetMenuChild, err := json.Marshal(GetMenuChild)
			if err == nil {
				r.Response.WriteTpl("manage/adminedit.html", g.Map{
					"header": "This is header",
					"newss":  g.Map{"uguid": 0},

					"jsonData":  string(jsonData),      //部门 分类
					"payload":   string(content),       //菜单 分类
					"classMenu": string(ClassjsonData), //新闻分类
				})
			}
		} else { //修改
			news, err := md.Where("uguid", cid).One()
			//strings := []string{}
			//items := []string{}
			Upower := news["upower"].String()
			// fmt.Println("str===", str)
			// items := strings.Split(str, ",")

			// arr := make([]string, len(items))
			// for i, r := range items {
			// 	arr[i] = string(r)
			// }

			//	GetMenuChild := tree.GetMenuChildrenlist(payload, items)
			//	jsGetMenuChild, err := json.Marshal(GetMenuChild)

			UCDpower := news["ucdpower"].String()

			if err == nil {
				r.Response.WriteTpl("manage/adminedit.html", g.Map{
					"header": "This is header",
					"newss":  news,
					//	"jsGetMenuChild": string(jsGetMenuChild),
					"powers":   Upower,
					"cdpowers": UCDpower,

					"jsonData":  string(jsonData),      //部门 分类
					"payload":   string(content),       //菜单 分类
					"classMenu": string(ClassjsonData), //新闻分类
				})
			}
		}

	} else { //提交表单用
		guid := r.GetForm("myguid").String()
		rolesid := r.Get("demoTree_select_nodeId")
		mima := r.Get("myumima").String()
		myumima := utils.GetMd5String(mima)

		if guid == "0" || guid == "" { //添加
			GetGuid := utils.GetGuid()

			if mima == "" {
				r.Response.WriteJson(g.Map{
					"code": -1,
					"msg":  "新加用户密码不能为空",
					"data": "",
				})
				return
			}

			mform := r.GetFormMap(map[string]interface{}{"uguid": GetGuid, "utel": "", "utruename": "", "uname": "", "umima": myumima, "upower": "", "ucdpower": "", "utype": rolesid, "issh": 0, "ispc": 0})

			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else { //修改
			mform := r.GetFormMap(map[string]interface{}{"utel": "", "utruename": "", "uname": "", "upower": "", "ucdpower": "", "utype": rolesid, "issh": 0, "ispc": 0})

			if mima != "" {
				mform["umima"] = myumima
			}
			result, err := md.Where("uguid", guid).Update(mform)

			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// 混编一体部门（角色）列表
func (c *cAdmin) Role_list(r *ghttp.Request) {
	md := dao.AdminRole.Ctx(r.Context())
	role, err := md.All()
	zhdatra := role.List()
	var a int64 = 0
	var chridnum []map[string]interface{}
	if zhdatra != nil {
		for _, v := range zhdatra {

			v["title"] = v["name"]
			v["id"] = v["id"].(int32) //int 要转成 int32
			if v["pid"] == nil {
				v["pid"] = a
			} else {
				v["pid"] = int32(v["pid"].(int)) //int 要转成 int32
			}
			chridnum = append(chridnum, v)

		}
	}

	GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
	jsonData, err := json.Marshal(GetRuleTreeArrayLayui)
	fmt.Println("Error when opening file: ", jsonData, string(jsonData))
	if err == nil {
		r.Response.WriteTpl("manage/role_list.html", g.Map{
			"header":   "This is header",
			"info":     role,
			"jsonData": string(jsonData),
		})
	}

}

// 混编一体部门（角色） 修改
func (c *cAdmin) Role_edit(r *ghttp.Request) {
	md := dao.AdminRole.Ctx(r.Context())
	if r.Request.Method == "GET" { //加载使用赋值用
		cid := r.Get("cid").String()
		mdclas := dao.AdminRole.Ctx(r.Context())
		role, err := mdclas.All()
		zhdatra := role.List()
		var a int64 = 0
		var chridnum []map[string]interface{}
		if zhdatra != nil {
			for _, v := range zhdatra {
				v["id"] = v["id"].(int32) //int 要转成 int64
				v["title"] = v["name"]
				num, _ := strconv.ParseInt(cid, 10, 64)
				if v["id"] == num { //当前id 不可选
					v["disabled"] = true
				}
				if v["pid"] == nil {
					v["pid"] = a
				} else {
					v["pid"] = int32(v["pid"].(int))
				}
				chridnum = append(chridnum, v)
			}
		}
		GetRuleTreeArrayLayui := tree.GetRuleTreeArrayLayui(chridnum, 0)
		var mytree []map[string]interface{}
		temp := make(map[string]interface{})
		temp["title"] = "顶级"
		temp["id"] = a
		temp["pid"] = nil
		temp["children"] = GetRuleTreeArrayLayui
		mytree = append(mytree, temp)

		jsonData, err := json.Marshal(mytree)

		if cid == "0" || cid == "" { //新增
			if err == nil {
				r.Response.WriteTpl("manage/role_edit.html", g.Map{
					"header":   "This is header",
					"newss":    g.Map{"cid": 0},
					"jsonData": string(jsonData),
				})
			}
		} else { //修改
			news, err := md.Where("id", cid).One()
			if err == nil {
				r.Response.WriteTpl("manage/role_edit.html", g.Map{
					"header":   "This is header",
					"newss":    news,
					"jsonData": string(jsonData),
				})
			}
		}

	} else { //提交表单用
		cid := r.GetForm("cid").String()
		pid := r.Get("demoTree_select_nodeId")

		if cid == "0" || cid == "" {

			mform := r.GetFormMap(map[string]interface{}{"name": "667", "pid": pid})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}

		} else {
			mform := r.GetFormMap(map[string]interface{}{"name": "", "pid": pid})
			result, err := md.Where("id", cid).Update(mform)

			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "成功",
					"data": result,
				})
			}
		}

	}

}

// 混编一体Tag 列表
func (c *cAdmin) Tag_list(r *ghttp.Request) {
	//	r.Response.Writeln("添加用户")
	md := dao.LTags.Ctx(r.Context())
	classs, _ := md.All()

	zhdatra := classs.List()
	var chridnum []map[string]interface{}
	if zhdatra != nil {
		for _, v := range zhdatra {
			v["id"] = v["tid"].(int64) //int 要转成 int64
			chridnum = append(chridnum, v)
		}
	}
	jsonData, err := json.Marshal(chridnum)
	if err == nil {
		r.Response.WriteTpl("manage/tag_list.html", g.Map{
			"header": "This is header",
			"info":   string(jsonData),
		})
	}

}

// 混编一体Tag 修改
func (c *cAdmin) Tag_edit(r *ghttp.Request) {
	md := dao.LTags.Ctx(r.Context())
	if r.Request.Method == "GET" { //加载使用赋值用
		cid := r.Get("tid").String()
		if cid == "0" || cid == "" { //新增
			r.Response.WriteTpl("manage/tag_edit.html", g.Map{
				"header": "This is header",
				"newss":  g.Map{"guid": 0},
			})
		} else { //修改
			news, err := md.Where("tid", cid).One()
			if err == nil {
				r.Response.WriteTpl("manage/tag_edit.html", g.Map{
					"header": "This is header",
					"newss":  news,
				})
			}
		}
	} else { //提交表单用
		TID := r.GetForm("tid").String()
		if TID == "0" || TID == "" { //添加
			mform := r.GetFormMap(map[string]interface{}{"tname": "", "tcontent": "", "tnotice": ""})
			result, err := md.Data(mform).Insert()
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "添加成功",
					"data": result,
				})
			}

		} else { //修改
			mform := r.GetFormMap(map[string]interface{}{"tname": "", "tcontent": "", "tnotice": ""})
			result, err := md.Where("tid", TID).Update(mform)
			if err == nil {
				r.Response.WriteJson(g.Map{
					"code": 200,
					"msg":  "修改成功",
					"data": result,
				})
			}
		}

	}

}
