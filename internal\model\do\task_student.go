// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskStudent is the golang structure of table Task_Student for DAO operations like Where/Data.
type TaskStudent struct {
	g.Meta      `orm:"table:Task_Student, do:true"`
	Id          interface{} //
	SGuid       interface{} //
	Tid         interface{} //
	Fid         interface{} //
	ISvipleve   interface{} //
	OState      interface{} //
	OType       interface{} //
	OCreatTime  *gtime.Time //
	Name        interface{} //
	Tel         interface{} //
	Provance    interface{} //
	City        interface{} //
	District    interface{} //
	Street      interface{} //
	ADDress     interface{} //
	BertheDay   *gtime.Time //
	ORemarks    interface{} //
	Com         interface{} //
	School      interface{} //
	Class       interface{} //
	ADDlocation interface{} //
	ADDname     interface{} //
	ADDtitle    interface{} //
	ADDdetail   interface{} //
	ADDlat      interface{} //
	ADDlon      interface{} //
}
