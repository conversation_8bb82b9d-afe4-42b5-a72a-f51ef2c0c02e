package wx

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type WxController struct{}

type PostReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.<PERSON>a `method:"post"  summary:"获得系统信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}

type GetReq struct {
	//g.Meta `path:"add/me" tags:"User" method:"get" x-group:"api/news" summary:"Get user list with basic info."`
	g.<PERSON>a `method:"get"  summary:"获得系统信息"`
	Page   int `dc:"页码，默认1" d:"1" x-sort:"1"`
	Size   int `dc:"一页数据量" d:"10" x-sort:"2"`
}
type GetListRes struct {
	Name string
	Id   int
}

func (c *WxController) Wxinit(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	ad := []map[string]interface{}{
		{"pic": "upload/ad1.jpg", "title": ""},
		{"pic": "upload/ad2.jpg", "title": ""},
		{"pic": "upload/ad3.jpg", "title": ""},
		{"pic": "upload/ad4.jpg", "title": ""},
		{"pic": "upload/ad5.jpg", "title": ""},
		{"pic": "upload/ad6.jpg", "title": ""},
	}

	Data := map[string]interface{}{
		"priceRules": map[string]interface{}{
			// 从配置文件中获取 basePrice 的值
			// 由于价格可能有两位小数，使用 Float64 类型获取配置值
			"basePrice":    g.Cfg().MustGet(ctx, "priceRules.basePrice").Float64(),
			"baseDistance": g.Cfg().MustGet(ctx, "priceRules.baseDistance").Int(),
			"extraPrice":   g.Cfg().MustGet(ctx, "priceRules.extraPrice").Float64(),
		},
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"logourl": "/images/logo.png",
			"name":    "安保施救系统",
			"tel":     "***********",
			"time":    "8:00-19:00",
			"ad":      ad,
			"data":    Data,
		})
	return
}

func (c *WxController) glist(ctx context.Context, req *GetReq) (res *GetListRes, err error) {

	r := g.RequestFromCtx(ctx)
	name := r.GetQuery("name", "没参数的默认返回值!") //获取name

	listgood := g.Map{
		"GPrice":      3213,
		"GPrice2":     12,
		"G_Introduce": "颜色分类：透明-抽象猫咪生产企业：深圳款式：保护壳",
		"Name":        "抽象猫咪手机壳",
		"gid":         1,
		"numb":        0,
		"pic":         "/Up/2",
	}

	listjson := g.Map{
		"typeName":    "a4",
		"GetImgs":     "img",
		"menuContent": listgood,
	}

	g.RequestFromCtx(ctx).Response.WriteJson(
		g.Map{
			"businessend":   "21:00",
			"logourl":       "/images/logo.png",
			"name":          "--演示系统 请勿下单",
			"minamount":     "99",
			"businessstart": "11:03",
			"address":       "2东路269号",
			"notice":        "洗衣洗鞋新时尚",
			"telephone":     "***********",
			"tel":           "0871-66227317",
			"expressfee":    "1",
			"ad":            name,
			"seller":        listjson,
		})
	return
}
