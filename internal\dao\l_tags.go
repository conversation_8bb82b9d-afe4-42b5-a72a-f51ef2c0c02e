// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"50go/internal/dao/internal"
)

// internalLTagsDao is internal type for wrapping internal DAO implements.
type internalLTagsDao = *internal.LTagsDao

// lTagsDao is the data access object for table L_Tags.
// You can define custom methods on it to extend its functionality as you wish.
type lTagsDao struct {
	internalLTagsDao
}

var (
	// LTags is globally public accessible object for table L_Tags operations.
	LTags = lTagsDao{
		internal.NewLTagsDao(),
	}
)

// Fill with you ideas below.
