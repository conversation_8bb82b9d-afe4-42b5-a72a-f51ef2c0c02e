﻿using Common.Wx;
using Models;
using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Web;
using WxPayAPI;

public partial class wxProcess2 : System.Web.UI.Page
{
    #region 属性
    private static string appid = WxPayConfig.APPID;
    private static string appsecret = WxPayConfig.APPSECRET;
    #endregion

    public string reurl = "", pid = "", myparent;
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            //获取从wxProcess.aspx传递过来的跳转地址reurl
            if (Request.QueryString["reurl"] != null && Request.QueryString["reurl"] != "")
            {
                reurl = Request.QueryString["reurl"].ToString();
            }

            pid = Utils.GetCookie("pid", "lncms");

            string code = "";
            if (Request.QueryString["code"] != null && Request.QueryString["code"] != "")
            {
                //获取微信回传的code
                code = Request.QueryString["code"].ToString();
                OAuth_Token Model = Get_token(code);  //获取token
                OAuthUser OAuthUser_Model = Get_UserInfo(Model.access_token, Model.openid);
               // OAuth_Token model = getToke();  //获取token(UnionID机制)
                OAuthUser UnionIDUser_Model = getUserInf(WxHelper.IsExistAccess_Token(), Model.openid);//(UnionID机制)
                //if (UnionIDUser_Model.subscribe == "0")//未关注公众号
                //{
                   
                //        Response.Redirect("/4g/reg.aspx?sub=0&reurl=" + HttpUtility.UrlEncode(reurl));
                    
                //}
             
                    if (OAuthUser_Model.openid != null && OAuthUser_Model.openid != "")  //已获取得openid及其他信息
                    {
                        //在页面上输出用户信息
                        //  Response.Write("用户OPENID:" + OAuthUser_Model.openid + "<br>用户昵称:" + OAuthUser_Model.nickname + "<br>性别:" + OAuthUser_Model.sex + "<br>所在省:" + OAuthUser_Model.province + "<br>所在市:" + OAuthUser_Model.city + "<br>所在国家:" + OAuthUser_Model.country + "<br>头像地址:" + OAuthUser_Model.headimgurl + "<br>用户特权信息:" + OAuthUser_Model.privilege);
                        //或跳转到自己的页面，想怎么处理就怎么处理

                        using (Entities db = new Entities())
                        {
                            var wxuser = db.User.FirstOrDefault(p => p.UOpenID == OAuthUser_Model.openid);
                            if (wxuser == null)//如果没有微信信息，表示第一次进入系统，
                            {
                                wxuser = new User();
                                wxuser.UGuid = _rky.GenerateStringID();
                                wxuser.UOpenID = OAuthUser_Model.openid;
                                wxuser.UNickName = OAuthUser_Model.nickname;
                                wxuser.UHeadImg = OAuthUser_Model.headimgurl;
                                wxuser.Ustate = 0;
                                wxuser.Ujifen = 0;
                                wxuser.RegTime = DateTime.Now;

                                //if (UnionIDUser_Model.subscribe_time != null)
                                //    wxuser.USubscribeTime = yhyy.GetTime(long.Parse(UnionIDUser_Model.subscribe_time), false);
                                //UparentId  团队 id
                                //UIndirectParentId  推荐人 id
                                 wxuser.UIndirectParentId = pid;  //直接推荐人

                                 reurl = "/4g/login/reg.aspx?reurl=" + HttpUtility.UrlEncode(reurl);
                                
                                db.User.Add(wxuser);
                                db.SaveChanges();
                              }
                            else //更新一次
                            {
                                if (!string.IsNullOrEmpty(pid))//是扫码进来的
                                {
                                //UparentId  团队 id
                                //UIndirectParentId  推荐人 id
                              
                               if (string.IsNullOrEmpty(wxuser.UTel))
                                    { 
                                    wxuser.UIndirectParentId = pid;  //直接推荐人
                                    db.SaveChanges();
                                   }
                                }
                            if (string.IsNullOrEmpty(wxuser.UIndirectParentId) || string.IsNullOrEmpty(wxuser.UTel))//没有上级，也没有认证电话
                                {
                                    reurl = "/4g/login/reg.aspx?reurl=" + HttpUtility.UrlEncode(reurl);
                                }
                            }

                            Session["SOPENID"] = OAuthUser_Model.openid;
                            Session["Subscribe"] = OAuthUser_Model.subscribe;//0未关注，1已关注
                            Session["UGUID"] = wxuser.UGuid;
                            Response.Redirect(reurl);
                        }
                    }
                    else  //未获得openid，回到wxProcess.aspx，访问弹出微信授权页面，提示用户授权
                    {
                        Response.Redirect("wxProcess.aspx?auth=1");
                    }
                
            }
        }
    }

    public void getVIPparent(string DirectPguid)
    {
        using (Entities db = new Entities())
        {
            var PUser = db.User.SingleOrDefault(p => p.UGuid == DirectPguid); //panduan 上级
            if (PUser == null)
                return;
            else if (PUser.Ustate == 2)    //总经销团队归自己
                myparent = PUser.UGuid;
            else                           //否则向上找
            {
                if (PUser.UparentId == null)
                    return;
                else
                    getVIPparent(PUser.UparentId);

            }
        }

    }

    //根据appid，secret，code获取微信openid、access token信息
    protected OAuth_Token Get_token(string Code)
    {
        //获取微信回传的openid、access token
        string Str = GetJson("https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appid + "&secret=" + appsecret + "&code=" + Code + "&grant_type=authorization_code");
        //微信回传的数据为Json格式，将Json格式转化成对象
        OAuth_Token Oauth_Token_Model = JsonHelper.ParseFromJson<OAuth_Token>(Str);
        return Oauth_Token_Model;
    }

    //刷新Token(好像这个刷新Token没有实际作用)
    protected OAuth_Token refresh_token(string REFRESH_TOKEN)
    {
        string Str = GetJson("https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=" + appid + "&grant_type=refresh_token&refresh_token=" + REFRESH_TOKEN);
        OAuth_Token Oauth_Token_Model = JsonHelper.ParseFromJson<OAuth_Token>(Str);
        return Oauth_Token_Model;
    }

    //根据openid，access token获得用户信息
    protected OAuthUser Get_UserInfo(string REFRESH_TOKEN, string OPENID)
    {
        string Str = GetJson("https://api.weixin.qq.com/sns/userinfo?access_token=" + REFRESH_TOKEN + "&openid=" + OPENID);
        OAuthUser OAuthUser_Model = JsonHelper.ParseFromJson<OAuthUser>(Str);
        return OAuthUser_Model;
    }

    //这个 有限制 一天1万次 存入 xml里面l
    //protected OAuth_Token getToke()
    //{

    //    //获取微信回传的openid、access token
    //    string Str = GetJson("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + appsecret);

    //    //微信回传的数据为Json格式，将Json格式转化成对象
    //    OAuth_Token Oauth_Token_Model = JsonHelper.ParseFromJson<OAuth_Token>(Str);
    //    return Oauth_Token_Model;
    //}
    protected OAuthUser getUserInf(string REFRESH_TOKEN, string OPENID)
    {
        string Str = GetJson("https://api.weixin.qq.com/cgi-bin/user/info?access_token=" + REFRESH_TOKEN + "&openid=" + OPENID + "&lang=zh_CN");


        OAuthUser OAuthUser_Model = JsonHelper.ParseFromJson<OAuthUser>(Str);
        return OAuthUser_Model;
    }

    //访问微信url并返回微信信息
    protected string GetJson(string url)
    {
        WebClient wc = new WebClient();
        wc.Credentials = CredentialCache.DefaultCredentials;
        wc.Encoding = Encoding.UTF8;
        string returnText = wc.DownloadString(url);

        if (returnText.Contains("errcode"))
        {
            //可能发生错误
        }
        return returnText;
    }


    /// <summary>
    /// token类
    /// </summary>
    public class OAuth_Token
    {
        public OAuth_Token()
        {

            //
            //TODO: 在此处添加构造函数逻辑
            //
        }
        //access_token	网页授权接口调用凭证,注意：此access_token与基础支持的access_token不同
        //expires_in	access_token接口调用凭证超时时间，单位（秒）
        //refresh_token	用户刷新access_token
        //openid	用户唯一标识，请注意，在未关注公众号时，用户访问公众号的网页，也会产生一个用户和公众号唯一的OpenID
        //scope	用户授权的作用域，使用逗号（,）分隔
        public string _access_token;
        public string _expires_in;
        public string _refresh_token;
        public string _openid;
        public string _scope;
        public string access_token
        {
            set { _access_token = value; }
            get { return _access_token; }
        }
        public string expires_in
        {
            set { _expires_in = value; }
            get { return _expires_in; }
        }

        public string refresh_token
        {
            set { _refresh_token = value; }
            get { return _refresh_token; }
        }
        public string openid
        {
            set { _openid = value; }
            get { return _openid; }
        }
        public string scope
        {
            set { _scope = value; }
            get { return _scope; }
        }

    }

    /// <summary>
    /// 用户信息类
    /// </summary>
    public class OAuthUser
    {
        public OAuthUser()
        { }
        #region 数据库字段
        private string _openID;
        private string _searchText;
        private string _nickname;
        private string _sex;
        private string _province;
        private string _city;
        private string _country;
        private string _headimgUrl;
        private string _privilege;
        private string _subscribe;
        private string _subscribe_time;
        #endregion

        #region 字段属性
        /// <summary>
        /// 用户的唯一标识
        /// </summary>
        public string openid
        {
            set { _openID = value; }
            get { return _openID; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string SearchText
        {
            set { _searchText = value; }
            get { return _searchText; }
        }
        /// <summary>
        /// 用户昵称 
        /// </summary>
        public string nickname
        {
            set { _nickname = value; }
            get { return _nickname; }
        }
        /// <summary>
        /// 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知 
        /// </summary>
        public string sex
        {
            set { _sex = value; }
            get { return _sex; }
        }
        /// <summary>
        /// 用户个人资料填写的省份
        /// </summary>
        public string province
        {
            set { _province = value; }
            get { return _province; }
        }
        /// <summary>
        /// 普通用户个人资料填写的城市 
        /// </summary>
        public string city
        {
            set { _city = value; }
            get { return _city; }
        }
        /// <summary>
        /// 国家，如中国为CN 
        /// </summary>
        public string country
        {
            set { _country = value; }
            get { return _country; }
        }
        /// <summary>
        /// 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空
        /// </summary>
        public string headimgurl
        {
            set { _headimgUrl = value; }
            get { return _headimgUrl; }
        }
        /// <summary>
        /// 用户特权信息，json 数组，如微信沃卡用户为（chinaunicom）其实这个格式称不上JSON，只是个单纯数组
        /// </summary>
        public string privilege
        {
            set { _privilege = value; }
            get { return _privilege; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string subscribe
        {
            set { _subscribe = value; }
            get { return _subscribe; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string subscribe_time
        {
            set { _subscribe_time = value; }
            get { return _subscribe_time; }
        }
        #endregion
    }

    /// <summary>
    /// 将Json格式数据转化成对象
    /// </summary>
    public class JsonHelper
    {
        /// <summary>  
        /// 生成Json格式  
        /// </summary>  
        /// <typeparam name="T"></typeparam>  
        /// <param name="obj"></param>  
        /// <returns></returns>  
        public static string GetJson<T>(T obj)
        {
            DataContractJsonSerializer json = new DataContractJsonSerializer(obj.GetType());
            using (MemoryStream stream = new MemoryStream())
            {
                json.WriteObject(stream, obj);
                string szJson = Encoding.UTF8.GetString(stream.ToArray()); return szJson;
            }
        }
        /// <summary>  
        /// 获取Json的Model  
        /// </summary>  
        /// <typeparam name="T"></typeparam>  
        /// <param name="szJson"></param>  
        /// <returns></returns>  
        public static T ParseFromJson<T>(string szJson)
        {
            T obj = Activator.CreateInstance<T>();
            using (MemoryStream ms = new MemoryStream(Encoding.UTF8.GetBytes(szJson)))
            {
                DataContractJsonSerializer serializer = new DataContractJsonSerializer(obj.GetType());
                return (T)serializer.ReadObject(ms);
            }
        }
    }
}
